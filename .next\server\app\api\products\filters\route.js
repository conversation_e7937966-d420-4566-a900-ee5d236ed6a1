"use strict";(()=>{var e={};e.id=2330,e.ids=[2330],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19730:(e,t,r)=>{let i;r.r(t),r.d(t,{originalPathname:()=>S,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y});var o={};r.r(o),r.d(o,{GET:()=>c});var n=r(49303),a=r(88716),s=r(60670),d=r(87070),u=r(89456),m=r(81515);let c=(i=async e=>{await (0,u.Z)();let{searchParams:t}=new URL(e.url),r=t.get("category"),i={isActive:!0};if(r){let e=await m.WD.findOne({slug:r}).select("_id").lean();e?._id&&(i.categoryId=String(e._id))}let[o,n,a]=await Promise.all([m.xs.aggregate([{$match:i},{$group:{_id:null,minPrice:{$min:"$price"},maxPrice:{$max:"$price"}}}]),m.WD.aggregate([{$match:{isActive:!0}},{$lookup:{from:"products",localField:"_id",foreignField:"categoryId",as:"products",pipeline:[{$match:{isActive:!0}}]}},{$project:{id:{$toString:"$_id"},name:1,slug:1,description:1,product_count:{$size:"$products"}}},{$sort:{name:1}}]),m.xs.countDocuments(i)]),s=o[0]||{minPrice:0,maxPrice:1e3};return d.NextResponse.json({success:!0,data:{priceRange:{min:Number(s.minPrice)||0,max:Number(s.maxPrice)||1e3},categories:n,totalProducts:Number(a)}})},async(...e)=>{try{return await i(...e)}catch(e){return console.error(e),d.NextResponse.json({success:!1,error:"Internal Server Error"},{status:500})}}),p=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/products/filters/route",pathname:"/api/products/filters",filename:"route",bundlePath:"app/api/products/filters/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\filters\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:l,staticGenerationAsyncStorage:y,serverHooks:g}=p,S="/api/products/filters/route";function f(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>C,Dd:()=>x,Order:()=>O,P_:()=>A,Pp:()=>M,Th:()=>v,Vv:()=>F,WD:()=>T,gc:()=>E,hQ:()=>B,kL:()=>j,mA:()=>U,n5:()=>N,nW:()=>w,p1:()=>k,qN:()=>D,wV:()=>R,xs:()=>P});var i=r(11185),o=r.n(i);let n=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),a=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),s=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),m=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),c=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),f=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),h=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),q=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),I=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=o().models.User||o().model("User",n),P=o().models.Product||o().model("Product",a),T=o().models.Category||o().model("Category",s),O=o().models.Order||o().model("Order",d),w=o().models.HomepageSetting||o().model("HomepageSetting",u),E=o().models.Testimonial||o().model("Testimonial",m),D=o().models.ProductImage||o().model("ProductImage",c),v=o().models.ProductVariant||o().model("ProductVariant",p),C=o().models.Review||o().model("Review",l),j=o().models.Address||o().model("Address",y),x=o().models.OrderItem||o().model("OrderItem",g),A=o().models.Notification||o().model("Notification",S),R=o().models.Coupon||o().model("Coupon",f);o().models.Wishlist||o().model("Wishlist",h);let U=o().models.Newsletter||o().model("Newsletter",q),M=o().models.ProductCategory||o().model("ProductCategory",I),B=o().models.WishlistItem||o().model("WishlistItem",b),$=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),F=o().models.NotificationTemplate||o().model("NotificationTemplate",$),_=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),k=o().models.Enquiry||o().model("Enquiry",_)},89456:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(11185),o=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let s=async function(){if(a.conn)return a.conn;a.promise||(a.promise=o().connect(n,{bufferCommands:!1}));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(19730));module.exports=i})();