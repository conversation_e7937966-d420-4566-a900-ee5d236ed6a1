"use strict";(()=>{var e={};e.id=1996,e.ids=[1996],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},93503:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>m,requestAsyncStorage:()=>d,routeModule:()=>f,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{GET:()=>c});var i=r(49303),n=r(88716),s=r(60670),a=r(87070),u=r(75571),p=r(95306),l=r(54211);async function c(e){try{let t=await (0,u.getServerSession)(p.L);if(!t?.user?.id)return a.NextResponse.json({error:"Authentication required"},{status:401});let{searchParams:r}=new URL(e.url),o=parseInt(r.get("page")||"1"),i=parseInt(r.get("limit")||"10");return l.kg.info("Notifications API temporarily disabled during migration"),a.NextResponse.json({success:!0,data:{notifications:[],pagination:{page:o,limit:i,totalCount:0,totalPages:0,hasNext:!1,hasPrev:!1},unreadCount:0}})}catch(e){return l.kg.error("Failed to fetch user notifications",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/notifications/route",pathname:"/api/notifications",filename:"route",bundlePath:"app/api/notifications/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:g}=f,y="/api/notifications/route";function m(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},54211:(e,t,r)=>{var o;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(o||(o={}));class i{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:i,context:n,error:s,userId:a,requestId:u}=e,p=o[r],l=`[${t}] ${p}: ${i}`;return a&&(l+=` | User: ${a}`),u&&(l+=` | Request: ${u}`),n&&Object.keys(n).length>0&&(l+=` | Context: ${JSON.stringify(n)}`),s&&(l+=` | Error: ${s.message}`,this.isDevelopment&&s.stack&&(l+=`
Stack: ${s.stack}`)),l}log(e,t,r,o){if(!this.shouldLog(e))return;let i={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:o},n=this.formatMessage(i);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(i))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,o){this.info(`API ${e} ${t}`,{...o,userId:r,type:"api_request"})}apiResponse(e,t,r,o,i){this.info(`API ${e} ${t} - ${r}`,{...i,statusCode:r,duration:o,type:"api_response"})}apiError(e,t,r,o,i){this.error(`API ${e} ${t} failed`,r,{...i,userId:o,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,o){this.warn("Authentication failed",{...o,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,o){this.debug(`DB ${e} on ${t}`,{...o,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,o){this.error(`DB ${e} on ${t} failed`,r,{...o,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,o){this.warn("Rate limit exceeded",{...o,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,o){this.info("Email sent",{...o,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,o){this.error("Email failed to send",r,{...o,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new i},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=i?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(o,n,a):o[n]=e[n]}return o.default=e,r&&r.set(e,o),o}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,5972,8691,2830,5306],()=>r(93503));module.exports=o})();