(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[328],{49087:function(e,t,s){Promise.resolve().then(s.bind(s,61358))},61358:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return j}});var r=s(57437),a=s(2265),n=s(80605),i=s(99376),l=s(22252),c=s(91723),o=s(65302),d=s(45131),u=s(73247),h=s(90740),m=s(92451),x=s(10407),g=s(92369),y=s(89345),f=s(31047),p=s(53417);let b={NEW:{label:"New",color:"bg-blue-100 text-blue-800",icon:l.Z},IN_PROGRESS:{label:"In Progress",color:"bg-yellow-100 text-yellow-800",icon:c.Z},RESOLVED:{label:"Resolved",color:"bg-green-100 text-green-800",icon:o.Z},CLOSED:{label:"Closed",color:"bg-gray-100 text-gray-800",icon:d.Z}};function j(){let{data:e,status:t}=(0,n.useSession)(),s=(0,i.useRouter)(),[l,c]=(0,a.useState)([]),[o,d]=(0,a.useState)(!0),[j,N]=(0,a.useState)(null),[v,w]=(0,a.useState)(""),[k,Z]=(0,a.useState)("ALL"),[S,C]=(0,a.useState)(1),[E,q]=(0,a.useState)(1),[P,L]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if("loading"!==t){if(!e||"ADMIN"!==e.user.role){s.push("/admin");return}R()}},[e,t,s,S,k,v]);let R=async()=>{try{d(!0);let e=new URLSearchParams({page:S.toString(),limit:"10",..."ALL"!==k&&{status:k},...v&&{search:v}}),t=await fetch("/api/enquiries?".concat(e));if(!t.ok)throw Error("Failed to fetch enquiries");let s=await t.json();c(s.enquiries),q(s.pagination.totalPages)}catch(e){console.error("Error fetching enquiries:",e)}finally{d(!1)}},A=async(e,t)=>{try{L(!0);let s=await fetch("/api/enquiries/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update enquiry");let r=await s.json();c(t=>t.map(t=>t.id===e?r.enquiry:t)),(null==j?void 0:j.id)===e&&N(r.enquiry)}catch(e){console.error("Error updating enquiry:",e)}finally{L(!1)}},M=async e=>{if(confirm("Are you sure you want to delete this enquiry?"))try{if(!(await fetch("/api/enquiries/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete enquiry");c(t=>t.filter(t=>t.id!==e)),(null==j?void 0:j.id)===e&&N(null)}catch(e){console.error("Error deleting enquiry:",e)}},O=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return"loading"===t||o?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):(0,r.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Contact Enquiries"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, subject, or message...",value:v,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.Z,{className:"text-gray-400 h-5 w-5"}),(0,r.jsxs)("select",{value:k,onChange:e=>Z(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"ALL",children:"All Status"}),(0,r.jsx)("option",{value:"NEW",children:"New"}),(0,r.jsx)("option",{value:"IN_PROGRESS",children:"In Progress"}),(0,r.jsx)("option",{value:"RESOLVED",children:"Resolved"}),(0,r.jsx)("option",{value:"CLOSED",children:"Closed"})]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsx)("h2",{className:"font-semibold text-gray-900",children:"Enquiries"})}),(0,r.jsx)("div",{className:"divide-y",children:l.map(e=>{let t=b[e.status].icon;return(0,r.jsxs)("div",{onClick:()=>N(e),className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors ".concat((null==j?void 0:j.id)===e.id?"bg-green-50":""),children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 truncate",children:e.name}),(0,r.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ".concat(b[e.status].color),children:[(0,r.jsx)(t,{className:"h-3 w-3"}),b[e.status].label]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 truncate",children:e.subject}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:O(e.createdAt)})]},e.id)})}),E>1&&(0,r.jsxs)("div",{className:"p-4 border-t flex items-center justify-between",children:[(0,r.jsx)("button",{onClick:()=>C(e=>Math.max(1,e-1)),disabled:1===S,className:"p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(m.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",S," of ",E]}),(0,r.jsx)("button",{onClick:()=>C(e=>Math.min(E,e+1)),disabled:S===E,className:"p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(x.Z,{className:"h-5 w-5"})})]})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:j?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[(0,r.jsx)("div",{className:"p-6 border-b",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:j.subject}),(0,r.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(g.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:j.name})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(y.Z,{className:"h-4 w-4"}),(0,r.jsx)("a",{href:"mailto:".concat(j.email),className:"text-green-600 hover:underline",children:j.email})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(f.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:O(j.createdAt)})]})]})]}),(0,r.jsx)("select",{value:j.status,onChange:e=>A(j.id,{status:e.target.value}),disabled:P,className:"px-3 py-1 rounded-full text-sm font-medium ".concat(b[j.status].color," border-0 focus:ring-2 focus:ring-green-500"),children:Object.entries(b).map(e=>{let[t,s]=e;return(0,r.jsx)("option",{value:t,children:s.label},t)})})]})}),(0,r.jsxs)("div",{className:"p-6 border-b",children:[(0,r.jsxs)("h3",{className:"font-medium text-gray-900 mb-3 flex items-center gap-2",children:[(0,r.jsx)(p.Z,{className:"h-4 w-4"}),"Message"]}),(0,r.jsx)("p",{className:"text-gray-700 whitespace-pre-wrap",children:j.message})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Admin Notes"}),(0,r.jsx)("textarea",{value:j.notes||"",onChange:e=>N({...j,notes:e.target.value}),placeholder:"Add internal notes about this enquiry...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none",rows:4}),(0,r.jsxs)("div",{className:"mt-4 flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>A(j.id,{notes:j.notes||""}),disabled:P,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Save Notes"}),(0,r.jsx)("button",{onClick:()=>M(j.id),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Delete Enquiry"})]})]})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[(0,r.jsx)(p.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Select an enquiry to view details"})]})})]})]})}},22252:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},31047:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},65302:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},92451:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},10407:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},91723:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},90740:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},89345:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},53417:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},73247:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},92369:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},45131:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},99376:function(e,t,s){"use strict";var r=s(35475);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})}},function(e){e.O(0,[7349,2971,2117,1744],function(){return e(e.s=49087)}),_N_E=e.O()}]);