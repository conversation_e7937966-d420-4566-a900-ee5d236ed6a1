(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3021],{4239:function(e,s,a){Promise.resolve().then(a.bind(a,28250)),Promise.resolve().then(a.bind(a,9545))},55497:function(e,s,a){"use strict";a.d(s,{Z:function(){return N}});var t=a(57437);a(2265);var l=a(27648),r=a(89547),i=a(82023),n=a(66927),c=a(88997),o=a(44794),d=a(48757),x=a(86595),m=a(42208),h=a(12805),u=a(38220),g=a(11239),f=a(9646),p=a(80512),j=a(93609),v=a(94394),b=a(33741),N=e=>{let{product:s,showAsLinks:a=!1,className:N="",maxCategories:y,size:w="xs"}=e,S=function(e){let s=[];return e.productCategories&&e.productCategories.length>0&&e.productCategories.forEach(e=>{e.category&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug})}),e.category&&!s.some(s=>s.id===e.category.id)&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug}),s}(s),Z=y?S.slice(0,y):S;if(0===Z.length)return null;let C={xs:"text-xs",sm:"text-sm",base:"text-base",lg:"text-lg"},k={xs:"w-3 h-3",sm:"w-4 h-4",base:"w-5 h-5",lg:"w-6 h-6"},E=e=>{let s=k[w],a=e.toLowerCase(),l={skincare:(0,t.jsx)(r.Z,{className:s}),"hair care":(0,t.jsx)(i.Z,{className:s}),"hair oils":(0,t.jsx)(n.Z,{className:s}),"body care":(0,t.jsx)(c.Z,{className:s}),"all products":(0,t.jsx)(o.Z,{className:s}),"cleansers & face wash":(0,t.jsx)(d.Z,{className:s}),"combo & complete care":(0,t.jsx)(x.Z,{className:s}),"creams & moisturizers":(0,t.jsx)(n.Z,{className:s}),"eye & lip care":(0,t.jsx)(m.Z,{className:s}),"facial kits":(0,t.jsx)(h.Z,{className:s}),"facial oils & elixirs":(0,t.jsx)(u.Z,{className:s}),"gels & serums":(0,t.jsx)(g.Z,{className:s}),"hair masks":(0,t.jsx)(f.Z,{className:s}),"massage & body oils":(0,t.jsx)(p.Z,{className:s}),"scrubs & exfoliants":(0,t.jsx)(i.Z,{className:s}),toners:(0,t.jsx)(j.Z,{className:s}),"ubtan & masks":(0,t.jsx)(v.Z,{className:s}),cleanser:(0,t.jsx)(d.Z,{className:s}),serum:(0,t.jsx)(g.Z,{className:s}),moisturizer:(0,t.jsx)(n.Z,{className:s}),mask:(0,t.jsx)(f.Z,{className:s}),exfoliator:(0,t.jsx)(i.Z,{className:s}),"eye-care":(0,t.jsx)(m.Z,{className:s}),oil:(0,t.jsx)(n.Z,{className:s}),toner:(0,t.jsx)(j.Z,{className:s}),kit:(0,t.jsx)(h.Z,{className:s}),ubtan:(0,t.jsx)(v.Z,{className:s}),baby:(0,t.jsx)(b.Z,{className:s})};if(l[a])return l[a];for(let[e,s]of Object.entries(l))if(a.includes(e)||e.includes(a))return s;return(0,t.jsx)(r.Z,{className:s})};return(0,t.jsx)("div",{className:"flex flex-wrap gap-3 ".concat(N),children:Z.map(e=>{let s=E(e.name);return a?(0,t.jsxs)(l.default,{href:"/shop?category=".concat(e.slug),className:"inline-flex items-center gap-1.5 ".concat(C[w]," font-medium text-green-600 hover:text-green-700 transition-colors"),children:[s,(0,t.jsx)("span",{children:e.name})]},e.id):(0,t.jsxs)("span",{className:"inline-flex items-center gap-1.5 ".concat(C[w]," font-medium text-green-600"),children:[s,(0,t.jsx)("span",{children:e.name})]},e.id)})})}},9545:function(e,s,a){"use strict";a.d(s,{default:function(){return L}});var t=a(57437),l=a(2265),r=a(99376),i=a(73247),n=a(35363),c=a(32489),o=a(41473),d=a(7586),x=a(28842),m=a(27648),h=a(80605),u=a(33145),g=a(88997),f=a(86595),p=a(99397),j=a(16275),v=a(53827);function b(e){return!e||e.includes("placeholder")||e.includes("api/placeholder")?"/images/default-product.jpg":e}var N=a(55497),y=a(1396),w=a(8329),S=a(92840),Z=a(19124),C=e=>{let{product:s,featured:a=!1,viewMode:i="grid"}=e,{dispatch:n}=(0,v.j)(),{flashSaleSettings:c}=(0,y.u)(),{data:o}=(0,h.useSession)(),d=(0,r.useRouter)(),{showToast:x}=(0,S.V)(),{refreshUnreadCount:C,refreshNotifications:k}=(0,Z.z)(),[E,P]=(0,l.useState)(!1),[F,D]=(0,l.useState)(!1),[M,_]=(0,l.useState)(!1);(0,l.useEffect)(()=>{(async()=>{var e;if((null==o?void 0:null===(e=o.user)||void 0===e?void 0:e.id)&&(null==s?void 0:s.id))try{let e=await fetch("/api/wishlist");if(e.ok){let a=(await e.json()).items.some(e=>e.id===s.id);P(a)}}catch(e){console.error("Error checking wishlist status:",e)}})()},[o,s]);let L=e=>{e.preventDefault(),e.stopPropagation();let a=s.price||0,t=s.variants;if(0===s.price&&t&&t.length>0){let e=t.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0}).filter(e=>e>0);e.length>0&&(a=Math.max(...e))}n({type:"ADD_ITEM",payload:{...s,price:a}})},O=async e=>{var a;if(e.preventDefault(),e.stopPropagation(),!(null==o?void 0:null===(a=o.user)||void 0===a?void 0:a.id)){d.push("/login");return}if(null==s?void 0:s.id){D(!0);try{if(E){let e=await fetch("/api/wishlist?productId=".concat(s.id),{method:"DELETE"});if(e.ok)P(!1),x("Removed from wishlist","success"),C(),k();else{let s=await e.json();x(s.error||"Failed to remove from wishlist","error")}}else{let e=await fetch("/api/wishlist",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:s.id})});if(e.ok)P(!0),x("Added to wishlist","success"),C(),k();else{let s=await e.json();x(s.error||"Failed to add to wishlist","error")}}}catch(e){console.error("Error updating wishlist:",e),x("An unexpected error occurred.","error")}finally{D(!1)}}},T=s.variants,R=s.price||0,A="",I="",z=!1;if(T&&T.length>0){let e=[s.price||0,...T.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0})].filter(e=>e>0);if(e.length>0){let s=Math.min(...e),a=Math.max(...e);if(s!==a){z=!0;let e=(0,w.FO)(s,c),t=(0,w.FO)(a,c);e.isOnSale?(A="₹".concat(e.salePrice," - ₹").concat(t.salePrice),I="₹".concat(s," - ₹").concat(a)):A="₹".concat(s," - ₹").concat(a)}else R=s}}if(!z){let e=(0,w.FO)(R,c);e.isOnSale?(A="₹".concat(e.salePrice),I="₹".concat(e.originalPrice)):A="₹".concat(R)}return"list"===i?(0,t.jsx)(m.default,{href:"/product/".concat(s.slug||s.id),className:"block group",children:(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-green-200",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"relative w-28 h-28 sm:w-40 sm:h-40 flex-shrink-0",children:(0,t.jsxs)("div",{className:"w-full h-full relative overflow-hidden rounded-l-2xl",children:[!M&&(0,t.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),(0,t.jsx)(u.default,{src:b(s.image),alt:s.name,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300 ".concat(M?"opacity-100":"opacity-0"),sizes:"(max-width: 640px) 112px, 160px",onLoad:()=>_(!0)})]})}),(0,t.jsxs)("div",{className:"flex-1 p-3 sm:p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-1 line-clamp-1 text-base sm:text-lg",children:s.name}),(0,t.jsx)("div",{className:"mb-2",children:(0,t.jsx)(N.Z,{product:s._raw||s,maxCategories:1})})]}),(0,t.jsx)("button",{onClick:O,className:"p-1.5 sm:p-2 rounded-full transition-colors ml-2 ".concat(E?"text-red-500 bg-red-50 hover:bg-red-100":"text-gray-400 hover:text-red-500 hover:bg-red-50"),children:(0,t.jsx)(g.Z,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4 ".concat(E?"fill-current":"")})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-base sm:text-lg font-bold text-gray-900",children:A}),I&&(0,t.jsx)("span",{className:"text-sm sm:text-base text-gray-500 line-through",children:I})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),(0,t.jsx)("span",{className:"text-xs text-gray-600 ml-1",children:s.rating})]}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(",s.reviews," reviews)"]})]})]}),(0,t.jsxs)("button",{onClick:L,className:"bg-green-600 text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl hover:bg-green-700 transition-colors shadow-sm hover:shadow-md flex items-center space-x-1.5 sm:space-x-2",children:[(0,t.jsx)(p.Z,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"}),(0,t.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Add"})]})]})]})]})})}):(0,t.jsx)(m.default,{href:"/product/".concat(s.slug||s.id),className:"block group",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-green-200 ".concat(a?"w-72 flex-shrink-0 lg:w-80":"w-full"),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"w-full aspect-square relative overflow-hidden",children:[!M&&(0,t.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),(0,t.jsx)(u.default,{src:b(s.image),alt:s.name,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300 ".concat(M?"opacity-100":"opacity-0"),sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",onLoad:()=>_(!0)})]}),(0,t.jsx)("div",{className:"absolute top-3 right-3 flex flex-col space-y-2",children:(0,t.jsx)("button",{onClick:O,className:"p-2 rounded-full shadow-sm transition-all duration-200 ".concat(E?"bg-red-500 text-white":"bg-white text-gray-600 hover:text-red-500 hover:bg-red-50"),children:(0,t.jsx)(g.Z,{className:"w-4 h-4 ".concat(E?"fill-current":"")})})}),(0,t.jsx)("div",{className:"absolute top-3 left-3 bg-white rounded-full px-2 py-1 shadow-sm",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(f.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),(0,t.jsx)("span",{className:"text-xs font-medium text-gray-700",children:s.rating})]})}),I&&(null==c?void 0:c.flashSalePercentage)&&(0,t.jsxs)("div",{className:"absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-sm",children:[c.flashSalePercentage,"% OFF"]})]}),(0,t.jsxs)("div",{className:"p-4 lg:p-5",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-1 text-base lg:text-lg leading-tight",children:s.name}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsx)(N.Z,{product:s._raw||s,className:"mb-2",maxCategories:1})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("span",{className:"text-base lg:text-lg font-bold text-gray-900",children:A}),I&&(0,t.jsx)("span",{className:"text-sm lg:text-base text-gray-500 line-through",children:I})]}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[s.reviews," reviews"]})]}),(0,t.jsx)("button",{onClick:L,className:"bg-green-600 text-white p-2.5 lg:p-3 rounded-full hover:bg-green-700 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105 active:scale-95",children:(0,t.jsx)(j.Z,{className:"w-4 h-4 lg:w-5 lg:h-5"})})]})]})]})})},k=a(15863),E=e=>{let{hasMore:s,loading:a,onLoadMore:r,threshold:i=100,children:n}=e,c=(0,l.useRef)(null),o=(0,l.useRef)(!1),d=(0,l.useCallback)(e=>{let[t]=e;t.isIntersecting&&s&&!a&&!o.current&&(o.current=!0,r(),setTimeout(()=>{o.current=!1},1e3))},[s,a,r]);return(0,l.useEffect)(()=>{let e=c.current;if(!e)return;let s=new IntersectionObserver(d,{threshold:.1,rootMargin:"".concat(i,"px")});return s.observe(e),()=>{e&&s.unobserve(e)}},[d,i]),(0,t.jsxs)("div",{children:[n,(0,t.jsxs)("div",{ref:c,className:"w-full py-8",children:[a&&(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[(0,t.jsx)(k.Z,{className:"w-5 h-5 animate-spin"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Loading more products..."})]})}),!s&&!a&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("div",{className:"inline-flex items-center px-4 py-2 bg-gray-100 rounded-full",children:(0,t.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"You've reached the end of our collection"})})})]})]})},P=a(13905),F=a(11239),D=a(91723);function M(){let{flashSaleSettings:e}=(0,y.u)(),[s,a]=(0,l.useState)("");return((0,l.useEffect)(()=>{if(!e||!(0,w.LR)(e))return;let s=()=>{if(!e.flashSaleEndDate)return"";let s=new Date().getTime(),a=new Date(e.flashSaleEndDate).getTime()-s;if(a<=0)return"Ended";let t=Math.floor(a/864e5),l=Math.floor(a%864e5/36e5),r=Math.floor(a%36e5/6e4),i=Math.floor(a%6e4/1e3);return t>0?"".concat(t,"d ").concat(l,"h ").concat(r,"m"):l>0?"".concat(l,"h ").concat(r,"m ").concat(i,"s"):"".concat(r,"m ").concat(i,"s")};a(s());let t=setInterval(()=>{a(s())},1e3);return()=>clearInterval(t)},[e]),e&&(0,w.LR)(e))?(0,t.jsxs)("div",{className:"relative overflow-hidden rounded-lg p-4 mb-6",style:{backgroundColor:e.flashSaleBackgroundColor||"#16a34a"},children:[(0,t.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,t.jsx)("div",{className:"absolute -top-4 -right-4 w-24 h-24 bg-white rounded-full"}),(0,t.jsx)("div",{className:"absolute -bottom-4 -left-4 w-32 h-32 bg-white rounded-full"})]}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between flex-wrap gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"bg-white bg-opacity-20 p-2 rounded-full",children:(0,t.jsx)(F.Z,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-bold text-lg",children:e.flashSaleTitle||"Flash Sale"}),(0,t.jsx)("p",{className:"text-white text-opacity-90 text-sm",children:e.flashSaleSubtitle||"Get ".concat(e.flashSalePercentage,"% off all products")})]})]}),s&&e.flashSaleEndDate&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 bg-white bg-opacity-20 px-4 py-2 rounded-full",children:[(0,t.jsx)(D.Z,{className:"w-4 h-4 text-white"}),(0,t.jsx)("span",{className:"text-white font-semibold text-sm",children:"Ended"===s?"Sale Ended":"Ends in: ".concat(s)})]})]}),e.flashSalePercentage&&(0,t.jsxs)("div",{className:"mt-3 inline-flex items-center bg-white bg-opacity-20 px-3 py-1 rounded-full",children:[(0,t.jsxs)("span",{className:"text-white font-bold text-2xl",children:[e.flashSalePercentage,"% OFF"]}),(0,t.jsx)("span",{className:"text-white text-opacity-90 ml-2",children:"Store-wide Discount"})]})]})]}):null}let _=e=>{var s,a;let t=(null===(s=e.category)||void 0===s?void 0:s.slug)||"skincare";if(e.price,e.variants&&e.variants.length>0){let s=[e.price||0,...e.variants.map(e=>{var s;return null!==(s=e.price)&&void 0!==s?s:0})].filter(e=>e>0);s.length>0&&([...s],[...s])}return{id:e.id,slug:e.slug,name:e.name,description:e.description,shortDescription:e.shortDescription,price:e.price||0,image:(null===(a=e.images[0])||void 0===a?void 0:a.url)||"/placeholde.jpg",images:e.images.map(e=>({id:e.id,url:e.url,alt:e.alt,position:e.position||0})),category:t,featured:e.isFeatured,ingredients:[],benefits:[],rating:0,reviews:0,variants:e.variants,_raw:e}};var L=()=>{let e=(0,r.useSearchParams)(),[s,a]=(0,l.useState)("all"),[m,h]=(0,l.useState)("random"),[u,g]=(0,l.useState)(!1),[f,p]=(0,l.useState)([]),[j,v]=(0,l.useState)([]),[b,N]=(0,l.useState)([]),[y,w]=(0,l.useState)([]),[S,Z]=(0,l.useState)(!0),[k,F]=(0,l.useState)(!1),[D,L]=(0,l.useState)(null),[O,T]=(0,l.useState)(""),[R,A]=(0,l.useState)("grid"),[I,z]=(0,l.useState)([0,1e4]),[H,B]=(0,l.useState)(!1),[J,U]=(0,l.useState)(1),[q,G]=(0,l.useState)(!0),V=(0,l.useRef)(null);(0,l.useEffect)(()=>{let s=e.get("category");s&&a(s)},[e]),(0,l.useEffect)(()=>{(async()=>{try{Z(!0);let e=new URLSearchParams({limit:"1000",sort:m}),[s,a]=await Promise.all([fetch("/api/products?".concat(e.toString())),fetch("/api/categories")]),[t,l]=await Promise.all([s.json(),a.json()]);if(t.success&&l.success){let e=t.data.filter(e=>e.isActive).map(e=>({..._(e),_raw:e}));if(p(e),v(e),0===y.length){let e=[{id:"all",name:"All Products"},...l.data.map(e=>({id:e.slug,name:e.name}))];w(e)}}else L("Failed to fetch data")}catch(e){console.error("Error fetching data:",e),L("Failed to load products")}finally{Z(!1)}})()},[m]),(0,l.useEffect)(()=>{let e=f;if("all"!==s&&(e=e.filter(e=>{var a,t,l;let r=e._raw;return(null===(a=r.category)||void 0===a?void 0:a.slug)===s||null!==(l=null===(t=r.productCategories)||void 0===t?void 0:t.some(e=>e.category.slug===s))&&void 0!==l&&l})),O.trim()){let s=O.toLowerCase().trim();e=e.filter(e=>e.name.toLowerCase().includes(s)||e.description.toLowerCase().includes(s)||e.shortDescription.toLowerCase().includes(s))}v(e=e.filter(e=>{let s=e.price||0;return s>=I[0]&&s<=I[1]})),U(1),G(e.length>12)},[s,f,O,I]),(0,l.useEffect)(()=>{let e=12*J;N(j.slice(0,e)),G(e<j.length)},[j,J]);let W=()=>{!k&&q&&(F(!0),setTimeout(()=>{U(e=>e+1),F(!1)},500))};(0,l.useEffect)(()=>{H&&V.current&&V.current.focus()},[H]);let Y=e=>{a(e)};return S?(0,t.jsx)(P.sW,{}):D?(0,t.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-red-600 mb-4",children:D}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Try Again"})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsxs)("div",{className:"lg:hidden",children:[(0,t.jsxs)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:[(0,t.jsxs)("div",{className:"px-4 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Shop"}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("button",{onClick:()=>B(!H),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors",children:(0,t.jsx)(i.Z,{className:"w-5 h-5"})}),(0,t.jsxs)("button",{onClick:()=>g(!u),className:"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,t.jsx)(n.Z,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Filters"})]})]})]}),(0,t.jsx)("div",{className:"transition-all duration-300 ".concat(H?"max-h-20 opacity-100 mb-3":"max-h-0 opacity-0 overflow-hidden"),children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(i.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)("input",{ref:V,type:"text",placeholder:"Search products...",value:O,onChange:e=>T(e.target.value),className:"w-full pl-10 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),O&&(0,t.jsx)("button",{onClick:()=>T(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,t.jsx)(c.Z,{className:"w-4 h-4"})})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[j.length," products found"]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("button",{onClick:()=>A("grid"),className:"p-1.5 rounded ".concat("grid"===R?"bg-green-100 text-green-600":"text-gray-400"),children:(0,t.jsx)(o.Z,{className:"w-4 h-4"})}),(0,t.jsx)("button",{onClick:()=>A("list"),className:"p-1.5 rounded ".concat("list"===R?"bg-green-100 text-green-600":"text-gray-400"),children:(0,t.jsx)(d.Z,{className:"w-4 h-4"})})]})]})]}),(0,t.jsx)("div",{className:"border-t bg-white transition-all duration-300 ".concat(u?"max-h-screen opacity-100":"max-h-0 opacity-0 overflow-hidden"),children:(0,t.jsxs)("div",{className:"px-4 py-4 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Sort by"}),(0,t.jsxs)("select",{value:m,onChange:e=>h(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-sm",children:[(0,t.jsx)("option",{value:"random",children:"Random (Default)"}),(0,t.jsx)("option",{value:"name_asc",children:"Name (A-Z)"}),(0,t.jsx)("option",{value:"name_desc",children:"Name (Z-A)"}),(0,t.jsx)("option",{value:"price_asc",children:"Price (Low to High)"}),(0,t.jsx)("option",{value:"price_desc",children:"Price (High to Low)"}),(0,t.jsx)("option",{value:"newest",children:"Newest First"}),(0,t.jsx)("option",{value:"oldest",children:"Oldest First"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Price Range"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"number",placeholder:"Min",value:I[0],onChange:e=>z([parseInt(e.target.value)||0,I[1]]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),(0,t.jsx)("span",{className:"text-gray-400",children:"-"}),(0,t.jsx)("input",{type:"number",placeholder:"Max",value:I[1],onChange:e=>z([I[0],parseInt(e.target.value)||1e4]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["₹",I[0]," - ₹",I[1]]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Categories"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:y.map(e=>(0,t.jsx)("button",{onClick:()=>Y(e.id),className:"px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ".concat(s===e.id?"bg-green-600 text-white shadow-md scale-105":"bg-gray-100 text-gray-700 hover:bg-gray-200 active:scale-95"),children:e.name},e.id))})]}),(0,t.jsx)("button",{onClick:()=>{a("all"),T(""),z([0,1e4]),h("random")},className:"w-full py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors",children:"Clear All Filters"})]})})]}),(0,t.jsx)("div",{className:"px-4 pt-4",children:(0,t.jsx)(M,{})}),(0,t.jsx)("div",{className:"px-4 py-6",children:0===j.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(i.Z,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Try adjusting your search or filters"}),(0,t.jsx)("button",{onClick:()=>{a("all"),T(""),z([0,1e4])},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):(0,t.jsx)(E,{hasMore:q,loading:k,onLoadMore:W,children:(0,t.jsx)("div",{className:"".concat("grid"===R?"grid grid-cols-2 gap-4":"space-y-4"),children:b.map(e=>(0,t.jsx)(C,{product:e,viewMode:R},e.id))})})})]}),(0,t.jsxs)("div",{className:"hidden lg:block",children:[(0,t.jsx)("div",{className:"bg-white shadow-sm",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,t.jsxs)("div",{className:"relative w-80",children:[(0,t.jsx)(i.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)("input",{type:"text",placeholder:"Search products...",value:O,onChange:e=>T(e.target.value),className:"w-full pl-12 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),O&&(0,t.jsx)("button",{onClick:()=>T(""),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,t.jsx)(c.Z,{className:"w-5 h-5"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 justify-center",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Price Range:"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"number",placeholder:"Min",value:I[0],onChange:e=>z([parseInt(e.target.value)||0,I[1]]),className:"w-24 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),(0,t.jsx)("span",{className:"text-gray-400",children:"-"}),(0,t.jsx)("input",{type:"number",placeholder:"Max",value:I[1],onChange:e=>z([I[0],parseInt(e.target.value)||1e4]),className:"w-24 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(x.Z,{className:"w-5 h-5 text-gray-600"}),(0,t.jsxs)("select",{value:m,onChange:e=>h(e.target.value),className:"px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white",children:[(0,t.jsx)("option",{value:"random",children:"Random (Default)"}),(0,t.jsx)("option",{value:"name_asc",children:"Name (A-Z)"}),(0,t.jsx)("option",{value:"name_desc",children:"Name (Z-A)"}),(0,t.jsx)("option",{value:"price_asc",children:"Price (Low to High)"}),(0,t.jsx)("option",{value:"price_desc",children:"Price (High to Low)"}),(0,t.jsx)("option",{value:"newest",children:"Newest First"}),(0,t.jsx)("option",{value:"oldest",children:"Oldest First"})]})]}),(0,t.jsxs)("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[(0,t.jsx)("button",{onClick:()=>A("grid"),className:"p-2 rounded ".concat("grid"===R?"bg-white shadow-sm text-green-600":"text-gray-500"),children:(0,t.jsx)(o.Z,{className:"w-4 h-4"})}),(0,t.jsx)("button",{onClick:()=>A("list"),className:"p-2 rounded ".concat("list"===R?"bg-white shadow-sm text-green-600":"text-gray-500"),children:(0,t.jsx)(d.Z,{className:"w-4 h-4"})})]})]})]}),(0,t.jsx)("div",{className:"flex justify-center mb-8",children:(0,t.jsx)("div",{className:"flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100",children:y.map(e=>(0,t.jsx)("button",{onClick:()=>Y(e.id),className:"px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ".concat(s===e.id?"bg-green-600 text-white shadow-md scale-105":"text-gray-700 hover:bg-gray-100 active:scale-95"),children:e.name},e.id))})})]})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,t.jsx)(M,{}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("p",{className:"text-gray-600",children:[j.length," products found"]}),(0,t.jsx)("button",{onClick:()=>{a("all"),T(""),z([0,1e4]),h("random")},className:"text-sm text-gray-500 hover:text-gray-700 underline",children:"Clear all filters"})]}),0===j.length?(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(i.Z,{className:"w-10 h-10 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"No products found"}),(0,t.jsx)("p",{className:"text-gray-500 mb-6",children:"Try adjusting your search or filters"}),(0,t.jsx)("button",{onClick:()=>{a("all"),T(""),z([0,1e4])},className:"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):(0,t.jsx)(E,{hasMore:q,loading:k,onLoadMore:W,children:(0,t.jsx)("div",{className:"".concat("grid"===R?"grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6":"grid grid-cols-1 lg:grid-cols-2 gap-6"),children:b.map(e=>(0,t.jsx)(C,{product:e,viewMode:R},e.id))})})]})]})]})}},1396:function(e,s,a){"use strict";a.d(s,{FlashSaleProvider:function(){return i},u:function(){return n}});var t=a(57437),l=a(2265);let r=(0,l.createContext)(void 0);function i(e){let{children:s}=e,[a,i]=(0,l.useState)(null),[n,c]=(0,l.useState)(!0),[o,d]=(0,l.useState)(!1),x=async()=>{try{let e=await fetch("/api/homepage-settings"),s=await e.json();if(s.success&&s.data.settings){let e=s.data.settings,a={showFlashSale:e.showFlashSale,flashSaleEndDate:e.flashSaleEndDate,flashSalePercentage:e.flashSalePercentage,flashSaleTitle:e.flashSaleTitle,flashSaleSubtitle:e.flashSaleSubtitle,flashSaleBackgroundColor:e.flashSaleBackgroundColor};i(a),localStorage.setItem("flashSaleSettings",JSON.stringify(a)),window.dispatchEvent(new Event("flashSaleSettingsUpdated"))}}catch(e){console.error("Error fetching flash sale settings:",e)}finally{c(!1)}};(0,l.useEffect)(()=>{x().then(()=>{d(!0)})},[]);let m=async()=>{c(!0),await x()};return(0,t.jsx)(r.Provider,{value:{flashSaleSettings:a,loading:n,isHydrated:o,refreshSettings:m},children:s})}function n(){let e=(0,l.useContext)(r);if(void 0===e)throw Error("useFlashSale must be used within a FlashSaleProvider");return e}},8329:function(e,s,a){"use strict";function t(e){return!!e&&!!e.showFlashSale&&(!e.flashSaleEndDate||new Date<new Date(e.flashSaleEndDate))}function l(e,s){let a=t(s),l=(null==s?void 0:s.flashSalePercentage)||0;return{isOnSale:a,salePrice:a?Math.round(e-e*l/100):e,originalPrice:e,discountPercentage:a?l:0}}a.d(s,{FO:function(){return l},LR:function(){return t}})}},function(e){e.O(0,[7349,7648,5644,2497,1682,8250,200,2971,2117,1744],function(){return e(e.s=4239)}),_N_E=e.O()}]);