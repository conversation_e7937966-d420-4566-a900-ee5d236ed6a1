"use strict";(()=>{var e={};e.id=7253,e.ids=[7253],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},63426:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>b,requestAsyncStorage:()=>g,routeModule:()=>I,serverHooks:()=>N,staticGenerationAsyncStorage:()=>y});var o={};t.r(o),t.d(o,{POST:()=>O});var s=t(49303),a=t(88716),n=t(60670),i=t(87070),u=t(65630),d=t(75571),c=t(95306),l=t(89456),p=t(81515),m=t(84875),f=t(54211),R=t(89585);let E=u.Ry({orderIds:u.IX(u.Z_()).min(1,"At least one order ID is required"),updates:u.Ry({status:u.Km(["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"]).optional(),paymentStatus:u.Km(["PENDING","PAID","FAILED","REFUNDED"]).optional()}).refine(e=>e.status||e.paymentStatus,{message:"At least one update field is required"})}),O=(0,m.lm)(async e=>{f.kg.apiRequest("POST","/api/orders/bulk-update");let r=await (0,d.getServerSession)(c.L);if(!r?.user?.id||"ADMIN"!==r.user.role)throw new m._7("Admin access required");let t=await e.json(),{orderIds:o,updates:s}=E.parse(t);try{await (0,l.Z)();let e=await p.Order.find({_id:{$in:o}}).lean();if(0===e.length)throw new m.p8("No valid orders found");let t={updatedAt:new Date};s.status&&(t.status=s.status),s.paymentStatus&&(t.paymentStatus=s.paymentStatus);let a=await p.Order.updateMany({_id:{$in:o}},{$set:t}),n={count:a.modifiedCount??a.nModified??0};if(s.status){let r=e.filter(e=>e.status!==s.status).map(async e=>{try{switch(s.status){case"CONFIRMED":await R.aZ.orderConfirmed(e.userId,{orderId:e._id,orderNumber:e.orderNumber});break;case"SHIPPED":await R.aZ.orderShipped(e.userId,{orderId:e._id,orderNumber:e.orderNumber});break;case"DELIVERED":await R.aZ.orderDelivered(e.userId,{orderId:e._id,orderNumber:e.orderNumber}),"COD"===e.paymentMethod&&"PAID"!==e.paymentStatus&&await p.Order.updateOne({_id:e._id},{$set:{paymentStatus:"PAID"}});break;case"CANCELLED":await R.aZ.orderCancelled(e.userId,{orderId:e._id,orderNumber:e.orderNumber,reason:"Bulk update by admin"})}}catch(r){f.kg.error(`Failed to send notification for order ${e._id}`,r)}});await Promise.allSettled(r)}return f.kg.info("Bulk order update completed",{adminId:r.user.id,orderCount:n.count,updates:s}),i.NextResponse.json({success:!0,updatedCount:n.count,message:`Successfully updated ${n.count} orders`})}catch(e){throw f.kg.error("Failed to bulk update orders",e),e}}),I=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/orders/bulk-update/route",pathname:"/api/orders/bulk-update",filename:"route",bundlePath:"app/api/orders/bulk-update/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\bulk-update\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:N}=I,_="/api/orders/bulk-update/route";function b(){return(0,n.patchFetch)({serverHooks:N,staticGenerationAsyncStorage:y})}},84875:(e,r,t)=>{t.d(r,{AY:()=>c,M_:()=>u,_7:()=>i,dR:()=>d,gz:()=>a,lm:()=>m,p8:()=>n});var o=t(87070),s=t(29489);class a extends Error{constructor(e,r=500,t="INTERNAL_ERROR",o){super(e),this.statusCode=r,this.code=t,this.details=o,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,a)}}class n extends a{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class i extends a{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class u extends a{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class d extends a{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class c extends a{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class l extends a{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function p(e){let r={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return r[e]||r.INTERNAL_ERROR}function m(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){let r=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,error:e}),e instanceof a){let t={success:!1,error:{code:e.code,message:p(e.code),requestId:r,...!1}};return o.NextResponse.json(t,{status:e.statusCode})}if(e instanceof s.j){let t=new n("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),s={success:!1,error:{code:t.code,message:"Validation failed",requestId:r,...!1}};return o.NextResponse.json(s,{status:t.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let t=function(e){if(11e3===e.code||"MongoServerError"===e.name){let r=Object.keys(e.keyPattern||{})[0]||"field";return new c(`${r} already exists`)}return"ValidationError"===e.name?new n("Validation failed"):"CastError"===e.name?new n("Invalid data format"):"DocumentNotFoundError"===e.name?new d:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new l("Database connection failed"):new l("Database operation failed",{name:e.name,message:e.message})}(e),s={success:!1,error:{code:t.code,message:p(t.code),requestId:r,...!1}};return o.NextResponse.json(s,{status:t.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let t=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new c(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new i("Invalid user session");return new n("Invalid reference to related record");case"P2025":case"P2001":return new d;case"P2014":return new n("Missing required relationship");case"P2000":return new n("Input value is too long");case"P2004":return new n("Data constraint violation");default:return new l("Database operation failed",{code:e.code,message:e.message})}}(e),s={success:!1,error:{code:t.code,message:p(t.code),requestId:r,...!1}};return o.NextResponse.json(s,{status:t.statusCode})}}let t={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:r,...!1}};return o.NextResponse.json(t,{status:500})}(e)}}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(o,a,i):o[a]=e[a]}return o.default=e,t&&t.set(e,o),o}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[9276,5972,8691,2830,9489,5630,5306,9585],()=>t(63426));module.exports=o})();