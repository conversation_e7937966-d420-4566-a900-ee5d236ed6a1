"use strict";(()=>{var e={};e.id=2475,e.ids=[2475],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},68726:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>w,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>y});var o={};r.r(o),r.d(o,{GET:()=>f,POST:()=>m});var s=r(49303),i=r(88716),n=r(60670),a=r(87070),u=r(75571),l=r(95306),c=r(89456),p=r(81515),d=r(54211);async function f(e){try{let t=await (0,u.getServerSession)(l.L),{searchParams:o}=new URL(e.url),s=parseInt(o.get("page")||"1",10),i=parseInt(o.get("limit")||"10",10),n=o.get("active"),d=o.get("showInModule"),f=o.get("type"),m=o.get("userId"),g=(s-1)*i;await (0,c.Z)();let h={};if("true"===n?h.isActive=!0:"false"===n&&(h.isActive=!1),"true"===d?h.showInModule=!0:"false"===d&&(h.showInModule=!1),f&&(h.type=f),!t?.user||"ADMIN"!==t.user.role){let e=new Date;h.isActive=!0,h.validFrom={$lte:e},h.$or=[{validUntil:null},{validUntil:{$gte:e}}]}let[y,v]=await Promise.all([p.wV.find(h).sort({createdAt:-1}).skip(g).limit(i).lean(),p.wV.countDocuments(h)]),x={};if(m){let{Order:e}=await Promise.resolve().then(r.bind(r,81515)),t=y.map(e=>[String(e._id),String(e.code).toUpperCase()]),o=Array.from(new Set(t.map(e=>e[1]))),s=await e.find({userId:m,couponCodes:{$in:o}}).select("_id couponCodes createdAt").lean(),i=Array.isArray(s)?s:[];x={};for(let e=0;e<t.length;e++){let[r,o]=t[e],s=i.filter(e=>Array.isArray(e.couponCodes)&&e.couponCodes.some(e=>String(e).toUpperCase()===o));x[r]=s.map(e=>({id:String(e._id),usedAt:e.createdAt}))}}let w=y.map(e=>({...e,...m?{couponUsages:x[String(e._id)]||[]}:{}}));return a.NextResponse.json({coupons:w,pagination:{page:s,limit:i,total:v,pages:Math.ceil(v/i)}})}catch(e){return d.kg.error("Error fetching coupons",e),a.NextResponse.json({error:"Failed to fetch coupons"},{status:500})}}async function m(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user||"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json();if(!r.code||!r.name||!r.type||!r.discountType||void 0===r.discountValue)return a.NextResponse.json({error:"Missing required fields"},{status:400});await (0,c.Z)();let o=r.code.toUpperCase();if(await p.wV.findOne({code:o}).lean())return a.NextResponse.json({error:"Coupon code already exists"},{status:400});let s=await p.wV.create({code:o,name:r.name,description:r.description??null,type:r.type,discountType:r.discountType,discountValue:r.discountValue,minimumAmount:r.minimumAmount??null,maximumDiscount:r.maximumDiscount??null,usageLimit:r.usageLimit??null,userUsageLimit:r.userUsageLimit??null,isActive:r.isActive??!0,isStackable:r.isStackable??!1,showInModule:r.showInModule??!1,validFrom:r.validFrom?new Date(r.validFrom):new Date,validUntil:r.validUntil?new Date(r.validUntil):null,applicableProducts:r.applicableProducts||[],applicableCategories:r.applicableCategories||[],excludedProducts:r.excludedProducts||[],excludedCategories:r.excludedCategories||[],customerSegments:r.customerSegments||[],createdAt:new Date,updatedAt:new Date}),i=s.toObject?s.toObject():s;return a.NextResponse.json(i,{status:201})}catch(e){if(e?.code===11e3)return a.NextResponse.json({error:"Coupon code already exists"},{status:400});return d.kg.error("Error creating coupon",e),a.NextResponse.json({error:"Failed to create coupon"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/coupons/route",pathname:"/api/coupons",filename:"route",bundlePath:"app/api/coupons/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:v}=g,x="/api/coupons/route";function w(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:y})}},54211:(e,t,r)=>{var o;r.d(t,{kg:()=>i}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(o||(o={}));class s{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:s,context:i,error:n,userId:a,requestId:u}=e,l=o[r],c=`[${t}] ${l}: ${s}`;return a&&(c+=` | User: ${a}`),u&&(c+=` | Request: ${u}`),i&&Object.keys(i).length>0&&(c+=` | Context: ${JSON.stringify(i)}`),n&&(c+=` | Error: ${n.message}`,this.isDevelopment&&n.stack&&(c+=`
Stack: ${n.stack}`)),c}log(e,t,r,o){if(!this.shouldLog(e))return;let s={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:o},i=this.formatMessage(s);if(this.isDevelopment)switch(e){case 0:console.error(i);break;case 1:console.warn(i);break;case 2:console.info(i);break;case 3:console.debug(i)}else console.log(JSON.stringify(s))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,o){this.info(`API ${e} ${t}`,{...o,userId:r,type:"api_request"})}apiResponse(e,t,r,o,s){this.info(`API ${e} ${t} - ${r}`,{...s,statusCode:r,duration:o,type:"api_response"})}apiError(e,t,r,o,s){this.error(`API ${e} ${t} failed`,r,{...s,userId:o,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,o){this.warn("Authentication failed",{...o,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,o){this.debug(`DB ${e} on ${t}`,{...o,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,o){this.error(`DB ${e} on ${t} failed`,r,{...o,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,o){this.warn("Rate limit exceeded",{...o,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,o){this.info("Email sent",{...o,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,o){this.error("Email failed to send",r,{...o,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let i=new s},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=s?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(o,i,a):o[i]=e[i]}return o.default=e,r&&r.set(e,o),o}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,5972,8691,2830,5306],()=>r(68726));module.exports=o})();