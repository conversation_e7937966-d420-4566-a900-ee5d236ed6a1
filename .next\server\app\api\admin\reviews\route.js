"use strict";(()=>{var e={};e.id=5314,e.ids=[5314],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},39091:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>b,requestAsyncStorage:()=>y,routeModule:()=>h,serverHooks:()=>x,staticGenerationAsyncStorage:()=>v});var i={};r.r(i),r.d(i,{GET:()=>m,PATCH:()=>g});var s=r(49303),n=r(88716),o=r(60670),a=r(87070),u=r(75571),l=r(95306),p=r(89456),c=r(81515),d=r(54211);let f=["PENDING","APPROVED","REJECTED"];async function m(e){try{let t;await (0,p.Z)();let r=await (0,u.getServerSession)(l.L);if(!r?.user)return a.NextResponse.json({error:"Authentication required"},{status:401});if(r.user.id?t=await c.n5.findById(r.user.id).select("id role email").lean():r.user.email&&(t=await c.n5.findOne({email:r.user.email}).select("id role email").lean()),!t||"ADMIN"!==t.role)return a.NextResponse.json({error:"Unauthorized - Admin access required"},{status:401});let{searchParams:i}=new URL(e.url),s=i.get("status")||"PENDING",n=parseInt(i.get("page")||"1"),o=parseInt(i.get("limit")||"20");return f.includes(s),d.kg.info("Admin reviews API temporarily disabled during migration"),a.NextResponse.json({success:!0,data:[],pagination:{page:n,limit:o,total:0,pages:0}})}catch(e){return d.kg.error("Error fetching reviews:",e),a.NextResponse.json({success:!1,error:"Failed to fetch reviews"},{status:500})}}async function g(e){try{let e;await (0,p.Z)();let t=await (0,u.getServerSession)(l.L);if(!t?.user)return a.NextResponse.json({error:"Authentication required"},{status:401});if(t.user.id?e=await c.n5.findById(t.user.id).select("id role email").lean():t.user.email&&(e=await c.n5.findOne({email:t.user.email}).select("id role email").lean()),!e||"ADMIN"!==e.role)return a.NextResponse.json({error:"Unauthorized - Admin access required"},{status:401});return d.kg.info("Admin review update API temporarily disabled during migration"),a.NextResponse.json({success:!0,data:null,message:"Review update temporarily disabled during migration"})}catch(e){return d.kg.error("Error updating review:",e),a.NextResponse.json({success:!1,error:"Failed to update review"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/reviews/route",pathname:"/api/admin/reviews",filename:"route",bundlePath:"app/api/admin/reviews/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\reviews\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:y,staticGenerationAsyncStorage:v,serverHooks:x}=h,w="/api/admin/reviews/route";function b(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:v})}},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class s{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:s,context:n,error:o,userId:a,requestId:u}=e,l=i[r],p=`[${t}] ${l}: ${s}`;return a&&(p+=` | User: ${a}`),u&&(p+=` | Request: ${u}`),n&&Object.keys(n).length>0&&(p+=` | Context: ${JSON.stringify(n)}`),o&&(p+=` | Error: ${o.message}`,this.isDevelopment&&o.stack&&(p+=`
Stack: ${o.stack}`)),p}log(e,t,r,i){if(!this.shouldLog(e))return;let s={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},n=this.formatMessage(s);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(s))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,s){this.info(`API ${e} ${t} - ${r}`,{...s,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,s){this.error(`API ${e} ${t} failed`,r,{...s,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new s},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=s?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(i,n,a):i[n]=e[n]}return i.default=e,r&&r.set(e,i),i}(r(45609));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,2830,5306],()=>r(39091));module.exports=i})();