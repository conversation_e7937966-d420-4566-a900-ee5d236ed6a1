(()=>{var e={};e.id=188,e.ids=[188],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},56105:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),t(73622),t(37254),t(35866);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73622)),"C:\\Users\\<USER>\\Desktop\\project\\app\\product\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\product\\[id]\\page.tsx"],x="/product/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/product/[id]/page",pathname:"/product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85841:(e,s,t)=>{Promise.resolve().then(t.bind(t,65090)),Promise.resolve().then(t.bind(t,69570))},69570:(e,s,t)=>{"use strict";t.d(s,{default:()=>T});var a=t(10326),r=t(17577),l=t(35047),i=t(77109),n=t(86333),c=t(67427),d=t(76557);let o=(0,d.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var x=t(33734),m=t(32933),h=t(57671),u=t(94494);let g=(0,d.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var p=t(941);let j=({productId:e})=>{let[s,t]=(0,r.useState)([]),[l,i]=(0,r.useState)(!0),[n,c]=(0,r.useState)(null);(0,r.useEffect)(()=>{d()},[e]);let d=async()=>{try{i(!0);let s=await fetch(`/api/products/${e}/faqs`),a=await s.json();a.success&&t(a.data)}catch(e){console.error("Error fetching FAQs:",e)}finally{i(!1)}},o=e=>{c(n===e?null:e)};return l?a.jsx("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded mb-2"}),a.jsx("div",{className:"h-4 bg-gray-100 rounded"})]},e))}):0===s.length?a.jsx("div",{className:"text-center py-8",children:a.jsx("div",{className:"text-gray-500",children:"No frequently asked questions available for this product."})}):(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Frequently Asked Questions"}),a.jsx("div",{className:"space-y-3",children:s.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[a.jsx("button",{onClick:()=>o(e.id),className:"w-full px-6 py-4 text-left bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-inset",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h4",{className:"font-medium text-gray-900 pr-4",children:e.question}),a.jsx("div",{className:"flex-shrink-0",children:n===e.id?a.jsx(g,{className:"w-5 h-5 text-gray-500"}):a.jsx(p.Z,{className:"w-5 h-5 text-gray-500"})})]})}),n===e.id&&a.jsx("div",{className:"px-6 pb-4 bg-gray-50",children:a.jsx("div",{className:"pt-2 text-gray-700 whitespace-pre-wrap leading-relaxed",children:e.answer})})]},e.id))}),a.jsx("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[a.jsx("strong",{children:"Have a question that's not answered here?"})," Feel free to contact our customer support team for more information about this product."]})})]})},b=({productId:e,basePrice:s,onVariationChange:t})=>{let[l,i]=(0,r.useState)([]),[n,c]=(0,r.useState)(!0),[d,o]=(0,r.useState)({});(0,r.useEffect)(()=>{x()},[e]),(0,r.useEffect)(()=>{if(l.length>0&&0===Object.keys(d).length){let e=l.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),s={};Object.entries(e).forEach(([e,t])=>{s[e]=t[t.length-1]}),o(s);let a=h(Object.values(s)),r=1===Object.keys(s).length?Object.values(s)[0]:null;t&&t(r,a)}},[l]);let x=async()=>{try{c(!0);let s=await fetch(`/api/products/${e}/variations`),t=await s.json();t.success&&i(t.data)}catch(e){console.error("Error fetching variations:",e)}finally{c(!1)}},m=l.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),h=e=>{if(0===e.length)return 0;if(1===e.length){let s=e[0];if(void 0!==s.price&&null!==s.price&&s.price>0)return s.price}let s=e.filter(e=>void 0!==e.price&&null!==e.price&&e.price>0);return s.length>0?s.reduce((e,s)=>(s.price??0)>(e.price??0)?s:e).price??0:0},u=(e,s)=>{let a={...d,[e]:s};o(a);let r=h(Object.values(a)),l=1===Object.keys(a).length?Object.values(a)[0]:null;t&&t(l,r)},g=e=>!0;return n?a.jsx("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),a.jsx("div",{className:"flex space-x-2",children:[1,2,3].map(e=>a.jsx("div",{className:"h-10 w-16 bg-gray-200 rounded"},e))})]})}):0===l.length?null:((()=>{let e=Object.values(d);return 0===e.length?null:{totalPrice:h(e),selectedValues:e}})(),(0,a.jsxs)("div",{className:"space-y-6",children:[Object.entries(m).map(([e,s])=>(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:e}),d[e]&&(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Selected: ",d[e].value]})]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:s.map(s=>{let t=d[e]?.id===s.id,r=g(s);return a.jsx("button",{onClick:()=>r&&u(e,s),disabled:!r,className:`
                    px-4 py-2 border rounded-lg text-sm font-medium transition-colors
                    ${t?"border-green-500 bg-green-50 text-green-700":r?"border-gray-300 bg-white text-gray-700 hover:border-gray-400":"border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed"}
                  `,children:a.jsx("span",{children:s.value})},s.id)})})]},e)),Object.keys(m).length>0&&Object.keys(d).length<Object.keys(m).length&&a.jsx("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:a.jsx("p",{className:"text-sm text-yellow-800",children:"Please select all required options before adding to cart."})})]}))},y=(0,d.Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var f=t(11890),v=t(39183),N=t(94019);let w=({images:e,productName:s})=>{let[t,l]=(0,r.useState)(0),[i,n]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!0),[o,x]=(0,r.useState)(null),[m,h]=(0,r.useState)(null),u=[...e].sort((e,s)=>e.position-s.position);(0,r.useEffect)(()=>{u.length>0&&d(!1)},[u]);let g=e=>{l(e)},p=()=>{l(e=>0===e?u.length-1:e-1)},j=()=>{l(e=>e===u.length-1?0:e+1)},b=()=>{n(!1)},w=e=>{i&&("Escape"===e.key&&b(),"ArrowLeft"===e.key&&p(),"ArrowRight"===e.key&&j())};if((0,r.useEffect)(()=>(document.addEventListener("keydown",w),()=>document.removeEventListener("keydown",w)),[i]),!u||0===u.length)return a.jsx("div",{className:"w-full",children:a.jsx("div",{className:"aspect-square bg-gray-200 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[a.jsx("div",{className:"w-16 h-16 bg-gray-300 rounded-lg mx-auto mb-2"}),a.jsx("p",{className:"text-sm",children:"No images available"})]})})});let k=u[t];return(0,a.jsxs)("div",{className:"w-full",children:[a.jsx("div",{className:"relative mb-4 group",children:(0,a.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-lg bg-gray-100",onTouchStart:e=>{h(null),x(e.targetTouches[0].clientX)},onTouchMove:e=>{h(e.targetTouches[0].clientX)},onTouchEnd:()=>{if(!o||!m)return;let e=o-m;e>50&&u.length>1&&j(),e<-50&&u.length>1&&p()},children:[c&&a.jsx("div",{className:"absolute inset-0 bg-gray-200 animate-pulse rounded-lg"}),a.jsx("img",{src:k.url,alt:k.alt||`${s} - Image ${t+1}`,className:"w-full h-full object-cover cursor-zoom-in transition-transform duration-300 hover:scale-105 select-none",onClick:()=>{n(!0)},onLoad:()=>d(!1),draggable:!1}),a.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:a.jsx("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:a.jsx("div",{className:"bg-white bg-opacity-90 p-2 rounded-full",children:a.jsx(y,{className:"w-6 h-6 text-gray-700"})})})}),u.length>1&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:p,className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:a.jsx(f.Z,{className:"w-5 h-5 text-gray-700"})}),a.jsx("button",{onClick:j,className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:a.jsx(v.Z,{className:"w-5 h-5 text-gray-700"})})]}),u.length>1&&(0,a.jsxs)("div",{className:"absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded",children:[t+1," / ",u.length]})]})}),u.length>1&&(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"hidden md:flex space-x-2 overflow-x-auto pb-2",children:u.map((e,r)=>a.jsx("button",{onClick:()=>g(r),className:`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${r===t?"border-green-500 ring-2 ring-green-200":"border-gray-200 hover:border-gray-300"}`,children:a.jsx("img",{src:e.url,alt:e.alt||`${s} thumbnail ${r+1}`,className:"w-full h-full object-cover"})},e.id||r))}),a.jsx("div",{className:"md:hidden flex space-x-2 overflow-x-auto pb-2",children:u.map((e,r)=>a.jsx("button",{onClick:()=>g(r),className:`flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all ${r===t?"border-green-500 ring-1 ring-green-200":"border-gray-200"}`,children:a.jsx("img",{src:e.url,alt:e.alt||`${s} thumbnail ${r+1}`,className:"w-full h-full object-cover"})},e.id||r))}),(0,a.jsxs)("div",{className:"md:hidden flex justify-center space-x-4",children:[a.jsx("button",{onClick:p,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:a.jsx(f.Z,{className:"w-5 h-5 text-gray-700"})}),a.jsx("button",{onClick:j,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:a.jsx(v.Z,{className:"w-5 h-5 text-gray-700"})})]})]}),i&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"relative max-w-4xl max-h-full",children:[a.jsx("button",{onClick:b,className:"absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full z-10",children:a.jsx(N.Z,{className:"w-6 h-6"})}),u.length>1&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("button",{onClick:p,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:a.jsx(f.Z,{className:"w-6 h-6"})}),a.jsx("button",{onClick:j,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:a.jsx(v.Z,{className:"w-6 h-6"})})]}),a.jsx("img",{src:k.url,alt:k.alt||`${s} - Image ${t+1}`,className:"max-w-full max-h-full object-contain"}),a.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-center",children:[t+1," of ",u.length,k.alt&&` - ${k.alt}`]})})]})})]})};var k=t(19922),S=t(48998),C=t(79635),Z=t(75290);let P=({productId:e,onReviewSubmitted:s})=>{let{data:t}=(0,i.useSession)(),[l,n]=(0,r.useState)(0),[c,d]=(0,r.useState)(""),[o,m]=(0,r.useState)(""),[h,u]=(0,r.useState)(!1),[g,p]=(0,r.useState)(null),[j,b]=(0,r.useState)(!1),y=async a=>{if(a.preventDefault(),!t?.user){p("Please sign in to leave a review");return}if(0===l){p("Please select a rating");return}u(!0),p(null);try{let t=await fetch(`/api/products/${e}/reviews`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({rating:l,title:c,content:o})}),a=await t.json();a.success?(b(!0),n(0),d(""),m(""),s?.()):p(a.error||"Failed to submit review")}catch(e){p("Failed to submit review")}finally{u(!1)}};return t?.user?j?null:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Write a Review"}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rating *"}),a.jsx("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>a.jsx("button",{type:"button",onClick:()=>n(e),className:"p-1 hover:scale-110 transition-transform",children:a.jsx(x.Z,{className:`w-6 h-6 ${e<=l?"text-yellow-400 fill-current":"text-gray-300"}`})},e))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Title"}),a.jsx("input",{type:"text",value:c,onChange:e=>d(e.target.value),placeholder:"Summarize your experience",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:100})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Details"}),a.jsx("textarea",{value:o,onChange:e=>m(e.target.value),placeholder:"Tell us about your experience with this product",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:1e3})]}),g&&a.jsx("div",{className:"text-red-600 text-sm",children:g}),a.jsx("button",{type:"submit",disabled:h||0===l,className:"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?(0,a.jsxs)(a.Fragment,{children:[a.jsx(Z.Z,{className:"w-4 h-4 inline mr-2 animate-spin"}),"Submitting..."]}):"Submit Review"})]})]}):a.jsx("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:a.jsx("p",{className:"text-gray-600",children:"Please sign in to leave a review"})})},$=({productId:e})=>{let{data:s}=(0,i.useSession)(),[t,l]=(0,r.useState)([]),[n,c]=(0,r.useState)(!0),[d,o]=(0,r.useState)(null),[m,h]=(0,r.useState)(!1),[u,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{p()},[e]);let p=async()=>{try{c(!0);let s=await fetch(`/api/products/${e}/reviews`),t=await s.json();t.success?l(t.data):o(t.error||"Failed to fetch reviews")}catch(e){o("Failed to fetch reviews")}finally{c(!1)}},j=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),b=t.filter(e=>"APPROVED"===e.status),y=b.length>0?(b.reduce((e,s)=>e+s.rating,0)/b.length).toFixed(1):0,f=t.find(e=>"PENDING"===e.status&&e.user?.id===s?.user?.id);return n?a.jsx("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900",children:["Customer Reviews (",b.length,")"]}),!f&&a.jsx("button",{onClick:()=>h(!m),className:"text-green-600 hover:text-green-700 font-medium",children:"Write a Review"})]}),b.length>0&&a.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"flex",children:[1,2,3,4,5].map(e=>a.jsx(x.Z,{className:`w-5 h-5 ${e<=Math.round(Number(y))?"text-yellow-400 fill-current":"text-gray-300"}`},e))}),a.jsx("span",{className:"text-lg font-semibold",children:y}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",b.length," reviews)"]})]})}),u&&f&&a.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(S.Z,{className:"w-5 h-5 text-green-600 mt-0.5"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-green-800 font-medium",children:"Thank you for your review!"}),a.jsx("p",{className:"text-green-600 text-sm mt-1",children:"Your review has been submitted and is pending approval. It will appear below once approved by our team."})]})]})}),m&&a.jsx(P,{productId:e,onReviewSubmitted:()=>{h(!1),g(!0),p(),setTimeout(()=>g(!1),5e3)}}),f&&a.jsx("div",{className:"mb-4",children:a.jsx("div",{className:"bg-yellow-50 rounded-lg shadow-sm border border-yellow-200 p-4",children:a.jsx("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center",children:f.user?.avatar?a.jsx("img",{src:f.user.avatar,alt:f.user.name||"User",className:"w-10 h-10 rounded-full"}):a.jsx(C.Z,{className:"w-5 h-5 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("span",{className:"font-medium text-gray-900",children:"Your Review"}),(0,a.jsxs)("span",{className:"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full flex items-center space-x-1",children:[a.jsx(S.Z,{className:"w-3 h-3"}),a.jsx("span",{children:"Pending Approval"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("div",{className:"flex",children:[1,2,3,4,5].map(e=>a.jsx(x.Z,{className:`w-4 h-4 ${e<=f.rating?"text-yellow-400 fill-current":"text-gray-300"}`},e))}),a.jsx("span",{className:"text-sm text-gray-500",children:j(f.createdAt)})]}),f.title&&a.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:f.title}),f.content&&a.jsx("p",{className:"text-gray-700 text-sm",children:f.content})]})]})})})}),0!==b.length||f?a.jsx("div",{className:"space-y-4",children:b.map(e=>a.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:a.jsx("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.user?.avatar?a.jsx("img",{src:e.user.avatar,alt:e.user.name||"User",className:"w-10 h-10 rounded-full"}):a.jsx(C.Z,{className:"w-5 h-5 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("span",{className:"font-medium text-gray-900",children:e.user?.name||"Anonymous"}),e.isVerified&&a.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Verified Purchase"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("div",{className:"flex",children:[1,2,3,4,5].map(s=>a.jsx(x.Z,{className:`w-4 h-4 ${s<=e.rating?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),a.jsx("span",{className:"text-sm text-gray-500",children:j(e.createdAt)})]}),e.title&&a.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:e.title}),e.content&&a.jsx("p",{className:"text-gray-700 text-sm",children:e.content})]})]})})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(x.Z,{className:"w-12 h-12 mx-auto"})}),a.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews yet"}),a.jsx("button",{onClick:()=>h(!0),className:"text-green-600 hover:text-green-700 font-medium",children:"Be the first to write a review"})]})]})};var E=t(11417),F=t(69719),A=t(39108),q=t(47537),O=t(52807);let D=e=>{let s=e.reviews||[],t=s.length>0?s.reduce((e,s)=>e+s.rating,0)/s.length:0,a=[];if(e.description){let s=e.description.match(/benefits?:\s*([^.]+)/i);s&&a.push(...s[1].split(",").map(e=>e.trim()).filter(e=>e))}return{id:e.id,name:e.name,description:e.description||"",shortDescription:e.shortDescription||"",price:e.price||0,image:e.images[0]?.url||"/images/default-product.jpg",images:e.images.map(s=>({id:s.id,url:s.url,alt:s.alt||e.name,position:s.position||0})),category:e.category?.slug||"skincare",featured:e.isFeatured,benefits:a,rating:Math.round(10*t)/10,reviews:s.length,_raw:e}},T=({id:e})=>{let s=(0,l.useRouter)(),{data:t}=(0,i.useSession)(),{dispatch:d}=(0,u.j)(),{flashSaleSettings:g}=(0,F.u)(),{showToast:p}=(0,q.V)(),{refreshUnreadCount:y,refreshNotifications:f}=(0,O.z)(),[v,N]=(0,r.useState)("description"),[S,C]=(0,r.useState)(!1),[Z,P]=(0,r.useState)(null),[T,_]=(0,r.useState)(1),[R,L]=(0,r.useState)(null),[I,z]=(0,r.useState)(!0),[M,U]=(0,r.useState)(null),[V,G]=(0,r.useState)(0),[Q,B]=(0,r.useState)(!1),[H,W]=(0,r.useState)(!1),[X,J]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let s=async()=>{try{z(!0);let s=await fetch(`/api/products/${e}`),t=await s.json();if(t.success){let e=D(t.data);L(e);let s=t.data.variants||[];J(s.length>0),0===s.length&&G(e.price)}else U("Product not found")}catch(e){console.error("Error fetching product:",e),U("Failed to load product")}finally{z(!1)}};e&&s()},[e]),(0,r.useEffect)(()=>{(async()=>{if(t?.user?.id&&R?.id)try{let e=await fetch("/api/wishlist");if(e.ok){let s=(await e.json()).items.some(e=>e.id===R.id);B(s)}}catch(e){console.error("Error checking wishlist status:",e)}})()},[t,R]);let Y=(e,s)=>{P(e),G(s)},K=()=>{if(!R)return;let e=(0,A.FO)(V,g);d({type:"ADD_ITEM",payload:{...R,price:e.salePrice},selectedVariants:Z?[{id:Z.id,name:Z.name,value:Z.value,price:Z.price}]:void 0}),C(!0),setTimeout(()=>C(!1),2e3)},ee=async()=>{if(!t?.user?.id){s.push("/login");return}if(R?.id){W(!0);try{if(Q){let e=await fetch(`/api/wishlist?productId=${R.id}`,{method:"DELETE"});if(e.ok)B(!1),p("Removed from wishlist","success"),y(),f();else{let s=await e.json();p(s.error||"Failed to remove from wishlist","error")}}else{let e=await fetch("/api/wishlist",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:R.id})});if(e.ok)B(!0),p("Added to wishlist","success"),y(),f();else{let s=await e.json();p(s.error||"Failed to add to wishlist","error")}}}catch(e){console.error("Error updating wishlist:",e),p("An unexpected error occurred.","error")}finally{W(!1)}}},es=async()=>{let e={title:R?.name||"Check out this product",text:R?.shortDescription||"Amazing product from Herbalicious",url:window.location.href};try{navigator.share&&navigator.canShare(e)?await navigator.share(e):(await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!"))}catch(e){console.error("Error sharing:",e);try{await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!")}catch(e){console.error("Clipboard error:",e),alert("Unable to share. Please copy the URL manually.")}}};if(I)return a.jsx(E.QW,{});if(M||!R)return a.jsx("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-red-600 mb-4",children:M||"Product not found"}),a.jsx("button",{onClick:()=>s.back(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Go Back"})]})});let et=(0,A.FO)(V,g),ea=et.salePrice,er=et.isOnSale&&V>0;return(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden bg-white",children:[(0,a.jsxs)("div",{className:"sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b",children:[a.jsx("button",{onClick:()=>s.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:ee,disabled:H,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(c.Z,{className:`w-5 h-5 ${Q?"text-red-500 fill-current":"text-gray-600"}`})}),a.jsx("button",{onClick:es,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(o,{className:"w-5 h-5 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"relative p-4",children:[a.jsx(w,{images:R.images||[],productName:R.name}),a.jsx("div",{className:"absolute top-8 right-8 bg-white rounded-full p-2 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(x.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:R.rating})]})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:R.name}),a.jsx("p",{className:"text-gray-600 mb-3",children:R.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("div",{children:a.jsx(k.Z,{product:R._raw||R,showAsLinks:!0,size:"base"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>a.jsx(x.Z,{className:`w-4 h-4 ${s<Math.floor(R?.rating||0)?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",R?.reviews||0," reviews)"]})]})]}),a.jsx("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("span",{className:"text-3xl font-bold text-gray-900",children:ea>0?T>1?`₹${(ea*T).toFixed(0)}`:`₹${ea}`:"Loading..."}),er&&a.jsx("span",{className:"text-xl text-gray-500 line-through",children:T>1?`₹${(V*T).toFixed(0)}`:`₹${V}`})]}),T>1&&ea>0&&(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["₹",ea," \xd7 ",T]}),et.isOnSale&&et.discountPercentage>0&&(0,a.jsxs)("span",{className:"text-sm text-red-600 font-semibold mt-1",children:["Save ",et.discountPercentage,"% - Flash Sale!"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>a.jsx(x.Z,{className:`w-4 h-4 ${s<Math.floor(R?.rating||0)?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",R?.reviews||0," reviews)"]})]})]})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("div",{className:"flex border-b border-gray-200",children:[{id:"description",label:"Description"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>a.jsx("button",{onClick:()=>N(e.id),className:`px-4 py-2 text-sm font-medium transition-colors ${v===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"}`,children:e.label},e.id))}),(0,a.jsxs)("div",{className:"py-4",children:["description"===v&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-600 mb-4",children:R.description}),R.benefits.length>0&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:"Benefits:"}),a.jsx("ul",{className:"space-y-1",children:R.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[a.jsx(m.Z,{className:"w-4 h-4 text-green-600"}),a.jsx("span",{className:"text-sm text-gray-600",children:e})]},s))})]})]}),"reviews"===v&&a.jsx("div",{children:a.jsx($,{productId:R.id})}),"faqs"===v&&a.jsx("div",{children:a.jsx(j,{productId:R.id})})]})]}),a.jsx("div",{className:"mb-6",children:a.jsx(b,{productId:R.id,basePrice:R.price,onVariationChange:Y})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>_(Math.max(1,T-1)),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"-"}),a.jsx("span",{className:"w-10 text-center font-medium",children:T}),a.jsx("button",{onClick:()=>_(T+1),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"+"})]}),a.jsx("button",{onClick:K,disabled:S||0===V,className:`w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${S?"bg-green-500 text-white":0===V?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-green-600 text-white hover:bg-green-700"}`,children:S?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Add to Cart"})]})})]})]})]}),a.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsxs)("button",{onClick:()=>s.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors mb-8",children:[a.jsx(n.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Back to Products"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(w,{images:R.images||[],productName:R.name}),a.jsx("div",{className:"absolute top-6 right-6 bg-white rounded-full p-3 shadow-lg",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(x.Z,{className:"w-5 h-5 text-yellow-400 fill-current"}),a.jsx("span",{className:"font-medium text-gray-700",children:R.rating})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:R.name}),a.jsx("p",{className:"text-xl text-gray-600 mb-6",children:R.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("div",{children:a.jsx(k.Z,{product:R._raw||R,showAsLinks:!0,size:"lg"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>a.jsx(x.Z,{className:`w-5 h-5 ${s<Math.floor(R?.rating||0)?"text-yellow-400 fill-current":"text-gray-300"}`},s))}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",R?.reviews||0," reviews)"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-4xl font-bold text-gray-900",children:ea>0?T>1?`₹${(ea*T).toFixed(0)}`:`₹${ea}`:"Loading..."}),er&&a.jsx("span",{className:"text-2xl text-gray-500 line-through",children:T>1?`₹${(V*T).toFixed(0)}`:`₹${V}`})]}),T>1&&ea>0&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["₹",ea," \xd7 ",T]}),et.isOnSale&&et.discountPercentage>0&&(0,a.jsxs)("div",{className:"text-lg text-red-600 font-semibold mt-2",children:["Save ",et.discountPercentage,"% - Flash Sale!"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:ee,disabled:H,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:a.jsx(c.Z,{className:`w-6 h-6 ${Q?"text-red-500 fill-current":"text-gray-600"}`})}),a.jsx("button",{onClick:es,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:a.jsx(o,{className:"w-6 h-6 text-gray-600"})})]})]}),a.jsx("div",{className:"mb-8",children:a.jsx(b,{productId:R.id,basePrice:R.price,onVariationChange:Y})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-gray-700 font-medium",children:"Quantity:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>_(Math.max(1,T-1)),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg transition-colors",children:"-"}),a.jsx("span",{className:"w-12 text-center font-medium text-lg",children:T}),a.jsx("button",{onClick:()=>_(T+1),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg transition-colors",children:"+"})]})]}),a.jsx("button",{onClick:K,disabled:S||0===V,className:`w-full py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-3 text-lg ${S?"bg-green-500 text-white":0===V?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-green-600 text-white hover:bg-green-700"}`,children:S?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{className:"w-6 h-6"}),a.jsx("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{className:"w-6 h-6"}),a.jsx("span",{children:"Add to Cart"})]})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"flex border-b border-gray-200 mb-6",children:[{id:"description",label:"Description"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>a.jsx("button",{onClick:()=>N(e.id),className:`px-6 py-3 font-medium transition-colors ${v===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"}`,children:e.label},e.id))}),(0,a.jsxs)("div",{children:["description"===v&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:R.description}),R.benefits.length>0&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Benefits:"}),a.jsx("ul",{className:"space-y-3",children:R.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-3",children:[a.jsx(m.Z,{className:"w-5 h-5 text-green-600"}),a.jsx("span",{className:"text-gray-600",children:e})]},s))})]})]}),"reviews"===v&&a.jsx("div",{children:a.jsx($,{productId:R.id})}),"faqs"===v&&a.jsx("div",{children:a.jsx(j,{productId:R.id})})]})]})]})]})]})})]})}},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},32933:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},941:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},11890:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},39183:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},73622:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\ProductDetail.tsx#default`);function i({params:e}){return a.jsx(r.Z,{children:a.jsx(l,{id:e.id})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,3757,434,9694,9536,5090,561,389],()=>t(56105));module.exports=a})();