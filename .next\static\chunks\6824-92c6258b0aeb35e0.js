(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6824],{84850:function(e,t,r){"use strict";let n,i;r.d(t,{g:function(){return sB}});var s,o,a,u,c,l,f,d,h,p,g,m,y=r(2171);let b={step:"build",tags:["SET_EXPECT_HEADER","EXPECT_HEADER"],name:"addExpectContinueMiddleware",override:!0},v=e=>({applyToStack:t=>{t.add(t=>async r=>{let{request:n}=r;return y.aW.isInstance(n)&&n.body&&"node"===e.runtime&&e.requestHandler?.constructor?.name!=="FetchHttpHandler"&&(n.headers={...n.headers,Expect:"100-continue"}),t({...r,request:n})},b)}}),w="WHEN_SUPPORTED";(s=d||(d={})).MD5="MD5",s.CRC32="CRC32",s.CRC32C="CRC32C",s.CRC64NVME="CRC64NVME",s.SHA1="SHA1",s.SHA256="SHA256",(o=h||(h={})).HEADER="header",o.TRAILER="trailer",d.CRC32,(a=p||(p={})).ENV="env",a.CONFIG="shared config entry";var S=r(75020);function E(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{u(n.next(e))}catch(e){s(e)}}function a(e){try{u(n.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}u((n=n.apply(e,t||[])).next())})}function A(e,t){var r,n,i,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}function x(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}d.CRC32,d.CRC32C,d.CRC64NVME,d.SHA1,d.SHA256,d.SHA256,d.SHA1,d.CRC32,d.CRC32C,d.CRC64NVME,"function"==typeof SuppressedError&&SuppressedError;var R=r(50022).lW,k=void 0!==R&&R.from?function(e){return R.from(e,"utf8")}:e=>new TextEncoder().encode(e);function T(e){return e instanceof Uint8Array?e:"string"==typeof e?k(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}function P(e){return"string"==typeof e?0===e.length:0===e.byteLength}function I(e){return new Uint8Array([(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e])}function M(e){if(!Uint32Array.from){for(var t=new Uint32Array(e.length),r=0;r<e.length;)t[r]=e[r],r+=1;return t}return Uint32Array.from(e)}!function(){function e(){this.crc32c=new O}e.prototype.update=function(e){P(e)||this.crc32c.update(T(e))},e.prototype.digest=function(){return E(this,void 0,void 0,function(){return A(this,function(e){return[2,I(this.crc32c.digest())]})})},e.prototype.reset=function(){this.crc32c=new O}}();var O=function(){function e(){this.checksum=4294967295}return e.prototype.update=function(e){var t,r;try{for(var n=x(e),i=n.next();!i.done;i=n.next()){var s=i.value;this.checksum=this.checksum>>>8^C[(this.checksum^s)&255]}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return this},e.prototype.digest=function(){return(4294967295^this.checksum)>>>0},e}(),C=M([0,4067132163,3778769143,324072436,3348797215,904991772,648144872,3570033899,2329499855,2024987596,1809983544,2575936315,1296289744,3207089363,2893594407,1578318884,274646895,3795141740,4049975192,51262619,3619967088,632279923,922689671,3298075524,2592579488,1760304291,2075979607,2312596564,1562183871,2943781820,3156637768,1313733451,549293790,3537243613,3246849577,871202090,3878099393,357341890,102525238,4101499445,2858735121,1477399826,1264559846,3107202533,1845379342,2677391885,2361733625,2125378298,820201905,3263744690,3520608582,598981189,4151959214,85089709,373468761,3827903834,3124367742,1213305469,1526817161,2842354314,2107672161,2412447074,2627466902,1861252501,1098587580,3004210879,2688576843,1378610760,2262928035,1955203488,1742404180,2511436119,3416409459,969524848,714683780,3639785095,205050476,4266873199,3976438427,526918040,1361435347,2739821008,2954799652,1114974503,2529119692,1691668175,2005155131,2247081528,3690758684,697762079,986182379,3366744552,476452099,3993867776,4250756596,255256311,1640403810,2477592673,2164122517,1922457750,2791048317,1412925310,1197962378,3037525897,3944729517,427051182,170179418,4165941337,746937522,3740196785,3451792453,1070968646,1905808397,2213795598,2426610938,1657317369,3053634322,1147748369,1463399397,2773627110,4215344322,153784257,444234805,3893493558,1021025245,3467647198,3722505002,797665321,2197175160,1889384571,1674398607,2443626636,1164749927,3070701412,2757221520,1446797203,137323447,4198817972,3910406976,461344835,3484808360,1037989803,781091935,3705997148,2460548119,1623424788,1939049696,2180517859,1429367560,2807687179,3020495871,1180866812,410100952,3927582683,4182430767,186734380,3756733383,763408580,1053836080,3434856499,2722870694,1344288421,1131464017,2971354706,1708204729,2545590714,2229949006,1988219213,680717673,3673779818,3383336350,1002577565,4010310262,493091189,238226049,4233660802,2987750089,1082061258,1395524158,2705686845,1972364758,2279892693,2494862625,1725896226,952904198,3399985413,3656866545,731699698,4283874585,222117402,510512622,3959836397,3280807620,837199303,582374963,3504198960,68661723,4135334616,3844915500,390545967,1230274059,3141532936,2825850620,1510247935,2395924756,2091215383,1878366691,2644384480,3553878443,565732008,854102364,3229815391,340358836,3861050807,4117890627,119113024,1493875044,2875275879,3090270611,1247431312,2660249211,1828433272,2141937292,2378227087,3811616794,291187481,34330861,4032846830,615137029,3603020806,3314634738,939183345,1776939221,2609017814,2295496738,2058945313,2926798794,1545135305,1330124605,3173225534,4084100981,17165430,307568514,3762199681,888469610,3332340585,3587147933,665062302,2042050490,2346497209,2559330125,1793573966,3190661285,1279665062,1595330642,2910671697]);!function(){function e(){this.crc32=new N}e.prototype.update=function(e){P(e)||this.crc32.update(T(e))},e.prototype.digest=function(){return E(this,void 0,void 0,function(){return A(this,function(e){return[2,I(this.crc32.digest())]})})},e.prototype.reset=function(){this.crc32=new N}}();var N=function(){function e(){this.checksum=4294967295}return e.prototype.update=function(e){var t,r;try{for(var n=x(e),i=n.next();!i.done;i=n.next()){var s=i.value;this.checksum=this.checksum>>>8^_[(this.checksum^s)&255]}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return this},e.prototype.digest=function(){return(4294967295^this.checksum)>>>0},e}(),_=M([0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117]),U=r(95225);let $=e=>{let{requestChecksumCalculation:t,responseChecksumValidation:r,requestStreamBufferSize:n}=e;return Object.assign(e,{requestChecksumCalculation:(0,U.$)(t??w),responseChecksumValidation:(0,U.$)(r??w),requestStreamBufferSize:Number(n??0)})},B=e=>t=>async r=>{if(!y.aW.isInstance(r.request))return t(r);let{request:n}=r,{handlerProtocol:i=""}=e.requestHandler.metadata||{};if(i.indexOf("h2")>=0&&!n.headers[":authority"])delete n.headers.host,n.headers[":authority"]=n.hostname+(n.port?":"+n.port:"");else if(!n.headers.host){let e=n.hostname;null!=n.port&&(e+=`:${n.port}`),n.headers.host=e}return t(r)},L={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},D=e=>({applyToStack:t=>{t.add(B(e),L)}}),F=()=>(e,t)=>async r=>{try{let n=await e(r),{clientName:i,commandName:s,logger:o,dynamoDbDocumentClientOptions:a={}}=t,{overrideInputFilterSensitiveLog:u,overrideOutputFilterSensitiveLog:c}=a,l=u??t.inputFilterSensitiveLog,f=c??t.outputFilterSensitiveLog,{$metadata:d,...h}=n.output;return o?.info?.({clientName:i,commandName:s,input:l(r.input),output:f(h),metadata:d}),n}catch(u){let{clientName:e,commandName:n,logger:i,dynamoDbDocumentClientOptions:s={}}=t,{overrideInputFilterSensitiveLog:o}=s,a=o??t.inputFilterSensitiveLog;throw i?.error?.({clientName:e,commandName:n,input:a(r.input),error:u,metadata:u.$metadata}),u}},j={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},H=e=>({applyToStack:e=>{e.add(F(),j)}});var q=r(40257);let z="X-Amzn-Trace-Id",V=e=>t=>async r=>{let n;let{request:i}=r;if(!y.aW.isInstance(i)||"node"!==e.runtime)return t(r);let s=Object.keys(i.headers??{}).find(e=>e.toLowerCase()===z.toLowerCase())??z;if(i.headers.hasOwnProperty(s))return t(r);let o=q.env.AWS_LAMBDA_FUNCTION_NAME,a=q.env._X_AMZN_TRACE_ID;return"string"==typeof o&&o.length>0&&"string"==typeof(n=a)&&n.length>0&&(i.headers[z]=a),t({...r,request:i})},W={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},Z=e=>({applyToStack:t=>{t.add(V(e),W)}});var G=r(86321),K=r(57201);let Y=void 0;class J{constructor({size:e,params:t}){this.data=new Map,this.parameters=[],this.capacity=e??50,t&&(this.parameters=t)}get(e,t){let r=this.hash(e);if(!1===r)return t();if(!this.data.has(r)){if(this.data.size>this.capacity+10){let e=this.data.keys(),t=0;for(;;){let{value:r,done:n}=e.next();if(this.data.delete(r),n||++t>10)break}}this.data.set(r,t())}return this.data.get(r)}size(){return this.data.size}hash(e){let t="",{parameters:r}=this;if(0===r.length)return!1;for(let n of r){let r=String(e[n]??"");if(r.includes("|;"))return!1;t+=r+"|;"}return t}}let X=RegExp("^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$"),Q=e=>X.test(e)||e.startsWith("[")&&e.endsWith("]"),ee=RegExp("^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$"),et=(e,t=!1)=>{if(!t)return ee.test(e);for(let t of e.split("."))if(!et(t))return!1;return!0},er={},en="endpoints";function ei(e){return"object"!=typeof e||null==e?e:"ref"in e?`$${ei(e.ref)}`:"fn"in e?`${e.fn}(${(e.argv||[]).map(ei).join(", ")})`:JSON.stringify(e,null,2)}class es extends Error{constructor(e){super(e),this.name="EndpointError"}}let eo=e=>{let t=e.split("."),r=[];for(let n of t){let t=n.indexOf("[");if(-1!==t){if(n.indexOf("]")!==n.length-1)throw new es(`Path: '${e}' does not end with ']'`);let i=n.slice(t+1,-1);if(Number.isNaN(parseInt(i)))throw new es(`Invalid array index: '${i}' in path: '${e}'`);0!==t&&r.push(n.slice(0,t)),r.push(i)}else r.push(n)}return r},ea=(e,t)=>eo(t).reduce((r,n)=>{if("object"!=typeof r)throw new es(`Index '${n}' in '${t}' not found in '${JSON.stringify(e)}'`);return Array.isArray(r)?r[parseInt(n)]:r[n]},e);var eu=r(23017);let ec={[eu.cj.HTTP]:80,[eu.cj.HTTPS]:443},el={booleanEquals:(e,t)=>e===t,getAttr:ea,isSet:e=>null!=e,isValidHostLabel:et,not:e=>!e,parseURL:e=>{let t=(()=>{try{if(e instanceof URL)return e;if("object"==typeof e&&"hostname"in e){let{hostname:t,port:r,protocol:n="",path:i="",query:s={}}=e,o=new URL(`${n}//${t}${r?`:${r}`:""}${i}`);return o.search=Object.entries(s).map(([e,t])=>`${e}=${t}`).join("&"),o}return new URL(e)}catch(e){return null}})();if(!t)return console.error(`Unable to parse ${JSON.stringify(e)} as a whatwg URL.`),null;let r=t.href,{host:n,hostname:i,pathname:s,protocol:o,search:a}=t;if(a)return null;let u=o.slice(0,-1);if(!Object.values(eu.cj).includes(u))return null;let c=Q(i),l=r.includes(`${n}:${ec[u]}`)||"string"==typeof e&&e.includes(`${n}:${ec[u]}`),f=`${n}${l?`:${ec[u]}`:""}`;return{scheme:u,authority:f,path:s,normalizedPath:s.endsWith("/")?s:`${s}/`,isIp:c}},stringEquals:(e,t)=>e===t,substring:(e,t,r,n)=>t>=r||e.length<r?null:n?e.substring(e.length-r,e.length-t):e.substring(t,r),uriEncode:e=>encodeURIComponent(e).replace(/[!*'()]/g,e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`)},ef=(e,t)=>{let r=[],n={...t.endpointParams,...t.referenceRecord},i=0;for(;i<e.length;){let t=e.indexOf("{",i);if(-1===t){r.push(e.slice(i));break}r.push(e.slice(i,t));let s=e.indexOf("}",t);if(-1===s){r.push(e.slice(t));break}"{"===e[t+1]&&"}"===e[s+1]&&(r.push(e.slice(t+1,s)),i=s+2);let o=e.substring(t+1,s);if(o.includes("#")){let[e,t]=o.split("#");r.push(ea(n[e],t))}else r.push(n[o]);i=s+1}return r.join("")},ed=({ref:e},t)=>({...t.endpointParams,...t.referenceRecord})[e],eh=(e,t,r)=>{if("string"==typeof e)return ef(e,r);if(e.fn)return ep(e,r);if(e.ref)return ed(e,r);throw new es(`'${t}': ${String(e)} is not a string, function or reference.`)},ep=({fn:e,argv:t},r)=>{let n=t.map(e=>["boolean","number"].includes(typeof e)?e:eh(e,"arg",r)),i=e.split(".");return i[0]in er&&null!=i[1]?er[i[0]][i[1]](...n):el[e](...n)},eg=({assign:e,...t},r)=>{if(e&&e in r.referenceRecord)throw new es(`'${e}' is already defined in Reference Record.`);let n=ep(t,r);return r.logger?.debug?.(`${en} evaluateCondition: ${ei(t)} = ${ei(n)}`),{result:""===n||!!n,...null!=e&&{toAssign:{name:e,value:n}}}},em=(e=[],t)=>{let r={};for(let n of e){let{result:e,toAssign:i}=eg(n,{...t,referenceRecord:{...t.referenceRecord,...r}});if(!e)return{result:e};i&&(r[i.name]=i.value,t.logger?.debug?.(`${en} assign: ${i.name} := ${ei(i.value)}`))}return{result:!0,referenceRecord:r}},ey=(e,t)=>Object.entries(e).reduce((e,[r,n])=>({...e,[r]:n.map(e=>{let n=eh(e,"Header value entry",t);if("string"!=typeof n)throw new es(`Header '${r}' value '${n}' is not a string`);return n})}),{}),eb=(e,t)=>{if(Array.isArray(e))return e.map(e=>eb(e,t));switch(typeof e){case"string":return ef(e,t);case"object":if(null===e)throw new es(`Unexpected endpoint property: ${e}`);return ev(e,t);case"boolean":return e;default:throw new es(`Unexpected endpoint property type: ${typeof e}`)}},ev=(e,t)=>Object.entries(e).reduce((e,[r,n])=>({...e,[r]:eb(n,t)}),{}),ew=(e,t)=>{let r=eh(e,"Endpoint URL",t);if("string"==typeof r)try{return new URL(r)}catch(e){throw console.error(`Failed to construct URL with ${r}`,e),e}throw new es(`Endpoint URL must be a string, got ${typeof r}`)},eS=(e,t)=>{let{conditions:r,endpoint:n}=e,{result:i,referenceRecord:s}=em(r,t);if(!i)return;let o={...t,referenceRecord:{...t.referenceRecord,...s}},{url:a,properties:u,headers:c}=n;return t.logger?.debug?.(`${en} Resolving endpoint from template: ${ei(n)}`),{...void 0!=c&&{headers:ey(c,o)},...void 0!=u&&{properties:ev(u,o)},url:ew(a,o)}},eE=(e,t)=>{let{conditions:r,error:n}=e,{result:i,referenceRecord:s}=em(r,t);if(i)throw new es(eh(n,"Error",{...t,referenceRecord:{...t.referenceRecord,...s}}))},eA=(e,t)=>{let{conditions:r,rules:n}=e,{result:i,referenceRecord:s}=em(r,t);if(i)return ex(n,{...t,referenceRecord:{...t.referenceRecord,...s}})},ex=(e,t)=>{for(let r of e)if("endpoint"===r.type){let e=eS(r,t);if(e)return e}else if("error"===r.type)eE(r,t);else if("tree"===r.type){let e=eA(r,t);if(e)return e}else throw new es(`Unknown endpoint rule: ${r}`);throw new es("Rules evaluation failed")},eR=(e,t)=>{let{endpointParams:r,logger:n}=t,{parameters:i,rules:s}=e;t.logger?.debug?.(`${en} Initial EndpointParams: ${ei(r)}`);let o=Object.entries(i).filter(([,e])=>null!=e.default).map(([e,t])=>[e,t.default]);if(o.length>0)for(let[e,t]of o)r[e]=r[e]??t;for(let e of Object.entries(i).filter(([,e])=>e.required).map(([e])=>e))if(null==r[e])throw new es(`Missing required parameter: '${e}'`);let a=ex(s,{endpointParams:r,logger:n,referenceRecord:{}});return t.logger?.debug?.(`${en} Resolved endpoint: ${ei(a)}`),a},ek=(e,t=!1)=>{if(t){for(let t of e.split("."))if(!ek(t))return!1;return!0}return!(!et(e)||e.length<3||e.length>63||e!==e.toLowerCase()||Q(e))};var eT=JSON.parse('{"partitions":[{"id":"aws","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-east-1","name":"aws","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^(us|eu|ap|sa|ca|me|af|il|mx)\\\\-\\\\w+\\\\-\\\\d+$","regions":{"af-south-1":{"description":"Africa (Cape Town)"},"ap-east-1":{"description":"Asia Pacific (Hong Kong)"},"ap-east-2":{"description":"Asia Pacific (Taipei)"},"ap-northeast-1":{"description":"Asia Pacific (Tokyo)"},"ap-northeast-2":{"description":"Asia Pacific (Seoul)"},"ap-northeast-3":{"description":"Asia Pacific (Osaka)"},"ap-south-1":{"description":"Asia Pacific (Mumbai)"},"ap-south-2":{"description":"Asia Pacific (Hyderabad)"},"ap-southeast-1":{"description":"Asia Pacific (Singapore)"},"ap-southeast-2":{"description":"Asia Pacific (Sydney)"},"ap-southeast-3":{"description":"Asia Pacific (Jakarta)"},"ap-southeast-4":{"description":"Asia Pacific (Melbourne)"},"ap-southeast-5":{"description":"Asia Pacific (Malaysia)"},"ap-southeast-7":{"description":"Asia Pacific (Thailand)"},"aws-global":{"description":"AWS Standard global region"},"ca-central-1":{"description":"Canada (Central)"},"ca-west-1":{"description":"Canada West (Calgary)"},"eu-central-1":{"description":"Europe (Frankfurt)"},"eu-central-2":{"description":"Europe (Zurich)"},"eu-north-1":{"description":"Europe (Stockholm)"},"eu-south-1":{"description":"Europe (Milan)"},"eu-south-2":{"description":"Europe (Spain)"},"eu-west-1":{"description":"Europe (Ireland)"},"eu-west-2":{"description":"Europe (London)"},"eu-west-3":{"description":"Europe (Paris)"},"il-central-1":{"description":"Israel (Tel Aviv)"},"me-central-1":{"description":"Middle East (UAE)"},"me-south-1":{"description":"Middle East (Bahrain)"},"mx-central-1":{"description":"Mexico (Central)"},"sa-east-1":{"description":"South America (Sao Paulo)"},"us-east-1":{"description":"US East (N. Virginia)"},"us-east-2":{"description":"US East (Ohio)"},"us-west-1":{"description":"US West (N. California)"},"us-west-2":{"description":"US West (Oregon)"}}},{"id":"aws-cn","outputs":{"dnsSuffix":"amazonaws.com.cn","dualStackDnsSuffix":"api.amazonwebservices.com.cn","implicitGlobalRegion":"cn-northwest-1","name":"aws-cn","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^cn\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-cn-global":{"description":"AWS China global region"},"cn-north-1":{"description":"China (Beijing)"},"cn-northwest-1":{"description":"China (Ningxia)"}}},{"id":"aws-us-gov","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-gov-west-1","name":"aws-us-gov","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-us-gov-global":{"description":"AWS GovCloud (US) global region"},"us-gov-east-1":{"description":"AWS GovCloud (US-East)"},"us-gov-west-1":{"description":"AWS GovCloud (US-West)"}}},{"id":"aws-iso","outputs":{"dnsSuffix":"c2s.ic.gov","dualStackDnsSuffix":"c2s.ic.gov","implicitGlobalRegion":"us-iso-east-1","name":"aws-iso","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-global":{"description":"AWS ISO (US) global region"},"us-iso-east-1":{"description":"US ISO East"},"us-iso-west-1":{"description":"US ISO WEST"}}},{"id":"aws-iso-b","outputs":{"dnsSuffix":"sc2s.sgov.gov","dualStackDnsSuffix":"sc2s.sgov.gov","implicitGlobalRegion":"us-isob-east-1","name":"aws-iso-b","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-b-global":{"description":"AWS ISOB (US) global region"},"us-isob-east-1":{"description":"US ISOB East (Ohio)"}}},{"id":"aws-iso-e","outputs":{"dnsSuffix":"cloud.adc-e.uk","dualStackDnsSuffix":"cloud.adc-e.uk","implicitGlobalRegion":"eu-isoe-west-1","name":"aws-iso-e","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^eu\\\\-isoe\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-e-global":{"description":"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{"description":"EU ISOE West"}}},{"id":"aws-iso-f","outputs":{"dnsSuffix":"csp.hci.ic.gov","dualStackDnsSuffix":"csp.hci.ic.gov","implicitGlobalRegion":"us-isof-south-1","name":"aws-iso-f","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isof\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-f-global":{"description":"AWS ISOF global region"},"us-isof-east-1":{"description":"US ISOF EAST"},"us-isof-south-1":{"description":"US ISOF SOUTH"}}},{"id":"aws-eusc","outputs":{"dnsSuffix":"amazonaws.eu","dualStackDnsSuffix":"amazonaws.eu","implicitGlobalRegion":"eusc-de-east-1","name":"aws-eusc","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^eusc\\\\-(de)\\\\-\\\\w+\\\\-\\\\d+$","regions":{"eusc-de-east-1":{"description":"EU (Germany)"}}}],"version":"1.1"}');let eP=()=>"",eI={isVirtualHostableS3Bucket:ek,parseArn:e=>{let t=e.split(":");if(t.length<6)return null;let[r,n,i,s,o,...a]=t;return"arn"!==r||""===n||""===i||""===a.join(":")?null:{partition:n,service:i,region:s,accountId:o,resourceId:a.map(e=>e.split("/")).flat()}},partition:e=>{let{partitions:t}=eT;for(let r of t){let{regions:t,outputs:n}=r;for(let[r,i]of Object.entries(t))if(r===e)return{...n,...i}}for(let r of t){let{regionRegex:t,outputs:n}=r;if(new RegExp(t).test(e))return{...n}}let r=t.find(e=>"aws"===e.id);if(!r)throw Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...r.outputs}}};er.aws=eI;var eM=r(77844);let eO=/\d{12}\.ddb/;async function eC(e,t,r){let n=r.request;if(n?.headers?.["smithy-protocol"]==="rpc-v2-cbor"&&(0,eM.h)(e,"PROTOCOL_RPC_V2_CBOR","M"),"function"==typeof t.retryStrategy){let r=await t.retryStrategy();"function"==typeof r.acquireInitialRetryToken?r.constructor?.name?.includes("Adaptive")?(0,eM.h)(e,"RETRY_MODE_ADAPTIVE","F"):(0,eM.h)(e,"RETRY_MODE_STANDARD","E"):(0,eM.h)(e,"RETRY_MODE_LEGACY","D")}if("function"==typeof t.accountIdEndpointMode){let r=e.endpointV2;switch(String(r?.url?.hostname).match(eO)&&(0,eM.h)(e,"ACCOUNT_ID_ENDPOINT","O"),await t.accountIdEndpointMode?.()){case"disabled":(0,eM.h)(e,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":(0,eM.h)(e,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":(0,eM.h)(e,"ACCOUNT_ID_MODE_REQUIRED","R")}}let i=e.__smithy_context?.selectedHttpAuthScheme?.identity;if(i?.$source)for(let[t,r]of(i.accountId&&(0,eM.h)(e,"RESOLVED_ACCOUNT_ID","T"),Object.entries(i.$source??{})))(0,eM.h)(e,t,r)}let eN="user-agent",e_="x-amz-user-agent",eU=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,e$=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,eB=e=>(t,r)=>async n=>{let{request:i}=n;if(!y.aW.isInstance(i))return t(n);let{headers:s}=i,o=r?.userAgent?.map(eL)||[],a=(await e.defaultUserAgentProvider()).map(eL);await eC(r,e,n),a.push(`m/${function(e){let t="";for(let r in e){let n=e[r];if(t.length+n.length+1<=1024){t.length?t+=","+n:t+=n;continue}break}return t}(Object.assign({},r.__smithy_context?.features,r.__aws_sdk_context?.features))}`);let u=e?.customUserAgent?.map(eL)||[],c=await e.userAgentAppId();c&&a.push(eL([`app/${c}`]));let l=eP(),f=(l?[l]:[]).concat([...a,...o,...u]).join(" "),d=[...a.filter(e=>e.startsWith("aws-sdk-")),...u].join(" ");return"browser"!==e.runtime?(d&&(s[e_]=s[e_]?`${s[eN]} ${d}`:d),s[eN]=f):s[e_]=f,t({...n,request:i})},eL=e=>{let t=e[0].split("/").map(e=>e.replace(eU,"-")).join("/"),r=e[1]?.replace(e$,"-"),n=t.indexOf("/"),i=t.substring(0,n),s=t.substring(n+1);return"api"===i&&(s=s.toLowerCase()),[i,s,r].filter(e=>e&&e.length>0).reduce((e,t,r)=>{switch(r){case 0:return t;case 1:return`${e}/${t}`;default:return`${e}#${t}`}},"")},eD={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},eF=e=>({applyToStack:t=>{t.add(eB(e),eD)}});r(32480);let ej=e=>"string"==typeof e&&(e.startsWith("fips-")||e.endsWith("-fips")),eH=e=>ej(e)?["fips-aws-global","aws-fips"].includes(e)?"us-east-1":e.replace(/fips-(dkr-|prod-)?|-fips/,""):e,eq=e=>{let{region:t,useFipsEndpoint:r}=e;if(!t)throw Error("Region is missing");return Object.assign(e,{region:async()=>"string"==typeof t?eH(t):eH(await t()),useFipsEndpoint:async()=>!!ej("string"==typeof t?t:await t())||("function"!=typeof r?Promise.resolve(!!r):r())})},ez=e=>Object.assign(e,{eventStreamMarshaller:e.eventStreamSerdeProvider(e)}),eV="content-length",eW={step:"build",tags:["SET_CONTENT_LENGTH","CONTENT_LENGTH"],name:"contentLengthMiddleware",override:!0},eZ=e=>({applyToStack:t=>{var r;t.add((r=e.bodyLengthChecker,e=>async t=>{let n=t.request;if(y.aW.isInstance(n)){let{body:e,headers:t}=n;if(e&&-1===Object.keys(t).map(e=>e.toLowerCase()).indexOf(eV))try{let t=r(e);n.headers={...n.headers,[eV]:String(t)}}catch(e){}}return e({...t,request:n})}),eW)}});var eG=r(65484);(u=g||(g={})).STANDARD="standard",u.ADAPTIVE="adaptive";let eK=g.STANDARD,eY=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException","TransactionInProgressException"],eJ=["TimeoutError","RequestTimeout","RequestTimeoutException"],eX=[500,502,503,504],eQ=["ECONNRESET","ECONNREFUSED","EPIPE","ETIMEDOUT"],e0=["EHOSTUNREACH","ENETUNREACH","ENOTFOUND"],e1=e=>e.$metadata?.clockSkewCorrected,e2=e=>{let t=new Set(["Failed to fetch","NetworkError when attempting to fetch resource","The Internet connection appears to be offline","Load failed","Network request failed"]);return!!(e&&e instanceof TypeError)&&t.has(e.message)},e3=e=>e.$metadata?.httpStatusCode===429||eY.includes(e.name)||e.$retryable?.throttling==!0,e4=(e,t=0)=>e1(e)||eJ.includes(e.name)||eQ.includes(e?.code||"")||e0.includes(e?.code||"")||eX.includes(e.$metadata?.httpStatusCode||0)||e2(e)||void 0!==e.cause&&t<=10&&e4(e.cause,t+1),e6=e=>{if(e.$metadata?.httpStatusCode!==void 0){let t=e.$metadata.httpStatusCode;if(500<=t&&t<=599&&!e4(e))return!0}return!1};class e8{constructor(e){this.currentCapacity=0,this.enabled=!1,this.lastMaxRate=0,this.measuredTxRate=0,this.requestCount=0,this.lastTimestamp=0,this.timeWindow=0,this.beta=e?.beta??.7,this.minCapacity=e?.minCapacity??1,this.minFillRate=e?.minFillRate??.5,this.scaleConstant=e?.scaleConstant??.4,this.smooth=e?.smooth??.8;let t=this.getCurrentTimeInSeconds();this.lastThrottleTime=t,this.lastTxRateBucket=Math.floor(this.getCurrentTimeInSeconds()),this.fillRate=this.minFillRate,this.maxCapacity=this.minCapacity}getCurrentTimeInSeconds(){return Date.now()/1e3}async getSendToken(){return this.acquireTokenBucket(1)}async acquireTokenBucket(e){if(this.enabled){if(this.refillTokenBucket(),e>this.currentCapacity){let t=(e-this.currentCapacity)/this.fillRate*1e3;await new Promise(e=>e8.setTimeoutFn(e,t))}this.currentCapacity=this.currentCapacity-e}}refillTokenBucket(){let e=this.getCurrentTimeInSeconds();if(!this.lastTimestamp){this.lastTimestamp=e;return}let t=(e-this.lastTimestamp)*this.fillRate;this.currentCapacity=Math.min(this.maxCapacity,this.currentCapacity+t),this.lastTimestamp=e}updateClientSendingRate(e){let t;if(this.updateMeasuredRate(),e3(e)){let e=this.enabled?Math.min(this.measuredTxRate,this.fillRate):this.measuredTxRate;this.lastMaxRate=e,this.calculateTimeWindow(),this.lastThrottleTime=this.getCurrentTimeInSeconds(),t=this.cubicThrottle(e),this.enableTokenBucket()}else this.calculateTimeWindow(),t=this.cubicSuccess(this.getCurrentTimeInSeconds());let r=Math.min(t,2*this.measuredTxRate);this.updateTokenBucketRate(r)}calculateTimeWindow(){this.timeWindow=this.getPrecise(Math.pow(this.lastMaxRate*(1-this.beta)/this.scaleConstant,1/3))}cubicThrottle(e){return this.getPrecise(e*this.beta)}cubicSuccess(e){return this.getPrecise(this.scaleConstant*Math.pow(e-this.lastThrottleTime-this.timeWindow,3)+this.lastMaxRate)}enableTokenBucket(){this.enabled=!0}updateTokenBucketRate(e){this.refillTokenBucket(),this.fillRate=Math.max(e,this.minFillRate),this.maxCapacity=Math.max(e,this.minCapacity),this.currentCapacity=Math.min(this.currentCapacity,this.maxCapacity)}updateMeasuredRate(){let e=Math.floor(2*this.getCurrentTimeInSeconds())/2;if(this.requestCount++,e>this.lastTxRateBucket){let t=this.requestCount/(e-this.lastTxRateBucket);this.measuredTxRate=this.getPrecise(t*this.smooth+this.measuredTxRate*(1-this.smooth)),this.requestCount=0,this.lastTxRateBucket=e}}getPrecise(e){return parseFloat(e.toFixed(8))}}e8.setTimeoutFn=setTimeout;let e5=()=>{let e=100;return{computeNextBackoffDelay:t=>Math.floor(Math.min(2e4,Math.random()*2**t*e)),setDelayBase:t=>{e=t}}},e7=({retryDelay:e,retryCount:t,retryCost:r})=>({getRetryCount:()=>t,getRetryDelay:()=>Math.min(2e4,e),getRetryCost:()=>r});class e9{constructor(e){this.maxAttempts=e,this.mode=g.STANDARD,this.capacity=500,this.retryBackoffStrategy=e5(),this.maxAttemptsProvider="function"==typeof e?e:async()=>e}async acquireInitialRetryToken(e){return e7({retryDelay:100,retryCount:0})}async refreshRetryTokenForRetry(e,t){let r=await this.getMaxAttempts();if(this.shouldRetry(e,t,r)){let r=t.errorType;this.retryBackoffStrategy.setDelayBase("THROTTLING"===r?500:100);let n=this.retryBackoffStrategy.computeNextBackoffDelay(e.getRetryCount()),i=t.retryAfterHint?Math.max(t.retryAfterHint.getTime()-Date.now()||0,n):n,s=this.getCapacityCost(r);return this.capacity-=s,e7({retryDelay:i,retryCount:e.getRetryCount()+1,retryCost:s})}throw Error("No retry token available")}recordSuccess(e){this.capacity=Math.max(500,this.capacity+(e.getRetryCost()??1))}getCapacity(){return this.capacity}async getMaxAttempts(){try{return await this.maxAttemptsProvider()}catch(e){return console.warn("Max attempts provider could not resolve. Using default of 3"),3}}shouldRetry(e,t,r){return e.getRetryCount()+1<r&&this.capacity>=this.getCapacityCost(t.errorType)&&this.isRetryableError(t.errorType)}getCapacityCost(e){return"TRANSIENT"===e?10:5}isRetryableError(e){return"THROTTLING"===e||"TRANSIENT"===e}}class te{constructor(e,t){this.maxAttemptsProvider=e,this.mode=g.ADAPTIVE;let{rateLimiter:r}=t??{};this.rateLimiter=r??new e8,this.standardRetryStrategy=new e9(e)}async acquireInitialRetryToken(e){return await this.rateLimiter.getSendToken(),this.standardRetryStrategy.acquireInitialRetryToken(e)}async refreshRetryTokenForRetry(e,t){return this.rateLimiter.updateClientSendingRate(t),this.standardRetryStrategy.refreshRetryTokenForRetry(e,t)}recordSuccess(e){this.rateLimiter.updateClientSendingRate({}),this.standardRetryStrategy.recordSuccess(e)}}var tt={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let tr=new Uint8Array(16),tn=[];for(let e=0;e<256;++e)tn.push((e+256).toString(16).slice(1));var ti=function(e,t,r){if(tt.randomUUID&&!t&&!e)return tt.randomUUID();let i=(e=e||{}).random||(e.rng||function(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(tr)})();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=i[e];return t}return function(e,t=0){return tn[e[t+0]]+tn[e[t+1]]+tn[e[t+2]]+tn[e[t+3]]+"-"+tn[e[t+4]]+tn[e[t+5]]+"-"+tn[e[t+6]]+tn[e[t+7]]+"-"+tn[e[t+8]]+tn[e[t+9]]+"-"+tn[e[t+10]]+tn[e[t+11]]+tn[e[t+12]]+tn[e[t+13]]+tn[e[t+14]]+tn[e[t+15]]}(i)};let ts=e=>e instanceof Error?e:e instanceof Object?Object.assign(Error(),e):"string"==typeof e?Error(e):Error(`AWS SDK error wrapper for ${e}`),to=e=>{let{retryStrategy:t,retryMode:r,maxAttempts:n}=e,i=(0,U.$)(n??3);return Object.assign(e,{maxAttempts:i,retryStrategy:async()=>t||(await (0,U.$)(r)()===g.ADAPTIVE?new te(i):new e9(i))})};var ta=r(4598);let tu=e=>e?.body instanceof ReadableStream,tc=e=>(t,r)=>async n=>{let i=await e.retryStrategy(),s=await e.maxAttempts();if(!tl(i))return i?.mode&&(r.userAgent=[...r.userAgent||[],["cfg/retry-mode",i.mode]]),i.retry(t,n);{let e=await i.acquireInitialRetryToken(r.partition_id),o=Error(),a=0,u=0,{request:c}=n,l=y.aW.isInstance(c);for(l&&(c.headers["amz-sdk-invocation-id"]=ti());;)try{l&&(c.headers["amz-sdk-request"]=`attempt=${a+1}; max=${s}`);let{response:r,output:o}=await t(n);return i.recordSuccess(e),o.$metadata.attempts=a+1,o.$metadata.totalRetryDelay=u,{response:r,output:o}}catch(s){let t=tf(s);if(o=ts(s),l&&tu(c))throw(r.logger instanceof ta.vk?console:r.logger)?.warn("An error was encountered in a non-retryable streaming request."),o;try{e=await i.refreshRetryTokenForRetry(e,t)}catch(e){throw o.$metadata||(o.$metadata={}),o.$metadata.attempts=a+1,o.$metadata.totalRetryDelay=u,o}a=e.getRetryCount();let n=e.getRetryDelay();u+=n,await new Promise(e=>setTimeout(e,n))}}},tl=e=>void 0!==e.acquireInitialRetryToken&&void 0!==e.refreshRetryTokenForRetry&&void 0!==e.recordSuccess,tf=e=>{let t={error:e,errorType:td(e)},r=tg(e.$response);return r&&(t.retryAfterHint=r),t},td=e=>e3(e)?"THROTTLING":e4(e)?"TRANSIENT":e6(e)?"SERVER_ERROR":"CLIENT_ERROR",th={name:"retryMiddleware",tags:["RETRY"],step:"finalizeRequest",priority:"high",override:!0},tp=e=>({applyToStack:t=>{t.add(tc(e),th)}}),tg=e=>{if(!y.Zn.isInstance(e))return;let t=Object.keys(e.headers).find(e=>"retry-after"===e.toLowerCase());if(!t)return;let r=e.headers[t],n=Number(r);return new Date(Number.isNaN(n)?r:1e3*n)};var tm=r(34819);let ty=e=>{let t,r,n=e.credentials,i=!!e.credentials;Object.defineProperty(e,"credentials",{set(r){r&&r!==n&&r!==t&&(i=!0);let s=function(e,{credentials:t,credentialDefaultProvider:r}){let n;return(n=t?t?.memoized?t:(0,K.CU)(t,K.BP,K.zV):r?(0,K.$E)(r(Object.assign({},e,{parentClientConfig:e}))):async()=>{throw Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")}).memoized=!0,n}(e,{credentials:n=r,credentialDefaultProvider:e.credentialDefaultProvider}),o=function(e,t){if(t.configBound)return t;let r=async r=>t({...r,callerClientConfig:e});return r.memoized=t.memoized,r.configBound=!0,r}(e,s);i&&!o.attributed?((t=async e=>o(e).then(e=>(e.$source||(e.$source={}),e.$source.CREDENTIALS_CODE="e",e))).memoized=o.memoized,t.configBound=o.configBound,t.attributed=!0):t=o},get:()=>t,enumerable:!0,configurable:!0}),e.credentials=n;let{signingEscapePath:s=!0,systemClockOffset:o=e.systemClockOffset||0,sha256:a}=e;return r=e.signer?(0,K.$E)(e.signer):e.regionInfoProvider?()=>(0,K.$E)(e.region)().then(async t=>[await e.regionInfoProvider(t,{useFipsEndpoint:await e.useFipsEndpoint(),useDualstackEndpoint:await e.useDualstackEndpoint()})||{},t]).then(([t,r])=>{let{signingRegion:n,signingService:i}=t;e.signingRegion=e.signingRegion||n||r,e.signingName=e.signingName||i||e.serviceId;let o={...e,credentials:e.credentials,region:e.signingRegion,service:e.signingName,sha256:a,uriEscapePath:s};return new(e.signerConstructor||tm.L1)(o)}):async t=>{let r=(t=Object.assign({},{name:"sigv4",signingName:e.signingName||e.defaultSigningName,signingRegion:await (0,K.$E)(e.region)(),properties:{}},t)).signingRegion,n=t.signingName;e.signingRegion=e.signingRegion||r,e.signingName=e.signingName||n||e.serviceId;let i={...e,credentials:e.credentials,region:e.signingRegion,service:e.signingName,sha256:a,uriEscapePath:s};return new(e.signerConstructor||tm.L1)(i)},Object.assign(e,{systemClockOffset:o,signingEscapePath:s,signer:r})},tb=(e,t,r)=>{let n,i,s;let o=!1,a=async()=>{i||(i=e());try{n=await i,s=!0,o=!1}finally{i=void 0}return n};return void 0===t?async e=>((!s||e?.forceRefresh)&&(n=await a()),n):async e=>((!s||e?.forceRefresh)&&(n=await a()),o||(r&&!r(n)?o=!0:t(n)&&await a()),n)},tv=e=>(e.sigv4aSigningRegionSet=(0,K.$E)(e.sigv4aSigningRegionSet),e);var tw=r(43873);let tS="required",tE="type",tA="rules",tx="conditions",tR="argv",tk="assign",tT="properties",tP="backend",tI="authSchemes",tM="disableDoubleEncoding",tO="signingName",tC="signingRegion",tN="headers",t_="signingRegionSet",tU="isSet",t$="booleanEquals",tB="error",tL="aws.partition",tD="stringEquals",tF="getAttr",tj="name",tH="substring",tq="bucketSuffix",tz="parseURL",tV="endpoint",tW="tree",tZ="aws.isVirtualHostableS3Bucket",tG="{url#scheme}://{Bucket}.{url#authority}{url#path}",tK="accessPointSuffix",tY="{url#scheme}://{url#authority}{url#path}",tJ="hardwareType",tX="regionPrefix",tQ="bucketAliasSuffix",t0="outpostId",t1="isValidHostLabel",t2="sigv4a",t3="s3-outposts",t4="{url#scheme}://{url#authority}{url#normalizedPath}{Bucket}",t6="https://{Bucket}.s3-accelerate.{partitionResult#dnsSuffix}",t8="https://{Bucket}.s3.{partitionResult#dnsSuffix}",t5="aws.parseArn",t7="bucketArn",t9="arnType",re="s3-object-lambda",rt="accesspoint",rr="accessPointName",rn="{url#scheme}://{accessPointName}-{bucketArn#accountId}.{url#authority}{url#path}",ri="mrapPartition",rs="outpostType",ro="arnPrefix",ra="{url#scheme}://{url#authority}{url#normalizedPath}{uri_encoded_bucket}",ru="https://s3.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",rc="https://s3.{partitionResult#dnsSuffix}",rl={[tS]:!1,[tE]:"String"},rf={[tS]:!0,default:!1,[tE]:"Boolean"},rd={[tS]:!1,[tE]:"Boolean"},rh={fn:t$,[tR]:[{ref:"Accelerate"},!0]},rp={fn:t$,[tR]:[{ref:"UseFIPS"},!0]},rg={fn:t$,[tR]:[{ref:"UseDualStack"},!0]},rm={fn:tU,[tR]:[{ref:"Endpoint"}]},ry={fn:tL,[tR]:[{ref:"Region"}],[tk]:"partitionResult"},rb={fn:tD,[tR]:[{fn:tF,[tR]:[{ref:"partitionResult"},tj]},"aws-cn"]},rv={fn:tU,[tR]:[{ref:"Bucket"}]},rw={ref:"Bucket"},rS={[tx]:[rg],[tB]:"S3Express does not support Dual-stack.",[tE]:tB},rE={[tx]:[rh],[tB]:"S3Express does not support S3 Accelerate.",[tE]:tB},rA={[tx]:[rm,{fn:tz,[tR]:[{ref:"Endpoint"}],[tk]:"url"}],[tA]:[{[tx]:[{fn:tU,[tR]:[{ref:"DisableS3ExpressSessionAuth"}]},{fn:t$,[tR]:[{ref:"DisableS3ExpressSessionAuth"},!0]}],[tA]:[{[tx]:[{fn:t$,[tR]:[{fn:tF,[tR]:[{ref:"url"},"isIp"]},!0]}],[tA]:[{[tx]:[{fn:"uriEncode",[tR]:[rw],[tk]:"uri_encoded_bucket"}],[tA]:[{[tV]:{url:"{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}",[tT]:{[tP]:"S3Express",[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:"s3express",[tC]:"{Region}"}]},[tN]:{}},[tE]:tV}],[tE]:tW}],[tE]:tW},{[tx]:[{fn:tZ,[tR]:[rw,!1]}],[tA]:[{[tV]:{url:tG,[tT]:{[tP]:"S3Express",[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:"s3express",[tC]:"{Region}"}]},[tN]:{}},[tE]:tV}],[tE]:tW},{[tB]:"S3Express bucket name is not a valid virtual hostable name.",[tE]:tB}],[tE]:tW},{[tx]:[{fn:t$,[tR]:[{fn:tF,[tR]:[{ref:"url"},"isIp"]},!0]}],[tA]:[{[tx]:[{fn:"uriEncode",[tR]:[rw],[tk]:"uri_encoded_bucket"}],[tA]:[{[tV]:{url:"{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}",[tT]:{[tP]:"S3Express",[tI]:[{[tM]:!0,[tj]:"sigv4-s3express",[tO]:"s3express",[tC]:"{Region}"}]},[tN]:{}},[tE]:tV}],[tE]:tW}],[tE]:tW},{[tx]:[{fn:tZ,[tR]:[rw,!1]}],[tA]:[{[tV]:{url:tG,[tT]:{[tP]:"S3Express",[tI]:[{[tM]:!0,[tj]:"sigv4-s3express",[tO]:"s3express",[tC]:"{Region}"}]},[tN]:{}},[tE]:tV}],[tE]:tW},{[tB]:"S3Express bucket name is not a valid virtual hostable name.",[tE]:tB}],[tE]:tW},rx={fn:tz,[tR]:[{ref:"Endpoint"}],[tk]:"url"},rR={fn:t$,[tR]:[{fn:tF,[tR]:[{ref:"url"},"isIp"]},!0]},rk={ref:"url"},rT={fn:"uriEncode",[tR]:[rw],[tk]:"uri_encoded_bucket"},rP={[tP]:"S3Express",[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:"s3express",[tC]:"{Region}"}]},rI={},rM={fn:tZ,[tR]:[rw,!1]},rO={[tB]:"S3Express bucket name is not a valid virtual hostable name.",[tE]:tB},rC={fn:tU,[tR]:[{ref:"UseS3ExpressControlEndpoint"}]},rN={fn:t$,[tR]:[{ref:"UseS3ExpressControlEndpoint"},!0]},r_={fn:"not",[tR]:[rm]},rU={[tB]:"Unrecognized S3Express bucket name format.",[tE]:tB},r$={fn:"not",[tR]:[rv]},rB={ref:tJ},rL={[tx]:[r_],[tB]:"Expected a endpoint to be specified but no endpoint was found",[tE]:tB},rD={[tI]:[{[tM]:!0,[tj]:t2,[tO]:t3,[t_]:["*"]},{[tM]:!0,[tj]:"sigv4",[tO]:t3,[tC]:"{Region}"}]},rF={fn:t$,[tR]:[{ref:"ForcePathStyle"},!1]},rj={fn:t$,[tR]:[{ref:"Accelerate"},!1]},rH={fn:tD,[tR]:[{ref:"Region"},"aws-global"]},rq={[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:"s3",[tC]:"us-east-1"}]},rz={fn:"not",[tR]:[rH]},rV={fn:t$,[tR]:[{ref:"UseGlobalEndpoint"},!0]},rW={url:"https://{Bucket}.s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}",[tT]:{[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:"s3",[tC]:"{Region}"}]},[tN]:{}},rZ={[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:"s3",[tC]:"{Region}"}]},rG={fn:t$,[tR]:[{ref:"UseGlobalEndpoint"},!1]},rK={fn:t$,[tR]:[{ref:"UseDualStack"},!1]},rY={url:"https://{Bucket}.s3-fips.{Region}.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},rJ={fn:t$,[tR]:[{ref:"UseFIPS"},!1]},rX={url:"https://{Bucket}.s3-accelerate.dualstack.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},rQ={url:"https://{Bucket}.s3.dualstack.{Region}.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},r0={fn:t$,[tR]:[{fn:tF,[tR]:[rk,"isIp"]},!1]},r1={url:t4,[tT]:rZ,[tN]:{}},r2={url:tG,[tT]:rZ,[tN]:{}},r3={[tV]:r2,[tE]:tV},r4={url:t6,[tT]:rZ,[tN]:{}},r6={url:"https://{Bucket}.s3.{Region}.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},r8={[tB]:"Invalid region: region was not a valid DNS name.",[tE]:tB},r5={ref:t7},r7={ref:t9},r9={fn:tF,[tR]:[r5,"service"]},ne={ref:rr},nt={[tx]:[rg],[tB]:"S3 Object Lambda does not support Dual-stack",[tE]:tB},nr={[tx]:[rh],[tB]:"S3 Object Lambda does not support S3 Accelerate",[tE]:tB},nn={[tx]:[{fn:tU,[tR]:[{ref:"DisableAccessPoints"}]},{fn:t$,[tR]:[{ref:"DisableAccessPoints"},!0]}],[tB]:"Access points are not supported for this operation",[tE]:tB},ni={[tx]:[{fn:tU,[tR]:[{ref:"UseArnRegion"}]},{fn:t$,[tR]:[{ref:"UseArnRegion"},!1]},{fn:"not",[tR]:[{fn:tD,[tR]:[{fn:tF,[tR]:[r5,"region"]},"{Region}"]}]}],[tB]:"Invalid configuration: region from ARN `{bucketArn#region}` does not match client region `{Region}` and UseArnRegion is `false`",[tE]:tB},ns={fn:tF,[tR]:[{ref:"bucketPartition"},tj]},no={fn:tF,[tR]:[r5,"accountId"]},na={[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:re,[tC]:"{bucketArn#region}"}]},nu={[tB]:"Invalid ARN: The access point name may only contain a-z, A-Z, 0-9 and `-`. Found: `{accessPointName}`",[tE]:tB},nc={[tB]:"Invalid ARN: The account id may only contain a-z, A-Z, 0-9 and `-`. Found: `{bucketArn#accountId}`",[tE]:tB},nl={[tB]:"Invalid region in ARN: `{bucketArn#region}` (invalid DNS name)",[tE]:tB},nf={[tB]:"Client was configured for partition `{partitionResult#name}` but ARN (`{Bucket}`) has `{bucketPartition#name}`",[tE]:tB},nd={[tB]:"Invalid ARN: The ARN may only contain a single resource component after `accesspoint`.",[tE]:tB},nh={[tB]:"Invalid ARN: Expected a resource of the format `accesspoint:<accesspoint name>` but no name was provided",[tE]:tB},np={[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:"s3",[tC]:"{bucketArn#region}"}]},ng={[tI]:[{[tM]:!0,[tj]:t2,[tO]:t3,[t_]:["*"]},{[tM]:!0,[tj]:"sigv4",[tO]:t3,[tC]:"{bucketArn#region}"}]},nm={fn:t5,[tR]:[rw]},ny={url:"https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rZ,[tN]:{}},nb={url:"https://s3-fips.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rZ,[tN]:{}},nv={url:"https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rZ,[tN]:{}},nw={url:ra,[tT]:rZ,[tN]:{}},nS={url:"https://s3.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rZ,[tN]:{}},nE={ref:"UseObjectLambdaEndpoint"},nA={[tI]:[{[tM]:!0,[tj]:"sigv4",[tO]:re,[tC]:"{Region}"}]},nx={url:"https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},nR={url:"https://s3-fips.{Region}.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},nk={url:"https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},nT={url:tY,[tT]:rZ,[tN]:{}},nP={url:"https://s3.{Region}.{partitionResult#dnsSuffix}",[tT]:rZ,[tN]:{}},nI=[{ref:"Region"}],nM=[rg],nO=[rh],nC=[rm,rx],nN=[{fn:tU,[tR]:[{ref:"DisableS3ExpressSessionAuth"}]},{fn:t$,[tR]:[{ref:"DisableS3ExpressSessionAuth"},!0]}],n_=[rM],nU=[ry],n$=[rp],nB=[{fn:tH,[tR]:[rw,6,14,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,14,16,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nL=[{[tx]:[rp],[tV]:{url:"https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}",[tT]:rP,[tN]:{}},[tE]:tV},{[tV]:{url:"https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}",[tT]:rP,[tN]:{}},[tE]:tV}],nD=[{fn:tH,[tR]:[rw,6,15,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,15,17,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nF=[{fn:tH,[tR]:[rw,6,19,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,19,21,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nj=[{fn:tH,[tR]:[rw,6,20,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,20,22,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nH=[{fn:tH,[tR]:[rw,6,26,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,26,28,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nq=[{[tx]:[rp],[tV]:{url:"https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}",[tT]:{[tP]:"S3Express",[tI]:[{[tM]:!0,[tj]:"sigv4-s3express",[tO]:"s3express",[tC]:"{Region}"}]},[tN]:{}},[tE]:tV},{[tV]:{url:"https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.{partitionResult#dnsSuffix}",[tT]:{[tP]:"S3Express",[tI]:[{[tM]:!0,[tj]:"sigv4-s3express",[tO]:"s3express",[tC]:"{Region}"}]},[tN]:{}},[tE]:tV}],nz=[rw,0,7,!0],nV=[{fn:tH,[tR]:[rw,7,15,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,15,17,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nW=[{fn:tH,[tR]:[rw,7,16,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,16,18,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nZ=[{fn:tH,[tR]:[rw,7,20,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,20,22,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nG=[{fn:tH,[tR]:[rw,7,21,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,21,23,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nK=[{fn:tH,[tR]:[rw,7,27,!0],[tk]:"s3expressAvailabilityZoneId"},{fn:tH,[tR]:[rw,27,29,!0],[tk]:"s3expressAvailabilityZoneDelim"},{fn:tD,[tR]:[{ref:"s3expressAvailabilityZoneDelim"},"--"]}],nY=[{fn:t1,[tR]:[{ref:t0},!1]}],nJ=[{fn:tD,[tR]:[{ref:tX},"beta"]}],nX=[{fn:t1,[tR]:[{ref:"Region"},!1]}],nQ=[{fn:tD,[tR]:[{ref:"Region"},"us-east-1"]}],n0=[{fn:tD,[tR]:[r7,rt]}],n1=[{fn:tF,[tR]:[r5,"resourceId[1]"],[tk]:rr},{fn:"not",[tR]:[{fn:tD,[tR]:[ne,""]}]}],n2=[{fn:"not",[tR]:[{fn:tD,[tR]:[{fn:tF,[tR]:[r5,"region"]},""]}]}],n3=[{fn:"not",[tR]:[{fn:tU,[tR]:[{fn:tF,[tR]:[r5,"resourceId[2]"]}]}]}],n4=[{fn:tL,[tR]:[{fn:tF,[tR]:[r5,"region"]}],[tk]:"bucketPartition"}],n6=[{fn:tD,[tR]:[ns,{fn:tF,[tR]:[{ref:"partitionResult"},tj]}]}],n8=[{fn:t1,[tR]:[{fn:tF,[tR]:[r5,"region"]},!0]}],n5=[{fn:t1,[tR]:[no,!1]}],n7=[{fn:t1,[tR]:[ne,!1]}],n9=[{fn:t1,[tR]:[{ref:"Region"},!0]}],ie={version:"1.0",parameters:{Bucket:rl,Region:rl,UseFIPS:rf,UseDualStack:rf,Endpoint:rl,ForcePathStyle:rf,Accelerate:rf,UseGlobalEndpoint:rf,UseObjectLambdaEndpoint:rd,Key:rl,Prefix:rl,CopySource:rl,DisableAccessPoints:rd,DisableMultiRegionAccessPoints:rf,UseArnRegion:rd,UseS3ExpressControlEndpoint:rd,DisableS3ExpressSessionAuth:rd},[tA]:[{[tx]:[{fn:tU,[tR]:nI}],[tA]:[{[tx]:[rh,rp],error:"Accelerate cannot be used with FIPS",[tE]:tB},{[tx]:[rg,rm],error:"Cannot set dual-stack in combination with a custom endpoint.",[tE]:tB},{[tx]:[rm,rp],error:"A custom endpoint cannot be combined with FIPS",[tE]:tB},{[tx]:[rm,rh],error:"A custom endpoint cannot be combined with S3 Accelerate",[tE]:tB},{[tx]:[rp,ry,rb],error:"Partition does not support FIPS",[tE]:tB},{[tx]:[rv,{fn:tH,[tR]:[rw,0,6,!0],[tk]:tq},{fn:tD,[tR]:[{ref:tq},"--x-s3"]}],[tA]:[rS,rE,rA,{[tx]:[rC,rN],[tA]:[{[tx]:nU,[tA]:[{[tx]:[rT,r_],[tA]:[{[tx]:n$,endpoint:{url:"https://s3express-control-fips.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rP,[tN]:rI},[tE]:tV},{endpoint:{url:"https://s3express-control.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rP,[tN]:rI},[tE]:tV}],[tE]:tW}],[tE]:tW}],[tE]:tW},{[tx]:n_,[tA]:[{[tx]:nU,[tA]:[{[tx]:nN,[tA]:[{[tx]:nB,[tA]:nL,[tE]:tW},{[tx]:nD,[tA]:nL,[tE]:tW},{[tx]:nF,[tA]:nL,[tE]:tW},{[tx]:nj,[tA]:nL,[tE]:tW},{[tx]:nH,[tA]:nL,[tE]:tW},rU],[tE]:tW},{[tx]:nB,[tA]:nq,[tE]:tW},{[tx]:nD,[tA]:nq,[tE]:tW},{[tx]:nF,[tA]:nq,[tE]:tW},{[tx]:nj,[tA]:nq,[tE]:tW},{[tx]:nH,[tA]:nq,[tE]:tW},rU],[tE]:tW}],[tE]:tW},rO],[tE]:tW},{[tx]:[rv,{fn:tH,[tR]:nz,[tk]:tK},{fn:tD,[tR]:[{ref:tK},"--xa-s3"]}],[tA]:[rS,rE,rA,{[tx]:n_,[tA]:[{[tx]:nU,[tA]:[{[tx]:nN,[tA]:[{[tx]:nV,[tA]:nL,[tE]:tW},{[tx]:nW,[tA]:nL,[tE]:tW},{[tx]:nZ,[tA]:nL,[tE]:tW},{[tx]:nG,[tA]:nL,[tE]:tW},{[tx]:nK,[tA]:nL,[tE]:tW},rU],[tE]:tW},{[tx]:nV,[tA]:nq,[tE]:tW},{[tx]:nW,[tA]:nq,[tE]:tW},{[tx]:nZ,[tA]:nq,[tE]:tW},{[tx]:nG,[tA]:nq,[tE]:tW},{[tx]:nK,[tA]:nq,[tE]:tW},rU],[tE]:tW}],[tE]:tW},rO],[tE]:tW},{[tx]:[r$,rC,rN],[tA]:[{[tx]:nU,[tA]:[{[tx]:nC,endpoint:{url:tY,[tT]:rP,[tN]:rI},[tE]:tV},{[tx]:n$,endpoint:{url:"https://s3express-control-fips.{Region}.{partitionResult#dnsSuffix}",[tT]:rP,[tN]:rI},[tE]:tV},{endpoint:{url:"https://s3express-control.{Region}.{partitionResult#dnsSuffix}",[tT]:rP,[tN]:rI},[tE]:tV}],[tE]:tW}],[tE]:tW},{[tx]:[rv,{fn:tH,[tR]:[rw,49,50,!0],[tk]:tJ},{fn:tH,[tR]:[rw,8,12,!0],[tk]:tX},{fn:tH,[tR]:nz,[tk]:tQ},{fn:tH,[tR]:[rw,32,49,!0],[tk]:t0},{fn:tL,[tR]:nI,[tk]:"regionPartition"},{fn:tD,[tR]:[{ref:tQ},"--op-s3"]}],[tA]:[{[tx]:nY,[tA]:[{[tx]:[{fn:tD,[tR]:[rB,"e"]}],[tA]:[{[tx]:nJ,[tA]:[rL,{[tx]:nC,endpoint:{url:"https://{Bucket}.ec2.{url#authority}",[tT]:rD,[tN]:rI},[tE]:tV}],[tE]:tW},{endpoint:{url:"https://{Bucket}.ec2.s3-outposts.{Region}.{regionPartition#dnsSuffix}",[tT]:rD,[tN]:rI},[tE]:tV}],[tE]:tW},{[tx]:[{fn:tD,[tR]:[rB,"o"]}],[tA]:[{[tx]:nJ,[tA]:[rL,{[tx]:nC,endpoint:{url:"https://{Bucket}.op-{outpostId}.{url#authority}",[tT]:rD,[tN]:rI},[tE]:tV}],[tE]:tW},{endpoint:{url:"https://{Bucket}.op-{outpostId}.s3-outposts.{Region}.{regionPartition#dnsSuffix}",[tT]:rD,[tN]:rI},[tE]:tV}],[tE]:tW},{error:'Unrecognized hardware type: "Expected hardware type o or e but got {hardwareType}"',[tE]:tB}],[tE]:tW},{error:"Invalid ARN: The outpost Id must only contain a-z, A-Z, 0-9 and `-`.",[tE]:tB}],[tE]:tW},{[tx]:[rv],[tA]:[{[tx]:[rm,{fn:"not",[tR]:[{fn:tU,[tR]:[{fn:tz,[tR]:[{ref:"Endpoint"}]}]}]}],error:"Custom endpoint `{Endpoint}` was not a valid URI",[tE]:tB},{[tx]:[rF,rM],[tA]:[{[tx]:nU,[tA]:[{[tx]:nX,[tA]:[{[tx]:[rh,rb],error:"S3 Accelerate cannot be used in this region",[tE]:tB},{[tx]:[rg,rp,rj,r_,rH],endpoint:{url:"https://{Bucket}.s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rg,rp,rj,r_,rz,rV],[tA]:[{endpoint:rW,[tE]:tV}],[tE]:tW},{[tx]:[rg,rp,rj,r_,rz,rG],endpoint:rW,[tE]:tV},{[tx]:[rK,rp,rj,r_,rH],endpoint:{url:"https://{Bucket}.s3-fips.us-east-1.{partitionResult#dnsSuffix}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,rp,rj,r_,rz,rV],[tA]:[{endpoint:rY,[tE]:tV}],[tE]:tW},{[tx]:[rK,rp,rj,r_,rz,rG],endpoint:rY,[tE]:tV},{[tx]:[rg,rJ,rh,r_,rH],endpoint:{url:"https://{Bucket}.s3-accelerate.dualstack.us-east-1.{partitionResult#dnsSuffix}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rg,rJ,rh,r_,rz,rV],[tA]:[{endpoint:rX,[tE]:tV}],[tE]:tW},{[tx]:[rg,rJ,rh,r_,rz,rG],endpoint:rX,[tE]:tV},{[tx]:[rg,rJ,rj,r_,rH],endpoint:{url:"https://{Bucket}.s3.dualstack.us-east-1.{partitionResult#dnsSuffix}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rg,rJ,rj,r_,rz,rV],[tA]:[{endpoint:rQ,[tE]:tV}],[tE]:tW},{[tx]:[rg,rJ,rj,r_,rz,rG],endpoint:rQ,[tE]:tV},{[tx]:[rK,rJ,rj,rm,rx,rR,rH],endpoint:{url:t4,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,rJ,rj,rm,rx,r0,rH],endpoint:{url:tG,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,rJ,rj,rm,rx,rR,rz,rV],[tA]:[{[tx]:nQ,endpoint:r1,[tE]:tV},{endpoint:r1,[tE]:tV}],[tE]:tW},{[tx]:[rK,rJ,rj,rm,rx,r0,rz,rV],[tA]:[{[tx]:nQ,endpoint:r2,[tE]:tV},r3],[tE]:tW},{[tx]:[rK,rJ,rj,rm,rx,rR,rz,rG],endpoint:r1,[tE]:tV},{[tx]:[rK,rJ,rj,rm,rx,r0,rz,rG],endpoint:r2,[tE]:tV},{[tx]:[rK,rJ,rh,r_,rH],endpoint:{url:t6,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,rJ,rh,r_,rz,rV],[tA]:[{[tx]:nQ,endpoint:r4,[tE]:tV},{endpoint:r4,[tE]:tV}],[tE]:tW},{[tx]:[rK,rJ,rh,r_,rz,rG],endpoint:r4,[tE]:tV},{[tx]:[rK,rJ,rj,r_,rH],endpoint:{url:t8,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,rJ,rj,r_,rz,rV],[tA]:[{[tx]:nQ,endpoint:{url:t8,[tT]:rZ,[tN]:rI},[tE]:tV},{endpoint:r6,[tE]:tV}],[tE]:tW},{[tx]:[rK,rJ,rj,r_,rz,rG],endpoint:r6,[tE]:tV}],[tE]:tW},r8],[tE]:tW}],[tE]:tW},{[tx]:[rm,rx,{fn:tD,[tR]:[{fn:tF,[tR]:[rk,"scheme"]},"http"]},{fn:tZ,[tR]:[rw,!0]},rF,rJ,rK,rj],[tA]:[{[tx]:nU,[tA]:[{[tx]:nX,[tA]:[r3],[tE]:tW},r8],[tE]:tW}],[tE]:tW},{[tx]:[rF,{fn:t5,[tR]:[rw],[tk]:t7}],[tA]:[{[tx]:[{fn:tF,[tR]:[r5,"resourceId[0]"],[tk]:t9},{fn:"not",[tR]:[{fn:tD,[tR]:[r7,""]}]}],[tA]:[{[tx]:[{fn:tD,[tR]:[r9,re]}],[tA]:[{[tx]:n0,[tA]:[{[tx]:n1,[tA]:[nt,nr,{[tx]:n2,[tA]:[nn,{[tx]:n3,[tA]:[ni,{[tx]:n4,[tA]:[{[tx]:nU,[tA]:[{[tx]:n6,[tA]:[{[tx]:n8,[tA]:[{[tx]:[{fn:tD,[tR]:[no,""]}],error:"Invalid ARN: Missing account id",[tE]:tB},{[tx]:n5,[tA]:[{[tx]:n7,[tA]:[{[tx]:nC,endpoint:{url:rn,[tT]:na,[tN]:rI},[tE]:tV},{[tx]:n$,endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}",[tT]:na,[tN]:rI},[tE]:tV},{endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda.{bucketArn#region}.{bucketPartition#dnsSuffix}",[tT]:na,[tN]:rI},[tE]:tV}],[tE]:tW},nu],[tE]:tW},nc],[tE]:tW},nl],[tE]:tW},nf],[tE]:tW}],[tE]:tW}],[tE]:tW},nd],[tE]:tW},{error:"Invalid ARN: bucket ARN is missing a region",[tE]:tB}],[tE]:tW},nh],[tE]:tW},{error:"Invalid ARN: Object Lambda ARNs only support `accesspoint` arn types, but found: `{arnType}`",[tE]:tB}],[tE]:tW},{[tx]:n0,[tA]:[{[tx]:n1,[tA]:[{[tx]:n2,[tA]:[{[tx]:n0,[tA]:[{[tx]:n2,[tA]:[nn,{[tx]:n3,[tA]:[ni,{[tx]:n4,[tA]:[{[tx]:nU,[tA]:[{[tx]:[{fn:tD,[tR]:[ns,"{partitionResult#name}"]}],[tA]:[{[tx]:n8,[tA]:[{[tx]:[{fn:tD,[tR]:[r9,"s3"]}],[tA]:[{[tx]:n5,[tA]:[{[tx]:n7,[tA]:[{[tx]:nO,error:"Access Points do not support S3 Accelerate",[tE]:tB},{[tx]:[rp,rg],endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}",[tT]:np,[tN]:rI},[tE]:tV},{[tx]:[rp,rK],endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}",[tT]:np,[tN]:rI},[tE]:tV},{[tx]:[rJ,rg],endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}",[tT]:np,[tN]:rI},[tE]:tV},{[tx]:[rJ,rK,rm,rx],endpoint:{url:rn,[tT]:np,[tN]:rI},[tE]:tV},{[tx]:[rJ,rK],endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.{bucketArn#region}.{bucketPartition#dnsSuffix}",[tT]:np,[tN]:rI},[tE]:tV}],[tE]:tW},nu],[tE]:tW},nc],[tE]:tW},{error:"Invalid ARN: The ARN was not for the S3 service, found: {bucketArn#service}",[tE]:tB}],[tE]:tW},nl],[tE]:tW},nf],[tE]:tW}],[tE]:tW}],[tE]:tW},nd],[tE]:tW}],[tE]:tW}],[tE]:tW},{[tx]:[{fn:t1,[tR]:[ne,!0]}],[tA]:[{[tx]:nM,error:"S3 MRAP does not support dual-stack",[tE]:tB},{[tx]:n$,error:"S3 MRAP does not support FIPS",[tE]:tB},{[tx]:nO,error:"S3 MRAP does not support S3 Accelerate",[tE]:tB},{[tx]:[{fn:t$,[tR]:[{ref:"DisableMultiRegionAccessPoints"},!0]}],error:"Invalid configuration: Multi-Region Access Point ARNs are disabled.",[tE]:tB},{[tx]:[{fn:tL,[tR]:nI,[tk]:ri}],[tA]:[{[tx]:[{fn:tD,[tR]:[{fn:tF,[tR]:[{ref:ri},tj]},{fn:tF,[tR]:[r5,"partition"]}]}],[tA]:[{endpoint:{url:"https://{accessPointName}.accesspoint.s3-global.{mrapPartition#dnsSuffix}",[tT]:{[tI]:[{[tM]:!0,name:t2,[tO]:"s3",[t_]:["*"]}]},[tN]:rI},[tE]:tV}],[tE]:tW},{error:"Client was configured for partition `{mrapPartition#name}` but bucket referred to partition `{bucketArn#partition}`",[tE]:tB}],[tE]:tW}],[tE]:tW},{error:"Invalid Access Point Name",[tE]:tB}],[tE]:tW},nh],[tE]:tW},{[tx]:[{fn:tD,[tR]:[r9,t3]}],[tA]:[{[tx]:nM,error:"S3 Outposts does not support Dual-stack",[tE]:tB},{[tx]:n$,error:"S3 Outposts does not support FIPS",[tE]:tB},{[tx]:nO,error:"S3 Outposts does not support S3 Accelerate",[tE]:tB},{[tx]:[{fn:tU,[tR]:[{fn:tF,[tR]:[r5,"resourceId[4]"]}]}],error:"Invalid Arn: Outpost Access Point ARN contains sub resources",[tE]:tB},{[tx]:[{fn:tF,[tR]:[r5,"resourceId[1]"],[tk]:t0}],[tA]:[{[tx]:nY,[tA]:[ni,{[tx]:n4,[tA]:[{[tx]:nU,[tA]:[{[tx]:n6,[tA]:[{[tx]:n8,[tA]:[{[tx]:n5,[tA]:[{[tx]:[{fn:tF,[tR]:[r5,"resourceId[2]"],[tk]:rs}],[tA]:[{[tx]:[{fn:tF,[tR]:[r5,"resourceId[3]"],[tk]:rr}],[tA]:[{[tx]:[{fn:tD,[tR]:[{ref:rs},rt]}],[tA]:[{[tx]:nC,endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.{outpostId}.{url#authority}",[tT]:ng,[tN]:rI},[tE]:tV},{endpoint:{url:"https://{accessPointName}-{bucketArn#accountId}.{outpostId}.s3-outposts.{bucketArn#region}.{bucketPartition#dnsSuffix}",[tT]:ng,[tN]:rI},[tE]:tV}],[tE]:tW},{error:"Expected an outpost type `accesspoint`, found {outpostType}",[tE]:tB}],[tE]:tW},{error:"Invalid ARN: expected an access point name",[tE]:tB}],[tE]:tW},{error:"Invalid ARN: Expected a 4-component resource",[tE]:tB}],[tE]:tW},nc],[tE]:tW},nl],[tE]:tW},nf],[tE]:tW}],[tE]:tW}],[tE]:tW},{error:"Invalid ARN: The outpost Id may only contain a-z, A-Z, 0-9 and `-`. Found: `{outpostId}`",[tE]:tB}],[tE]:tW},{error:"Invalid ARN: The Outpost Id was not set",[tE]:tB}],[tE]:tW},{error:"Invalid ARN: Unrecognized format: {Bucket} (type: {arnType})",[tE]:tB}],[tE]:tW},{error:"Invalid ARN: No ARN type specified",[tE]:tB}],[tE]:tW},{[tx]:[{fn:tH,[tR]:[rw,0,4,!1],[tk]:ro},{fn:tD,[tR]:[{ref:ro},"arn:"]},{fn:"not",[tR]:[{fn:tU,[tR]:[nm]}]}],error:"Invalid ARN: `{Bucket}` was not a valid ARN",[tE]:tB},{[tx]:[{fn:t$,[tR]:[{ref:"ForcePathStyle"},!0]},nm],error:"Path-style addressing cannot be used with ARN buckets",[tE]:tB},{[tx]:[rT],[tA]:[{[tx]:nU,[tA]:[{[tx]:[rj],[tA]:[{[tx]:[rg,r_,rp,rH],endpoint:{url:"https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rg,r_,rp,rz,rV],[tA]:[{endpoint:ny,[tE]:tV}],[tE]:tW},{[tx]:[rg,r_,rp,rz,rG],endpoint:ny,[tE]:tV},{[tx]:[rK,r_,rp,rH],endpoint:{url:"https://s3-fips.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,r_,rp,rz,rV],[tA]:[{endpoint:nb,[tE]:tV}],[tE]:tW},{[tx]:[rK,r_,rp,rz,rG],endpoint:nb,[tE]:tV},{[tx]:[rg,r_,rJ,rH],endpoint:{url:"https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rg,r_,rJ,rz,rV],[tA]:[{endpoint:nv,[tE]:tV}],[tE]:tW},{[tx]:[rg,r_,rJ,rz,rG],endpoint:nv,[tE]:tV},{[tx]:[rK,rm,rx,rJ,rH],endpoint:{url:ra,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,rm,rx,rJ,rz,rV],[tA]:[{[tx]:nQ,endpoint:nw,[tE]:tV},{endpoint:nw,[tE]:tV}],[tE]:tW},{[tx]:[rK,rm,rx,rJ,rz,rG],endpoint:nw,[tE]:tV},{[tx]:[rK,r_,rJ,rH],endpoint:{url:ru,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rK,r_,rJ,rz,rV],[tA]:[{[tx]:nQ,endpoint:{url:ru,[tT]:rZ,[tN]:rI},[tE]:tV},{endpoint:nS,[tE]:tV}],[tE]:tW},{[tx]:[rK,r_,rJ,rz,rG],endpoint:nS,[tE]:tV}],[tE]:tW},{error:"Path-style addressing cannot be used with S3 Accelerate",[tE]:tB}],[tE]:tW}],[tE]:tW}],[tE]:tW},{[tx]:[{fn:tU,[tR]:[nE]},{fn:t$,[tR]:[nE,!0]}],[tA]:[{[tx]:nU,[tA]:[{[tx]:n9,[tA]:[nt,nr,{[tx]:nC,endpoint:{url:tY,[tT]:nA,[tN]:rI},[tE]:tV},{[tx]:n$,endpoint:{url:"https://s3-object-lambda-fips.{Region}.{partitionResult#dnsSuffix}",[tT]:nA,[tN]:rI},[tE]:tV},{endpoint:{url:"https://s3-object-lambda.{Region}.{partitionResult#dnsSuffix}",[tT]:nA,[tN]:rI},[tE]:tV}],[tE]:tW},r8],[tE]:tW}],[tE]:tW},{[tx]:[r$],[tA]:[{[tx]:nU,[tA]:[{[tx]:n9,[tA]:[{[tx]:[rp,rg,r_,rH],endpoint:{url:"https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rp,rg,r_,rz,rV],[tA]:[{endpoint:nx,[tE]:tV}],[tE]:tW},{[tx]:[rp,rg,r_,rz,rG],endpoint:nx,[tE]:tV},{[tx]:[rp,rK,r_,rH],endpoint:{url:"https://s3-fips.us-east-1.{partitionResult#dnsSuffix}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rp,rK,r_,rz,rV],[tA]:[{endpoint:nR,[tE]:tV}],[tE]:tW},{[tx]:[rp,rK,r_,rz,rG],endpoint:nR,[tE]:tV},{[tx]:[rJ,rg,r_,rH],endpoint:{url:"https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}",[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rJ,rg,r_,rz,rV],[tA]:[{endpoint:nk,[tE]:tV}],[tE]:tW},{[tx]:[rJ,rg,r_,rz,rG],endpoint:nk,[tE]:tV},{[tx]:[rJ,rK,rm,rx,rH],endpoint:{url:tY,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rJ,rK,rm,rx,rz,rV],[tA]:[{[tx]:nQ,endpoint:nT,[tE]:tV},{endpoint:nT,[tE]:tV}],[tE]:tW},{[tx]:[rJ,rK,rm,rx,rz,rG],endpoint:nT,[tE]:tV},{[tx]:[rJ,rK,r_,rH],endpoint:{url:rc,[tT]:rq,[tN]:rI},[tE]:tV},{[tx]:[rJ,rK,r_,rz,rV],[tA]:[{[tx]:nQ,endpoint:{url:rc,[tT]:rZ,[tN]:rI},[tE]:tV},{endpoint:nP,[tE]:tV}],[tE]:tW},{[tx]:[rJ,rK,r_,rz,rG],endpoint:nP,[tE]:tV}],[tE]:tW},r8],[tE]:tW}],[tE]:tW}],[tE]:tW},{error:"A region must be set when sending requests to S3.",[tE]:tB}]},it=new J({size:50,params:["Accelerate","Bucket","DisableAccessPoints","DisableMultiRegionAccessPoints","DisableS3ExpressSessionAuth","Endpoint","ForcePathStyle","Region","UseArnRegion","UseDualStack","UseFIPS","UseGlobalEndpoint","UseObjectLambdaEndpoint","UseS3ExpressControlEndpoint"]}),ir=(e,t={})=>it.get(e,()=>eR(ie,{endpointParams:e,logger:t.logger}));er.aws=eI;let ii=(i=async(e,t,r)=>({operation:(0,U.J)(t).operation,region:await (0,U.$)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),async(e,t,r)=>{if(!r)throw Error("Could not find `input` for `defaultEndpointRuleSetHttpAuthSchemeParametersProvider`");let n=await i(e,t,r),s=U.J(t)?.commandInstance?.constructor?.getEndpointParameterInstructions;if(!s)throw Error(`getEndpointParameterInstructions() is not defined on \`${t.commandName}\``);return Object.assign(n,await (0,eG.Xr)(r,{getEndpointParameterInstructions:s},e))});function is(e){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"s3",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}}function io(e){return{schemeId:"aws.auth#sigv4a",signingProperties:{name:"s3",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}}let ia=(c=e=>{let t=[];return e.operation,t.push(is(e)),t.push(io(e)),t},l={"aws.auth#sigv4":is,"aws.auth#sigv4a":io},e=>{let t=ir(e),r=t.properties?.authSchemes;if(!r)return c(e);let n=[];for(let t of r){let i;let{name:s,properties:o={},...a}=t,u=s.toLowerCase();if(s!==u&&console.warn(`HttpAuthScheme has been normalized with lowercasing: \`${s}\` to \`${u}\``),"sigv4a"===u){i="aws.auth#sigv4a";let e=r.find(e=>{let t=e.name.toLowerCase();return"sigv4a"!==t&&t.startsWith("sigv4")});if("none"===tw.J.sigv4aDependency()&&e)continue}else if(u.startsWith("sigv4"))i="aws.auth#sigv4";else throw Error(`Unknown HttpAuthScheme found in \`@smithy.rules#endpointRuleSet\`: \`${u}\``);let c=l[i];if(!c)throw Error(`Could not find HttpAuthOption create function for \`${i}\``);let f=c(e);f.schemeId=i,f.signingProperties={...f.signingProperties||{},...a,...o},n.push(f)}return n}),iu=e=>Object.assign(tv(ty(e)),{authSchemePreference:(0,U.$)(e.authSchemePreference??[])});var ic=r(86090);let il=e=>Object.assign(e,{useFipsEndpoint:e.useFipsEndpoint??!1,useDualstackEndpoint:e.useDualstackEndpoint??!1,forcePathStyle:e.forcePathStyle??!1,useAccelerateEndpoint:e.useAccelerateEndpoint??!1,useGlobalEndpoint:e.useGlobalEndpoint??!1,disableMultiregionAccessPoints:e.disableMultiregionAccessPoints??!1,defaultSigningName:"s3"}),id={ForcePathStyle:{type:"clientContextParams",name:"forcePathStyle"},UseArnRegion:{type:"clientContextParams",name:"useArnRegion"},DisableMultiRegionAccessPoints:{type:"clientContextParams",name:"disableMultiregionAccessPoints"},Accelerate:{type:"clientContextParams",name:"useAccelerateEndpoint"},DisableS3ExpressSessionAuth:{type:"clientContextParams",name:"disableS3ExpressSessionAuth"},UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};var ih=r(99538),ip=r(71853);class ig extends ta.mY.classBuilder().ep({...id,DisableS3ExpressSessionAuth:{type:"staticContextParams",value:!0},Bucket:{type:"contextParams",name:"Bucket"}}).m(function(e,t,r,n){return[(0,ic.p2)(r,this.serialize,this.deserialize),(0,eG.a3)(r,e.getEndpointParameterInstructions()),(0,G.mS)(r)]}).s("AmazonS3","CreateSession",{}).n("S3Client","CreateSessionCommand").f(ih.NW,ih.ZF).ser(ip.UX3).de(ip.eqj).build(){}var im={i8:"3.844.0"};let iy=e=>new TextEncoder().encode(e);function ib(e){return"string"==typeof e?0===e.length:0===e.byteLength}var iv={name:"SHA-1"},iw={name:"HMAC",hash:iv},iS=new Uint8Array([218,57,163,238,94,107,75,13,50,85,191,239,149,96,24,144,175,216,7,9]);let iE={};function iA(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:iE}var ix=function(){function e(e){this.toHash=new Uint8Array(0),void 0!==e&&(this.key=new Promise(function(t,r){iA().crypto.subtle.importKey("raw",iR(e),iw,!1,["sign"]).then(t,r)}),this.key.catch(function(){}))}return e.prototype.update=function(e){if(!ib(e)){var t=iR(e),r=new Uint8Array(this.toHash.byteLength+t.byteLength);r.set(this.toHash,0),r.set(t,this.toHash.byteLength),this.toHash=r}},e.prototype.digest=function(){var e=this;return this.key?this.key.then(function(t){return iA().crypto.subtle.sign(iw,t,e.toHash).then(function(e){return new Uint8Array(e)})}):ib(this.toHash)?Promise.resolve(iS):Promise.resolve().then(function(){return iA().crypto.subtle.digest(iv,e.toHash)}).then(function(e){return Promise.resolve(new Uint8Array(e))})},e.prototype.reset=function(){this.toHash=new Uint8Array(0)},e}();function iR(e){return"string"==typeof e?iy(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}var ik=["decrypt","digest","encrypt","exportKey","generateKey","importKey","sign","verify"];function iT(e){var t;return!!("object"==typeof e&&"object"==typeof e.crypto&&"function"==typeof e.crypto.getRandomValues)&&"object"==typeof e.crypto.subtle&&(t=e.crypto.subtle)&&ik.every(function(e){return"function"==typeof t[e]})}var iP=function(){function e(e){if(iT(iA()))this.hash=new ix(e);else throw Error("SHA1 not supported")}return e.prototype.update=function(e,t){this.hash.update(T(e))},e.prototype.digest=function(){return this.hash.digest()},e.prototype.reset=function(){this.hash.reset()},e}(),iI={name:"SHA-256"},iM={name:"HMAC",hash:iI},iO=new Uint8Array([227,176,196,66,152,252,28,20,154,251,244,200,153,111,185,36,39,174,65,228,100,155,147,76,164,149,153,27,120,82,184,85]),iC=function(){function e(e){this.toHash=new Uint8Array(0),this.secret=e,this.reset()}return e.prototype.update=function(e){if(!P(e)){var t=T(e),r=new Uint8Array(this.toHash.byteLength+t.byteLength);r.set(this.toHash,0),r.set(t,this.toHash.byteLength),this.toHash=r}},e.prototype.digest=function(){var e=this;return this.key?this.key.then(function(t){return iA().crypto.subtle.sign(iM,t,e.toHash).then(function(e){return new Uint8Array(e)})}):P(this.toHash)?Promise.resolve(iO):Promise.resolve().then(function(){return iA().crypto.subtle.digest(iI,e.toHash)}).then(function(e){return Promise.resolve(new Uint8Array(e))})},e.prototype.reset=function(){var e=this;this.toHash=new Uint8Array(0),this.secret&&void 0!==this.secret&&(this.key=new Promise(function(t,r){iA().crypto.subtle.importKey("raw",T(e.secret),iM,!1,["sign"]).then(t,r)}),this.key.catch(function(){}))},e}(),iN=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),i_=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],iU=function(){function e(){this.state=Int32Array.from(i_),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return e.prototype.update=function(e){if(this.finished)throw Error("Attempted to update an already finished hash.");var t=0,r=e.byteLength;if(this.bytesHashed+=r,8*this.bytesHashed>9007199254740991)throw Error("Cannot hash more than 2^53 - 1 bits");for(;r>0;)this.buffer[this.bufferLength++]=e[t++],r--,64===this.bufferLength&&(this.hashBuffer(),this.bufferLength=0)},e.prototype.digest=function(){if(!this.finished){var e=8*this.bytesHashed,t=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),r=this.bufferLength;if(t.setUint8(this.bufferLength++,128),r%64>=56){for(var n=this.bufferLength;n<64;n++)t.setUint8(n,0);this.hashBuffer(),this.bufferLength=0}for(var n=this.bufferLength;n<56;n++)t.setUint8(n,0);t.setUint32(56,Math.floor(e/4294967296),!0),t.setUint32(60,e),this.hashBuffer(),this.finished=!0}for(var i=new Uint8Array(32),n=0;n<8;n++)i[4*n]=this.state[n]>>>24&255,i[4*n+1]=this.state[n]>>>16&255,i[4*n+2]=this.state[n]>>>8&255,i[4*n+3]=this.state[n]>>>0&255;return i},e.prototype.hashBuffer=function(){for(var e=this.buffer,t=this.state,r=t[0],n=t[1],i=t[2],s=t[3],o=t[4],a=t[5],u=t[6],c=t[7],l=0;l<64;l++){if(l<16)this.temp[l]=(255&e[4*l])<<24|(255&e[4*l+1])<<16|(255&e[4*l+2])<<8|255&e[4*l+3];else{var f=this.temp[l-2],d=(f>>>17|f<<15)^(f>>>19|f<<13)^f>>>10,h=((f=this.temp[l-15])>>>7|f<<25)^(f>>>18|f<<14)^f>>>3;this.temp[l]=(d+this.temp[l-7]|0)+(h+this.temp[l-16]|0)}var p=(((o>>>6|o<<26)^(o>>>11|o<<21)^(o>>>25|o<<7))+(o&a^~o&u)|0)+(c+(iN[l]+this.temp[l]|0)|0)|0,g=((r>>>2|r<<30)^(r>>>13|r<<19)^(r>>>22|r<<10))+(r&n^r&i^n&i)|0;c=u,u=a,a=o,o=s+p|0,s=i,i=n,n=r,r=p+g|0}t[0]+=r,t[1]+=n,t[2]+=i,t[3]+=s,t[4]+=o,t[5]+=a,t[6]+=u,t[7]+=c},e}(),i$=function(){function e(e){this.secret=e,this.hash=new iU,this.reset()}return e.prototype.update=function(e){if(!P(e)&&!this.error)try{this.hash.update(T(e))}catch(e){this.error=e}},e.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},e.prototype.digest=function(){return E(this,void 0,void 0,function(){return A(this,function(e){return[2,this.digestSync()]})})},e.prototype.reset=function(){if(this.hash=new iU,this.secret){this.outer=new iU;var e=function(e){var t=T(e);if(t.byteLength>64){var r=new iU;r.update(t),t=r.digest()}var n=new Uint8Array(64);return n.set(t),n}(this.secret),t=new Uint8Array(64);t.set(e);for(var r=0;r<64;r++)e[r]^=54,t[r]^=92;this.hash.update(e),this.outer.update(t);for(var r=0;r<e.byteLength;r++)e[r]=0}},e}(),iB=function(){function e(e){iT(iA())?this.hash=new iC(e):this.hash=new i$(e)}return e.prototype.update=function(e,t){this.hash.update(T(e))},e.prototype.digest=function(){return this.hash.digest()},e.prototype.reset=function(){this.hash.reset()},e}(),iL=r(73493),iD=r.n(iL);let iF=({serviceId:e,clientVersion:t})=>async r=>{let n="undefined"!=typeof window&&window?.navigator?.userAgent?iD().parse(window.navigator.userAgent):void 0,i=[["aws-sdk-js",t],["ua","2.1"],[`os/${n?.os?.name||"other"}`,n?.os?.version],["lang/js"],["md/browser",`${n?.browser?.name??"unknown"}_${n?.browser?.version??"unknown"}`]];e&&i.push([`api/${e}`,t]);let s=await r?.userAgentAppId?.();return s&&i.push([`app/${s}`]),i};var ij=r(73663);class iH{constructor(e){if(this.bytes=e,8!==e.byteLength)throw Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(e){if(e>0x7fffffffffffffff||e<-0x8000000000000000)throw Error(`${e} is too large (or, if negative, too small) to represent as an Int64`);let t=new Uint8Array(8);for(let r=7,n=Math.abs(Math.round(e));r>-1&&n>0;r--,n/=256)t[r]=n;return e<0&&iq(t),new iH(t)}valueOf(){let e=this.bytes.slice(0),t=128&e[0];return t&&iq(e),parseInt((0,ij.N)(e),16)*(t?-1:1)}toString(){return String(this.valueOf())}}function iq(e){for(let t=0;t<8;t++)e[t]^=255;for(let t=7;t>-1&&(e[t]++,0===e[t]);t--);}class iz{constructor(e,t){this.toUtf8=e,this.fromUtf8=t}format(e){let t=[];for(let r of Object.keys(e)){let n=this.fromUtf8(r);t.push(Uint8Array.from([n.byteLength]),n,this.formatHeaderValue(e[r]))}let r=new Uint8Array(t.reduce((e,t)=>e+t.byteLength,0)),n=0;for(let e of t)r.set(e,n),n+=e.byteLength;return r}formatHeaderValue(e){switch(e.type){case"boolean":return Uint8Array.from([e.value?0:1]);case"byte":return Uint8Array.from([2,e.value]);case"short":let t=new DataView(new ArrayBuffer(3));return t.setUint8(0,3),t.setInt16(1,e.value,!1),new Uint8Array(t.buffer);case"integer":let r=new DataView(new ArrayBuffer(5));return r.setUint8(0,4),r.setInt32(1,e.value,!1),new Uint8Array(r.buffer);case"long":let n=new Uint8Array(9);return n[0]=5,n.set(e.value.bytes,1),n;case"binary":let i=new DataView(new ArrayBuffer(3+e.value.byteLength));i.setUint8(0,6),i.setUint16(1,e.value.byteLength,!1);let s=new Uint8Array(i.buffer);return s.set(e.value,3),s;case"string":let o=this.fromUtf8(e.value),a=new DataView(new ArrayBuffer(3+o.byteLength));a.setUint8(0,7),a.setUint16(1,o.byteLength,!1);let u=new Uint8Array(a.buffer);return u.set(o,3),u;case"timestamp":let c=new Uint8Array(9);return c[0]=8,c.set(iH.fromNumber(e.value.valueOf()).bytes,1),c;case"uuid":if(!i0.test(e.value))throw Error(`Invalid UUID received: ${e.value}`);let l=new Uint8Array(17);return l[0]=9,l.set((0,ij.H)(e.value.replace(/\-/g,"")),1),l}}parse(e){let t={},r=0;for(;r<e.byteLength;){let n=e.getUint8(r++),i=this.toUtf8(new Uint8Array(e.buffer,e.byteOffset+r,n));switch(r+=n,e.getUint8(r++)){case 0:t[i]={type:iV,value:!0};break;case 1:t[i]={type:iV,value:!1};break;case 2:t[i]={type:iW,value:e.getInt8(r++)};break;case 3:t[i]={type:iZ,value:e.getInt16(r,!1)},r+=2;break;case 4:t[i]={type:iG,value:e.getInt32(r,!1)},r+=4;break;case 5:t[i]={type:iK,value:new iH(new Uint8Array(e.buffer,e.byteOffset+r,8))},r+=8;break;case 6:let s=e.getUint16(r,!1);r+=2,t[i]={type:iY,value:new Uint8Array(e.buffer,e.byteOffset+r,s)},r+=s;break;case 7:let o=e.getUint16(r,!1);r+=2,t[i]={type:iJ,value:this.toUtf8(new Uint8Array(e.buffer,e.byteOffset+r,o))},r+=o;break;case 8:t[i]={type:iX,value:new Date(new iH(new Uint8Array(e.buffer,e.byteOffset+r,8)).valueOf())},r+=8;break;case 9:let a=new Uint8Array(e.buffer,e.byteOffset+r,16);r+=16,t[i]={type:iQ,value:`${(0,ij.N)(a.subarray(0,4))}-${(0,ij.N)(a.subarray(4,6))}-${(0,ij.N)(a.subarray(6,8))}-${(0,ij.N)(a.subarray(8,10))}-${(0,ij.N)(a.subarray(10))}`};break;default:throw Error("Unrecognized header type tag")}}return t}}(f=m||(m={}))[f.boolTrue=0]="boolTrue",f[f.boolFalse=1]="boolFalse",f[f.byte=2]="byte",f[f.short=3]="short",f[f.integer=4]="integer",f[f.long=5]="long",f[f.byteArray=6]="byteArray",f[f.string=7]="string",f[f.timestamp=8]="timestamp",f[f.uuid=9]="uuid";let iV="boolean",iW="byte",iZ="short",iG="integer",iK="long",iY="binary",iJ="string",iX="timestamp",iQ="uuid",i0=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;class i1{constructor(e,t){this.headerMarshaller=new iz(e,t),this.messageBuffer=[],this.isEndOfStream=!1}feed(e){this.messageBuffer.push(this.decode(e))}endOfStream(){this.isEndOfStream=!0}getMessage(){let e=this.messageBuffer.pop(),t=this.isEndOfStream;return{getMessage:()=>e,isEndOfStream:()=>t}}getAvailableMessages(){let e=this.messageBuffer;this.messageBuffer=[];let t=this.isEndOfStream;return{getMessages:()=>e,isEndOfStream:()=>t}}encode({headers:e,body:t}){let r=this.headerMarshaller.format(e),n=r.byteLength+t.byteLength+16,i=new Uint8Array(n),s=new DataView(i.buffer,i.byteOffset,i.byteLength),o=new N;return s.setUint32(0,n,!1),s.setUint32(4,r.byteLength,!1),s.setUint32(8,o.update(i.subarray(0,8)).digest(),!1),i.set(r,12),i.set(t,r.byteLength+12),s.setUint32(n-4,o.update(i.subarray(8,n-4)).digest(),!1),i}decode(e){let{headers:t,body:r}=function({byteLength:e,byteOffset:t,buffer:r}){if(e<16)throw Error("Provided message too short to accommodate event stream message overhead");let n=new DataView(r,t,e),i=n.getUint32(0,!1);if(e!==i)throw Error("Reported message length does not match received message length");let s=n.getUint32(4,!1),o=n.getUint32(8,!1),a=n.getUint32(e-4,!1),u=new N().update(new Uint8Array(r,t,8));if(o!==u.digest())throw Error(`The prelude checksum specified in the message (${o}) does not match the calculated CRC32 checksum (${u.digest()})`);if(u.update(new Uint8Array(r,t+8,e-12)),a!==u.digest())throw Error(`The message checksum (${u.digest()}) did not match the expected value of ${a}`);return{headers:new DataView(r,t+8+4,s),body:new Uint8Array(r,t+8+4+s,i-s-16)}}(e);return{headers:this.headerMarshaller.parse(t),body:r}}formatHeaders(e){return this.headerMarshaller.format(e)}}class i2{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.inputStream){let t=this.options.decoder.decode(e);yield t}}}class i3{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.messageStream){let t=this.options.encoder.encode(e);yield t}this.options.includeEndFrame&&(yield new Uint8Array(0))}}class i4{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.messageStream){let t=await this.options.deserializer(e);void 0!==t&&(yield t)}}}class i6{constructor(e){this.options=e}[Symbol.asyncIterator](){return this.asyncIterator()}async *asyncIterator(){for await(let e of this.options.inputStream){let t=this.options.serializer(e);yield t}}}class i8{constructor({utf8Encoder:e,utf8Decoder:t}){this.eventStreamCodec=new i1(e,t),this.utfEncoder=e}deserialize(e,t){var r;return new i4({messageStream:new i2({inputStream:function(e){let t=0,r=0,n=null,i=null,s=e=>{if("number"!=typeof e)throw Error("Attempted to allocate an event message where size was not a number: "+e);t=e,r=4,new DataView((n=new Uint8Array(e)).buffer).setUint32(0,e,!1)};return{[Symbol.asyncIterator]:async function*(){let o=e[Symbol.asyncIterator]();for(;;){let{value:e,done:a}=await o.next();if(a){if(t){if(t===r)yield n;else throw Error("Truncated event message received.")}return}let u=e.length,c=0;for(;c<u;){if(!n){let t=u-c;i||(i=new Uint8Array(4));let n=Math.min(4-r,t);if(i.set(e.slice(c,c+n),r),r+=n,c+=n,r<4)break;s(new DataView(i.buffer).getUint32(0,!1)),i=null}let o=Math.min(t-r,u-c);n.set(e.slice(c,c+o),r),r+=o,c+=o,t&&t===r&&(yield n,n=null,t=0,r=0)}}}}}(e),decoder:this.eventStreamCodec}),deserializer:(r=this.utfEncoder,async function(e){let{value:n}=e.headers[":message-type"];if("error"===n){let t=Error(e.headers[":error-message"].value||"UnknownError");throw t.name=e.headers[":error-code"].value,t}if("exception"===n){let n=e.headers[":exception-type"].value,i=await t({[n]:e});if(i.$unknown){let t=Error(r(e.body));throw t.name=n,t}throw i[n]}if("event"===n){let r={[e.headers[":event-type"].value]:e},n=await t(r);if(n.$unknown)return;return n}throw Error(`Unrecognizable event type: ${e.headers[":event-type"].value}`)})})}serialize(e,t){return new i3({messageStream:new i6({inputStream:e,serializer:t}),encoder:this.eventStreamCodec,includeEndFrame:!0})}}let i5=e=>({[Symbol.asyncIterator]:async function*(){let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)return;yield r}}finally{t.releaseLock()}}}),i7=e=>{let t=e[Symbol.asyncIterator]();return new ReadableStream({async pull(e){let{done:r,value:n}=await t.next();if(r)return e.close();e.enqueue(n)}})};class i9{constructor({utf8Encoder:e,utf8Decoder:t}){this.universalMarshaller=new i8({utf8Decoder:t,utf8Encoder:e})}deserialize(e,t){let r=se(e)?i5(e):e;return this.universalMarshaller.deserialize(r,t)}serialize(e,t){let r=this.universalMarshaller.serialize(e,t);return"function"==typeof ReadableStream?i7(r):r}}let se=e=>"function"==typeof ReadableStream&&e instanceof ReadableStream,st=e=>new i9(e);var sr=r(74027);async function sn(e,t,r=1048576){let n=e.size,i=0;for(;i<n;){let s=e.slice(i,Math.min(n,i+r));t(new Uint8Array(await s.arrayBuffer())),i+=s.size}}let si=async function(e,t){let r=new e;return await sn(t,e=>{r.update(e)}),r.digest()},ss=e=>()=>Promise.reject(e);var so=r(99492);let sa=[1732584193,4023233417,2562383102,271733878];class su{constructor(){this.reset()}update(e){if("string"==typeof e?0===e.length:0===e.byteLength)return;if(this.finished)throw Error("Attempted to update an already finished hash.");let t="string"==typeof e?(0,so.$)(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e),r=0,{byteLength:n}=t;for(this.bytesHashed+=n;n>0;)this.buffer.setUint8(this.bufferLength++,t[r++]),n--,64===this.bufferLength&&(this.hashBuffer(),this.bufferLength=0)}async digest(){if(!this.finished){let{buffer:e,bufferLength:t,bytesHashed:r}=this,n=8*r;if(e.setUint8(this.bufferLength++,128),t%64>=56){for(let t=this.bufferLength;t<64;t++)e.setUint8(t,0);this.hashBuffer(),this.bufferLength=0}for(let t=this.bufferLength;t<56;t++)e.setUint8(t,0);e.setUint32(56,n>>>0,!0),e.setUint32(60,Math.floor(n/4294967296),!0),this.hashBuffer(),this.finished=!0}let e=new DataView(new ArrayBuffer(16));for(let t=0;t<4;t++)e.setUint32(4*t,this.state[t],!0);return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}hashBuffer(){let{buffer:e,state:t}=this,r=t[0],n=t[1],i=t[2],s=t[3];r=sl(r,n,i,s,e.getUint32(0,!0),7,3614090360),s=sl(s,r,n,i,e.getUint32(4,!0),12,3905402710),i=sl(i,s,r,n,e.getUint32(8,!0),17,606105819),n=sl(n,i,s,r,e.getUint32(12,!0),22,3250441966),r=sl(r,n,i,s,e.getUint32(16,!0),7,4118548399),s=sl(s,r,n,i,e.getUint32(20,!0),12,1200080426),i=sl(i,s,r,n,e.getUint32(24,!0),17,2821735955),n=sl(n,i,s,r,e.getUint32(28,!0),22,4249261313),r=sl(r,n,i,s,e.getUint32(32,!0),7,1770035416),s=sl(s,r,n,i,e.getUint32(36,!0),12,2336552879),i=sl(i,s,r,n,e.getUint32(40,!0),17,4294925233),n=sl(n,i,s,r,e.getUint32(44,!0),22,2304563134),r=sl(r,n,i,s,e.getUint32(48,!0),7,1804603682),s=sl(s,r,n,i,e.getUint32(52,!0),12,4254626195),i=sl(i,s,r,n,e.getUint32(56,!0),17,2792965006),n=sl(n,i,s,r,e.getUint32(60,!0),22,1236535329),r=sf(r,n,i,s,e.getUint32(4,!0),5,4129170786),s=sf(s,r,n,i,e.getUint32(24,!0),9,3225465664),i=sf(i,s,r,n,e.getUint32(44,!0),14,643717713),n=sf(n,i,s,r,e.getUint32(0,!0),20,3921069994),r=sf(r,n,i,s,e.getUint32(20,!0),5,3593408605),s=sf(s,r,n,i,e.getUint32(40,!0),9,38016083),i=sf(i,s,r,n,e.getUint32(60,!0),14,3634488961),n=sf(n,i,s,r,e.getUint32(16,!0),20,3889429448),r=sf(r,n,i,s,e.getUint32(36,!0),5,568446438),s=sf(s,r,n,i,e.getUint32(56,!0),9,3275163606),i=sf(i,s,r,n,e.getUint32(12,!0),14,4107603335),n=sf(n,i,s,r,e.getUint32(32,!0),20,1163531501),r=sf(r,n,i,s,e.getUint32(52,!0),5,2850285829),s=sf(s,r,n,i,e.getUint32(8,!0),9,4243563512),i=sf(i,s,r,n,e.getUint32(28,!0),14,1735328473),n=sf(n,i,s,r,e.getUint32(48,!0),20,2368359562),r=sd(r,n,i,s,e.getUint32(20,!0),4,4294588738),s=sd(s,r,n,i,e.getUint32(32,!0),11,2272392833),i=sd(i,s,r,n,e.getUint32(44,!0),16,1839030562),n=sd(n,i,s,r,e.getUint32(56,!0),23,4259657740),r=sd(r,n,i,s,e.getUint32(4,!0),4,2763975236),s=sd(s,r,n,i,e.getUint32(16,!0),11,1272893353),i=sd(i,s,r,n,e.getUint32(28,!0),16,4139469664),n=sd(n,i,s,r,e.getUint32(40,!0),23,3200236656),r=sd(r,n,i,s,e.getUint32(52,!0),4,681279174),s=sd(s,r,n,i,e.getUint32(0,!0),11,3936430074),i=sd(i,s,r,n,e.getUint32(12,!0),16,3572445317),n=sd(n,i,s,r,e.getUint32(24,!0),23,76029189),r=sd(r,n,i,s,e.getUint32(36,!0),4,3654602809),s=sd(s,r,n,i,e.getUint32(48,!0),11,3873151461),i=sd(i,s,r,n,e.getUint32(60,!0),16,530742520),n=sd(n,i,s,r,e.getUint32(8,!0),23,3299628645),r=sh(r,n,i,s,e.getUint32(0,!0),6,4096336452),s=sh(s,r,n,i,e.getUint32(28,!0),10,1126891415),i=sh(i,s,r,n,e.getUint32(56,!0),15,2878612391),n=sh(n,i,s,r,e.getUint32(20,!0),21,4237533241),r=sh(r,n,i,s,e.getUint32(48,!0),6,1700485571),s=sh(s,r,n,i,e.getUint32(12,!0),10,2399980690),i=sh(i,s,r,n,e.getUint32(40,!0),15,4293915773),n=sh(n,i,s,r,e.getUint32(4,!0),21,2240044497),r=sh(r,n,i,s,e.getUint32(32,!0),6,1873313359),s=sh(s,r,n,i,e.getUint32(60,!0),10,4264355552),i=sh(i,s,r,n,e.getUint32(24,!0),15,2734768916),n=sh(n,i,s,r,e.getUint32(52,!0),21,1309151649),r=sh(r,n,i,s,e.getUint32(16,!0),6,4149444226),s=sh(s,r,n,i,e.getUint32(44,!0),10,3174756917),i=sh(i,s,r,n,e.getUint32(8,!0),15,718787259),n=sh(n,i,s,r,e.getUint32(36,!0),21,3951481745),t[0]=r+t[0]&4294967295,t[1]=n+t[1]&4294967295,t[2]=i+t[2]&4294967295,t[3]=s+t[3]&4294967295}reset(){this.state=Uint32Array.from(sa),this.buffer=new DataView(new ArrayBuffer(64)),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}}function sc(e,t,r,n,i,s){return((t=(t+e&4294967295)+(n+s&4294967295)&4294967295)<<i|t>>>32-i)+r&4294967295}function sl(e,t,r,n,i,s,o){return sc(t&r|~t&n,e,t,i,s,o)}function sf(e,t,r,n,i,s,o){return sc(t&n|r&~n,e,t,i,s,o)}function sd(e,t,r,n,i,s,o){return sc(t^r^n,e,t,i,s,o)}function sh(e,t,r,n,i,s,o){return sc(r^(t|~n),e,t,i,s,o)}let sp="function"==typeof TextEncoder?new TextEncoder:null,sg=e=>{if("string"==typeof e){if(sp)return sp.encode(e).byteLength;let t=e.length;for(let r=t-1;r>=0;r--){let n=e.charCodeAt(r);n>127&&n<=2047?t++:n>2047&&n<=65535&&(t+=2),n>=56320&&n<=57343&&r--}return t}if("number"==typeof e.byteLength)return e.byteLength;if("number"==typeof e.size)return e.size;throw Error(`Body Length computation failed for ${e}`)},sm=e=>new Date(Date.now()+e),sy=e=>y.Zn.isInstance(e)?e.headers?.date??e.headers?.Date:void 0,sb=(e,t)=>Math.abs(sm(t).getTime()-e)>=3e5,sv=(e,t)=>{let r=Date.parse(e);return sb(r,t)?r-Date.now():t},sw=(e,t)=>{if(!t)throw Error(`Property \`${e}\` is not resolved for AWS SDK SigV4Auth`);return t},sS=async e=>{let t=sw("context",e.context),r=sw("config",e.config),n=t.endpointV2?.properties?.authSchemes?.[0],i=sw("signer",r.signer),s=await i(n);return{config:r,signer:s,signingRegion:e?.signingRegion,signingRegionSet:e?.signingRegionSet,signingName:e?.signingName}};class sE{async sign(e,t,r){if(!y.aW.isInstance(e))throw Error("The request is not an instance of `HttpRequest` and cannot be signed");let n=await sS(r),{config:i,signer:s}=n,{signingRegion:o,signingName:a}=n,u=r.context;if(u?.authSchemes?.length){let[e,t]=u.authSchemes;e?.name==="sigv4a"&&t?.name==="sigv4"&&(o=t?.signingRegion??o,a=t?.signingName??a)}return await s.sign(e,{signingDate:sm(i.systemClockOffset),signingRegion:o,signingService:a})}errorHandler(e){return t=>{let r=t.ServerTime??sy(t.$response);if(r){let n=sw("config",e.config),i=n.systemClockOffset;n.systemClockOffset=sv(r,n.systemClockOffset),n.systemClockOffset!==i&&t.$metadata&&(t.$metadata.clockSkewCorrected=!0)}throw t}}successHandler(e,t){let r=sy(e);if(r){let e=sw("config",t.config);e.systemClockOffset=sv(r,e.systemClockOffset)}}}class sA extends sE{async sign(e,t,r){if(!y.aW.isInstance(e))throw Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:n,signer:i,signingRegion:s,signingRegionSet:o,signingName:a}=await sS(r),u=(await n.sigv4aSigningRegionSet?.()??o??[s]).join(",");return await i.sign(e,{signingDate:sm(n.systemClockOffset),signingRegion:u,signingService:a})}}var sx=r(87121),sR=r(306),sk=r(21555);let sT=e=>({apiVersion:"2006-03-01",base64Decoder:e?.base64Decoder??sR.G,base64Encoder:e?.base64Encoder??sR.s,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??ir,extensions:e?.extensions??[],getAwsChunkedEncodingStream:e?.getAwsChunkedEncodingStream??S.LN,httpAuthSchemeProvider:e?.httpAuthSchemeProvider??ia,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new sE},{schemeId:"aws.auth#sigv4a",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4a"),signer:new sA}],logger:e?.logger??new ta.vk,sdkStreamMixin:e?.sdkStreamMixin??S.Ou,serviceId:e?.serviceId??"S3",signerConstructor:e?.signerConstructor??tw.J,signingEscapePath:e?.signingEscapePath??!1,urlParser:e?.urlParser??sx.e,useArnRegion:e?.useArnRegion??void 0,utf8Decoder:e?.utf8Decoder??so.$,utf8Encoder:e?.utf8Encoder??sk.G}),sP=["in-region","cross-region","mobile","standard","legacy"],sI=({defaultsMode:e}={})=>tb(async()=>{let t="function"==typeof e?await e():e;switch(t?.toLowerCase()){case"auto":return Promise.resolve(sM()?"mobile":"standard");case"mobile":case"in-region":case"cross-region":case"standard":case"legacy":return Promise.resolve(t?.toLocaleLowerCase());case void 0:return Promise.resolve("legacy");default:throw Error(`Invalid parameter for "defaultsMode", expect ${sP.join(", ")}, got ${t}`)}}),sM=()=>{let e="undefined"!=typeof window&&window?.navigator?.userAgent?iD().parse(window.navigator.userAgent):void 0,t=e?.platform?.type;return"tablet"===t||"mobile"===t},sO=e=>{let t=sI(e),r=()=>t().then(ta.jv),n=sT(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??sg,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??iF({serviceId:n.serviceId,clientVersion:im.i8}),eventStreamSerdeProvider:e?.eventStreamSerdeProvider??st,maxAttempts:e?.maxAttempts??3,md5:e?.md5??su,region:e?.region??ss("Region is missing"),requestHandler:sr.BX.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||eK),sha1:e?.sha1??iP,sha256:e?.sha256??iB,streamCollector:e?.streamCollector??sr.CF,streamHasher:e?.streamHasher??si,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(!1)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(!1))}},sC=e=>({setRegion(t){e.region=t},region:()=>e.region}),sN=e=>({region:e.region()}),s_=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},sU=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),s$=(e,t)=>{let r=Object.assign(sC(e),(0,ta.kE)(e),(0,y.cA)(e),s_(e));return t.forEach(e=>e.configure(r)),Object.assign(e,sN(r),(0,ta.SQ)(r),(0,y.AO)(r),sU(r))};class sB extends ta.KU{config;constructor(...[e]){let t=sO(e||{});super(t),this.initConfig=t;let r=eq(to($(function(e){let t=(0,K.$E)(e.userAgentAppId??Y),{customUserAgent:r}=e;return Object.assign(e,{customUserAgent:"string"==typeof r?[[r]]:r,userAgentAppId:async()=>{let r=await t();if(!(void 0===r||"string"==typeof r&&r.length<=50)){let t=e.logger?.constructor?.name!=="NoOpLogger"&&e.logger?e.logger:console;"string"!=typeof r?t?.warn("userAgentAppId must be a string or undefined."):r.length>50&&t?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return r}})}(il(t))))),n=iu(ez((0,eG.uW)(r))),i=s$((0,G.x4)(n,{session:[()=>this,ig]}),e?.extensions||[]);this.config=i,this.middlewareStack.use(eF(this.config)),this.middlewareStack.use(tp(this.config)),this.middlewareStack.use(eZ(this.config)),this.middlewareStack.use(D(this.config)),this.middlewareStack.use(H(this.config)),this.middlewareStack.use(Z(this.config)),this.middlewareStack.use((0,K.tZ)(this.config,{httpAuthSchemeParametersProvider:ii,identityProviderConfigProvider:async e=>new K.K5({"aws.auth#sigv4":e.credentials,"aws.auth#sigv4a":e.credentials})})),this.middlewareStack.use((0,K.aZ)(this.config)),this.middlewareStack.use((0,G.D4)(this.config)),this.middlewareStack.use(v(this.config)),this.middlewareStack.use((0,G.Ae)(this.config)),this.middlewareStack.use((0,G.nd)(this.config)),this.middlewareStack.use((0,G.JB)(this.config))}destroy(){super.destroy()}}},72838:function(e,t,r){"use strict";r.d(t,{k:function(){return i}});var n=r(4598);class i extends n.sI{constructor(e){super(e),Object.setPrototypeOf(this,i.prototype)}}},99538:function(e,t,r){"use strict";r.d(t,{M0:function(){return l},NW:function(){return y},SH:function(){return c},TX:function(){return p},Vn:function(){return u},Yw:function(){return h},ZF:function(){return m},mn:function(){return f},oN:function(){return d},wi:function(){return a}});var n,i,s=r(4598),o=r(72838);class a extends o.k{name="NoSuchUpload";$fault="client";constructor(e){super({name:"NoSuchUpload",$fault:"client",...e}),Object.setPrototypeOf(this,a.prototype)}}class u extends o.k{name="ObjectNotInActiveTierError";$fault="client";constructor(e){super({name:"ObjectNotInActiveTierError",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class c extends o.k{name="BucketAlreadyExists";$fault="client";constructor(e){super({name:"BucketAlreadyExists",$fault:"client",...e}),Object.setPrototypeOf(this,c.prototype)}}class l extends o.k{name="BucketAlreadyOwnedByYou";$fault="client";constructor(e){super({name:"BucketAlreadyOwnedByYou",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class f extends o.k{name="NoSuchBucket";$fault="client";constructor(e){super({name:"NoSuchBucket",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}(n||(n={})).visit=(e,t)=>void 0!==e.Prefix?t.Prefix(e.Prefix):void 0!==e.Tag?t.Tag(e.Tag):void 0!==e.And?t.And(e.And):t._(e.$unknown[0],e.$unknown[1]),(i||(i={})).visit=(e,t)=>void 0!==e.Prefix?t.Prefix(e.Prefix):void 0!==e.Tag?t.Tag(e.Tag):void 0!==e.AccessPointArn?t.AccessPointArn(e.AccessPointArn):void 0!==e.And?t.And(e.And):t._(e.$unknown[0],e.$unknown[1]);class d extends o.k{name="InvalidObjectState";$fault="client";StorageClass;AccessTier;constructor(e){super({name:"InvalidObjectState",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype),this.StorageClass=e.StorageClass,this.AccessTier=e.AccessTier}}class h extends o.k{name="NoSuchKey";$fault="client";constructor(e){super({name:"NoSuchKey",$fault:"client",...e}),Object.setPrototypeOf(this,h.prototype)}}class p extends o.k{name="NotFound";$fault="client";constructor(e){super({name:"NotFound",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}let g=e=>({...e,...e.SecretAccessKey&&{SecretAccessKey:s.oc},...e.SessionToken&&{SessionToken:s.oc}}),m=e=>({...e,...e.SSEKMSKeyId&&{SSEKMSKeyId:s.oc},...e.SSEKMSEncryptionContext&&{SSEKMSEncryptionContext:s.oc},...e.Credentials&&{Credentials:g(e.Credentials)}}),y=e=>({...e,...e.SSEKMSKeyId&&{SSEKMSKeyId:s.oc},...e.SSEKMSEncryptionContext&&{SSEKMSEncryptionContext:s.oc}})},32349:function(e,t,r){"use strict";r.d(t,{K$:function(){return c},Nx:function(){return a},T8:function(){return l},Xc:function(){return s},g0:function(){return u},sd:function(){return o}}),r(4598);var n,i=r(72838);class s extends i.k{name="EncryptionTypeMismatch";$fault="client";constructor(e){super({name:"EncryptionTypeMismatch",$fault:"client",...e}),Object.setPrototypeOf(this,s.prototype)}}class o extends i.k{name="InvalidRequest";$fault="client";constructor(e){super({name:"InvalidRequest",$fault:"client",...e}),Object.setPrototypeOf(this,o.prototype)}}class a extends i.k{name="InvalidWriteOffset";$fault="client";constructor(e){super({name:"InvalidWriteOffset",$fault:"client",...e}),Object.setPrototypeOf(this,a.prototype)}}class u extends i.k{name="TooManyParts";$fault="client";constructor(e){super({name:"TooManyParts",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class c extends i.k{name="IdempotencyParameterMismatch";$fault="client";constructor(e){super({name:"IdempotencyParameterMismatch",$fault:"client",...e}),Object.setPrototypeOf(this,c.prototype)}}class l extends i.k{name="ObjectAlreadyInActiveTierError";$fault="client";constructor(e){super({name:"ObjectAlreadyInActiveTierError",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}(n||(n={})).visit=(e,t)=>void 0!==e.Records?t.Records(e.Records):void 0!==e.Stats?t.Stats(e.Stats):void 0!==e.Progress?t.Progress(e.Progress):void 0!==e.Cont?t.Cont(e.Cont):void 0!==e.End?t.End(e.End):t._(e.$unknown[0],e.$unknown[1])},77844:function(e,t,r){"use strict";function n(e,t,r){e.__aws_sdk_context?e.__aws_sdk_context.features||(e.__aws_sdk_context.features={}):e.__aws_sdk_context={features:{}},e.__aws_sdk_context.features[t]=r}r.d(t,{h:function(){return n}})},11602:function(e,t,r){"use strict";let n;r.d(t,{Js:function(){return G},he:function(){return W},OH:function(){return Z}});var i=r(4598);let s={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,r){return e},captureMetaData:!1},o=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",a=RegExp("^"+("["+o+"][")+o+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$");function u(e,t){let r=[],n=t.exec(e);for(;n;){let i=[];i.startIndex=t.lastIndex-n[0].length;let s=n.length;for(let e=0;e<s;e++)i.push(n[e]);r.push(i),n=t.exec(e)}return r}let c=function(e){return null!=a.exec(e)};n="function"!=typeof Symbol?"@@xmlMetadata":Symbol("XML Node Metadata");class l{constructor(e){this.tagname=e,this.child=[],this[":@"]={}}add(e,t){"__proto__"===e&&(e="#__proto__"),this.child.push({[e]:t})}addChild(e,t){"__proto__"===e.tagname&&(e.tagname="#__proto__"),e[":@"]&&Object.keys(e[":@"]).length>0?this.child.push({[e.tagname]:e.child,":@":e[":@"]}):this.child.push({[e.tagname]:e.child}),void 0!==t&&(this.child[this.child.length-1][n]={startIndex:t})}static getMetaDataSymbol(){return n}}let f=(e,t)=>{for(;t<e.length&&/\s/.test(e[t]);)t++;return t};function d(e,t,r){let n="",i=e[t];if('"'!==i&&"'"!==i)throw Error(`Expected quoted string, found "${i}"`);for(t++;t<e.length&&e[t]!==i;)n+=e[t],t++;if(e[t]!==i)throw Error(`Unterminated ${r} value`);return[++t,n]}function h(e,t,r){for(let n=0;n<t.length;n++)if(t[n]!==e[r+n+1])return!1;return!0}function p(e){if(c(e))return e;throw Error(`Invalid entity name ${e}`)}let g=/^[-+]?0x[a-fA-F0-9]+$/,m=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,y={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0},b=/^([-+])?(0*)(\d*(\.\d*)?[eE][-\+]?\d+)$/;class v{constructor(e){var t;this.options=e,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xa2"},pound:{regex:/&(pound|#163);/g,val:"\xa3"},yen:{regex:/&(yen|#165);/g,val:"\xa5"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"\xa9"},reg:{regex:/&(reg|#174);/g,val:"\xae"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(e,t)=>String.fromCodePoint(Number.parseInt(t,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(e,t)=>String.fromCodePoint(Number.parseInt(t,16))}},this.addExternalEntities=w,this.parseXml=R,this.parseTextData=S,this.resolveNameSpace=E,this.buildAttributesMap=x,this.isItStopNode=I,this.replaceEntitiesValue=T,this.readStopNodeData=C,this.saveTextToParentTag=P,this.addChild=k,this.ignoreAttributesFn="function"==typeof(t=this.options.ignoreAttributes)?t:Array.isArray(t)?e=>{for(let r of t)if("string"==typeof r&&e===r||r instanceof RegExp&&r.test(e))return!0}:()=>!1}}function w(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let n=t[r];this.lastEntities[n]={regex:RegExp("&"+n+";","g"),val:e[n]}}}function S(e,t,r,n,i,s,o){if(void 0!==e&&(this.options.trimValues&&!n&&(e=e.trim()),e.length>0)){o||(e=this.replaceEntitiesValue(e));let n=this.options.tagValueProcessor(t,e,r,i,s);return null==n?e:typeof n!=typeof e||n!==e?n:this.options.trimValues?N(e,this.options.parseTagValue,this.options.numberParseOptions):e.trim()===e?N(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function E(e){if(this.options.removeNSPrefix){let t=e.split(":"),r="/"===e.charAt(0)?"/":"";if("xmlns"===t[0])return"";2===t.length&&(e=r+t[1])}return e}let A=RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function x(e,t,r){if(!0!==this.options.ignoreAttributes&&"string"==typeof e){let r=u(e,A),n=r.length,i={};for(let e=0;e<n;e++){let n=this.resolveNameSpace(r[e][1]);if(this.ignoreAttributesFn(n,t))continue;let s=r[e][4],o=this.options.attributeNamePrefix+n;if(n.length){if(this.options.transformAttributeName&&(o=this.options.transformAttributeName(o)),"__proto__"===o&&(o="#__proto__"),void 0!==s){this.options.trimValues&&(s=s.trim()),s=this.replaceEntitiesValue(s);let e=this.options.attributeValueProcessor(n,s,t);null==e?i[o]=s:typeof e!=typeof s||e!==s?i[o]=e:i[o]=N(s,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(i[o]=!0)}}if(Object.keys(i).length){if(this.options.attributesGroupName){let e={};return e[this.options.attributesGroupName]=i,e}return i}}}let R=function(e){e=e.replace(/\r\n?/g,"\n");let t=new l("!xml"),r=t,n="",i="";for(let s=0;s<e.length;s++)if("<"===e[s]){if("/"===e[s+1]){let t=M(e,">",s,"Closing Tag is not closed."),o=e.substring(s+2,t).trim();if(this.options.removeNSPrefix){let e=o.indexOf(":");-1!==e&&(o=o.substr(e+1))}this.options.transformTagName&&(o=this.options.transformTagName(o)),r&&(n=this.saveTextToParentTag(n,r,i));let a=i.substring(i.lastIndexOf(".")+1);if(o&&-1!==this.options.unpairedTags.indexOf(o))throw Error(`Unpaired tag can not be used as closing tag: </${o}>`);let u=0;a&&-1!==this.options.unpairedTags.indexOf(a)?(u=i.lastIndexOf(".",i.lastIndexOf(".")-1),this.tagsNodeStack.pop()):u=i.lastIndexOf("."),i=i.substring(0,u),r=this.tagsNodeStack.pop(),n="",s=t}else if("?"===e[s+1]){let t=O(e,s,!1,"?>");if(!t)throw Error("Pi Tag is not closed.");if(n=this.saveTextToParentTag(n,r,i),this.options.ignoreDeclaration&&"?xml"===t.tagName||this.options.ignorePiTags);else{let e=new l(t.tagName);e.add(this.options.textNodeName,""),t.tagName!==t.tagExp&&t.attrExpPresent&&(e[":@"]=this.buildAttributesMap(t.tagExp,i,t.tagName)),this.addChild(r,e,i,s)}s=t.closeIndex+1}else if("!--"===e.substr(s+1,3)){let t=M(e,"-->",s+4,"Comment is not closed.");if(this.options.commentPropName){let o=e.substring(s+4,t-2);n=this.saveTextToParentTag(n,r,i),r.add(this.options.commentPropName,[{[this.options.textNodeName]:o}])}s=t}else if("!D"===e.substr(s+1,2)){let t=function(e,t){let r={};if("O"===e[t+3]&&"C"===e[t+4]&&"T"===e[t+5]&&"Y"===e[t+6]&&"P"===e[t+7]&&"E"===e[t+8]){t+=9;let n=1,i=!1,s=!1;for(;t<e.length;t++)if("<"!==e[t]||s){if(">"===e[t]){if(s?"-"===e[t-1]&&"-"===e[t-2]&&(s=!1,n--):n--,0===n)break}else"["===e[t]?i=!0:e[t]}else{if(i&&h(e,"!ENTITY",t)){let n,i;t+=7,[n,i,t]=function(e,t){t=f(e,t);let r="";for(;t<e.length&&!/\s/.test(e[t])&&'"'!==e[t]&&"'"!==e[t];)r+=e[t],t++;if(p(r),t=f(e,t),"SYSTEM"===e.substring(t,t+6).toUpperCase())throw Error("External entities are not supported");if("%"===e[t])throw Error("Parameter entities are not supported");let n="";return[t,n]=d(e,t,"entity"),[r,n,--t]}(e,t+1),-1===i.indexOf("&")&&(r[n]={regx:RegExp(`&${n};`,"g"),val:i})}else if(i&&h(e,"!ELEMENT",t)){let{index:r}=function(e,t){t=f(e,t);let r="";for(;t<e.length&&!/\s/.test(e[t]);)r+=e[t],t++;if(!p(r))throw Error(`Invalid element name: "${r}"`);t=f(e,t);let n="";if("E"===e[t]&&h(e,"MPTY",t))t+=4;else if("A"===e[t]&&h(e,"NY",t))t+=2;else if("("===e[t]){for(t++;t<e.length&&")"!==e[t];)n+=e[t],t++;if(")"!==e[t])throw Error("Unterminated content model")}else throw Error(`Invalid Element Expression, found "${e[t]}"`);return{elementName:r,contentModel:n.trim(),index:t}}(e,(t+=8)+1);t=r}else if(i&&h(e,"!ATTLIST",t))t+=8;else if(i&&h(e,"!NOTATION",t)){let{index:r}=function(e,t){t=f(e,t);let r="";for(;t<e.length&&!/\s/.test(e[t]);)r+=e[t],t++;p(r),t=f(e,t);let n=e.substring(t,t+6).toUpperCase();if("SYSTEM"!==n&&"PUBLIC"!==n)throw Error(`Expected SYSTEM or PUBLIC, found "${n}"`);t+=n.length,t=f(e,t);let i=null,s=null;if("PUBLIC"===n)[t,i]=d(e,t,"publicIdentifier"),t=f(e,t),('"'===e[t]||"'"===e[t])&&([t,s]=d(e,t,"systemIdentifier"));else if("SYSTEM"===n&&([t,s]=d(e,t,"systemIdentifier"),!s))throw Error("Missing mandatory system identifier for SYSTEM notation");return{notationName:r,publicIdentifier:i,systemIdentifier:s,index:--t}}(e,(t+=9)+1);t=r}else if(h(e,"!--",t))s=!0;else throw Error("Invalid DOCTYPE");n++}if(0!==n)throw Error("Unclosed DOCTYPE")}else throw Error("Invalid Tag instead of DOCTYPE");return{entities:r,i:t}}(e,s);this.docTypeEntities=t.entities,s=t.i}else if("!["===e.substr(s+1,2)){let t=M(e,"]]>",s,"CDATA is not closed.")-2,o=e.substring(s+9,t);n=this.saveTextToParentTag(n,r,i);let a=this.parseTextData(o,r.tagname,i,!0,!1,!0,!0);void 0==a&&(a=""),this.options.cdataPropName?r.add(this.options.cdataPropName,[{[this.options.textNodeName]:o}]):r.add(this.options.textNodeName,a),s=t+2}else{let o=O(e,s,this.options.removeNSPrefix),a=o.tagName,u=o.rawTagName,c=o.tagExp,f=o.attrExpPresent,d=o.closeIndex;this.options.transformTagName&&(a=this.options.transformTagName(a)),r&&n&&"!xml"!==r.tagname&&(n=this.saveTextToParentTag(n,r,i,!1));let h=r;h&&-1!==this.options.unpairedTags.indexOf(h.tagname)&&(r=this.tagsNodeStack.pop(),i=i.substring(0,i.lastIndexOf("."))),a!==t.tagname&&(i+=i?"."+a:a);let p=s;if(this.isItStopNode(this.options.stopNodes,i,a)){let t="";if(c.length>0&&c.lastIndexOf("/")===c.length-1)"/"===a[a.length-1]?(a=a.substr(0,a.length-1),i=i.substr(0,i.length-1),c=a):c=c.substr(0,c.length-1),s=o.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(a))s=o.closeIndex;else{let r=this.readStopNodeData(e,u,d+1);if(!r)throw Error(`Unexpected end of ${u}`);s=r.i,t=r.tagContent}let n=new l(a);a!==c&&f&&(n[":@"]=this.buildAttributesMap(c,i,a)),t&&(t=this.parseTextData(t,a,i,!0,f,!0,!0)),i=i.substr(0,i.lastIndexOf(".")),n.add(this.options.textNodeName,t),this.addChild(r,n,i,p)}else{if(c.length>0&&c.lastIndexOf("/")===c.length-1){"/"===a[a.length-1]?(a=a.substr(0,a.length-1),i=i.substr(0,i.length-1),c=a):c=c.substr(0,c.length-1),this.options.transformTagName&&(a=this.options.transformTagName(a));let e=new l(a);a!==c&&f&&(e[":@"]=this.buildAttributesMap(c,i,a)),this.addChild(r,e,i,p),i=i.substr(0,i.lastIndexOf("."))}else{let e=new l(a);this.tagsNodeStack.push(r),a!==c&&f&&(e[":@"]=this.buildAttributesMap(c,i,a)),this.addChild(r,e,i,p),r=e}n="",s=d}}}else n+=e[s];return t.child};function k(e,t,r,n){this.options.captureMetaData||(n=void 0);let i=this.options.updateTag(t.tagname,r,t[":@"]);!1===i||("string"==typeof i&&(t.tagname=i),e.addChild(t,n))}let T=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){let r=this.docTypeEntities[t];e=e.replace(r.regx,r.val)}for(let t in this.lastEntities){let r=this.lastEntities[t];e=e.replace(r.regex,r.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){let r=this.htmlEntities[t];e=e.replace(r.regex,r.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function P(e,t,r,n){return e&&(void 0===n&&(n=0===t.child.length),void 0!==(e=this.parseTextData(e,t.tagname,r,!1,!!t[":@"]&&0!==Object.keys(t[":@"]).length,n))&&""!==e&&t.add(this.options.textNodeName,e),e=""),e}function I(e,t,r){let n="*."+r;for(let r in e){let i=e[r];if(n===i||t===i)return!0}return!1}function M(e,t,r,n){let i=e.indexOf(t,r);if(-1!==i)return i+t.length-1;throw Error(n)}function O(e,t,r,n=">"){let i=function(e,t,r=">"){let n;let i="";for(let s=t;s<e.length;s++){let t=e[s];if(n)t===n&&(n="");else if('"'===t||"'"===t)n=t;else if(t===r[0]){if(!r[1]||e[s+1]===r[1])return{data:i,index:s}}else"	"===t&&(t=" ");i+=t}}(e,t+1,n);if(!i)return;let s=i.data,o=i.index,a=s.search(/\s/),u=s,c=!0;-1!==a&&(u=s.substring(0,a),s=s.substring(a+1).trimStart());let l=u;if(r){let e=u.indexOf(":");-1!==e&&(c=(u=u.substr(e+1))!==i.data.substr(e+1))}return{tagName:u,tagExp:s,closeIndex:o,attrExpPresent:c,rawTagName:l}}function C(e,t,r){let n=r,i=1;for(;r<e.length;r++)if("<"===e[r]){if("/"===e[r+1]){let s=M(e,">",r,`${t} is not closed`);if(e.substring(r+2,s).trim()===t&&0==--i)return{tagContent:e.substring(n,r),i:s};r=s}else if("?"===e[r+1])r=M(e,"?>",r+1,"StopNode is not closed.");else if("!--"===e.substr(r+1,3))r=M(e,"-->",r+3,"StopNode is not closed.");else if("!["===e.substr(r+1,2))r=M(e,"]]>",r,"StopNode is not closed.")-2;else{let n=O(e,r,">");n&&((n&&n.tagName)===t&&"/"!==n.tagExp[n.tagExp.length-1]&&i++,r=n.closeIndex)}}}function N(e,t,r){if(t&&"string"==typeof e){let t=e.trim();return"true"===t||"false"!==t&&function(e,t={}){if(t=Object.assign({},y,t),!e||"string"!=typeof e)return e;let r=e.trim();if(void 0!==t.skipLike&&t.skipLike.test(r))return e;if("0"===e)return 0;if(t.hex&&g.test(r))return function(e,t){if(parseInt)return parseInt(e,16);if(Number.parseInt)return Number.parseInt(e,16);if(window&&window.parseInt)return window.parseInt(e,t);throw Error("parseInt, Number.parseInt, window.parseInt are not supported")}(r,16);{if(-1!==r.search(/.+[eE].+/))return function(e,t,r){if(!r.eNotation)return e;let n=t.match(b);if(!n)return e;{let i=n[1]||"",s=-1===n[3].indexOf("e")?"E":"e",o=n[2],a=i?e[o.length+1]===s:e[o.length]===s;return o.length>1&&a?e:1===o.length&&(n[3].startsWith(`.${s}`)||n[3][0]===s)?Number(t):r.leadingZeros&&!a?Number(t=(n[1]||"")+n[3]):e}}(e,r,t);let i=m.exec(r);if(!i)return e;{var n;let s=i[1]||"",o=i[2],a=((n=i[3])&&-1!==n.indexOf(".")&&("."===(n=n.replace(/0+$/,""))?n="0":"."===n[0]?n="0"+n:"."===n[n.length-1]&&(n=n.substring(0,n.length-1))),n),u=s?"."===e[o.length+1]:"."===e[o.length];if(!t.leadingZeros&&(o.length>1||1===o.length&&!u))return e;{let n=Number(r),i=String(n);if(0===n)return n;if(-1!==i.search(/[eE]/))return t.eNotation?n:e;if(-1!==r.indexOf("."))return"0"===i?n:i===a?n:i===`${s}${a}`?n:e;let u=o?a:r;return o?u===i||s+u===i?n:e:u===i||u===s+i?n:e}}}}(e,r)}return void 0!==e?e:""}let _=l.getMetaDataSymbol(),U={allowBooleanAttributes:!1,unpairedTags:[]};function $(e){return" "===e||"	"===e||"\n"===e||"\r"===e}function B(e,t){let r=t;for(;t<e.length;t++)if("?"==e[t]||" "==e[t]){let n=e.substr(r,t-r);if(t>5&&"xml"===n)return j("InvalidXml","XML declaration allowed only at the start of the document.",H(e,t));if("?"!=e[t]||">"!=e[t+1])continue;t++;break}return t}function L(e,t){if(e.length>t+5&&"-"===e[t+1]&&"-"===e[t+2]){for(t+=3;t<e.length;t++)if("-"===e[t]&&"-"===e[t+1]&&">"===e[t+2]){t+=2;break}}else if(e.length>t+8&&"D"===e[t+1]&&"O"===e[t+2]&&"C"===e[t+3]&&"T"===e[t+4]&&"Y"===e[t+5]&&"P"===e[t+6]&&"E"===e[t+7]){let r=1;for(t+=8;t<e.length;t++)if("<"===e[t])r++;else if(">"===e[t]&&0==--r)break}else if(e.length>t+9&&"["===e[t+1]&&"C"===e[t+2]&&"D"===e[t+3]&&"A"===e[t+4]&&"T"===e[t+5]&&"A"===e[t+6]&&"["===e[t+7]){for(t+=8;t<e.length;t++)if("]"===e[t]&&"]"===e[t+1]&&">"===e[t+2]){t+=2;break}}return t}let D=RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function F(e,t){let r=u(e,D),n={};for(let e=0;e<r.length;e++){if(0===r[e][1].length)return j("InvalidAttr","Attribute '"+r[e][2]+"' has no space in starting.",q(r[e]));if(void 0!==r[e][3]&&void 0===r[e][4])return j("InvalidAttr","Attribute '"+r[e][2]+"' is without value.",q(r[e]));if(void 0===r[e][3]&&!t.allowBooleanAttributes)return j("InvalidAttr","boolean attribute '"+r[e][2]+"' is not allowed.",q(r[e]));let i=r[e][2];if(!c(i))return j("InvalidAttr","Attribute '"+i+"' is an invalid name.",q(r[e]));if(n.hasOwnProperty(i))return j("InvalidAttr","Attribute '"+i+"' is repeated.",q(r[e]));n[i]=1}return!0}function j(e,t,r){return{err:{code:e,msg:t,line:r.line||r,col:r.col}}}function H(e,t){let r=e.substring(0,t).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function q(e){return e.startIndex+e[1].length}class z{constructor(e){this.externalEntities={},this.options=Object.assign({},s,e)}parse(e,t){if("string"==typeof e);else if(e.toString)e=e.toString();else throw Error("XML data is accepted in String or Bytes[] form.");if(t){!0===t&&(t={});let r=function(e,t){t=Object.assign({},U,t);let r=[],n=!1,i=!1;"\uFEFF"===e[0]&&(e=e.substr(1));for(let s=0;s<e.length;s++)if("<"===e[s]&&"?"===e[s+1]){if(s+=2,(s=B(e,s)).err)return s}else if("<"===e[s]){let o=s;if("!"===e[++s]){s=L(e,s);continue}{let a=!1;"/"===e[s]&&(a=!0,s++);let u="";for(;s<e.length&&">"!==e[s]&&" "!==e[s]&&"	"!==e[s]&&"\n"!==e[s]&&"\r"!==e[s];s++)u+=e[s];if("/"===(u=u.trim())[u.length-1]&&(u=u.substring(0,u.length-1),s--),!c(u))return j("InvalidTag",0===u.trim().length?"Invalid space after '<'.":"Tag '"+u+"' is an invalid name.",H(e,s));let l=function(e,t){let r="",n="",i=!1;for(;t<e.length;t++){if('"'===e[t]||"'"===e[t])""===n?n=e[t]:n!==e[t]||(n="");else if(">"===e[t]&&""===n){i=!0;break}r+=e[t]}return""===n&&{value:r,index:t,tagClosed:i}}(e,s);if(!1===l)return j("InvalidAttr","Attributes for '"+u+"' have open quote.",H(e,s));let f=l.value;if(s=l.index,"/"===f[f.length-1]){let r=s-f.length,i=F(f=f.substring(0,f.length-1),t);if(!0!==i)return j(i.err.code,i.err.msg,H(e,r+i.err.line));n=!0}else if(a){if(!l.tagClosed)return j("InvalidTag","Closing tag '"+u+"' doesn't have proper closing.",H(e,s));if(f.trim().length>0)return j("InvalidTag","Closing tag '"+u+"' can't have attributes or invalid starting.",H(e,o));{if(0===r.length)return j("InvalidTag","Closing tag '"+u+"' has not been opened.",H(e,o));let t=r.pop();if(u!==t.tagName){let r=H(e,t.tagStartPos);return j("InvalidTag","Expected closing tag '"+t.tagName+"' (opened in line "+r.line+", col "+r.col+") instead of closing tag '"+u+"'.",H(e,o))}0==r.length&&(i=!0)}}else{let a=F(f,t);if(!0!==a)return j(a.err.code,a.err.msg,H(e,s-f.length+a.err.line));if(!0===i)return j("InvalidXml","Multiple possible root nodes found.",H(e,s));-1!==t.unpairedTags.indexOf(u)||r.push({tagName:u,tagStartPos:o}),n=!0}for(s++;s<e.length;s++)if("<"===e[s]){if("!"===e[s+1]){s=L(e,++s);continue}if("?"===e[s+1]){if((s=B(e,++s)).err)return s}else break}else if("&"===e[s]){let t=function(e,t){if(";"===e[++t])return -1;if("#"===e[t])return function(e,t){let r=/\d/;for("x"===e[t]&&(t++,r=/[\da-fA-F]/);t<e.length;t++){if(";"===e[t])return t;if(!e[t].match(r))break}return -1}(e,++t);let r=0;for(;t<e.length;t++,r++)if(!e[t].match(/\w/)||!(r<20)){if(";"===e[t])break;return -1}return t}(e,s);if(-1==t)return j("InvalidChar","char '&' is not expected.",H(e,s));s=t}else if(!0===i&&!$(e[s]))return j("InvalidXml","Extra text at the end",H(e,s));"<"===e[s]&&s--}}else{if($(e[s]))continue;return j("InvalidChar","char '"+e[s]+"' is not expected.",H(e,s))}return n?1==r.length?j("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",H(e,r[0].tagStartPos)):!(r.length>0)||j("InvalidXml","Invalid '"+JSON.stringify(r.map(e=>e.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):j("InvalidXml","Start tag expected.",1)}(e,t);if(!0!==r)throw Error(`${r.err.msg}:${r.err.line}:${r.err.col}`)}let r=new v(this.options);r.addExternalEntities(this.externalEntities);let n=r.parseXml(e);return this.options.preserveOrder||void 0===n?n:function e(t,r,n){let i;let s={};for(let o=0;o<t.length;o++){let a=t[o],u=function(e){let t=Object.keys(e);for(let e=0;e<t.length;e++){let r=t[e];if(":@"!==r)return r}}(a),c="";if(c=void 0===n?u:n+"."+u,u===r.textNodeName)void 0===i?i=a[u]:i+=""+a[u];else if(void 0===u)continue;else if(a[u]){let t=e(a[u],r,c),n=function(e,t){let{textNodeName:r}=t,n=Object.keys(e).length;return 0===n||1===n&&(!!e[r]||"boolean"==typeof e[r]||0===e[r])}(t,r);void 0!==a[_]&&(t[_]=a[_]),a[":@"]?function(e,t,r,n){if(t){let i=Object.keys(t),s=i.length;for(let o=0;o<s;o++){let s=i[o];n.isArray(s,r+"."+s,!0,!0)?e[s]=[t[s]]:e[s]=t[s]}}}(t,a[":@"],c,r):1!==Object.keys(t).length||void 0===t[r.textNodeName]||r.alwaysCreateTextNode?0===Object.keys(t).length&&(r.alwaysCreateTextNode?t[r.textNodeName]="":t=""):t=t[r.textNodeName],void 0!==s[u]&&s.hasOwnProperty(u)?(Array.isArray(s[u])||(s[u]=[s[u]]),s[u].push(t)):r.isArray(u,c,n)?s[u]=[t]:s[u]=t}}return"string"==typeof i?i.length>0&&(s[r.textNodeName]=i):void 0!==i&&(s[r.textNodeName]=i),s}(n,this.options)}addEntity(e,t){if(-1!==t.indexOf("&"))throw Error("Entity value can't have '&'");if(-1!==e.indexOf("&")||-1!==e.indexOf(";"))throw Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===t)throw Error("An entity with value '&' is not permitted");this.externalEntities[e]=t}static getMetaDataSymbol(){return l.getMetaDataSymbol()}}let V=(e,t)=>(0,i.Wg)(e,t).then(e=>t.utf8Encoder(e)),W=(e,t)=>V(e,t).then(e=>{if(e.length){let t;let r=new z({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:(e,t)=>""===t.trim()&&t.includes("\n")?"":void 0});r.addEntity("#xD","\r"),r.addEntity("#10","\n");try{t=r.parse(e,!0)}catch(t){throw t&&"object"==typeof t&&Object.defineProperty(t,"$responseBodyText",{value:e}),t}let n="#text",s=Object.keys(t)[0],o=t[s];return o[n]&&(o[s]=o[n],delete o[n]),(0,i.sT)(o)}return{}}),Z=async(e,t)=>{let r=await W(e,t);return r.Error&&(r.Error.message=r.Error.message??r.Error.Message),r},G=(e,t)=>t?.Error?.Code!==void 0?t.Error.Code:t?.Code!==void 0?t.Code:404==e.statusCode?"NotFound":void 0},86321:function(e,t,r){"use strict";r.d(t,{$I:function(){return p},Ae:function(){return a},JB:function(){return T},nd:function(){return w},mS:function(){return _},D4:function(){return L},x4:function(){return P}});var n=r(2171);r(4598);let i=e=>(t,r)=>async n=>{let i=await e.region(),s=e.region,o=()=>{};r.__s3RegionRedirect&&(Object.defineProperty(e,"region",{writable:!1,value:async()=>r.__s3RegionRedirect}),o=()=>Object.defineProperty(e,"region",{writable:!0,value:s}));try{let s=await t(n);if(r.__s3RegionRedirect){o();let t=await e.region();if(i!==t)throw Error("Region was not restored following S3 region redirect.")}return s}catch(e){throw o(),e}},s={tags:["REGION_REDIRECT","S3"],name:"regionRedirectEndpointMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"},o={step:"initialize",tags:["REGION_REDIRECT","S3"],name:"regionRedirectMiddleware",override:!0},a=e=>({applyToStack:t=>{t.add((t,r)=>async n=>{try{return await t(n)}catch(i){if(e.followRegionRedirects&&(i?.$metadata?.httpStatusCode===301||i?.$metadata?.httpStatusCode===400&&i?.name==="IllegalLocationConstraintException")){try{let t=i.$response.headers["x-amz-bucket-region"];r.logger?.debug(`Redirecting from ${await e.region()} to ${t}`),r.__s3RegionRedirect=t}catch(e){throw Error("Region redirect failed: "+e)}return t(n)}throw i}},o),t.addRelativeTo(i(e),s)}});class u{data;lastPurgeTime=Date.now();static EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS=3e4;constructor(e={}){this.data=e}get(e){let t=this.data[e];if(t)return t}set(e,t){return this.data[e]=t,t}delete(e){delete this.data[e]}async purgeExpired(){let e=Date.now();if(!(this.lastPurgeTime+u.EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS>e))for(let t in this.data){let r=this.data[t];if(!r.isRefreshing){let n=await r.identity;n.expiration&&n.expiration.getTime()<e&&delete this.data[t]}}}}class c{_identity;isRefreshing;accessed;constructor(e,t=!1,r=Date.now()){this._identity=e,this.isRefreshing=t,this.accessed=r}get identity(){return this.accessed=Date.now(),this._identity}}class l{createSessionFn;cache;static REFRESH_WINDOW_MS=6e4;constructor(e,t=new u){this.createSessionFn=e,this.cache=t}async getS3ExpressIdentity(e,t){let r=t.Bucket,{cache:n}=this,i=n.get(r);return i?i.identity.then(e=>(e.expiration?.getTime()??0)<Date.now()?n.set(r,new c(this.getIdentity(r))).identity:((e.expiration?.getTime()??0)<Date.now()+l.REFRESH_WINDOW_MS&&!i.isRefreshing&&(i.isRefreshing=!0,this.getIdentity(r).then(e=>{n.set(r,new c(Promise.resolve(e)))})),e)):n.set(r,new c(this.getIdentity(r))).identity}async getIdentity(e){await this.cache.purgeExpired().catch(e=>{console.warn("Error while clearing expired entries in S3ExpressIdentityCache: \n"+e)});let t=await this.createSessionFn(e);if(!t.Credentials?.AccessKeyId||!t.Credentials?.SecretAccessKey)throw Error("s3#createSession response credential missing AccessKeyId or SecretAccessKey.");return{accessKeyId:t.Credentials.AccessKeyId,secretAccessKey:t.Credentials.SecretAccessKey,sessionToken:t.Credentials.SessionToken,expiration:t.Credentials.Expiration?new Date(t.Credentials.Expiration):void 0}}}var f=r(34819);r(32480);let d="X-Amz-S3session-Token",h=d.toLowerCase();class p extends f.L1{async signWithCredentials(e,t,r){let n=g(t);return e.headers[h]=t.sessionToken,m(this,n),this.signRequest(e,r??{})}async presignWithCredentials(e,t,r){let n=g(t);return delete e.headers[h],e.headers[d]=t.sessionToken,e.query=e.query??{},e.query[d]=t.sessionToken,m(this,n),this.presign(e,r)}}function g(e){return{accessKeyId:e.accessKeyId,secretAccessKey:e.secretAccessKey,expiration:e.expiration}}function m(e,t){let r=setTimeout(()=>{throw Error("SignatureV4S3Express credential override was created but not called.")},10),n=e.credentialProvider;e.credentialProvider=()=>(clearTimeout(r),e.credentialProvider=n,Promise.resolve(t))}var y=r(77844);let b=e=>(t,r)=>async i=>{if(r.endpointV2){let t=r.endpointV2,s=t.properties?.authSchemes?.[0]?.name==="sigv4-s3express";if((t.properties?.backend==="S3Express"||t.properties?.bucketType==="Directory")&&((0,y.h)(r,"S3_EXPRESS_BUCKET","J"),r.isS3ExpressBucket=!0),s){let t=i.input.Bucket;if(t){let s=await e.s3ExpressIdentityProvider.getS3ExpressIdentity(await e.credentials(),{Bucket:t});r.s3ExpressIdentity=s,n.aW.isInstance(i.request)&&s.sessionToken&&(i.request.headers[h]=s.sessionToken)}}}return t(i)},v={name:"s3ExpressMiddleware",step:"build",tags:["S3","S3_EXPRESS"],override:!0},w=e=>({applyToStack:t=>{t.add(b(e),v)}});var S=r(57201);r(23017);var E=r(95225);let A=async(e,t,r,n)=>{let i=await n.signWithCredentials(r,e,{});if(i.headers["X-Amz-Security-Token"]||i.headers["x-amz-security-token"])throw Error("X-Amz-Security-Token must not be set for s3-express requests.");return i},x=e=>e=>{throw e},R=(e,t)=>{},k=e=>(t,r)=>async i=>{let s;if(!n.aW.isInstance(i.request))return t(i);let o=(0,E.J)(r).selectedHttpAuthScheme;if(!o)throw Error("No HttpAuthScheme was selected: unable to sign request");let{httpAuthOption:{signingProperties:a={}},identity:u,signer:c}=o;s=r.s3ExpressIdentity?await A(r.s3ExpressIdentity,a,i.request,await e.signer()):await c.sign(i.request,u,a);let l=await t({...i,request:s}).catch((c.errorHandler||x)(a));return(c.successHandler||R)(l.response,a),l},T=e=>({applyToStack:t=>{t.addRelativeTo(k(e),S.wA)}}),P=(e,{session:t})=>{let[r,n]=t,{forcePathStyle:i,useAccelerateEndpoint:s,disableMultiregionAccessPoints:o,followRegionRedirects:a,s3ExpressIdentityProvider:u,bucketEndpoint:c}=e;return Object.assign(e,{forcePathStyle:i??!1,useAccelerateEndpoint:s??!1,disableMultiregionAccessPoints:o??!1,followRegionRedirects:a??!1,s3ExpressIdentityProvider:u??new l(async e=>r().send(new n({Bucket:e}))),bucketEndpoint:c??!1})};var I=r(75020);let M={CopyObjectCommand:!0,UploadPartCopyCommand:!0,CompleteMultipartUploadCommand:!0},O=e=>(t,r)=>async i=>{let s=await t(i),{response:o}=s;if(!n.Zn.isInstance(o))return s;let{statusCode:a,body:u}=o;if(a<200||a>=300||!("function"==typeof u?.stream||"function"==typeof u?.pipe||"function"==typeof u?.tee))return s;let c=u,l=u;!u||"object"!=typeof u||u instanceof Uint8Array||([c,l]=await (0,I.vH)(u)),o.body=l;let f=await C(c,{streamCollector:async e=>(0,I.TS)(e,3e3)});"function"==typeof c?.destroy&&c.destroy();let d=e.utf8Encoder(f.subarray(f.length-16));if(0===f.length&&M[r.commandName]){let e=Error("S3 aborted request");throw e.name="InternalError",e}return d&&d.endsWith("</Error>")&&(o.statusCode=400),s},C=(e=new Uint8Array,t)=>e instanceof Uint8Array?Promise.resolve(e):t.streamCollector(e)||Promise.resolve(new Uint8Array),N={relation:"after",toMiddleware:"deserializerMiddleware",tags:["THROW_200_EXCEPTIONS","S3"],name:"throw200ExceptionsMiddleware",override:!0},_=e=>({applyToStack:t=>{t.addRelativeTo(O(e),N)}}),U=e=>"string"==typeof e&&0===e.indexOf("arn:")&&e.split(":").length>=6,$={name:"bucketEndpointMiddleware",override:!0,relation:"after",toMiddleware:"endpointV2Middleware"},B={step:"initialize",tags:["VALIDATE_BUCKET_NAME"],name:"validateBucketNameMiddleware",override:!0},L=e=>({applyToStack:t=>{t.add(function({bucketEndpoint:e}){return t=>async r=>{let{input:{Bucket:n}}=r;if(!e&&"string"==typeof n&&!U(n)&&n.indexOf("/")>=0){let e=Error(`Bucket name shouldn't contain '/', received '${n}'`);throw e.name="InvalidBucketName",e}return t({...r})}}(e),B),t.addRelativeTo((t,r)=>async n=>{if(e.bucketEndpoint){let e=r.endpointV2;if(e){let t=n.input.Bucket;if("string"==typeof t)try{let n=new URL(t);r.endpointV2={...e,url:n}}catch(n){let e=`@aws-sdk/middleware-sdk-s3: bucketEndpoint=true was set but Bucket=${t} could not be parsed as URL.`;throw r.logger?.constructor?.name==="NoOpLogger"?console.warn(e):r.logger?.warn?.(e),n}}}return t(n)},$)}})},66395:function(e,t,r){"use strict";r(65484),r(2171),r(43873)},43873:function(e,t,r){"use strict";r.d(t,{J:function(){return o}});var n=r(86321),i=r(34819);let s={CrtSignerV4:null};class o{sigv4aSigner;sigv4Signer;signerOptions;static sigv4aDependency(){return"function"==typeof s.CrtSignerV4?"crt":"function"==typeof i.q4.SignatureV4a?"js":"none"}constructor(e){this.sigv4Signer=new n.$I(e),this.signerOptions=e}async sign(e,t={}){return"*"===t.signingRegion?this.getSigv4aSigner().sign(e,t):this.sigv4Signer.sign(e,t)}async signWithCredentials(e,t,r={}){if("*"===r.signingRegion){let n=this.getSigv4aSigner(),i=s.CrtSignerV4;if(i&&n instanceof i)return n.signWithCredentials(e,t,r);throw Error('signWithCredentials with signingRegion \'*\' is only supported when using the CRT dependency @aws-sdk/signature-v4-crt. Please check whether you have installed the "@aws-sdk/signature-v4-crt" package explicitly. You must also register the package by calling [require("@aws-sdk/signature-v4-crt");] or an ESM equivalent such as [import "@aws-sdk/signature-v4-crt";]. For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt')}return this.sigv4Signer.signWithCredentials(e,t,r)}async presign(e,t={}){if("*"===t.signingRegion){let r=this.getSigv4aSigner(),n=s.CrtSignerV4;if(n&&r instanceof n)return r.presign(e,t);throw Error('presign with signingRegion \'*\' is only supported when using the CRT dependency @aws-sdk/signature-v4-crt. Please check whether you have installed the "@aws-sdk/signature-v4-crt" package explicitly. You must also register the package by calling [require("@aws-sdk/signature-v4-crt");] or an ESM equivalent such as [import "@aws-sdk/signature-v4-crt";]. For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt')}return this.sigv4Signer.presign(e,t)}async presignWithCredentials(e,t,r={}){if("*"===r.signingRegion)throw Error("Method presignWithCredentials is not supported for [signingRegion=*].");return this.sigv4Signer.presignWithCredentials(e,t,r)}getSigv4aSigner(){if(!this.sigv4aSigner){let e=s.CrtSignerV4,t=i.q4.SignatureV4a;if("node"===this.signerOptions.runtime){if(!e&&!t)throw Error("Neither CRT nor JS SigV4a implementation is available. Please load either @aws-sdk/signature-v4-crt or @aws-sdk/signature-v4a. For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt");if(e&&"function"==typeof e)this.sigv4aSigner=new e({...this.signerOptions,signingAlgorithm:1});else if(t&&"function"==typeof t)this.sigv4aSigner=new t({...this.signerOptions});else throw Error("Available SigV4a implementation is not a valid constructor. Please ensure you've properly imported @aws-sdk/signature-v4-crt or @aws-sdk/signature-v4a.For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt")}else{if(!t||"function"!=typeof t)throw Error("JS SigV4a implementation is not available or not a valid constructor. Please check whether you have installed the @aws-sdk/signature-v4a package explicitly. The CRT implementation is not available for browsers. You must also register the package by calling [require('@aws-sdk/signature-v4a');] or an ESM equivalent such as [import '@aws-sdk/signature-v4a';]. For more information please go to https://github.com/aws/aws-sdk-js-v3#using-javascript-non-crt-implementation-of-sigv4a");this.sigv4aSigner=new t({...this.signerOptions})}}return this.sigv4aSigner}}},57201:function(e,t,r){"use strict";r.d(t,{K5:function(){return y},zV:function(){return v},tZ:function(){return a},aZ:function(){return h},wA:function(){return d},BP:function(){return b},CU:function(){return w},$E:function(){return p},cu:function(){return g.cu},hr:function(){return m}}),r(23017);var n=r(95225);let i=(e,t)=>{if(!t||0===t.length)return e;let r=[];for(let n of t)for(let t of e)t.schemeId.split("#")[1]===n&&r.push(t);for(let t of e)r.find(({schemeId:e})=>e===t.schemeId)||r.push(t);return r},s=(e,t)=>(r,s)=>async o=>{let a=i(e.httpAuthSchemeProvider(await t.httpAuthSchemeParametersProvider(e,s,o.input)),e.authSchemePreference?await e.authSchemePreference():[]),u=function(e){let t=new Map;for(let r of e)t.set(r.schemeId,r);return t}(e.httpAuthSchemes),c=(0,n.J)(s),l=[];for(let r of a){let n=u.get(r.schemeId);if(!n){l.push(`HttpAuthScheme \`${r.schemeId}\` was not enabled for this service.`);continue}let i=n.identityProvider(await t.identityProviderConfigProvider(e));if(!i){l.push(`HttpAuthScheme \`${r.schemeId}\` did not have an IdentityProvider configured.`);continue}let{identityProperties:o={},signingProperties:a={}}=r.propertiesExtractor?.(e,s)||{};r.identityProperties=Object.assign(r.identityProperties||{},o),r.signingProperties=Object.assign(r.signingProperties||{},a),c.selectedHttpAuthScheme={httpAuthOption:r,identity:await i(r.identityProperties),signer:n.signer};break}if(!c.selectedHttpAuthScheme)throw Error(l.join("\n"));return r(o)},o={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"},a=(e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r})=>({applyToStack:n=>{n.addRelativeTo(s(e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r}),o)}});r(86090).JK.name;var u=r(2171);let c=e=>e=>{throw e},l=(e,t)=>{},f=e=>(e,t)=>async r=>{if(!u.aW.isInstance(r.request))return e(r);let i=(0,n.J)(t).selectedHttpAuthScheme;if(!i)throw Error("No HttpAuthScheme was selected: unable to sign request");let{httpAuthOption:{signingProperties:s={}},identity:o,signer:a}=i,f=await e({...r,request:await a.sign(r.request,o,s)}).catch((a.errorHandler||c)(s));return(a.successHandler||l)(f.response,s),f},d={step:"finalizeRequest",tags:["HTTP_SIGNING"],name:"httpSigningMiddleware",aliases:["apiKeyMiddleware","tokenMiddleware","awsAuthMiddleware"],override:!0,relation:"after",toMiddleware:"retryMiddleware"},h=e=>({applyToStack:t=>{t.addRelativeTo(f(e),d)}}),p=e=>{if("function"==typeof e)return e;let t=Promise.resolve(e);return()=>t};var g=r(12731);function m(e,t,r){e.__smithy_context?e.__smithy_context.features||(e.__smithy_context.features={}):e.__smithy_context={features:{}},e.__smithy_context.features[t]=r}class y{constructor(e){for(let[t,r]of(this.authSchemes=new Map,Object.entries(e)))void 0!==r&&this.authSchemes.set(t,r)}getIdentityProvider(e){return this.authSchemes.get(e)}}let b=e=>v(e)&&e.expiration.getTime()-Date.now()<3e5,v=e=>void 0!==e.expiration,w=(e,t,r)=>{let n,i,s;if(void 0===e)return;let o="function"!=typeof e?async()=>Promise.resolve(e):e,a=!1,u=async e=>{i||(i=o(e));try{n=await i,s=!0,a=!1}finally{i=void 0}return n};return void 0===t?async e=>((!s||e?.forceRefresh)&&(n=await u(e)),n):async e=>((!s||e?.forceRefresh)&&(n=await u(e)),a||(r(n)?t(n)&&await u(e):a=!0),n)}},12731:function(e,t,r){"use strict";r.d(t,{Wg:function(){return i},cu:function(){return u}});var n=r(75020);let i=async(e=new Uint8Array,t)=>{if(e instanceof Uint8Array)return n.HE.mutate(e);if(!e)return n.HE.mutate(new Uint8Array);let r=t.streamCollector(e);return n.HE.mutate(await r)};r(68383);var s=r(2171);function o(e){return encodeURIComponent(e).replace(/[!'()*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}r(13516);let a=(e,t,r,n,i,s)=>{if(null!=t&&void 0!==t[r]){let t=n();if(t.length<=0)throw Error("Empty value provided for input HTTP label: "+r+".");e=e.replace(i,s?t.split("/").map(e=>o(e)).join("/"):o(t))}else throw Error("No value provided for input HTTP label: "+r+".");return e};function u(e,t){return new c(e,t)}class c{constructor(e,t){this.input=e,this.context=t,this.query={},this.method="",this.headers={},this.path="",this.body=null,this.hostname="",this.resolvePathStack=[]}async build(){let{hostname:e,protocol:t="https",port:r,path:n}=await this.context.endpoint();for(let e of(this.path=n,this.resolvePathStack))e(this.path);return new s.aW({protocol:t,hostname:this.hostname||e,port:r,method:this.method,path:this.path,query:this.query,body:this.body,headers:this.headers})}hn(e){return this.hostname=e,this}bp(e){return this.resolvePathStack.push(t=>{this.path=`${t?.endsWith("/")?t.slice(0,-1):t||""}`+e}),this}p(e,t,r,n){return this.resolvePathStack.push(i=>{this.path=a(i,this.input,e,t,r,n)}),this}h(e){return this.headers=e,this}q(e){return this.query=e,this}b(e){return this.body=e,this}m(e){return this.method=e,this}}r(306)},68383:function(e,t,r){"use strict";r(2171),r(95225);class n{constructor(e,t=new Map){this.namespace=e,this.schemas=t}static for(e){return n.registries.has(e)||n.registries.set(e,new n(e)),n.registries.get(e)}register(e,t){let r=this.normalizeShapeId(e);n.for(this.getNamespace(e)).schemas.set(r,t)}getSchema(e){let t=this.normalizeShapeId(e);if(!this.schemas.has(t))throw Error(`@smithy/core/schema - schema not found for ${t}`);return this.schemas.get(t)}getBaseException(){for(let[e,t]of this.schemas.entries())if(e.startsWith("smithy.ts.sdk.synthetic.")&&e.endsWith("ServiceException"))return t}find(e){return[...this.schemas.values()].find(e)}destroy(){n.registries.delete(this.namespace),this.schemas.clear()}normalizeShapeId(e){return e.includes("#")?e:this.namespace+"#"+e}getNamespace(e){return this.normalizeShapeId(e).split("#")[0]}}n.registries=new Map;class i{constructor(e,t){this.name=e,this.traits=t}}class s extends i{constructor(e,t,r){super(e,t),this.name=e,this.traits=t,this.valueSchema=r,this.symbol=s.symbol}static[Symbol.hasInstance](e){let t=s.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===s.symbol}}s.symbol=Symbol.for("@smithy/core/schema::ListSchema");class o extends i{constructor(e,t,r,n){super(e,t),this.name=e,this.traits=t,this.keySchema=r,this.valueSchema=n,this.symbol=o.symbol}static[Symbol.hasInstance](e){let t=o.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===o.symbol}}o.symbol=Symbol.for("@smithy/core/schema::MapSchema");class a extends i{constructor(e,t,r,n){super(e,t),this.name=e,this.traits=t,this.memberNames=r,this.memberList=n,this.symbol=a.symbol,this.members={};for(let e=0;e<r.length;++e)this.members[r[e]]=Array.isArray(n[e])?n[e]:[n[e],0]}static[Symbol.hasInstance](e){let t=a.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===a.symbol}}a.symbol=Symbol.for("@smithy/core/schema::StructureSchema");class u extends a{constructor(e,t,r,n,i){super(e,t,r,n),this.name=e,this.traits=t,this.memberNames=r,this.memberList=n,this.ctor=i,this.symbol=u.symbol}static[Symbol.hasInstance](e){let t=u.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===u.symbol}}u.symbol=Symbol.for("@smithy/core/schema::ErrorSchema");let c=e=>"function"==typeof e?e():e,l={BLOB:21,STREAMING_BLOB:42,BOOLEAN:2,STRING:0,NUMERIC:1,BIG_INTEGER:17,BIG_DECIMAL:19,DOCUMENT:15,TIMESTAMP_DEFAULT:4,TIMESTAMP_DATE_TIME:5,TIMESTAMP_HTTP_DATE:6,TIMESTAMP_EPOCH_SECONDS:7,LIST_MODIFIER:64,MAP_MODIFIER:128};class f extends i{constructor(e,t,r){super(e,r),this.name=e,this.schemaRef=t,this.traits=r,this.symbol=f.symbol}static[Symbol.hasInstance](e){let t=f.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===f.symbol}}f.symbol=Symbol.for("@smithy/core/schema::SimpleSchema");class d{constructor(e,t){this.ref=e,this.memberName=t,this.symbol=d.symbol;let r=[],n=e,i=e;for(this._isMemberSchema=!1;Array.isArray(n);)r.push(n[1]),i=c(n=n[0]),this._isMemberSchema=!0;if(r.length>0){this.memberTraits={};for(let e=r.length-1;e>=0;--e){let t=r[e];Object.assign(this.memberTraits,d.translateTraits(t))}}else this.memberTraits=0;if(i instanceof d){this.name=i.name,this.traits=i.traits,this._isMemberSchema=i._isMemberSchema,this.schema=i.schema,this.memberTraits=Object.assign({},i.getMemberTraits(),this.getMemberTraits()),this.normalizedTraits=void 0,this.ref=i.ref,this.memberName=t??i.memberName;return}if(this.schema=c(i),this.schema&&"object"==typeof this.schema?this.traits=this.schema?.traits??{}:this.traits=0,this.name=("object"==typeof this.schema?this.schema?.name:void 0)??this.memberName??this.getSchemaName(),this._isMemberSchema&&!t)throw Error(`@smithy/core/schema - NormalizedSchema member schema ${this.getName(!0)} must initialize with memberName argument.`)}static[Symbol.hasInstance](e){let t=d.prototype.isPrototypeOf(e);return t||"object"!=typeof e||null===e?t:e.symbol===d.symbol}static of(e,t){return e instanceof d?e:new d(e,t)}static translateTraits(e){if("object"==typeof e)return e;let t={};return(1&(e|=0))==1&&(t.httpLabel=1),(e>>1&1)==1&&(t.idempotent=1),(e>>2&1)==1&&(t.idempotencyToken=1),(e>>3&1)==1&&(t.sensitive=1),(e>>4&1)==1&&(t.httpPayload=1),(e>>5&1)==1&&(t.httpResponseCode=1),(e>>6&1)==1&&(t.httpQueryParams=1),t}static memberFrom(e,t){return e instanceof d?(e.memberName=t,e._isMemberSchema=!0,e):new d(e,t)}getSchema(){return this.schema instanceof d?this.schema=this.schema.getSchema():this.schema instanceof f?c(this.schema.schemaRef):c(this.schema)}getName(e=!1){return!e&&this.name&&this.name.includes("#")?this.name.split("#")[1]:this.name||void 0}getMemberName(){if(!this.isMemberSchema())throw Error(`@smithy/core/schema - cannot get member name on non-member schema: ${this.getName(!0)}`);return this.memberName}isMemberSchema(){return this._isMemberSchema}isUnitSchema(){return"unit"===this.getSchema()}isListSchema(){let e=this.getSchema();return"number"==typeof e?e>=l.LIST_MODIFIER&&e<l.MAP_MODIFIER:e instanceof s}isMapSchema(){let e=this.getSchema();return"number"==typeof e?e>=l.MAP_MODIFIER&&e<=255:e instanceof o}isDocumentSchema(){return this.getSchema()===l.DOCUMENT}isStructSchema(){let e=this.getSchema();return null!==e&&"object"==typeof e&&"members"in e||e instanceof a}isBlobSchema(){return this.getSchema()===l.BLOB||this.getSchema()===l.STREAMING_BLOB}isTimestampSchema(){let e=this.getSchema();return"number"==typeof e&&e>=l.TIMESTAMP_DEFAULT&&e<=l.TIMESTAMP_EPOCH_SECONDS}isStringSchema(){return this.getSchema()===l.STRING}isBooleanSchema(){return this.getSchema()===l.BOOLEAN}isNumericSchema(){return this.getSchema()===l.NUMERIC}isBigIntegerSchema(){return this.getSchema()===l.BIG_INTEGER}isBigDecimalSchema(){return this.getSchema()===l.BIG_DECIMAL}isStreaming(){return!!this.getMergedTraits().streaming||this.getSchema()===l.STREAMING_BLOB}getMergedTraits(){return this.normalizedTraits||(this.normalizedTraits={...this.getOwnTraits(),...this.getMemberTraits()}),this.normalizedTraits}getMemberTraits(){return d.translateTraits(this.memberTraits)}getOwnTraits(){return d.translateTraits(this.traits)}getKeySchema(){if(this.isDocumentSchema())return d.memberFrom([l.DOCUMENT,0],"key");if(!this.isMapSchema())throw Error(`@smithy/core/schema - cannot get key schema for non-map schema: ${this.getName(!0)}`);let e=this.getSchema();return"number"==typeof e?d.memberFrom([63&e,0],"key"):d.memberFrom([e.keySchema,0],"key")}getValueSchema(){let e=this.getSchema();if("number"==typeof e){if(this.isMapSchema())return d.memberFrom([63&e,0],"value");if(this.isListSchema())return d.memberFrom([63&e,0],"member")}if(e&&"object"==typeof e){if(this.isStructSchema())throw Error(`cannot call getValueSchema() with StructureSchema ${this.getName(!0)}`);if("valueSchema"in e){if(this.isMapSchema())return d.memberFrom([e.valueSchema,0],"value");if(this.isListSchema())return d.memberFrom([e.valueSchema,0],"member")}}if(this.isDocumentSchema())return d.memberFrom([l.DOCUMENT,0],"value");throw Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a value member.`)}getMemberSchema(e){if(this.isStructSchema()){let t=this.getSchema();if(!(e in t.members))throw Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a member with name=${e}.`);return d.memberFrom(t.members[e],e)}if(this.isDocumentSchema())return d.memberFrom([l.DOCUMENT,0],e);throw Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have members.`)}getMemberSchemas(){let{schema:e}=this;if(!e||"object"!=typeof e)return{};if("members"in e){let t={};for(let r of e.memberNames)t[r]=this.getMemberSchema(r);return t}return{}}*structIterator(){if(this.isUnitSchema())return;if(!this.isStructSchema())throw Error("@smithy/core/schema - cannot acquire structIterator on non-struct schema.");let e=this.getSchema();for(let t=0;t<e.memberNames.length;++t)yield[e.memberNames[t],d.memberFrom([e.memberList[t],0],e.memberNames[t])]}getSchemaName(){let e=this.getSchema();if("number"==typeof e){let t=63&e,r=Object.entries(l).find(([,e])=>e===t)?.[0]??"Unknown";switch(192&e){case l.MAP_MODIFIER:return`${r}Map`;case l.LIST_MODIFIER:return`${r}List`;case 0:return r}}return"Unknown"}}d.symbol=Symbol.for("@smithy/core/schema::NormalizedSchema")},13516:function(e,t,r){"use strict";r.d(t,{CE:function(){return d},Wh:function(){return h},pY:function(){return p},gx:function(){return n},aH:function(){return x}}),r(68383);let n=e=>{switch(e){case"true":return!0;case"false":return!1;default:throw Error(`Unable to parse boolean value "${e}"`)}},i=e=>{if(null!=e){if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return String(t)!==String(e)&&S.warn(w(`Expected number but observed string: ${e}`)),t}if("number"==typeof e)return e;throw TypeError(`Expected number, got ${typeof e}: ${e}`)}},s=Math.ceil(17014118346046923e22*(2-11920928955078125e-23)),o=e=>{let t=i(e);if(void 0!==t&&!Number.isNaN(t)&&t!==1/0&&t!==-1/0&&Math.abs(t)>s)throw TypeError(`Expected 32-bit float, got ${e}`);return t},a=e=>{if(null!=e){if(Number.isInteger(e)&&!Number.isNaN(e))return e;throw TypeError(`Expected integer, got ${typeof e}: ${e}`)}},u=e=>l(e,16),c=e=>l(e,8),l=(e,t)=>{let r=a(e);if(void 0!==r&&f(r,t)!==r)throw TypeError(`Expected ${t}-bit integer, got ${e}`);return r},f=(e,t)=>{switch(t){case 32:return Int32Array.of(e)[0];case 16:return Int16Array.of(e)[0];case 8:return Int8Array.of(e)[0]}},d=(e,t)=>{if(null==e){if(t)throw TypeError(`Expected a non-null value for ${t}`);throw TypeError("Expected a non-null value")}return e},h=e=>{if(null==e)return;if("object"==typeof e&&!Array.isArray(e))return e;let t=Array.isArray(e)?"array":typeof e;throw TypeError(`Expected object, got ${t}: ${e}`)},p=e=>{if(null!=e){if("string"==typeof e)return e;if(["boolean","number","bigint"].includes(typeof e))return S.warn(w(`Expected string, got ${typeof e}: ${e}`)),String(e);throw TypeError(`Expected string, got ${typeof e}: ${e}`)}},g=e=>"string"==typeof e?o(y(e)):o(e),m=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,y=e=>{let t=e.match(m);if(null===t||t[0].length!==e.length)throw TypeError("Expected real number, got implicit NaN");return parseFloat(e)},b=e=>"string"==typeof e?u(y(e)):u(e),v=e=>"string"==typeof e?c(y(e)):c(e),w=e=>String(TypeError(e).stack||e).split("\n").slice(0,5).filter(e=>!e.includes("stackTraceWarning")).join("\n"),S={warn:console.warn},E=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],A=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),x=e=>{if(null==e)return;if("string"!=typeof e)throw TypeError("RFC-3339 date-times must be expressed as strings");let t=A.exec(e);if(!t)throw TypeError("Invalid RFC-3339 date-time value");let[r,n,i,s,o,a,u,c,l]=t,f=R(b(C(n)),I(i,"month",1,12),I(s,"day",1,31),{hours:o,minutes:a,seconds:u,fractionalMilliseconds:c});return"Z"!=l.toUpperCase()&&f.setTime(f.getTime()-O(l)),f},R=(e,t,r,n)=>{let i=t-1;return T(e,i,r),new Date(Date.UTC(e,i,r,I(n.hours,"hour",0,23),I(n.minutes,"minute",0,59),I(n.seconds,"seconds",0,60),M(n.fractionalMilliseconds)))},k=[31,28,31,30,31,30,31,31,30,31,30,31],T=(e,t,r)=>{let n=k[t];if(1===t&&P(e)&&(n=29),r>n)throw TypeError(`Invalid day for ${E[t]} in ${e}: ${r}`)},P=e=>e%4==0&&(e%100!=0||e%400==0),I=(e,t,r,n)=>{let i=v(C(e));if(i<r||i>n)throw TypeError(`${t} must be between ${r} and ${n}, inclusive`);return i},M=e=>null==e?0:1e3*g("0."+e),O=e=>{let t=e[0],r=1;if("+"==t)r=1;else if("-"==t)r=-1;else throw TypeError(`Offset direction, ${t}, must be "+" or "-"`);return r*(60*Number(e.substring(1,3))+Number(e.substring(4,6)))*6e4},C=e=>{let t=0;for(;t<e.length-1&&"0"===e.charAt(t);)t++;return 0===t?e:e.slice(t)},N=function(e){return Object.assign(new String(e),{deserializeJSON:()=>JSON.parse(String(e)),toString:()=>String(e),toJSON:()=>String(e)})};N.from=e=>e&&"object"==typeof e&&(e instanceof N||"deserializeJSON"in e)?e:"string"==typeof e||Object.getPrototypeOf(e)===String.prototype?N(String(e)):N(JSON.stringify(e)),N.fromObject=N.from},74027:function(e,t,r){"use strict";r.d(t,{BX:function(){return a},CF:function(){return c}});var n=r(2171),i=r(98175);function s(e,t){return new Request(e,t)}let o={supported:void 0};class a{static create(e){return"function"==typeof e?.handle?e:new a(e)}constructor(e){"function"==typeof e?this.configProvider=e().then(e=>e||{}):(this.config=e??{},this.configProvider=Promise.resolve(this.config)),void 0===o.supported&&(o.supported=!!("undefined"!=typeof Request&&"keepalive"in s("https://[::1]")))}destroy(){}async handle(e,{abortSignal:t,requestTimeout:r}={}){this.config||(this.config=await this.configProvider);let a=r??this.config.requestTimeout,u=!0===this.config.keepAlive,c=this.config.credentials;if(t?.aborted){let e=Error("Request aborted");return e.name="AbortError",Promise.reject(e)}let l=e.path,f=function(e){let t=[];for(let r of Object.keys(e).sort()){let n=e[r];if(r=(0,i.i)(r),Array.isArray(n))for(let e=0,s=n.length;e<s;e++)t.push(`${r}=${(0,i.i)(n[e])}`);else{let e=r;(n||"string"==typeof n)&&(e+=`=${(0,i.i)(n)}`),t.push(e)}}return t.join("&")}(e.query||{});f&&(l+=`?${f}`),e.fragment&&(l+=`#${e.fragment}`);let d="";if(null!=e.username||null!=e.password){let t=e.username??"",r=e.password??"";d=`${t}:${r}@`}let{port:h,method:p}=e,g=`${e.protocol}//${d}${e.hostname}${h?`:${h}`:""}${l}`,m="GET"===p||"HEAD"===p?void 0:e.body,y={body:m,headers:new Headers(e.headers),method:p,credentials:c};this.config?.cache&&(y.cache=this.config.cache),m&&(y.duplex="half"),"undefined"!=typeof AbortController&&(y.signal=t),o.supported&&(y.keepalive=u),"function"==typeof this.config.requestInit&&Object.assign(y,this.config.requestInit(e));let b=()=>{},v=[fetch(s(g,y)).then(e=>{let t=e.headers,r={};for(let e of t.entries())r[e[0]]=e[1];return void 0!=e.body?{response:new n.Zn({headers:r,reason:e.statusText,statusCode:e.status,body:e.body})}:e.blob().then(t=>({response:new n.Zn({headers:r,reason:e.statusText,statusCode:e.status,body:t})}))}),function(e=0){return new Promise((t,r)=>{e&&setTimeout(()=>{let t=Error(`Request did not complete within ${e} ms`);t.name="TimeoutError",r(t)},e)})}(a)];return t&&v.push(new Promise((e,r)=>{let n=()=>{let e=Error("Request aborted");e.name="AbortError",r(e)};"function"==typeof t.addEventListener?(t.addEventListener("abort",n,{once:!0}),b=()=>t.removeEventListener("abort",n)):t.onabort=n})),Promise.race(v).finally(b)}updateHttpClientConfig(e,t){this.config=void 0,this.configProvider=this.configProvider.then(r=>(r[e]=t,r))}httpHandlerConfigs(){return this.config??{}}}var u=r(306);let c=async e=>"function"==typeof Blob&&e instanceof Blob||e.constructor?.name==="Blob"?void 0!==Blob.prototype.arrayBuffer?new Uint8Array(await e.arrayBuffer()):l(e):f(e);async function l(e){let t=await new Promise((t,r)=>{let n=new FileReader;n.onloadend=()=>{if(2!==n.readyState)return r(Error("Reader aborted too early"));let e=n.result??"",i=e.indexOf(","),s=i>-1?i+1:e.length;t(e.substring(s))},n.onabort=()=>r(Error("Read aborted")),n.onerror=()=>r(n.error),n.readAsDataURL(e)});return new Uint8Array((0,u.G)(t))}async function f(e){let t=[],r=e.getReader(),n=!1,i=0;for(;!n;){let{done:e,value:s}=await r.read();s&&(t.push(s),i+=s.length),n=e}let s=new Uint8Array(i),o=0;for(let e of t)s.set(e,o),o+=e.length;return s}},65484:function(e,t,r){"use strict";r.d(t,{a3:function(){return v},uW:function(){return w},Xr:function(){return p}});let n=async e=>{let t=e?.Bucket||"";if("string"==typeof e.Bucket&&(e.Bucket=t.replace(/#/g,encodeURIComponent("#")).replace(/\?/g,encodeURIComponent("?"))),u(t)){if(!0===e.ForcePathStyle)throw Error("Path-style addressing cannot be used with ARN buckets")}else a(t)&&(-1===t.indexOf(".")||String(e.Endpoint).startsWith("http:"))&&t.toLowerCase()===t&&!(t.length<3)||(e.ForcePathStyle=!0);return e.DisableMultiRegionAccessPoints&&(e.disableMultiRegionAccessPoints=!0,e.DisableMRAP=!0),e},i=/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/,s=/(\d+\.){3}\d+/,o=/\.\./,a=e=>i.test(e)&&!s.test(e)&&!o.test(e),u=e=>{let[t,r,n,,,i]=e.split(":"),s="arn"===t&&e.split(":").length>=6,o=!!(s&&r&&n&&i);if(s&&!o)throw Error(`Invalid ARN: ${e} was an invalid ARN.`);return o},c=(e,t,r)=>{let n=async()=>{let n=r[e]??r[t];return"function"==typeof n?n():n};return"credentialScope"===e||"CredentialScope"===t?async()=>{let e="function"==typeof r.credentials?await r.credentials():r.credentials;return e?.credentialScope??e?.CredentialScope}:"accountId"===e||"AccountId"===t?async()=>{let e="function"==typeof r.credentials?await r.credentials():r.credentials;return e?.accountId??e?.AccountId}:"endpoint"===e||"endpoint"===t?async()=>{if(!1===r.isCustomEndpoint)return;let e=await n();if(e&&"object"==typeof e){if("url"in e)return e.url.href;if("hostname"in e){let{protocol:t,hostname:r,port:n,path:i}=e;return`${t}//${r}${n?":"+n:""}${i}`}}return e}:n},l=async e=>void 0;var f=r(87121);let d=e=>"object"==typeof e?"url"in e?(0,f.e)(e.url):e:(0,f.e)(e),h=async(e,t,r,n)=>{if(!r.isCustomEndpoint){let e;(e=r.serviceConfiguredEndpoint?await r.serviceConfiguredEndpoint():await l(r.serviceId))&&(r.endpoint=()=>Promise.resolve(d(e)))}let i=await p(e,t,r);if("function"!=typeof r.endpointProvider)throw Error("config.endpointProvider is not set.");return r.endpointProvider(i,n)},p=async(e,t,r)=>{let i={},s=t?.getEndpointParameterInstructions?.()||{};for(let[t,n]of Object.entries(s))switch(n.type){case"staticContextParams":i[t]=n.value;break;case"contextParams":i[t]=e[n.name];break;case"clientContextParams":case"builtInParams":i[t]=await c(n.name,t,r)();break;case"operationContextParams":i[t]=n.get(e);break;default:throw Error("Unrecognized endpoint parameter instruction: "+JSON.stringify(n))}return 0===Object.keys(s).length&&Object.assign(i,r),"s3"===String(r.serviceId).toLowerCase()&&await n(i),i};var g=r(57201),m=r(95225);let y=({config:e,instructions:t})=>(r,n)=>async i=>{e.isCustomEndpoint&&(0,g.hr)(n,"ENDPOINT_OVERRIDE","N");let s=await h(i.input,{getEndpointParameterInstructions:()=>t},{...e},n);n.endpointV2=s,n.authSchemes=s.properties?.authSchemes;let o=n.authSchemes?.[0];if(o){n.signing_region=o.signingRegion,n.signing_service=o.signingName;let e=(0,m.J)(n),t=e?.selectedHttpAuthScheme?.httpAuthOption;t&&(t.signingProperties=Object.assign(t.signingProperties||{},{signing_region:o.signingRegion,signingRegion:o.signingRegion,signing_service:o.signingName,signingName:o.signingName,signingRegionSet:o.signingRegionSet},o.properties))}return r({...i})},b={step:"serialize",tags:["ENDPOINT_PARAMETERS","ENDPOINT_V2","ENDPOINT"],name:"endpointV2Middleware",override:!0,relation:"before",toMiddleware:r(86090).JK.name},v=(e,t)=>({applyToStack:r=>{r.addRelativeTo(y({config:e,instructions:t}),b)}}),w=e=>{let t;let r=e.tls??!0,{endpoint:n,useDualstackEndpoint:i,useFipsEndpoint:s}=e,o=Object.assign(e,{endpoint:null!=n?async()=>d(await (0,m.$)(n)()):void 0,tls:r,isCustomEndpoint:!!n,useDualstackEndpoint:(0,m.$)(i??!1),useFipsEndpoint:(0,m.$)(s??!1)});return o.serviceConfiguredEndpoint=async()=>(e.serviceId&&!t&&(t=l(e.serviceId)),t),o}},86090:function(e,t,r){"use strict";r.d(t,{p2:function(){return c},JK:function(){return u}});var n=r(2171);let i=(e,t)=>(r,i)=>async o=>{let{response:a}=await r(o);try{let r=await t(a,e);return{response:a,output:r}}catch(e){if(Object.defineProperty(e,"$response",{value:a}),!("$metadata"in e)){let t="Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.";try{e.message+="\n  "+t}catch(e){i.logger&&i.logger?.constructor?.name!=="NoOpLogger"?i.logger?.warn?.(t):console.warn(t)}void 0!==e.$responseBodyText&&e.$response&&(e.$response.body=e.$responseBodyText);try{if(n.Zn.isInstance(a)){let{headers:t={}}=a,r=Object.entries(t);e.$metadata={httpStatusCode:a.statusCode,requestId:s(/^x-[\w-]+-request-?id$/,r),extendedRequestId:s(/^x-[\w-]+-id-2$/,r),cfId:s(/^x-[\w-]+-cf-id$/,r)}}}catch(e){}}throw e}},s=(e,t)=>(t.find(([t])=>t.match(e))||[void 0,void 0])[1],o=(e,t)=>(r,n)=>async i=>{let s=n.endpointV2?.url&&e.urlParser?async()=>e.urlParser(n.endpointV2.url):e.endpoint;if(!s)throw Error("No valid endpoint provider available.");let o=await t(i.input,{...e,endpoint:s});return r({...i,request:o})},a={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},u={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function c(e,t,r){return{applyToStack:n=>{n.add(i(e,r),a),n.add(o(e,t),u)}}}},2171:function(e,t,r){"use strict";r.d(t,{aW:function(){return s},Zn:function(){return o},cA:function(){return n},AO:function(){return i}});let n=e=>({setHttpHandler(t){e.httpHandler=t},httpHandler:()=>e.httpHandler,updateHttpClientConfig(t,r){e.httpHandler?.updateHttpClientConfig(t,r)},httpHandlerConfigs:()=>e.httpHandler.httpHandlerConfigs()}),i=e=>({httpHandler:e.httpHandler()});r(23017);class s{constructor(e){this.method=e.method||"GET",this.hostname=e.hostname||"localhost",this.port=e.port,this.query=e.query||{},this.headers=e.headers||{},this.body=e.body,this.protocol=e.protocol?":"!==e.protocol.slice(-1)?`${e.protocol}:`:e.protocol:"https:",this.path=e.path?"/"!==e.path.charAt(0)?`/${e.path}`:e.path:"/",this.username=e.username,this.password=e.password,this.fragment=e.fragment}static clone(e){var t;let r=new s({...e,headers:{...e.headers}});return r.query&&(r.query=Object.keys(t=r.query).reduce((e,r)=>{let n=t[r];return{...e,[r]:Array.isArray(n)?[...n]:n}},{})),r}static isInstance(e){return!!e&&"method"in e&&"protocol"in e&&"hostname"in e&&"path"in e&&"object"==typeof e.query&&"object"==typeof e.headers}clone(){return s.clone(this)}}class o{constructor(e){this.statusCode=e.statusCode,this.reason=e.reason,this.headers=e.headers||{},this.body=e.body}static isInstance(e){return!!e&&"number"==typeof e.statusCode&&"object"==typeof e.headers}}},34819:function(e,t,r){"use strict";r.d(t,{L1:function(){return z},q4:function(){return V}});var n,i,s=r(73663),o=r(99492);let a=e=>"string"==typeof e?(0,o.$)(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e),u="X-Amz-Date",c="X-Amz-Signature",l="X-Amz-Security-Token",f="authorization",d=u.toLowerCase(),h=[f,d,"date"],p=c.toLowerCase(),g="x-amz-content-sha256",m=l.toLowerCase(),y={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},b=/^proxy-/,v=/^sec-/,w="AWS4-HMAC-SHA256",S="aws4_request",E={},A=[],x=(e,t,r)=>`${e}/${t}/${r}/${S}`,R=async(e,t,r,n,i)=>{let o=await k(e,t.secretAccessKey,t.accessKeyId),a=`${r}:${n}:${i}:${(0,s.N)(o)}:${t.sessionToken}`;if(a in E)return E[a];for(A.push(a);A.length>50;)delete E[A.shift()];let u=`AWS4${t.secretAccessKey}`;for(let t of[r,n,i,S])u=await k(e,u,t);return E[a]=u},k=(e,t,r)=>{let n=new e(t);return n.update(a(r)),n.digest()},T=({headers:e},t,r)=>{let n={};for(let i of Object.keys(e).sort()){if(void 0==e[i])continue;let s=i.toLowerCase();(!(s in y||t?.has(s)||b.test(s)||v.test(s))||r&&(!r||r.has(s)))&&(n[s]=e[i].trim().replace(/\s+/g," "))}return n},P=e=>"function"==typeof ArrayBuffer&&e instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(e),I=async({headers:e,body:t},r)=>{for(let t of Object.keys(e))if(t.toLowerCase()===g)return e[t];if(void 0==t)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";if("string"==typeof t||ArrayBuffer.isView(t)||P(t)){let e=new r;return e.update(a(t)),(0,s.N)(await e.digest())}return"UNSIGNED-PAYLOAD"};class M{format(e){let t=[];for(let r of Object.keys(e)){let n=(0,o.$)(r);t.push(Uint8Array.from([n.byteLength]),n,this.formatHeaderValue(e[r]))}let r=new Uint8Array(t.reduce((e,t)=>e+t.byteLength,0)),n=0;for(let e of t)r.set(e,n),n+=e.byteLength;return r}formatHeaderValue(e){switch(e.type){case"boolean":return Uint8Array.from([e.value?0:1]);case"byte":return Uint8Array.from([2,e.value]);case"short":let t=new DataView(new ArrayBuffer(3));return t.setUint8(0,3),t.setInt16(1,e.value,!1),new Uint8Array(t.buffer);case"integer":let r=new DataView(new ArrayBuffer(5));return r.setUint8(0,4),r.setInt32(1,e.value,!1),new Uint8Array(r.buffer);case"long":let n=new Uint8Array(9);return n[0]=5,n.set(e.value.bytes,1),n;case"binary":let i=new DataView(new ArrayBuffer(3+e.value.byteLength));i.setUint8(0,6),i.setUint16(1,e.value.byteLength,!1);let a=new Uint8Array(i.buffer);return a.set(e.value,3),a;case"string":let u=(0,o.$)(e.value),c=new DataView(new ArrayBuffer(3+u.byteLength));c.setUint8(0,7),c.setUint16(1,u.byteLength,!1);let l=new Uint8Array(c.buffer);return l.set(u,3),l;case"timestamp":let f=new Uint8Array(9);return f[0]=8,f.set(C.fromNumber(e.value.valueOf()).bytes,1),f;case"uuid":if(!O.test(e.value))throw Error(`Invalid UUID received: ${e.value}`);let d=new Uint8Array(17);return d[0]=9,d.set((0,s.H)(e.value.replace(/\-/g,"")),1),d}}}(n=i||(i={}))[n.boolTrue=0]="boolTrue",n[n.boolFalse=1]="boolFalse",n[n.byte=2]="byte",n[n.short=3]="short",n[n.integer=4]="integer",n[n.long=5]="long",n[n.byteArray=6]="byteArray",n[n.string=7]="string",n[n.timestamp=8]="timestamp",n[n.uuid=9]="uuid";let O=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;class C{constructor(e){if(this.bytes=e,8!==e.byteLength)throw Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(e){if(e>0x7fffffffffffffff||e<-0x8000000000000000)throw Error(`${e} is too large (or, if negative, too small) to represent as an Int64`);let t=new Uint8Array(8);for(let r=7,n=Math.abs(Math.round(e));r>-1&&n>0;r--,n/=256)t[r]=n;return e<0&&N(t),new C(t)}valueOf(){let e=this.bytes.slice(0),t=128&e[0];return t&&N(e),parseInt((0,s.N)(e),16)*(t?-1:1)}toString(){return String(this.valueOf())}}function N(e){for(let t=0;t<8;t++)e[t]^=255;for(let t=7;t>-1&&(e[t]++,0===e[t]);t--);}let _=(e,t)=>{for(let r of(e=e.toLowerCase(),Object.keys(t)))if(e===r.toLowerCase())return!0;return!1};var U=r(2171);let $=(e,t={})=>{let{headers:r,query:n={}}=U.aW.clone(e);for(let e of Object.keys(r)){let i=e.toLowerCase();("x-amz-"===i.slice(0,6)&&!t.unhoistableHeaders?.has(i)||t.hoistableHeaders?.has(i))&&(n[e]=r[e],delete r[e])}return{...e,headers:r,query:n}},B=e=>{for(let t of Object.keys((e=U.aW.clone(e)).headers))h.indexOf(t.toLowerCase())>-1&&delete e.headers[t];return e};var L=r(95225),D=r(98175);let F=({query:e={}})=>{let t=[],r={};for(let n of Object.keys(e)){if(n.toLowerCase()===p)continue;let i=(0,D.i)(n);t.push(i);let s=e[n];"string"==typeof s?r[i]=`${i}=${(0,D.i)(s)}`:Array.isArray(s)&&(r[i]=s.slice(0).reduce((e,t)=>e.concat([`${i}=${(0,D.i)(t)}`]),[]).sort().join("&"))}return t.sort().map(e=>r[e]).filter(e=>e).join("&")},j=e=>H(e).toISOString().replace(/\.\d{3}Z$/,"Z"),H=e=>"number"==typeof e?new Date(1e3*e):"string"==typeof e?new Date(Number(e)?1e3*Number(e):e):e;class q{constructor({applyChecksum:e,credentials:t,region:r,service:n,sha256:i,uriEscapePath:s=!0}){this.service=n,this.sha256=i,this.uriEscapePath=s,this.applyChecksum="boolean"!=typeof e||e,this.regionProvider=(0,L.$)(r),this.credentialProvider=(0,L.$)(t)}createCanonicalRequest(e,t,r){let n=Object.keys(t).sort();return`${e.method}
${this.getCanonicalPath(e)}
${F(e)}
${n.map(e=>`${e}:${t[e]}`).join("\n")}

${n.join(";")}
${r}`}async createStringToSign(e,t,r,n){let i=new this.sha256;i.update(a(r));let o=await i.digest();return`${n}
${e}
${t}
${(0,s.N)(o)}`}getCanonicalPath({path:e}){if(this.uriEscapePath){let t=[];for(let r of e.split("/"))r?.length!==0&&"."!==r&&(".."===r?t.pop():t.push(r));let r=`${e?.startsWith("/")?"/":""}${t.join("/")}${t.length>0&&e?.endsWith("/")?"/":""}`;return(0,D.i)(r).replace(/%2F/g,"/")}return e}validateResolvedCredentials(e){if("object"!=typeof e||"string"!=typeof e.accessKeyId||"string"!=typeof e.secretAccessKey)throw Error("Resolved credential object is not valid")}formatDate(e){let t=j(e).replace(/[\-:]/g,"");return{longDate:t,shortDate:t.slice(0,8)}}getCanonicalHeaderList(e){return Object.keys(e).sort().join(";")}}class z extends q{constructor({applyChecksum:e,credentials:t,region:r,service:n,sha256:i,uriEscapePath:s=!0}){super({applyChecksum:e,credentials:t,region:r,service:n,sha256:i,uriEscapePath:s}),this.headerFormatter=new M}async presign(e,t={}){let{signingDate:r=new Date,expiresIn:n=3600,unsignableHeaders:i,unhoistableHeaders:s,signableHeaders:o,hoistableHeaders:a,signingRegion:f,signingService:d}=t,h=await this.credentialProvider();this.validateResolvedCredentials(h);let p=f??await this.regionProvider(),{longDate:g,shortDate:m}=this.formatDate(r);if(n>604800)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let y=x(m,p,d??this.service),b=$(B(e),{unhoistableHeaders:s,hoistableHeaders:a});h.sessionToken&&(b.query[l]=h.sessionToken),b.query["X-Amz-Algorithm"]=w,b.query["X-Amz-Credential"]=`${h.accessKeyId}/${y}`,b.query[u]=g,b.query["X-Amz-Expires"]=n.toString(10);let v=T(b,i,o);return b.query["X-Amz-SignedHeaders"]=this.getCanonicalHeaderList(v),b.query[c]=await this.getSignature(g,y,this.getSigningKey(h,p,m,d),this.createCanonicalRequest(b,v,await I(e,this.sha256))),b}async sign(e,t){return"string"==typeof e?this.signString(e,t):e.headers&&e.payload?this.signEvent(e,t):e.message?this.signMessage(e,t):this.signRequest(e,t)}async signEvent({headers:e,payload:t},{signingDate:r=new Date,priorSignature:n,signingRegion:i,signingService:o}){let a=i??await this.regionProvider(),{shortDate:u,longDate:c}=this.formatDate(r),l=x(u,a,o??this.service),f=await I({headers:{},body:t},this.sha256),d=new this.sha256;d.update(e);let h=["AWS4-HMAC-SHA256-PAYLOAD",c,l,n,(0,s.N)(await d.digest()),f].join("\n");return this.signString(h,{signingDate:r,signingRegion:a,signingService:o})}async signMessage(e,{signingDate:t=new Date,signingRegion:r,signingService:n}){return this.signEvent({headers:this.headerFormatter.format(e.message.headers),payload:e.message.body},{signingDate:t,signingRegion:r,signingService:n,priorSignature:e.priorSignature}).then(t=>({message:e.message,signature:t}))}async signString(e,{signingDate:t=new Date,signingRegion:r,signingService:n}={}){let i=await this.credentialProvider();this.validateResolvedCredentials(i);let o=r??await this.regionProvider(),{shortDate:u}=this.formatDate(t),c=new this.sha256(await this.getSigningKey(i,o,u,n));return c.update(a(e)),(0,s.N)(await c.digest())}async signRequest(e,{signingDate:t=new Date,signableHeaders:r,unsignableHeaders:n,signingRegion:i,signingService:s}={}){let o=await this.credentialProvider();this.validateResolvedCredentials(o);let a=i??await this.regionProvider(),u=B(e),{longDate:c,shortDate:l}=this.formatDate(t),h=x(l,a,s??this.service);u.headers[d]=c,o.sessionToken&&(u.headers[m]=o.sessionToken);let p=await I(u,this.sha256);!_(g,u.headers)&&this.applyChecksum&&(u.headers[g]=p);let y=T(u,n,r),b=await this.getSignature(c,h,this.getSigningKey(o,a,l,s),this.createCanonicalRequest(u,y,p));return u.headers[f]=`${w} Credential=${o.accessKeyId}/${h}, SignedHeaders=${this.getCanonicalHeaderList(y)}, Signature=${b}`,u}async getSignature(e,t,r,n){let i=await this.createStringToSign(e,t,n,w),o=new this.sha256(await r);return o.update(a(i)),(0,s.N)(await o.digest())}getSigningKey(e,t,r,n){return R(this.sha256,e,r,t,n||this.service)}}let V={SignatureV4a:null}},4598:function(e,t,r){"use strict";r.d(t,{KU:function(){return u},mY:function(){return f},vk:function(){return P},oc:function(){return h},sI:function(){return p},Wg:function(){return c.Wg},to:function(){return g},CE:function(){return _.CE},Wh:function(){return _.Wh},pY:function(){return _.pY},kE:function(){return x},sT:function(){return k},gj:function(){return T},jv:function(){return v},UI:function(){return I},gx:function(){return _.gx},aH:function(){return _.aH},SQ:function(){return R},PC:function(){return y}});let n=(e,t)=>{let r=[];if(e&&r.push(e),t)for(let e of t)r.push(e);return r},i=(e,t)=>`${e||"anonymous"}${t&&t.length>0?` (a.k.a. ${t.join(",")})`:""}`,s=()=>{let e=[],t=[],r=!1,u=new Set,c=e=>e.sort((e,t)=>o[t.step]-o[e.step]||a[t.priority||"normal"]-a[e.priority||"normal"]),l=r=>{let i=!1,s=e=>{let t=n(e.name,e.aliases);if(t.includes(r)){for(let e of(i=!0,t))u.delete(e);return!1}return!0};return e=e.filter(s),t=t.filter(s),i},f=r=>{let i=!1,s=e=>{if(e.middleware===r){for(let t of(i=!0,n(e.name,e.aliases)))u.delete(t);return!1}return!0};return e=e.filter(s),t=t.filter(s),i},d=r=>(e.forEach(e=>{r.add(e.middleware,{...e})}),t.forEach(e=>{r.addRelativeTo(e.middleware,{...e})}),r.identifyOnResolve?.(g.identifyOnResolve()),r),h=e=>{let t=[];return e.before.forEach(e=>{0===e.before.length&&0===e.after.length?t.push(e):t.push(...h(e))}),t.push(e),e.after.reverse().forEach(e=>{0===e.before.length&&0===e.after.length?t.push(e):t.push(...h(e))}),t},p=(r=!1)=>{let s=[],o=[],a={};return e.forEach(e=>{let t={...e,before:[],after:[]};for(let e of n(t.name,t.aliases))a[e]=t;s.push(t)}),t.forEach(e=>{let t={...e,before:[],after:[]};for(let e of n(t.name,t.aliases))a[e]=t;o.push(t)}),o.forEach(e=>{if(e.toMiddleware){let t=a[e.toMiddleware];if(void 0===t){if(r)return;throw Error(`${e.toMiddleware} is not found when adding ${i(e.name,e.aliases)} middleware ${e.relation} ${e.toMiddleware}`)}"after"===e.relation&&t.after.push(e),"before"===e.relation&&t.before.push(e)}}),c(s).map(h).reduce((e,t)=>(e.push(...t),e),[])},g={add:(t,r={})=>{let{name:s,override:o,aliases:a}=r,c={step:"initialize",priority:"normal",middleware:t,...r},l=n(s,a);if(l.length>0){if(l.some(e=>u.has(e))){if(!o)throw Error(`Duplicate middleware name '${i(s,a)}'`);for(let t of l){let r=e.findIndex(e=>e.name===t||e.aliases?.some(e=>e===t));if(-1===r)continue;let n=e[r];if(n.step!==c.step||c.priority!==n.priority)throw Error(`"${i(n.name,n.aliases)}" middleware with ${n.priority} priority in ${n.step} step cannot be overridden by "${i(s,a)}" middleware with ${c.priority} priority in ${c.step} step.`);e.splice(r,1)}}for(let e of l)u.add(e)}e.push(c)},addRelativeTo:(e,r)=>{let{name:s,override:o,aliases:a}=r,c={middleware:e,...r},l=n(s,a);if(l.length>0){if(l.some(e=>u.has(e))){if(!o)throw Error(`Duplicate middleware name '${i(s,a)}'`);for(let e of l){let r=t.findIndex(t=>t.name===e||t.aliases?.some(t=>t===e));if(-1===r)continue;let n=t[r];if(n.toMiddleware!==c.toMiddleware||n.relation!==c.relation)throw Error(`"${i(n.name,n.aliases)}" middleware ${n.relation} "${n.toMiddleware}" middleware cannot be overridden by "${i(s,a)}" middleware ${c.relation} "${c.toMiddleware}" middleware.`);t.splice(r,1)}}for(let e of l)u.add(e)}t.push(c)},clone:()=>d(s()),use:e=>{e.applyToStack(g)},remove:e=>"string"==typeof e?l(e):f(e),removeByTag:r=>{let i=!1,s=e=>{let{tags:t,name:s,aliases:o}=e;if(t&&t.includes(r)){for(let e of n(s,o))u.delete(e);return i=!0,!1}return!0};return e=e.filter(s),t=t.filter(s),i},concat:e=>{let t=d(s());return t.use(e),t.identifyOnResolve(r||t.identifyOnResolve()||(e.identifyOnResolve?.()??!1)),t},applyToStack:d,identify:()=>p(!0).map(e=>{let t=e.step??e.relation+" "+e.toMiddleware;return i(e.name,e.aliases)+" - "+t}),identifyOnResolve:e=>("boolean"==typeof e&&(r=e),r),resolve:(e,t)=>{for(let r of p().map(e=>e.middleware).reverse())e=r(e,t);return r&&console.log(g.identify()),e}};return g},o={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},a={high:3,normal:2,low:1};class u{constructor(e){this.config=e,this.middlewareStack=s()}send(e,t,r){let n;let i="function"!=typeof t?t:void 0,s="function"==typeof t?t:r;if(void 0===i&&!0===this.config.cacheMiddleware){this.handlers||(this.handlers=new WeakMap);let t=this.handlers;t.has(e.constructor)?n=t.get(e.constructor):(n=e.resolveMiddleware(this.middlewareStack,this.config,i),t.set(e.constructor,n))}else delete this.handlers,n=e.resolveMiddleware(this.middlewareStack,this.config,i);if(!s)return n(e).then(e=>e.output);n(e).then(e=>s(null,e.output),e=>s(e)).catch(()=>{})}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}}var c=r(12731),l=r(23017);class f{constructor(){this.middlewareStack=s()}static classBuilder(){return new d}resolveMiddlewareWithContext(e,t,r,{middlewareFn:n,clientName:i,commandName:s,inputFilterSensitiveLog:o,outputFilterSensitiveLog:a,smithyContext:u,additionalContext:c,CommandCtor:f}){for(let i of n.bind(this)(f,e,t,r))this.middlewareStack.use(i);let d=e.concat(this.middlewareStack),{logger:h}=t,p={logger:h,clientName:i,commandName:s,inputFilterSensitiveLog:o,outputFilterSensitiveLog:a,[l.zK]:{commandInstance:this,...u},...c},{requestHandler:g}=t;return d.resolve(e=>g.handle(e.request,r||{}),p)}}class d{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=e=>e,this._outputFilterSensitiveLog=e=>e,this._serializer=null,this._deserializer=null}init(e){this._init=e}ep(e){return this._ep=e,this}m(e){return this._middlewareFn=e,this}s(e,t,r={}){return this._smithyContext={service:e,operation:t,...r},this}c(e={}){return this._additionalContext=e,this}n(e,t){return this._clientName=e,this._commandName=t,this}f(e=e=>e,t=e=>e){return this._inputFilterSensitiveLog=e,this._outputFilterSensitiveLog=t,this}ser(e){return this._serializer=e,this}de(e){return this._deserializer=e,this}sc(e){return this._operationSchema=e,this._smithyContext.operationSchema=e,this}build(){let e;let t=this;return e=class extends f{static getEndpointParameterInstructions(){return t._ep}constructor(...[e]){super(),this.serialize=t._serializer,this.deserialize=t._deserializer,this.input=e??{},t._init(this),this.schema=t._operationSchema}resolveMiddleware(r,n,i){return this.resolveMiddlewareWithContext(r,n,i,{CommandCtor:e,middlewareFn:t._middlewareFn,clientName:t._clientName,commandName:t._commandName,inputFilterSensitiveLog:t._inputFilterSensitiveLog,outputFilterSensitiveLog:t._outputFilterSensitiveLog,smithyContext:t._smithyContext,additionalContext:t._additionalContext})}}}}let h="***SensitiveInformation***";class p extends Error{constructor(e){super(e.message),Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=e.name,this.$fault=e.$fault,this.$metadata=e.$metadata}static isInstance(e){return!!e&&(p.prototype.isPrototypeOf(e)||!!e.$fault&&!!e.$metadata&&("client"===e.$fault||"server"===e.$fault))}static[Symbol.hasInstance](e){return!!e&&(this===p?p.isInstance(e):!!p.isInstance(e)&&(e.name&&this.name?this.prototype.isPrototypeOf(e)||e.name===this.name:this.prototype.isPrototypeOf(e)))}}let g=(e,t={})=>{Object.entries(t).filter(([,e])=>void 0!==e).forEach(([t,r])=>{(void 0==e[t]||""===e[t])&&(e[t]=r)});let r=e.message||e.Message||"UnknownError";return e.message=r,delete e.Message,e},m=({output:e,parsedBody:t,exceptionCtor:r,errorCode:n})=>{let i=b(e),s=i.httpStatusCode?i.httpStatusCode+"":void 0;throw g(new r({name:t?.code||t?.Code||n||s||"UnknownError",$fault:"client",$metadata:i}),t)},y=e=>({output:t,parsedBody:r,errorCode:n})=>{m({output:t,parsedBody:r,exceptionCtor:e,errorCode:n})},b=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),v=e=>{switch(e){case"standard":case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"mobile":return{retryMode:"standard",connectionTimeout:3e4};default:return{}}},w=e=>{let t=[];for(let r in l.f3){let n=l.f3[r];void 0!==e[n]&&t.push({algorithmId:()=>n,checksumConstructor:()=>e[n]})}return{addChecksumAlgorithm(e){t.push(e)},checksumAlgorithms:()=>t}},S=e=>{let t={};return e.checksumAlgorithms().forEach(e=>{t[e.algorithmId()]=e.checksumConstructor()}),t},E=e=>({setRetryStrategy(t){e.retryStrategy=t},retryStrategy:()=>e.retryStrategy}),A=e=>{let t={};return t.retryStrategy=e.retryStrategy(),t},x=e=>Object.assign(w(e),E(e)),R=e=>Object.assign(S(e),A(e)),k=e=>{let t="#text";for(let r in e)e.hasOwnProperty(r)&&void 0!==e[r][t]?e[r]=e[r][t]:"object"==typeof e[r]&&null!==e[r]&&(e[r]=k(e[r]));return e},T=e=>null!=e;class P{trace(){}debug(){}info(){}warn(){}error(){}}function I(e,t,r){let n,i;if(void 0===t&&void 0===r)n={},i=e;else{if(n=e,"function"==typeof t)return M(n,t,i=r);i=t}for(let e of Object.keys(i)){if(!Array.isArray(i[e])){n[e]=i[e];continue}O(n,null,i,e)}return n}let M=(e,t,r)=>I(e,Object.entries(r).reduce((e,[r,n])=>(Array.isArray(n)?e[r]=n:"function"==typeof n?e[r]=[t,n()]:e[r]=[t,n],e),{})),O=(e,t,r,n)=>{if(null!==t){let i=r[n];"function"==typeof i&&(i=[,i]);let[s=C,o=N,a=n]=i;("function"==typeof s&&s(t[a])||"function"!=typeof s&&s)&&(e[n]=o(t[a]));return}let[i,s]=r[n];if("function"==typeof s){let t;let r=void 0===i&&null!=(t=s()),o="function"==typeof i&&!!i(void 0)||"function"!=typeof i&&!!i;r?e[n]=t:o&&(e[n]=s())}else{let t=void 0===i&&null!=s,r="function"==typeof i&&!!i(s)||"function"!=typeof i&&!!i;(t||r)&&(e[n]=s)}},C=e=>null!=e,N=e=>e;var _=r(13516)},23017:function(e,t,r){"use strict";var n,i,s,o,a,u,c,l,f,d,h,p,g,m;r.d(t,{f3:function(){return o},cj:function(){return s},zK:function(){return y}}),(l=n||(n={})).HEADER="header",l.QUERY="query",(f=i||(i={})).HEADER="header",f.QUERY="query",(d=s||(s={})).HTTP="http",d.HTTPS="https",(h=o||(o={})).MD5="md5",h.CRC32="crc32",h.CRC32C="crc32c",h.SHA1="sha1",h.SHA256="sha256",(p=a||(a={}))[p.HEADER=0]="HEADER",p[p.TRAILER=1]="TRAILER";let y="__smithy_context";(g=u||(u={})).PROFILE="profile",g.SSO_SESSION="sso-session",g.SERVICES="services",(m=c||(c={})).HTTP_0_9="http/0.9",m.HTTP_1_0="http/1.0",m.TDS_8_0="tds/8.0"},87121:function(e,t,r){"use strict";r.d(t,{e:function(){return n}});let n=e=>{let t;if("string"==typeof e)return n(new URL(e));let{hostname:r,pathname:i,port:s,protocol:o,search:a}=e;return a&&(t=function(e){let t={};if(e=e.replace(/^\?/,""))for(let r of e.split("&")){let[e,n=null]=r.split("=");e=decodeURIComponent(e),n&&(n=decodeURIComponent(n)),e in t?Array.isArray(t[e])?t[e].push(n):t[e]=[t[e],n]:t[e]=n}return t}(a)),{hostname:r,port:s?parseInt(s):void 0,protocol:o,path:i,query:t}}},306:function(e,t,r){"use strict";r.d(t,{G:function(){return s},s:function(){return a}});let n={},i=Array(64);for(let e=0;e+65<=90;e++){let t=String.fromCharCode(e+65);n[t]=e,i[e]=t}for(let e=0;e+97<=122;e++){let t=String.fromCharCode(e+97),r=e+26;n[t]=r,i[r]=t}for(let e=0;e<10;e++){n[e.toString(10)]=e+52;let t=e.toString(10),r=e+52;n[t]=r,i[r]=t}n["+"]=62,i[62]="+",n["/"]=63,i[63]="/";let s=e=>{let t=e.length/4*3;"=="===e.slice(-2)?t-=2:"="===e.slice(-1)&&t--;let r=new ArrayBuffer(t),i=new DataView(r);for(let t=0;t<e.length;t+=4){let r=0,s=0;for(let i=t,o=t+3;i<=o;i++)if("="!==e[i]){if(!(e[i]in n))throw TypeError(`Invalid character ${e[i]} in base64 string.`);r|=n[e[i]]<<(o-i)*6,s+=6}else r>>=6;let o=t/4*3;r>>=s%8;let a=Math.floor(s/8);for(let e=0;e<a;e++){let t=(a-e-1)*8;i.setUint8(o+e,(r&255<<t)>>t)}}return new Uint8Array(r)};var o=r(99492);function a(e){let t;let r="object"==typeof(t="string"==typeof e?(0,o.$)(e):e)&&"number"==typeof t.length,n="object"==typeof t&&"number"==typeof t.byteOffset&&"number"==typeof t.byteLength;if(!r&&!n)throw Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");let s="";for(let e=0;e<t.length;e+=3){let r=0,n=0;for(let i=e,s=Math.min(e+3,t.length);i<s;i++)r|=t[i]<<(s-i-1)*8,n+=8;let o=Math.ceil(n/6);r<<=6*o-n;for(let e=1;e<=o;e++){let t=(o-e)*6;s+=i[(r&63<<t)>>t]}s+="==".slice(0,4-o)}return s}},32480:function(e,t,r){"use strict";var n,i;r.d(t,{Bt:function(){return n},hu:function(){return s}});let s=(e,t,r)=>{if(t in e){if("true"===e[t])return!0;if("false"===e[t])return!1;throw Error(`Cannot load ${r} "${t}". Expected "true" or "false", got ${e[t]}.`)}};(i=n||(n={})).ENV="env",i.CONFIG="shared config entry"},73663:function(e,t,r){"use strict";r.d(t,{H:function(){return s},N:function(){return o}});let n={},i={};for(let e=0;e<256;e++){let t=e.toString(16).toLowerCase();1===t.length&&(t=`0${t}`),n[e]=t,i[t]=e}function s(e){if(e.length%2!=0)throw Error("Hex encoded strings must have an even number length");let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2){let n=e.slice(r,r+2).toLowerCase();if(n in i)t[r/2]=i[n];else throw Error(`Cannot decode unrecognized sequence ${n} as hexadecimal`)}return t}function o(e){let t="";for(let r=0;r<e.byteLength;r++)t+=n[e[r]];return t}},95225:function(e,t,r){"use strict";r.d(t,{J:function(){return i},$:function(){return s}});var n=r(23017);let i=e=>e[n.zK]||(e[n.zK]={}),s=e=>{if("function"==typeof e)return e;let t=Promise.resolve(e);return()=>t}},75020:function(e,t,r){"use strict";r.d(t,{HE:function(){return o},LN:function(){return a},TS:function(){return u},Ou:function(){return h},vH:function(){return g}});var n=r(306),i=r(21555),s=r(99492);class o extends Uint8Array{static fromString(e,t="utf-8"){if("string"==typeof e)return"base64"===t?o.mutate((0,n.G)(e)):o.mutate((0,s.$)(e));throw Error(`Unsupported conversion from ${typeof e} to Uint8ArrayBlobAdapter.`)}static mutate(e){return Object.setPrototypeOf(e,o.prototype),e}transformToString(e="utf-8"){return function(e,t="utf-8"){return"base64"===t?(0,n.s)(e):(0,i.G)(e)}(this,e)}}"function"==typeof ReadableStream&&ReadableStream,r(50022).lW;let a=(e,t)=>{let{base64Encoder:r,bodyLengthChecker:n,checksumAlgorithmFn:i,checksumLocationName:s,streamHasher:o}=t,a=void 0!==r&&void 0!==n&&void 0!==i&&void 0!==s&&void 0!==o,u=a?o(i,e):void 0,c=e.getReader();return new ReadableStream({async pull(e){let{value:t,done:i}=await c.read();if(i){if(e.enqueue(`0\r
`),a){let t=r(await u);e.enqueue(`${s}:${t}\r
`),e.enqueue(`\r
`)}e.close()}else e.enqueue(`${(n(t)||0).toString(16)}\r
${t}\r
`)}})};async function u(e,t){let r=0,n=[],i=e.getReader(),s=!1;for(;!s;){let{done:e,value:o}=await i.read();if(o&&(n.push(o),r+=o?.byteLength??0),r>=t)break;s=e}i.releaseLock();let o=new Uint8Array(Math.min(t,r)),a=0;for(let e of n){if(e.byteLength>o.byteLength-a){o.set(e.subarray(0,o.byteLength-a),a);break}o.set(e,a),a+=e.length}return o}var c=r(74027),l=r(73663);let f=e=>"function"==typeof ReadableStream&&(e?.constructor?.name===ReadableStream.name||e instanceof ReadableStream),d="The stream has already been transformed.",h=e=>{if(!p(e)&&!f(e)){let t=e?.__proto__?.constructor?.name||e;throw Error(`Unexpected stream implementation, expect Blob or ReadableStream, got ${t}`)}let t=!1,r=async()=>{if(t)throw Error(d);return t=!0,await (0,c.CF)(e)},s=e=>{if("function"!=typeof e.stream)throw Error("Cannot transform payload Blob to web stream. Please make sure the Blob.stream() is polyfilled.\nIf you are using React Native, this API is not yet supported, see: https://react-native.canny.io/feature-requests/p/fetch-streaming-body");return e.stream()};return Object.assign(e,{transformToByteArray:r,transformToString:async e=>{let t=await r();if("base64"===e)return(0,n.s)(t);if("hex"===e)return(0,l.N)(t);if(void 0===e||"utf8"===e||"utf-8"===e)return(0,i.G)(t);if("function"==typeof TextDecoder)return new TextDecoder(e).decode(t);throw Error("TextDecoder is not available, please make sure polyfill is provided.")},transformToWebStream:()=>{if(t)throw Error(d);if(t=!0,p(e))return s(e);if(f(e))return e;throw Error(`Cannot transform payload to web stream, got ${e}`)}})},p=e=>"function"==typeof Blob&&e instanceof Blob;async function g(e){return"function"==typeof e.stream&&(e=e.stream()),e.tee()}},98175:function(e,t,r){"use strict";r.d(t,{i:function(){return n}});let n=e=>encodeURIComponent(e).replace(/[!'()*]/g,i),i=e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`},99492:function(e,t,r){"use strict";r.d(t,{$:function(){return n}});let n=e=>new TextEncoder().encode(e)},21555:function(e,t,r){"use strict";r.d(t,{G:function(){return n}});let n=e=>{if("string"==typeof e)return e;if("object"!=typeof e||"number"!=typeof e.byteOffset||"number"!=typeof e.byteLength)throw Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return new TextDecoder("utf-8").decode(e)}},73493:function(e){var t;t=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t||4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,(function(t){return e[t]}).bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=r(18),i=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map(function(e){return parseInt(e,10)||0});if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map(function(e){return parseInt(e,10)||0});if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var i=e.getVersionPrecision(t),s=e.getVersionPrecision(r),o=Math.max(i,s),a=0,u=e.map([t,r],function(t){var r=o-e.getVersionPrecision(t),n=t+Array(r+1).join(".0");return e.map(n.split("."),function(e){return Array(20-e.length).join("0")+e}).reverse()});for(n&&(a=o-Math.min(i,s)),o-=1;o>=a;){if(u[0][o]>u[1][o])return 1;if(u[0][o]===u[1][o]){if(o===a)return 0;o-=1}else if(u[0][o]<u[1][o])return -1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var i=e[r];if(t(i,r))return i}},e.assign=function(e){for(var t,r,n=arguments.length,i=Array(n>1?n-1:0),s=1;s<n;s++)i[s-1]=arguments[s];if(Object.assign)return Object.assign.apply(Object,[e].concat(i));for(t=0,r=i.length;t<r;t+=1)(function(){var r=i[t];"object"==typeof r&&null!==r&&Object.keys(r).forEach(function(t){e[t]=r[t]})})();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=i,e.exports=t.default},18:function(e,t,r){"use strict";t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(91))&&n.__esModule?n:{default:n},s=r(18),o=function(){function e(){}return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw Error("UserAgent should be a string");return new i.default(e,t)},e.parse=function(e){return new i.default(e).getResult()},function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e,[{key:"BROWSER_MAP",get:function(){return s.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return s.ENGINE_MAP}},{key:"OS_MAP",get:function(){return s.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return s.PLATFORMS_MAP}}]),e}();t.default=o,e.exports=t.default},91:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=u(r(92)),i=u(r(93)),s=u(r(94)),o=u(r(95)),a=u(r(17));function u(e){return e&&e.__esModule?e:{default:e}}var c=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(n.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(i.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(s.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(o.default,function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some(function(t){return e.test(t)});throw Error("Browser's test function is not valid")});return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,i={},s=0;if(Object.keys(e).forEach(function(t){var o=e[t];"string"==typeof o?(i[t]=o,s+=1):"object"==typeof o&&(r[t]=o,n+=1)}),n>0){var o=Object.keys(r),u=a.default.find(o,function(e){return t.isOS(e)});if(u){var c=this.satisfies(r[u]);if(void 0!==c)return c}var l=a.default.find(o,function(e){return t.isPlatform(e)});if(l){var f=this.satisfies(r[l]);if(void 0!==f)return f}}if(s>0){var d=Object.keys(i),h=a.default.find(d,function(e){return t.isBrowser(e,!0)});if(void 0!==h)return this.compareVersion(i[h])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),i=a.default.getBrowserTypeByAlias(n);return t&&i&&(n=i.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(a.default.compareVersions(i,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some(function(e){return t.is(e)})},e}();t.default=c,e.exports=t.default},92:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=/version\/(\d+(\.?_?\d+)+)/i,o=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(t,e),version:i.default.getSecondMatch(t,e)}}}];t.default=o,e.exports=t.default},93:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=r(18),o=[{test:[/Roku\/DVP/],describe:function(e){var t=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:s.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=i.default.getWindowsVersionName(t);return{name:s.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:s.OS_MAP.iOS},r=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=i.default.getMacOSVersionName(t),n={name:s.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:s.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=i.default.getAndroidVersionName(t),n={name:s.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:s.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||i.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:s.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:s.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:s.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.PlayStation4,version:t}}}];t.default=o,e.exports=t.default},94:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=r(18),o=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=i.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:s.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=i.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:s.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:s.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:s.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:s.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:s.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.tv}}}];t.default=o,e.exports=t.default},95:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=r(18),o=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:s.ENGINE_MAP.Blink};var t=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:s.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:s.ENGINE_MAP.Trident},r=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:s.ENGINE_MAP.Presto},r=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:s.ENGINE_MAP.Gecko},r=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:s.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:s.ENGINE_MAP.WebKit},r=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=o,e.exports=t.default}})},e.exports=t()},39763:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(2265),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{color:a="currentColor",size:u=24,strokeWidth:c=2,absoluteStrokeWidth:l,className:f="",children:d,...h}=r;return(0,n.createElement)("svg",{ref:o,...i,width:u,height:u,stroke:a,strokeWidth:l?24*Number(c)/Number(u):c,className:["lucide","lucide-".concat(s(e)),f].join(" "),...h},[...t.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])});return r.displayName="".concat(e),r}},30401:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},92735:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},42208:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},69658:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},41473:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},53113:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7586:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},73247:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},18930:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},17689:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},29374:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]])},32489:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},40257:function(e,t,r){"use strict";var n,i;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(44227)},44227:function(e){!function(){var t={229:function(e){var t,r,n,i=e.exports={};function s(){throw Error("setTimeout has not been defined")}function o(){throw Error("clearTimeout has not been defined")}function a(e){if(t===setTimeout)return setTimeout(e,0);if((t===s||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:s}catch(e){t=s}try{r="function"==typeof clearTimeout?clearTimeout:o}catch(e){r=o}}();var u=[],c=!1,l=-1;function f(){c&&n&&(c=!1,n.length?u=n.concat(u):l=-1,u.length&&d())}function d(){if(!c){var e=a(f);c=!0;for(var t=u.length;t;){for(n=u,u=[];++l<t;)n&&n[l].run();l=-1,t=u.length}n=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function p(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];u.push(new h(e,t)),1!==u.length||c||a(d)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=p,i.addListener=p,i.once=p,i.off=p,i.removeListener=p,i.removeAllListeners=p,i.emit=p,i.prependListener=p,i.prependOnceListener=p,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},o=!0;try{t[e](s,s.exports,n),o=!1}finally{o&&delete r[e]}return s.exports}n.ab="//";var i=n(229);e.exports=i}()},5004:function(e,t){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,s=u(e),o=s[0],a=s[1],c=new i((o+a)*3/4-a),l=0,f=a>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[l++]=t>>16&255,c[l++]=t>>8&255,c[l++]=255&t;return 2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[l++]=255&t),1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[l++]=t>>8&255,c[l++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,s=[],o=0,a=n-i;o<a;o+=16383)s.push(function(e,t,n){for(var i,s=[],o=t;o<n;o+=3)s.push(r[(i=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]))>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return s.join("")}(e,o,o+16383>a?a:o+16383));return 1===i?s.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&s.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),s.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)r[o]=s[o],n[s.charCodeAt(o)]=o;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},50022:function(e,t,r){"use strict";var n=r(5004),i=r(125),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>2147483647)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return l(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!a.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|p(e,t),n=o(r),i=n.write(e,t);return i!==r&&(n=n.slice(0,i)),n}(e,t);if(ArrayBuffer.isView(e))return function(e){if(I(e,Uint8Array)){var t=new Uint8Array(e);return d(t.buffer,t.byteOffset,t.byteLength)}return f(e)}(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(I(e,ArrayBuffer)||e&&I(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(I(e,SharedArrayBuffer)||e&&I(e.buffer,SharedArrayBuffer)))return d(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return a.from(n,t,r);var i=function(e){if(a.isBuffer(e)){var t,r=0|h(e.length),n=o(r);return 0===n.length||e.copy(n,0,0,r),n}return void 0!==e.length?"number"!=typeof e.length||(t=e.length)!=t?o(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function l(e){return c(e),o(e<0?0:0|h(e))}function f(e){for(var t=e.length<0?0:0|h(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function d(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),a.prototype),n}function h(e){if(e>=2147483647)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||I(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return k(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return T(e).length;default:if(i)return n?-1:k(e).length;t=(""+t).toLowerCase(),i=!0}}function g(e,t,r){var i,s,o=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=t;s<r;++s)i+=M[e[s]];return i}(this,t,r);case"utf8":case"utf-8":return v(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=t,s=r,0===i&&s===this.length?n.fromByteArray(this):n.fromByteArray(this.slice(i,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",s=0;s<n.length-1;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}(this,t,r);default:if(o)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,i){var s;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),(s=r=+r)!=s&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return -1;r=e.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof t&&(t=a.from(t,n)),a.isBuffer(t))return 0===t.length?-1:b(e,t,r,n,i);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):b(e,[t],r,n,i);throw TypeError("val must be string, number or Buffer")}function b(e,t,r,n,i){var s,o=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,a/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var l=-1;for(s=r;s<a;s++)if(c(e,s)===c(t,-1===l?0:s-l)){if(-1===l&&(l=s),s-l+1===u)return l*o}else -1!==l&&(s-=s-l),l=-1}else for(r+u>a&&(r=a-u),s=r;s>=0;s--){for(var f=!0,d=0;d<u;d++)if(c(e,s+d)!==c(t,d)){f=!1;break}if(f)return s}return -1}function v(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var s,o,a,u,c=e[i],l=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(l=c);break;case 2:(192&(s=e[i+1]))==128&&(u=(31&c)<<6|63&s)>127&&(l=u);break;case 3:s=e[i+1],o=e[i+2],(192&s)==128&&(192&o)==128&&(u=(15&c)<<12|(63&s)<<6|63&o)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:s=e[i+1],o=e[i+2],a=e[i+3],(192&s)==128&&(192&o)==128&&(192&a)==128&&(u=(15&c)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}function w(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function S(e,t,r,n,i,s){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<s)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function E(e,t,r,n,i,s){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function A(e,t,r,n,s){return t=+t,r>>>=0,s||E(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function x(e,t,r,n,s){return t=+t,r>>>=0,s||E(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}t.lW=a,t.h2=50,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,r){return(c(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},a.allocUnsafe=function(e){return l(e)},a.allocUnsafeSlow=function(e){return l(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(I(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),I(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,s=Math.min(r,n);i<s;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=a.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(I(s,Uint8Array))i+s.length>n.length?a.from(s).copy(n,i):Uint8Array.prototype.set.call(n,s,i);else if(a.isBuffer(s))s.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=s.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?v(this,0,e):g.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",r=t.h2;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(e,t,r,n,i){if(I(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var s=i-n,o=r-t,u=Math.min(s,o),c=this.slice(n,i),l=e.slice(t,r),f=0;f<u;++f)if(c[f]!==l[f]){s=c[f],o=l[f];break}return s<o?-1:o<s?1:0},a.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},a.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,s,o,a,u,c,l,f,d=this.length-t;if((void 0===r||r>d)&&(r=d),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var h=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=t.length;n>s/2&&(n=s/2);for(var o=0;o<n;++o){var a=parseInt(t.substr(2*o,2),16);if(a!=a)break;e[r+o]=a}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,s=r,P(k(e,this.length-i),this,i,s);case"ascii":case"latin1":case"binary":return o=t,a=r,P(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(e),this,o,a);case"base64":return u=t,c=r,P(T(e),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=t,f=r,P(function(e,t){for(var r,n,i=[],s=0;s<e.length&&!((t-=2)<0);++s)n=(r=e.charCodeAt(s))>>8,i.push(r%256),i.push(n);return i}(e,this.length-l),this,l,f);default:if(h)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),h=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUintLE=a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],i=1,s=0;++s<t&&(i*=256);)n+=this[e+s]*i;return n},a.prototype.readUintBE=a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},a.prototype.readUint8=a.prototype.readUInt8=function(e,t){return e>>>=0,t||w(e,1,this.length),this[e]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],i=1,s=0;++s<t&&(i*=256);)n+=this[e+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=t,i=1,s=this[e+--n];n>0&&(i*=256);)s+=this[e+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*t)),s},a.prototype.readInt8=function(e,t){return(e>>>=0,t||w(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;S(this,e,t,r,i,0)}var s=1,o=0;for(this[t]=255&e;++o<r&&(s*=256);)this[t+o]=e/s&255;return t+r},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;S(this,e,t,r,i,0)}var s=r-1,o=1;for(this[t+s]=255&e;--s>=0&&(o*=256);)this[t+s]=e/o&255;return t+r},a.prototype.writeUint8=a.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);S(this,e,t,r,i-1,-i)}var s=0,o=1,a=0;for(this[t]=255&e;++s<r&&(o*=256);)e<0&&0===a&&0!==this[t+s-1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+r},a.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);S(this,e,t,r,i-1,-i)}var s=r-1,o=1,a=0;for(this[t+s]=255&e;--s>=0&&(o*=256);)e<0&&0===a&&0!==this[t+s+1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||S(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return A(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return A(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return x(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return x(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,n){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),i},a.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,s=e.charCodeAt(0);("utf8"===n&&s<128||"latin1"===n)&&(e=s)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=a.isBuffer(e)?e:a.from(e,n),u=o.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%u]}return this};var R=/[^+/0-9A-Za-z-_]/g;function k(e,t){t=t||1/0;for(var r,n=e.length,i=null,s=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&s.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;s.push(r)}else if(r<2048){if((t-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return s}function T(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(R,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function P(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function I(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var M=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},125:function(e,t){t.read=function(e,t,r,n,i){var s,o,a=8*i-n-1,u=(1<<a)-1,c=u>>1,l=-7,f=r?i-1:0,d=r?-1:1,h=e[t+f];for(f+=d,s=h&(1<<-l)-1,h>>=-l,l+=a;l>0;s=256*s+e[t+f],f+=d,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=n;l>0;o=256*o+e[t+f],f+=d,l-=8);if(0===s)s=1-c;else{if(s===u)return o?NaN:1/0*(h?-1:1);o+=Math.pow(2,n),s-=c}return(h?-1:1)*o*Math.pow(2,s-n)},t.write=function(e,t,r,n,i,s){var o,a,u,c=8*s-i-1,l=(1<<c)-1,f=l>>1,d=23===i?5960464477539062e-23:0,h=n?0:s-1,p=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(isNaN(t=Math.abs(t))||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?t+=d/u:t+=d*Math.pow(2,1-f),t*u>=2&&(o++,u/=2),o+f>=l?(a=0,o=l):o+f>=1?(a=(t*u-1)*Math.pow(2,i),o+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+h]=255&a,h+=p,a/=256,i-=8);for(o=o<<i|a,c+=i;c>0;e[r+h]=255&o,h+=p,o/=256,c-=8);e[r+h-p]|=128*g}}}]);