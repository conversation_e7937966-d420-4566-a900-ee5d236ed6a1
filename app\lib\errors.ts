import { NextResponse } from "next/server";
import { ZodError } from "zod";

export interface ApiError {
  code: string;
  message: string;
  statusCode: number;
  details?: any;
}

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = "INTERNAL_ERROR",
    details?: any,
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.name = "AppError";

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, "VALIDATION_ERROR", details);
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = "Authentication required") {
    super(message, 401, "AUTHENTICATION_ERROR");
    this.name = "AuthenticationError";
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = "Insufficient permissions") {
    super(message, 403, "AUTHORIZATION_ERROR");
    this.name = "AuthorizationError";
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = "Resource") {
    super(`${resource} not found`, 404, "NOT_FOUND_ERROR");
    this.name = "NotFoundError";
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, "CONFLICT_ERROR");
    this.name = "ConflictError";
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = "Rate limit exceeded") {
    super(message, 429, "RATE_LIMIT_ERROR");
    this.name = "RateLimitError";
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 500, "DATABASE_ERROR", details);
    this.name = "DatabaseError";
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, details?: any) {
    super(
      `${service} service error: ${message}`,
      502,
      "EXTERNAL_SERVICE_ERROR",
      details,
    );
    this.name = "ExternalServiceError";
  }
}

// MongoDB/Mongoose error handling
export interface MongoError {
  code?: number;
  name?: string;
  message: string;
  keyPattern?: Record<string, any>;
  keyValue?: Record<string, any>;
}

export function handleMongoError(error: any): AppError {
  const mongoError = error as MongoError;

  // Handle duplicate key errors (unique constraint violations)
  if (mongoError.code === 11000 || mongoError.name === "MongoServerError") {
    const field = Object.keys(mongoError.keyPattern || {})[0] || "field";
    return new ConflictError(`${field} already exists`);
  }

  // Handle validation errors
  if (mongoError.name === "ValidationError") {
    return new ValidationError("Validation failed");
  }

  // Handle cast errors (invalid ObjectId, etc.)
  if (mongoError.name === "CastError") {
    return new ValidationError("Invalid data format");
  }

  // Handle document not found
  if (mongoError.name === "DocumentNotFoundError") {
    return new NotFoundError();
  }

  // Handle connection errors
  if (mongoError.name === "MongoNetworkError" || mongoError.name === "MongoTimeoutError") {
    return new DatabaseError("Database connection failed");
  }

  // Default MongoDB error
  return new DatabaseError("Database operation failed", {
    name: mongoError.name,
    message: mongoError.message,
  });
}

// Legacy Prisma error handling (kept for compatibility during migration)
export interface PrismaError {
  code: string;
  meta?: {
    target?: string[];
    constraint?: string;
    field_name?: string;
  };
  message: string;
}

export function handlePrismaError(error: any): AppError {
  const prismaError = error as PrismaError;

  switch (prismaError.code) {
    case "P2002":
      // Unique constraint violation
      const target = prismaError.meta?.target?.[0] || "field";
      return new ConflictError(`${target} already exists`);

    case "P2003":
      // Foreign key constraint violation
      const constraint = prismaError.meta?.constraint;
      if (constraint?.includes("userId")) {
        return new AuthenticationError("Invalid user session");
      }
      return new ValidationError("Invalid reference to related record");

    case "P2025":
      // Record not found
      return new NotFoundError();

    case "P2014":
      // Required relation violation
      return new ValidationError("Missing required relationship");

    case "P2000":
      // Value too long
      return new ValidationError("Input value is too long");

    case "P2001":
      // Record does not exist
      return new NotFoundError();

    case "P2004":
      // Constraint failed
      return new ValidationError("Data constraint violation");

    default:
      return new DatabaseError("Database operation failed", {
        code: prismaError.code,
        message: prismaError.message,
      });
  }
}

// Zod error handling
export function handleZodError(error: ZodError): ValidationError {
  const errors = error.issues.map((err: any) => ({
    field: err.path.join("."),
    message: err.message,
    code: err.code,
  }));

  return new ValidationError("Validation failed", { errors });
}

// Generic error handler for API routes
export function handleApiError(error: unknown): NextResponse {
  const isDevelopment = process.env.NODE_ENV === "development";
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  // Log error details server-side
  if (error instanceof Error) {
    console.error(`[${new Date().toISOString()}] API Error:`, {
      requestId,
      name: error.name,
      message: error.message,
      stack: isDevelopment ? error.stack : undefined,
      code: (error as any).code,
    });
  } else {
    console.error(`[${new Date().toISOString()}] API Error:`, {
      requestId,
      error,
    });
  }

  // Handle known error types
  if (error instanceof AppError) {
    const response = {
      success: false,
      error: {
        code: error.code,
        message: isDevelopment
          ? error.message
          : getProductionMessage(error.code),
        requestId,
        ...(isDevelopment && error.details && { details: error.details }),
      },
    };

    return NextResponse.json(response, { status: error.statusCode });
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const validationError = handleZodError(error);
    const response = {
      success: false,
      error: {
        code: validationError.code,
        message: "Validation failed",
        requestId,
        ...(isDevelopment && { details: validationError.details }),
      },
    };

    return NextResponse.json(response, { status: validationError.statusCode });
  }

  // Handle MongoDB errors
  if (
    error &&
    typeof error === "object" &&
    ("code" in error || "name" in error)
  ) {
    const errorObj = error as any;

    // Check if it's a MongoDB error
    if (
      errorObj.name === "MongoServerError" ||
      errorObj.name === "ValidationError" ||
      errorObj.name === "CastError" ||
      errorObj.name === "DocumentNotFoundError" ||
      errorObj.name === "MongoNetworkError" ||
      errorObj.name === "MongoTimeoutError" ||
      (typeof errorObj.code === "number" && errorObj.code === 11000)
    ) {
      const mongoError = handleMongoError(error);
      const response = {
        success: false,
        error: {
          code: mongoError.code,
          message: isDevelopment
            ? mongoError.message
            : getProductionMessage(mongoError.code),
          requestId,
          ...(isDevelopment &&
            mongoError.details && { details: mongoError.details }),
        },
      };

      return NextResponse.json(response, { status: mongoError.statusCode });
    }

    // Handle legacy Prisma errors (for compatibility during migration)
    if (typeof errorObj.code === "string" && errorObj.code.startsWith("P")) {
      const prismaError = handlePrismaError(error);
      const response = {
        success: false,
        error: {
          code: prismaError.code,
          message: isDevelopment
            ? prismaError.message
            : getProductionMessage(prismaError.code),
          requestId,
          ...(isDevelopment &&
            prismaError.details && { details: prismaError.details }),
        },
      };

      return NextResponse.json(response, { status: prismaError.statusCode });
    }
  }

  // Handle generic errors
  const response = {
    success: false,
    error: {
      code: "INTERNAL_ERROR",
      message: "An error occurred processing your request",
      requestId,
      ...(isDevelopment &&
        error instanceof Error && { details: error.message }),
    },
  };

  return NextResponse.json(response, { status: 500 });
}

// Production-safe error messages
function getProductionMessage(code: string): string {
  const messages: Record<string, string> = {
    VALIDATION_ERROR: "Invalid input provided",
    AUTHENTICATION_ERROR: "Authentication required",
    AUTHORIZATION_ERROR: "Insufficient permissions",
    NOT_FOUND_ERROR: "Resource not found",
    CONFLICT_ERROR: "Request conflicts with current state",
    RATE_LIMIT_ERROR: "Too many requests",
    DATABASE_ERROR: "Database operation failed",
    EXTERNAL_SERVICE_ERROR: "External service unavailable",
    INTERNAL_ERROR: "An error occurred processing your request",
  };

  return messages[code] || messages["INTERNAL_ERROR"];
}

// Error response helpers
export const ErrorResponses = {
  unauthorized: () =>
    NextResponse.json(
      {
        success: false,
        error: { code: "UNAUTHORIZED", message: "Authentication required" },
      },
      { status: 401 },
    ),

  forbidden: () =>
    NextResponse.json(
      {
        success: false,
        error: { code: "FORBIDDEN", message: "Insufficient permissions" },
      },
      { status: 403 },
    ),

  notFound: (resource: string = "Resource") =>
    NextResponse.json(
      {
        success: false,
        error: { code: "NOT_FOUND", message: `${resource} not found` },
      },
      { status: 404 },
    ),

  validation: (message: string, details?: any) =>
    NextResponse.json(
      {
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message,
          ...(details && { details }),
        },
      },
      { status: 400 },
    ),

  conflict: (message: string) =>
    NextResponse.json(
      { success: false, error: { code: "CONFLICT", message } },
      { status: 409 },
    ),

  rateLimit: () =>
    NextResponse.json(
      {
        success: false,
        error: { code: "RATE_LIMIT", message: "Rate limit exceeded" },
      },
      { status: 429 },
    ),

  internal: (message?: string) => {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message:
            process.env.NODE_ENV === "production"
              ? "An error occurred processing your request"
              : message || "An unexpected error occurred",
          requestId,
        },
      },
      { status: 500 },
    );
  },
};

// Async error wrapper for API routes
export function asyncHandler<T extends (...args: any[]) => Promise<any>>(fn: T) {
  return async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Type guard for checking if error is an AppError
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}
