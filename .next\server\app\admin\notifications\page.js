(()=>{var e={};e.id=7395,e.ids=[7395],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93748:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>d}),s(44163),s(90596),s(37254),s(35866);var i=s(23191),r=s(88716),a=s(37922),l=s.n(a),o=s(95231),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let d=["",{children:["admin",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,44163)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\page.tsx"],x="/admin/notifications/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/notifications/page",pathname:"/admin/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},30968:(e,t,s)=>{Promise.resolve().then(s.bind(s,594))},594:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var i=s(10326),r=s(17577),a=s(90434),l=s(69436),o=s(40617);let n=(0,s(76557).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var d=s(88378),c=s(17069),x=s(12714),m=s(37358),h=s(83855);let p=()=>{let[e,t]=(0,r.useState)(null),[s,p]=(0,r.useState)(!0),[g,u]=(0,r.useState)(null);(0,r.useEffect)(()=>{f()},[]);let f=async()=>{try{p(!0);let e=await fetch("/api/admin/notifications/stats"),s=await e.json();s.success?t(s.stats):u("Failed to fetch notification statistics")}catch(e){console.error("Error fetching notification stats:",e),u("Failed to fetch notification statistics")}finally{p(!1)}},v=[{title:"Send Notification",description:"Send a notification to users",href:"/admin/notifications/send",icon:l.Z,color:"bg-green-500",textColor:"text-green-600"},{title:"Broadcast Message",description:"Send message to all users",href:"/admin/notifications/broadcast",icon:o.Z,color:"bg-blue-500",textColor:"text-blue-600"},{title:"Notification History",description:"View sent notifications",href:"/admin/notifications/history",icon:n,color:"bg-purple-500",textColor:"text-purple-600"},{title:"Templates",description:"Manage notification templates",href:"/admin/notifications/templates",icon:d.Z,color:"bg-orange-500",textColor:"text-orange-600"}],y=[{title:"Total Sent",value:e?.totalSent||0,icon:l.Z,color:"bg-blue-500"},{title:"Delivered",value:e?.totalDelivered||0,icon:c.Z,color:"bg-green-500"},{title:"Read",value:e?.totalRead||0,icon:x.Z,color:"bg-purple-500"},{title:"Recent Activity",value:e?.recentActivity||0,icon:m.Z,color:"bg-orange-500"}];return s?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"animate-pulse",children:[i.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-2"}),i.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse",children:[i.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),i.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]},t))})]}):g?(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[i.jsx("p",{className:"text-red-600",children:g}),i.jsx("button",{onClick:f,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[i.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Notifications"}),i.jsx("p",{className:"text-gray-600 mt-2",children:"Manage and send notifications to your customers"})]}),(0,i.jsxs)(a.default,{href:"/admin/notifications/send",className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[i.jsx(h.Z,{className:"w-4 h-4"}),i.jsx("span",{children:"Send Notification"})]})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:y.map((e,t)=>{let s=e.icon;return i.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.title}),i.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value.toLocaleString()})]}),i.jsx("div",{className:`w-12 h-12 ${e.color} rounded-lg flex items-center justify-center`,children:i.jsx(s,{className:"w-6 h-6 text-white"})})]})},t)})}),e&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Delivery Rate"}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[i.jsx("div",{className:"flex-1",children:i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:i.jsx("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${e.deliveryRate}%`}})})}),(0,i.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[e.deliveryRate.toFixed(1),"%"]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Read Rate"}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[i.jsx("div",{className:"flex-1",children:i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:i.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${e.readRate}%`}})})}),(0,i.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[e.readRate.toFixed(1),"%"]})]})]})]}),(0,i.jsxs)("div",{children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:v.map((e,t)=>{let s=e.icon;return i.jsx(a.default,{href:e.href,className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[i.jsx("div",{className:`w-12 h-12 ${e.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`,children:i.jsx(s,{className:"w-6 h-6 text-white"})}),(0,i.jsxs)("div",{className:"flex-1",children:[i.jsx("h3",{className:`font-semibold ${e.textColor} group-hover:text-gray-900 transition-colors`,children:e.title}),i.jsx("p",{className:"text-sm text-gray-600",children:e.description})]})]})},t)})})]})]})}},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},83855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},69436:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(76557).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},17069:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},44163:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\notifications\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[9276,3757,434,9536,4855],()=>s(93748));module.exports=i})();