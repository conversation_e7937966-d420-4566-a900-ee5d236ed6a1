"use strict";exports.id=5306,exports.ids=[5306],exports.modules={95306:(e,t,r)=>{r.d(t,{L:()=>d});var i=r(77234),n=r(53797),o=r(98691),a=r(89456),s=r(81515);let d={providers:[(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");await (0,a.Z)();let t=await s.n5.findOne({email:e.email});if(!t||!t.password||!await o.qu(e.password,t.password))throw Error("Invalid credentials");return{id:t._id.toString(),email:t.email,name:t.name,role:t.role}}})],session:{strategy:"jwt",maxAge:604800,updateAge:86400},jwt:{maxAge:604800},cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,domain:process.env.NEXTAUTH_URL?.replace("https://","").replace("http://","")}}},callbacks:{async jwt({token:e,user:t,account:r,profile:i}){if(console.log("JWT callback called with:",{hasToken:!!e,hasUser:!!t,hasAccount:!!r,accountProvider:r?.provider,userId:t?.id,userEmail:t?.email}),r&&t){if("google"===r.provider)try{let r=t.email||i?.email;if(!r)throw Error("No email found for Google account");await (0,a.Z)();let n=await s.n5.findOne({email:r});n||(n=await s.n5.create({email:r,name:t.name||i?.name||r.split("@")[0],role:"CUSTOMER",emailVerified:new Date})),n&&(e.sub=n._id.toString(),e.role=n.role,e.email=n.email,e.name=n.name)}catch(e){throw console.error("Error handling Google sign in:",e),e}else t&&(console.log("Processing credentials user:",t),e.sub=t.id,e.role=t.role,e.email=t.email,e.name=t.name)}return console.log("JWT token being returned:",{sub:e.sub,email:e.email,role:e.role,name:e.name}),e},session:async({session:e,token:t})=>(console.log("Session callback called with:",{hasSession:!!e,hasToken:!!t,tokenSub:t?.sub,tokenRole:t?.role,tokenEmail:t?.email,tokenName:t?.name}),t&&(e.user={id:t.sub??"",role:t.role??"CUSTOMER",email:t.email??"",name:t.name??null,image:null},console.log("Session populated with:",e.user)),console.log("Session being returned:",e),e),async redirect({url:e,baseUrl:t}){try{let r=new URL(e,t);if("null"===r.origin)return t;if(r.pathname.startsWith("/"))return`${t}${r.pathname}${r.search}${r.hash}`;if(r.origin===t)return r.toString()}catch{}return t}},events:{async signIn({user:e,account:t,profile:r,isNewUser:i}){}},pages:{signIn:"/login",signOut:"/",error:"/login"},secret:process.env.NEXTAUTH_SECRET,debug:!1}},81515:(e,t,r)=>{r.d(t,{Cq:()=>D,Dd:()=>R,Order:()=>E,P_:()=>A,Pp:()=>x,Th:()=>v,Vv:()=>B,WD:()=>T,gc:()=>P,hQ:()=>L,kL:()=>j,mA:()=>k,n5:()=>N,nW:()=>O,p1:()=>_,qN:()=>C,wV:()=>U,xs:()=>w});var i=r(11185),n=r.n(i);let o=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),a=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),s=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),l=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),m=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),c=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),u=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),b=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),I=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),q=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=n().models.User||n().model("User",o),w=n().models.Product||n().model("Product",a),T=n().models.Category||n().model("Category",s),E=n().models.Order||n().model("Order",d),O=n().models.HomepageSetting||n().model("HomepageSetting",l),P=n().models.Testimonial||n().model("Testimonial",m),C=n().models.ProductImage||n().model("ProductImage",c),v=n().models.ProductVariant||n().model("ProductVariant",u),D=n().models.Review||n().model("Review",p),j=n().models.Address||n().model("Address",y),R=n().models.OrderItem||n().model("OrderItem",g),A=n().models.Notification||n().model("Notification",S),U=n().models.Coupon||n().model("Coupon",h);n().models.Wishlist||n().model("Wishlist",f);let k=n().models.Newsletter||n().model("Newsletter",b),x=n().models.ProductCategory||n().model("ProductCategory",I),L=n().models.WishlistItem||n().model("WishlistItem",q),M=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),B=n().models.NotificationTemplate||n().model("NotificationTemplate",M),G=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),_=n().models.Enquiry||n().model("Enquiry",G)},89456:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(11185),n=r.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let s=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(o,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}}};