"use strict";(()=>{var e={};e.id=1978,e.ids=[1978],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},72033:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>q,patchFetch:()=>I,requestAsyncStorage:()=>S,routeModule:()=>y,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{DELETE:()=>l,GET:()=>p,PUT:()=>m});var s=r(49303),n=r(88716),o=r(60670),a=r(87070),d=r(89456),u=r(81515);let c=e=>async(...t)=>{try{return await e(...t)}catch(e){return console.error(e),a.NextResponse.json({success:!1,error:"Internal Server Error"},{status:500})}},p=c(async(e,{params:t})=>{await (0,d.Z)();let r=await u.xs.findOne({_id:t.id,"faqs._id":t.faqId}).select("faqs.$").lean(),i=r?.faqs?.[0];return i?a.NextResponse.json({success:!0,data:i}):a.NextResponse.json({success:!1,error:"FAQ not found"},{status:404})}),m=c(async(e,{params:t})=>{await (0,d.Z)();let{question:r,answer:i,position:s,isActive:n}=await e.json()||{},o={};void 0!==r&&(o.question=r),void 0!==i&&(o.answer=i),void 0!==s&&(o.position=s),void 0!==n&&(o.isActive=n);let c=await u.xs.findOneAndUpdate({_id:t.id,"faqs._id":t.faqId},{$set:Object.fromEntries(Object.entries(o).map(([e,t])=>[`faqs.$.${e}`,t])),$currentDate:{"faqs.$.updatedAt":!0}},{new:!0,projection:{"faqs.$":1}}).lean(),p=c?.faqs?.[0];return p?a.NextResponse.json({success:!0,data:p,message:"FAQ updated successfully"}):a.NextResponse.json({success:!1,error:"FAQ not found"},{status:404})}),l=c(async(e,{params:t})=>{await (0,d.Z)();let r=await u.xs.updateOne({_id:t.id},{$pull:{faqs:{_id:t.faqId}}});return r&&0!==r.modifiedCount?a.NextResponse.json({success:!0,message:"FAQ deleted successfully"}):a.NextResponse.json({success:!1,error:"FAQ not found"},{status:404})}),y=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/products/[id]/faqs/[faqId]/route",pathname:"/api/products/[id]/faqs/[faqId]",filename:"route",bundlePath:"app/api/products/[id]/faqs/[faqId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\[faqId]\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:S,staticGenerationAsyncStorage:g,serverHooks:f}=y,q="/api/products/[id]/faqs/[faqId]/route";function I(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>D,Dd:()=>A,Order:()=>w,P_:()=>x,Pp:()=>M,Th:()=>v,Vv:()=>G,WD:()=>T,gc:()=>P,hQ:()=>B,kL:()=>C,mA:()=>U,n5:()=>N,nW:()=>E,p1:()=>_,qN:()=>j,wV:()=>R,xs:()=>O});var i=r(11185),s=r.n(i);let n=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),o=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),a=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),c=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),S=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),g=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),f=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),q=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),I=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),h=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=s().models.User||s().model("User",n),O=s().models.Product||s().model("Product",o),T=s().models.Category||s().model("Category",a),w=s().models.Order||s().model("Order",d),E=s().models.HomepageSetting||s().model("HomepageSetting",u),P=s().models.Testimonial||s().model("Testimonial",c),j=s().models.ProductImage||s().model("ProductImage",p),v=s().models.ProductVariant||s().model("ProductVariant",m),D=s().models.Review||s().model("Review",l),C=s().models.Address||s().model("Address",y),A=s().models.OrderItem||s().model("OrderItem",S),x=s().models.Notification||s().model("Notification",g),R=s().models.Coupon||s().model("Coupon",f);s().models.Wishlist||s().model("Wishlist",q);let U=s().models.Newsletter||s().model("Newsletter",I),M=s().models.ProductCategory||s().model("ProductCategory",h),B=s().models.WishlistItem||s().model("WishlistItem",b),F=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),G=s().models.NotificationTemplate||s().model("NotificationTemplate",F),L=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),_=s().models.Enquiry||s().model("Enquiry",L)},89456:(e,t,r)=>{r.d(t,{Z:()=>a});var i=r(11185),s=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let a=async function(){if(o.conn)return o.conn;o.promise||(o.promise=s().connect(n,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(72033));module.exports=i})();