(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4178],{43526:function(e,s,t){Promise.resolve().then(t.bind(t,28250)),Promise.resolve().then(t.bind(t,46370))},46370:function(e,s,t){"use strict";var r=t(57437),a=t(2265),l=t(27648),i=t(99376),n=t(80605),d=t(44794),c=t(88997),o=t(92369),x=t(88906),m=t(10407),h=t(47692);s.default=()=>{var e,s,t,u,g,f;let{data:j,status:p}=(0,n.useSession)();(0,i.useRouter)();let[b,y]=(0,a.useState)(null),[N,v]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{var e;if(null==j?void 0:null===(e=j.user)||void 0===e?void 0:e.id)try{let e=await fetch("/api/users/".concat(j.user.id,"/stats"));if(e.ok){let s=await e.json();y(s.data)}}catch(e){console.error("Error fetching user stats:",e)}finally{v(!1)}})()},[null==j?void 0:null===(e=j.user)||void 0===e?void 0:e.id]);let w={name:(null==j?void 0:null===(s=j.user)||void 0===s?void 0:s.name)||"User",email:(null==j?void 0:null===(t=j.user)||void 0===t?void 0:t.email)||"",joinDate:(null==b?void 0:null===(u=b.user)||void 0===u?void 0:u.joinDate)?new Date(b.user.joinDate).toLocaleDateString("en-US",{year:"numeric",month:"long"}):"Loading...",totalOrders:(null==b?void 0:null===(g=b.orders)||void 0===g?void 0:g.total)||0,isAdmin:(null==j?void 0:null===(f=j.user)||void 0===f?void 0:f.role)==="ADMIN",accountStatus:(null==b?void 0:b.accountStatus)||"Loading..."},Z=async()=>{try{await (0,n.signOut)({redirect:!1,callbackUrl:"/"}),window.location.replace("/")}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},k=[{icon:d.Z,title:"Order History",description:"View your past orders",href:"/order-history",color:"bg-green-100 text-green-600"},{icon:c.Z,title:"Wishlist",description:"Your saved items",href:"/wishlist",color:"bg-green-100 text-green-600"},{icon:o.Z,title:"Edit Profile",description:"Update your profile details",href:"/edit-profile",color:"bg-green-100 text-green-600"}],A=[{icon:x.Z,title:"Admin Panel",description:"Manage store (Admin only)",href:"/admin",color:"bg-green-100 text-green-600"}];return"loading"===p?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):"unauthenticated"===p?null:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 grid grid-cols-1 lg:grid-cols-12 gap-8",children:[(0,r.jsx)("div",{className:"lg:hidden col-span-12",children:(0,r.jsxs)("div",{className:"px-4 py-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(o.Z,{className:"w-8 h-8 text-white"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:w.name}),(0,r.jsx)("p",{className:"text-gray-600",children:w.email}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Member since ",w.joinDate]})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center mb-6",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-800",children:w.totalOrders}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total Orders"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[k.map((e,s)=>(0,r.jsxs)(l.default,{href:e.href,className:"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat(e.color),children:(0,r.jsx)(e.icon,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,r.jsx)(m.Z,{className:"w-5 h-5 text-gray-400"})]},s)),w.isAdmin&&A.map((e,s)=>(0,r.jsxs)(l.default,{href:e.href,className:"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat(e.color),children:(0,r.jsx)(e.icon,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),(0,r.jsx)(m.Z,{className:"w-5 h-5 text-gray-400"})]},"admin-".concat(s)))]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("button",{onClick:Z,className:"w-full flex items-center justify-center space-x-2 p-4 bg-red-50 text-red-600 rounded-2xl font-medium hover:bg-red-100 transition-colors",children:[(0,r.jsx)(h.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign Out"})]})})]})}),(0,r.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,r.jsxs)("div",{className:"py-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-12",children:"Profile"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1",children:[(0,r.jsx)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 mb-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(o.Z,{className:"w-12 h-12 text-white"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-1",children:w.name}),(0,r.jsx)("p",{className:"text-gray-600 mb-1",children:w.email}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Member since ",w.joinDate]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Account Stats"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Orders"}),(0,r.jsx)("span",{className:"font-semibold text-gray-800",children:w.totalOrders})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Account Status"}),(0,r.jsx)("span",{className:"font-semibold text-green-600",children:w.accountStatus})]})]})]})]}),(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Quick Actions"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:k.map((e,s)=>(0,r.jsxs)(l.default,{href:e.href,className:"flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-colors border border-gray-100",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center ".concat(e.color),children:(0,r.jsx)(e.icon,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-800",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},s))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Account Actions"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsxs)(l.default,{href:"/edit-profile",className:"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors",children:[(0,r.jsx)(o.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Edit Profile"})]}),w.isAdmin&&(0,r.jsxs)(l.default,{href:"/admin",className:"flex items-center space-x-2 px-6 py-3 bg-green-700 text-white rounded-xl font-medium hover:bg-green-800 transition-colors",children:[(0,r.jsx)(x.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Admin Panel"})]}),(0,r.jsxs)("button",{onClick:Z,className:"flex items-center space-x-2 px-6 py-3 bg-red-50 text-red-600 rounded-xl font-medium hover:bg-red-100 transition-colors ml-auto",children:[(0,r.jsx)(h.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})]})}},10407:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},88997:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},47692:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},44794:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=43526)}),_N_E=e.O()}]);