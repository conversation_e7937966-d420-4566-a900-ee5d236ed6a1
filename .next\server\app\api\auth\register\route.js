"use strict";(()=>{var e={};e.id=3002,e.ids=[3002],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},75849:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>I,patchFetch:()=>w,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>b,staticGenerationAsyncStorage:()=>q});var i={};r.r(i),r.d(i,{POST:()=>S});var a=r(49303),s=r(88716),o=r(60670),n=r(87070),u=r(98691),d=r(65630),l=r(3474),m=r(8149),p=r(95921),c=r(84875),y=r(54211);let g=d.Ry({name:d.Z_().min(2,"Name must be at least 2 characters"),email:d.Z_().email("Invalid email address"),password:d.Z_().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number")}),S=(0,c.lm)(async e=>{let t=Date.now();y.kg.apiRequest("POST","/api/auth/register"),await (0,m.er)(e,m.Ri,5);let r=await e.json(),{name:i,email:a,password:s}=g.parse(r);if(y.kg.info("Registration attempt",{email:a,name:i}),await (0,l.uD)(),await l.n5.findOne({email:a}).lean())throw y.kg.authFailure(a,"registration","User already exists"),new c.AY("User with this email already exists");let o=await u.vp(s,12),d=await l.n5.create({name:i,email:a,password:o,role:"CUSTOMER"}),S={id:d._id,name:d.name,email:d.email,role:d.role,createdAt:d.createdAt};y.kg.authSuccess(S.id,"registration",{email:a,name:i});try{await (0,p.Pi)(a,i),y.kg.emailSent(a,"Welcome Email","welcome")}catch(e){y.kg.emailError(a,"Welcome Email",e)}let h=Date.now()-t;return y.kg.performance("user_registration",h),n.NextResponse.json({success:!0,message:"User created successfully",user:S},{status:201})}),h=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:f,staticGenerationAsyncStorage:q,serverHooks:b}=h,I="/api/auth/register/route";function w(){return(0,o.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:q})}},3474:(e,t,r)=>{r.d(t,{KM:()=>a.Order,WD:()=>a.WD,n5:()=>a.n5,uD:()=>i.Z,xs:()=>a.xs});var i=r(89456),a=r(81515)},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>s}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class a{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:a,context:s,error:o,userId:n,requestId:u}=e,d=i[r],l=`[${t}] ${d}: ${a}`;return n&&(l+=` | User: ${n}`),u&&(l+=` | Request: ${u}`),s&&Object.keys(s).length>0&&(l+=` | Context: ${JSON.stringify(s)}`),o&&(l+=` | Error: ${o.message}`,this.isDevelopment&&o.stack&&(l+=`
Stack: ${o.stack}`)),l}log(e,t,r,i){if(!this.shouldLog(e))return;let a={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},s=this.formatMessage(a);if(this.isDevelopment)switch(e){case 0:console.error(s);break;case 1:console.warn(s);break;case 2:console.info(s);break;case 3:console.debug(s)}else console.log(JSON.stringify(a))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,a){this.info(`API ${e} ${t} - ${r}`,{...a,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,a){this.error(`API ${e} ${t} failed`,r,{...a,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let s=new a},81515:(e,t,r)=>{r.d(t,{Cq:()=>x,Dd:()=>C,Order:()=>v,P_:()=>A,Pp:()=>U,Th:()=>D,Vv:()=>M,WD:()=>P,gc:()=>O,hQ:()=>$,kL:()=>R,mA:()=>k,n5:()=>w,nW:()=>E,p1:()=>L,qN:()=>T,wV:()=>j,xs:()=>N});var i=r(11185),a=r.n(i);let s=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),o=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),n=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),u=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),d=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),l=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),c=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),q=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),b=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),I=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),w=a().models.User||a().model("User",s),N=a().models.Product||a().model("Product",o),P=a().models.Category||a().model("Category",n),v=a().models.Order||a().model("Order",u),E=a().models.HomepageSetting||a().model("HomepageSetting",d),O=a().models.Testimonial||a().model("Testimonial",l),T=a().models.ProductImage||a().model("ProductImage",m),D=a().models.ProductVariant||a().model("ProductVariant",p),x=a().models.Review||a().model("Review",c),R=a().models.Address||a().model("Address",y),C=a().models.OrderItem||a().model("OrderItem",g),A=a().models.Notification||a().model("Notification",S),j=a().models.Coupon||a().model("Coupon",h);a().models.Wishlist||a().model("Wishlist",f);let k=a().models.Newsletter||a().model("Newsletter",q),U=a().models.ProductCategory||a().model("ProductCategory",b),$=a().models.WishlistItem||a().model("WishlistItem",I),_=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),M=a().models.NotificationTemplate||a().model("NotificationTemplate",_),B=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),L=a().models.Enquiry||a().model("Enquiry",B)},89456:(e,t,r)=>{r.d(t,{Z:()=>n});var i=r(11185),a=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let n=async function(){if(o.conn)return o.conn;o.promise||(o.promise=a().connect(s,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},8149:(e,t,r)=>{r.d(t,{Ri:()=>s,Xw:()=>o,er:()=>u,jO:()=>n});var i=r(919);function a(e){let t=new i.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,r)=>new Promise((i,a)=>{let s=t.get(r)||[0];0===s[0]&&t.set(r,s),s[0]+=1,s[0]>=e?a(Error("Rate limit exceeded")):i()})}}let s=a({interval:9e5,uniqueTokenPerInterval:500}),o=a({interval:6e4,uniqueTokenPerInterval:500}),n=a({interval:36e5,uniqueTokenPerInterval:500});async function u(e,t,r){let i=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);try{await t.check(r,i)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,9489,5630,138,5245,83],()=>r(75849));module.exports=i})();