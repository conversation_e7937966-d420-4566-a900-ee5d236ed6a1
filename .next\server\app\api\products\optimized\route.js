"use strict";(()=>{var e={};e.id=9754,e.ids=[9754],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55347:(e,t,r)=>{let i;r.r(t),r.d(t,{originalPathname:()=>S,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>u});var o=r(49303),n=r(88716),s=r(60670),d=r(87070),c=r(89456),p=r(81515);let u=(i=async e=>{await (0,c.Z)();let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1",10),i=parseInt(t.get("limit")||"10",10),a=t.get("category"),o=t.get("search"),n=t.get("sort")||"newest",s=t.get("priceMin"),u=t.get("priceMax"),m=(r-1)*i,l={isActive:!0};if(a&&"all"!==a){let e=await p.WD.findOne({slug:a}).select("_id").lean();e?._id&&(l.categoryId=String(e._id))}o&&(l.$or=[{name:{$regex:o,$options:"i"}},{shortDescription:{$regex:o,$options:"i"}}]),(s||u)&&(l.price={},s&&(l.price.$gte=parseFloat(String(s))),u&&(l.price.$lte=parseFloat(String(u))));let y={createdAt:-1};switch(n){case"name_asc":y={name:1};break;case"name_desc":y={name:-1};break;case"price_asc":y={price:1};break;case"price_desc":y={price:-1};break;case"newest":y={createdAt:-1};break;case"oldest":y={createdAt:1}}let[g,S]=await Promise.all([p.xs.find(l).sort(y).skip(m).limit(i).lean(),p.xs.countDocuments(l)]),h=await Promise.all(g.map(async e=>{let[t,r,i,a]=await Promise.all([e.categoryId?p.WD.findById(e.categoryId).select("name slug").lean():null,p.qN.find({productId:String(e._id)}).sort({position:1}).limit(1).lean(),p.Th.find({productId:String(e._id)}).sort({price:1}).limit(3).lean(),p.Cq.countDocuments({productId:String(e._id)})]);return{id:String(e._id),name:e.name,slug:e.slug,shortDescription:e.shortDescription,price:e.price,comparePrice:e.comparePrice,isFeatured:e.isFeatured,createdAt:e.createdAt,category:t,categories:t?[t]:[],image:r[0]||null,reviewCount:a,variants:i}}));return d.NextResponse.json({success:!0,data:h,pagination:{page:r,limit:i,total:S,pages:Math.ceil(S/i)}})},async(...e)=>{try{return await i(...e)}catch(e){return console.error(e),d.NextResponse.json({success:!1,error:"Internal Server Error"},{status:500})}}),m=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/products/optimized/route",pathname:"/api/products/optimized",filename:"route",bundlePath:"app/api/products/optimized/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\optimized\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:y,serverHooks:g}=m,S="/api/products/optimized/route";function h(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>C,Dd:()=>A,Order:()=>T,P_:()=>x,Pp:()=>M,Th:()=>v,Vv:()=>_,WD:()=>w,gc:()=>O,hQ:()=>k,kL:()=>j,mA:()=>U,n5:()=>N,nW:()=>D,p1:()=>G,qN:()=>E,wV:()=>R,xs:()=>P});var i=r(11185),a=r.n(i);let o=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),n=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),s=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),c=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),p=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),u=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),I=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),q=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),f=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=a().models.User||a().model("User",o),P=a().models.Product||a().model("Product",n),w=a().models.Category||a().model("Category",s),T=a().models.Order||a().model("Order",d),D=a().models.HomepageSetting||a().model("HomepageSetting",c),O=a().models.Testimonial||a().model("Testimonial",p),E=a().models.ProductImage||a().model("ProductImage",u),v=a().models.ProductVariant||a().model("ProductVariant",m),C=a().models.Review||a().model("Review",l),j=a().models.Address||a().model("Address",y),A=a().models.OrderItem||a().model("OrderItem",g),x=a().models.Notification||a().model("Notification",S),R=a().models.Coupon||a().model("Coupon",h);a().models.Wishlist||a().model("Wishlist",I);let U=a().models.Newsletter||a().model("Newsletter",q),M=a().models.ProductCategory||a().model("ProductCategory",f),k=a().models.WishlistItem||a().model("WishlistItem",b),B=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),_=a().models.NotificationTemplate||a().model("NotificationTemplate",B),F=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),G=a().models.Enquiry||a().model("Enquiry",F)},89456:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(11185),a=r.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let s=async function(){if(n.conn)return n.conn;n.promise||(n.promise=a().connect(o,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(55347));module.exports=i})();