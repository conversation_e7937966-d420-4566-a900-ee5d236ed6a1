"use strict";(()=>{var e={};e.id=9118,e.ids=[9118],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},59268:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>b,patchFetch:()=>w,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>I,staticGenerationAsyncStorage:()=>q});var i={};r.r(i),r.d(i,{POST:()=>S});var s=r(49303),o=r(88716),a=r(60670),n=r(87070),u=r(65630),d=r(84770),p=r(3474),l=r(95921),m=r(8149),c=r(84875),y=r(54211);let g=u.Ry({email:u.Z_().email("Invalid email address")}),S=(0,c.lm)(async e=>{y.kg.apiRequest("POST","/api/auth/forgot-password"),await (0,m.er)(e,m.jO,3);let t=await e.json(),{email:r}=g.parse(t);y.kg.info("Password reset requested",{email:r}),await (0,p.uD)();let i=await p.n5.findOne({email:r});if(!i)return y.kg.warn("Password reset requested for non-existent email",{email:r}),n.NextResponse.json({success:!0,message:"If an account with that email exists, we have sent a password reset link."},{status:200});let s=d.randomBytes(32).toString("hex"),o=new Date(Date.now()+36e5);await p.n5.updateOne({email:r},{resetToken:s,resetTokenExpiry:o});try{await (0,l.LS)(r,s),y.kg.info("Password reset email sent successfully",{email:r,userId:i.id})}catch(e){y.kg.emailError(r,"Password Reset Email",e)}return n.NextResponse.json({success:!0,message:"If an account with that email exists, we have sent a password reset link."},{status:200})}),h=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/forgot-password/route",pathname:"/api/auth/forgot-password",filename:"route",bundlePath:"app/api/auth/forgot-password/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\forgot-password\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:f,staticGenerationAsyncStorage:q,serverHooks:I}=h,b="/api/auth/forgot-password/route";function w(){return(0,a.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:q})}},3474:(e,t,r)=>{r.d(t,{KM:()=>s.Order,WD:()=>s.WD,n5:()=>s.n5,uD:()=>i.Z,xs:()=>s.xs});var i=r(89456),s=r(81515)},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>o}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class s{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:s,context:o,error:a,userId:n,requestId:u}=e,d=i[r],p=`[${t}] ${d}: ${s}`;return n&&(p+=` | User: ${n}`),u&&(p+=` | Request: ${u}`),o&&Object.keys(o).length>0&&(p+=` | Context: ${JSON.stringify(o)}`),a&&(p+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(p+=`
Stack: ${a.stack}`)),p}log(e,t,r,i){if(!this.shouldLog(e))return;let s={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},o=this.formatMessage(s);if(this.isDevelopment)switch(e){case 0:console.error(o);break;case 1:console.warn(o);break;case 2:console.info(o);break;case 3:console.debug(o)}else console.log(JSON.stringify(s))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,s){this.info(`API ${e} ${t} - ${r}`,{...s,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,s){this.error(`API ${e} ${t} failed`,r,{...s,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let o=new s},81515:(e,t,r)=>{r.d(t,{Cq:()=>x,Dd:()=>C,Order:()=>v,P_:()=>j,Pp:()=>U,Th:()=>T,Vv:()=>M,WD:()=>P,gc:()=>E,hQ:()=>$,kL:()=>R,mA:()=>A,n5:()=>w,nW:()=>O,p1:()=>L,qN:()=>D,wV:()=>k,xs:()=>N});var i=r(11185),s=r.n(i);let o=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),a=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),n=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),u=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),d=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),p=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),c=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),q=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),I=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),w=s().models.User||s().model("User",o),N=s().models.Product||s().model("Product",a),P=s().models.Category||s().model("Category",n),v=s().models.Order||s().model("Order",u),O=s().models.HomepageSetting||s().model("HomepageSetting",d),E=s().models.Testimonial||s().model("Testimonial",p),D=s().models.ProductImage||s().model("ProductImage",l),T=s().models.ProductVariant||s().model("ProductVariant",m),x=s().models.Review||s().model("Review",c),R=s().models.Address||s().model("Address",y),C=s().models.OrderItem||s().model("OrderItem",g),j=s().models.Notification||s().model("Notification",S),k=s().models.Coupon||s().model("Coupon",h);s().models.Wishlist||s().model("Wishlist",f);let A=s().models.Newsletter||s().model("Newsletter",q),U=s().models.ProductCategory||s().model("ProductCategory",I),$=s().models.WishlistItem||s().model("WishlistItem",b),B=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),M=s().models.NotificationTemplate||s().model("NotificationTemplate",B),_=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),L=s().models.Enquiry||s().model("Enquiry",_)},89456:(e,t,r)=>{r.d(t,{Z:()=>n});var i=r(11185),s=r.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let n=async function(){if(a.conn)return a.conn;a.promise||(a.promise=s().connect(o,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},8149:(e,t,r)=>{r.d(t,{Ri:()=>o,Xw:()=>a,er:()=>u,jO:()=>n});var i=r(919);function s(e){let t=new i.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,r)=>new Promise((i,s)=>{let o=t.get(r)||[0];0===o[0]&&t.set(r,o),o[0]+=1,o[0]>=e?s(Error("Rate limit exceeded")):i()})}}let o=s({interval:9e5,uniqueTokenPerInterval:500}),a=s({interval:6e4,uniqueTokenPerInterval:500}),n=s({interval:36e5,uniqueTokenPerInterval:500});async function u(e,t,r){let i=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);try{await t.check(r,i)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,9489,5630,138,5245,83],()=>r(59268));module.exports=i})();