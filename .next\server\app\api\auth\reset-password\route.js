"use strict";(()=>{var e={};e.id=9436,e.ids=[9436],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},6092:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>q,patchFetch:()=>I,requestAsyncStorage:()=>S,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h});var i={};r.r(i),r.d(i,{POST:()=>y});var n=r(49303),a=r(88716),o=r(60670),s=r(87070),d=r(98691),u=r(65630),p=r(29489),m=r(3474),c=r(8149);let l=u.Ry({token:u.Z_().min(1,"Reset token is required"),password:u.Z_().min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number")});async function y(e){try{await (0,c.er)(e,c.Ri,10);let t=await e.json(),{token:r,password:i}=l.parse(t);await (0,m.uD)();let n=new Date,a=await m.n5.findOne({resetToken:r,resetTokenExpiry:{$gt:n}}).lean();if(!a)return s.NextResponse.json({error:"Invalid or expired reset token"},{status:400});let o=await d.vp(i,12);return await m.n5.updateOne({_id:a._id},{$set:{password:o},$unset:{resetToken:"",resetTokenExpiry:""}}),s.NextResponse.json({message:"Password reset successfully"},{status:200})}catch(e){if(e instanceof p.j)return s.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});if(e instanceof Error&&e.message.includes("Rate limit"))return s.NextResponse.json({error:"Too many password reset attempts. Please try again later."},{status:429});return console.error("Reset password error:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\reset-password\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:S,staticGenerationAsyncStorage:h,serverHooks:f}=g,q="/api/auth/reset-password/route";function I(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}},3474:(e,t,r)=>{r.d(t,{KM:()=>n.Order,WD:()=>n.WD,n5:()=>n.n5,uD:()=>i.Z,xs:()=>n.xs});var i=r(89456),n=r(81515)},81515:(e,t,r)=>{r.d(t,{Cq:()=>j,Dd:()=>R,Order:()=>P,P_:()=>C,Pp:()=>U,Th:()=>D,Vv:()=>F,WD:()=>T,gc:()=>O,hQ:()=>M,kL:()=>x,mA:()=>k,n5:()=>w,nW:()=>v,p1:()=>L,qN:()=>E,wV:()=>A,xs:()=>N});var i=r(11185),n=r.n(i);let a=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),o=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),s=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),p=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),c=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),q=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),I=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),w=n().models.User||n().model("User",a),N=n().models.Product||n().model("Product",o),T=n().models.Category||n().model("Category",s),P=n().models.Order||n().model("Order",d),v=n().models.HomepageSetting||n().model("HomepageSetting",u),O=n().models.Testimonial||n().model("Testimonial",p),E=n().models.ProductImage||n().model("ProductImage",m),D=n().models.ProductVariant||n().model("ProductVariant",c),j=n().models.Review||n().model("Review",l),x=n().models.Address||n().model("Address",y),R=n().models.OrderItem||n().model("OrderItem",g),C=n().models.Notification||n().model("Notification",S),A=n().models.Coupon||n().model("Coupon",h);n().models.Wishlist||n().model("Wishlist",f);let k=n().models.Newsletter||n().model("Newsletter",q),U=n().models.ProductCategory||n().model("ProductCategory",I),M=n().models.WishlistItem||n().model("WishlistItem",b),B=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),F=n().models.NotificationTemplate||n().model("NotificationTemplate",B),G=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),L=n().models.Enquiry||n().model("Enquiry",G)},89456:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(11185),n=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let s=async function(){if(o.conn)return o.conn;o.promise||(o.promise=n().connect(a,{bufferCommands:!1}).then(e=>e));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},8149:(e,t,r)=>{r.d(t,{Ri:()=>a,Xw:()=>o,er:()=>d,jO:()=>s});var i=r(919);function n(e){let t=new i.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,r)=>new Promise((i,n)=>{let a=t.get(r)||[0];0===a[0]&&t.set(r,a),a[0]+=1,a[0]>=e?n(Error("Rate limit exceeded")):i()})}}let a=n({interval:9e5,uniqueTokenPerInterval:500}),o=n({interval:6e4,uniqueTokenPerInterval:500}),s=n({interval:36e5,uniqueTokenPerInterval:500});async function d(e,t,r){let i=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);try{await t.check(r,i)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,9489,5630,138],()=>r(6092));module.exports=i})();