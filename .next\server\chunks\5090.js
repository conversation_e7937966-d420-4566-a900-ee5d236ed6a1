"use strict";exports.id=5090,exports.ids=[5090],exports.modules={65090:(e,s,t)=>{t.d(s,{default:()=>H});var a=t(10326),l=t(17577),r=t(90434),i=t(46226),n=t(35047),c=t(77109),x=t(90748),o=t(6507),d=t(57671),h=t(79635),m=t(95920),f=t(34565),u=t(94494),g=t(52807),j=t(18019),p=t(5932),N=t(58038),b=t(36283),y=t(14228),v=t(35351),w=t(94019),Z=t(88307),C=t(34738),S=t(71806),k=t(74857);let A=({isOpen:e,onClose:s})=>{let{data:t}=(0,c.useSession)();if(!e)return null;let l=[{href:"/",icon:a.jsx(m.Z,{className:"w-5 h-5 text-white"}),label:"Home"},{href:"/shop",icon:a.jsx(f.Z,{className:"w-5 h-5 text-white"}),label:"Shop"},{href:"/about",icon:a.jsx(j.Z,{className:"w-5 h-5 text-white"}),label:"About"},{href:"/contact",icon:a.jsx(p.Z,{className:"w-5 h-5 text-white"}),label:"Contact"}],i=[{href:"/privacy",icon:a.jsx(N.Z,{className:"w-5 h-5 text-white"}),label:"Privacy Policy"},{href:"/terms",icon:a.jsx(b.Z,{className:"w-5 h-5 text-white"}),label:"Terms and Conditions"},{href:"/shipping",icon:a.jsx(y.Z,{className:"w-5 h-5 text-white"}),label:"Shippings and Returns"},{href:"/faq",icon:a.jsx(v.Z,{className:"w-5 h-5 text-white"}),label:"FAQs"}];return a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden",children:a.jsx("div",{className:"fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-60",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"p-4 flex items-center justify-between border-b",children:[(0,a.jsxs)("h2",{className:"font-bold text-lg",children:["Welcome, ",t?.user?.name?.split(" ")[0]||"User"]}),a.jsx("button",{onClick:s,className:"p-2 rounded-full hover:bg-gray-100",children:a.jsx(w.Z,{className:"w-5 h-5 text-gray-600"})})]}),a.jsx("div",{className:"p-4 border-b",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(Z.Z,{className:"absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400"}),a.jsx("input",{type:"text",placeholder:"Search products...",className:"w-full bg-gray-100 border border-gray-200 rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-green-500"})]})}),(0,a.jsxs)("nav",{className:"flex-1 overflow-y-auto p-4 space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2",children:"Main"}),a.jsx("ul",{className:"space-y-1",children:l.map(e=>a.jsx("li",{children:(0,a.jsxs)(r.default,{href:e.href,onClick:s,className:"flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium",children:[a.jsx("div",{className:"mr-3 bg-green-600 p-2 rounded-full",children:e.icon}),e.label]})},e.href))})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2",children:"Information"}),a.jsx("ul",{className:"space-y-1",children:i.map(e=>a.jsx("li",{children:(0,a.jsxs)(r.default,{href:e.href,onClick:s,className:"flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium",children:[a.jsx("div",{className:"mr-3 bg-green-600 p-2 rounded-full",children:e.icon}),e.label]})},e.href))})]})]}),a.jsx("div",{className:"p-4 border-t",children:(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[a.jsx("a",{href:"#",className:"p-2 text-gray-500 hover:text-green-600",children:a.jsx(C.Z,{className:"w-6 h-6"})}),a.jsx("a",{href:"#",className:"p-2 text-gray-500 hover:text-green-600",children:a.jsx(S.Z,{className:"w-6 h-6"})}),a.jsx("a",{href:"#",className:"p-2 text-gray-500 hover:text-green-600",children:a.jsx(k.Z,{className:"w-6 h-6"})})]})})]})})})},H=({children:e})=>{let s=(0,n.usePathname)(),{data:t}=(0,c.useSession)(),{state:j}=(0,u.j)(),{unreadCount:p}=(0,g.z)(),[N,b]=(0,l.useState)(!1),[y,v]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{b(!0)},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[a.jsx(A,{isOpen:y,onClose:()=>v(!1)}),a.jsx("header",{className:"bg-white shadow-sm sticky top-0 z-40",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto lg:px-8",children:[(0,a.jsxs)("div",{className:"max-w-md mx-auto lg:hidden px-4 py-3 flex items-center justify-between",children:[a.jsx("button",{onClick:()=>v(!0),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(x.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx(r.default,{href:"/",className:"flex items-center",children:a.jsx(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[40px] w-auto"})}),(0,a.jsxs)(r.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[a.jsx(o.Z,{className:"w-5 h-5 text-gray-600"}),N&&t?.user&&p>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:p>99?"99+":p})]})]}),(0,a.jsxs)("div",{className:"hidden lg:flex px-4 py-3 items-center justify-between",children:[a.jsx(r.default,{href:"/",className:"flex items-center",children:a.jsx(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[60px] w-auto"})}),(0,a.jsxs)("nav",{className:"flex items-center space-x-8",children:[a.jsx(r.default,{href:"/",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Home"}),a.jsx(r.default,{href:"/shop",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Shop"}),a.jsx(r.default,{href:"/about",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"About"}),a.jsx(r.default,{href:"/contact",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Contact"}),a.jsx(r.default,{href:"/faq",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"FAQs"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(r.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[a.jsx(o.Z,{className:"w-5 h-5 text-gray-600"}),N&&t?.user&&p>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:p>99?"99+":p})]}),(0,a.jsxs)(r.default,{href:"/cart",className:"relative p-2 rounded-full hover:bg-gray-100 transition-colors",children:[a.jsx(d.Z,{className:"w-5 h-5 text-gray-600"}),N&&j.itemCount>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:j.itemCount})]}),a.jsx(r.default,{href:"/profile",className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:a.jsx(h.Z,{className:"w-5 h-5 text-gray-600"})})]})]})]})}),a.jsx("main",{className:"flex-1 pb-20 lg:pb-8",children:a.jsx("div",{className:"max-w-7xl mx-auto lg:px-8",children:a.jsx("div",{className:"max-w-md mx-auto lg:max-w-none",children:e})})}),a.jsx("nav",{className:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 lg:hidden",children:a.jsx("div",{className:"max-w-md mx-auto px-4 py-2",children:(0,a.jsxs)("div",{className:"flex justify-around",children:[(0,a.jsxs)(r.default,{href:"/",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${"/"===s?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(m.Z,{className:"w-6 h-6 mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Home"})]}),(0,a.jsxs)(r.default,{href:"/shop",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${"/shop"===s?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(f.Z,{className:"w-6 h-6 mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Shop"})]}),(0,a.jsxs)(r.default,{href:"/cart",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors relative ${"/cart"===s?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(d.Z,{className:"w-6 h-6 mb-1"}),N&&j.itemCount>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:j.itemCount}),a.jsx("span",{className:"text-xs font-medium",children:"Cart"})]}),(0,a.jsxs)(r.default,{href:"/profile",className:`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${"/profile"===s?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx(h.Z,{className:"w-6 h-6 mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Profile"})]})]})})}),a.jsx("footer",{className:"hidden lg:block bg-white border-t border-gray-200",children:a.jsx("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["\xa9 ",new Date().getFullYear()," Herbalicious. All rights reserved."]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[a.jsx(r.default,{href:"/privacy",className:"text-sm text-gray-500 hover:text-green-600",children:"Privacy Policy"}),a.jsx(r.default,{href:"/terms",className:"text-sm text-gray-500 hover:text-green-600",children:"Terms and Conditions"}),a.jsx(r.default,{href:"/shipping",className:"text-sm text-gray-500 hover:text-green-600",children:"Shippings and Returns"}),a.jsx(r.default,{href:"/faq",className:"text-sm text-gray-500 hover:text-green-600",children:"FAQs"})]})]})})})]})}}};