(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7395],{21037:function(e,t,s){Promise.resolve().then(s.bind(s,34422))},34422:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return m}});var i=s(57437),a=s(2265),r=s(27648),l=s(44743),n=s(53417);let o=(0,s(39763).Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var c=s(98728),d=s(70525),h=s(42208),u=s(31047),x=s(99397),m=()=>{let[e,t]=(0,a.useState)(null),[s,m]=(0,a.useState)(!0),[g,f]=(0,a.useState)(null);(0,a.useEffect)(()=>{v()},[]);let v=async()=>{try{m(!0);let e=await fetch("/api/admin/notifications/stats"),s=await e.json();s.success?t(s.stats):f("Failed to fetch notification statistics")}catch(e){console.error("Error fetching notification stats:",e),f("Failed to fetch notification statistics")}finally{m(!1)}},p=[{title:"Send Notification",description:"Send a notification to users",href:"/admin/notifications/send",icon:l.Z,color:"bg-green-500",textColor:"text-green-600"},{title:"Broadcast Message",description:"Send message to all users",href:"/admin/notifications/broadcast",icon:n.Z,color:"bg-blue-500",textColor:"text-blue-600"},{title:"Notification History",description:"View sent notifications",href:"/admin/notifications/history",icon:o,color:"bg-purple-500",textColor:"text-purple-600"},{title:"Templates",description:"Manage notification templates",href:"/admin/notifications/templates",icon:c.Z,color:"bg-orange-500",textColor:"text-orange-600"}],y=[{title:"Total Sent",value:(null==e?void 0:e.totalSent)||0,icon:l.Z,color:"bg-blue-500"},{title:"Delivered",value:(null==e?void 0:e.totalDelivered)||0,icon:d.Z,color:"bg-green-500"},{title:"Read",value:(null==e?void 0:e.totalRead)||0,icon:h.Z,color:"bg-purple-500"},{title:"Recent Activity",value:(null==e?void 0:e.recentActivity)||0,icon:u.Z,color:"bg-orange-500"}];return s?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"animate-pulse",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-2"}),(0,i.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,i.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]},t))})]}):g?(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,i.jsx)("p",{className:"text-red-600",children:g}),(0,i.jsx)("button",{onClick:v,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Notifications"}),(0,i.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage and send notifications to your customers"})]}),(0,i.jsxs)(r.default,{href:"/admin/notifications/send",className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,i.jsx)(x.Z,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Send Notification"})]})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:y.map((e,t)=>{let s=e.icon;return(0,i.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value.toLocaleString()})]}),(0,i.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-lg flex items-center justify-center"),children:(0,i.jsx)(s,{className:"w-6 h-6 text-white"})})]})},t)})}),e&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Delivery Rate"}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(e.deliveryRate,"%")}})})}),(0,i.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[e.deliveryRate.toFixed(1),"%"]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Read Rate"}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.readRate,"%")}})})}),(0,i.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:[e.readRate.toFixed(1),"%"]})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map((e,t)=>{let s=e.icon;return(0,i.jsx)(r.default,{href:e.href,className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform"),children:(0,i.jsx)(s,{className:"w-6 h-6 text-white"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"font-semibold ".concat(e.textColor," group-hover:text-gray-900 transition-colors"),children:e.title}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]})},t)})})]})]})}},39763:function(e,t,s){"use strict";s.d(t,{Z:function(){return l}});var i=s(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let s=(0,i.forwardRef)((s,l)=>{let{color:n="currentColor",size:o=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:h="",children:u,...x}=s;return(0,i.createElement)("svg",{ref:l,...a,width:o,height:o,stroke:n,strokeWidth:d?24*Number(c)/Number(o):c,className:["lucide","lucide-".concat(r(e)),h].join(" "),...x},[...t.map(e=>{let[t,s]=e;return(0,i.createElement)(t,s)}),...Array.isArray(u)?u:[u]])});return s.displayName="".concat(e),s}},31047:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});let i=(0,s(39763).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42208:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});let i=(0,s(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},53417:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});let i=(0,s(39763).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},99397:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});let i=(0,s(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},44743:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});let i=(0,s(39763).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},98728:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});let i=(0,s(39763).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},70525:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});let i=(0,s(39763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])}},function(e){e.O(0,[7648,2971,2117,1744],function(){return e(e.s=21037)}),_N_E=e.O()}]);