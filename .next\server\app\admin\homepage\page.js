(()=>{var e={};e.id=7416,e.ids=[7416],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},59569:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(15849),t(90596),t(37254),t(35866);var r=t(23191),a=t(88716),l=t(37922),n=t.n(l),o=t(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let d=["",{children:["admin",{children:["homepage",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15849)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\homepage\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\homepage\\page.tsx"],m="/admin/homepage/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/homepage/page",pathname:"/admin/homepage",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78835:(e,s,t)=>{Promise.resolve().then(t.bind(t,42169))},42169:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var r=t(10326),a=t(17577),l=t(77109),n=t(35047),o=t(92878),i=t(32933),d=t(87888),c=t(24230),m=t(40617),u=t(94019),x=t(941),h=t(88307);let g=({products:e,selectedProductId:s,onProductSelect:t,placeholder:l="Search and select a product...",className:n=""})=>{let[o,d]=(0,a.useState)(!1),[c,m]=(0,a.useState)(""),g=(0,a.useRef)(null),p=(0,a.useRef)(null),b=e.filter(e=>e.name.toLowerCase().includes(c.toLowerCase())||e.slug&&e.slug.toLowerCase().includes(c.toLowerCase())),f=e.find(e=>e.id===s);(0,a.useEffect)(()=>{let e=e=>{g.current&&!g.current.contains(e.target)&&(d(!1),m(""))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.useEffect)(()=>{o&&p.current&&p.current.focus()},[o]);let y=e=>{t(e),d(!1),m("")};return(0,r.jsxs)("div",{className:`relative ${n}`,ref:g,children:[r.jsx("div",{onClick:()=>{d(!o),o||m("")},className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus-within:border-indigo-500 focus-within:ring-1 focus-within:ring-indigo-500 cursor-pointer",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex-1 min-w-0",children:f?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[f.images?.[0]&&r.jsx("img",{src:f.images[0].url,alt:f.images[0].alt||f.name,className:"w-6 h-6 rounded object-cover flex-shrink-0"}),r.jsx("span",{className:"text-sm text-gray-900 truncate",children:f.name})]}):r.jsx("span",{className:"text-sm text-gray-500",children:l})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[f&&r.jsx("button",{onClick:e=>{e.stopPropagation(),t(null)},className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"Clear selection",children:r.jsx(u.Z,{className:"w-4 h-4 text-gray-400"})}),r.jsx(x.Z,{className:`w-4 h-4 text-gray-400 transition-transform ${o?"rotate-180":""}`})]})]})}),o&&(0,r.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden",children:[r.jsx("div",{className:"p-3 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),r.jsx("input",{ref:p,type:"text",placeholder:"Search products...",value:c,onChange:e=>m(e.target.value),className:"w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"})]})}),(0,r.jsxs)("div",{className:"max-h-60 overflow-y-auto",children:[(0,r.jsxs)("div",{onClick:()=>y(null),className:`px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${s?"text-gray-900":"bg-indigo-50 text-indigo-700"}`,children:[r.jsx("span",{className:"text-sm",children:"None - Use automatic selection"}),!s&&r.jsx(i.Z,{className:"w-4 h-4"})]}),b.length>0?b.map(e=>(0,r.jsxs)("div",{onClick:()=>y(e.id),className:`px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${s===e.id?"bg-indigo-50 text-indigo-700":"text-gray-900"}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 min-w-0 flex-1",children:[e.images?.[0]&&r.jsx("img",{src:e.images[0].url,alt:e.images[0].alt||e.name,className:"w-8 h-8 rounded object-cover flex-shrink-0"}),(0,r.jsxs)("div",{className:"min-w-0 flex-1",children:[r.jsx("div",{className:"text-sm font-medium truncate",children:e.name}),e.price&&(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["₹",e.price]})]})]}),s===e.id&&r.jsx(i.Z,{className:"w-4 h-4 flex-shrink-0"})]},e.id)):(0,r.jsxs)("div",{className:"px-3 py-4 text-sm text-gray-500 text-center",children:['No products found matching "',c,'"']})]})]})]})};var p=t(89392);let b=({value:e,onChange:s,label:t,presetColors:l=["#22c55e","#16a34a","#15803d","#f0fdf4","#dcfce7","#bbf7d0","#86efac","#4ade80","#3b82f6","#6366f1","#8b5cf6","#ec4899","#f59e0b","#ef4444","#6b7280","#1f2937"],className:n=""})=>{let[o,d]=(0,a.useState)(!1),[c,m]=(0,a.useState)(e),u=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=e=>{u.current&&!u.current.contains(e.target)&&d(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let x=e=>{s(e),m(e),d(!1)};return(0,r.jsxs)("div",{className:`relative ${n}`,ref:u,children:[t&&r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:t}),(0,r.jsxs)("div",{onClick:()=>d(!o),className:"w-full h-10 border border-gray-300 rounded-md cursor-pointer flex items-center px-3 bg-white hover:border-gray-400 transition-colors",children:[r.jsx("div",{className:"w-6 h-6 rounded border border-gray-300 mr-3 flex-shrink-0",style:{backgroundColor:e}}),r.jsx("span",{className:"text-sm text-gray-700 flex-1",children:e}),r.jsx(p.Z,{className:"w-4 h-4 text-gray-400"})]}),o&&(0,r.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg p-4",children:[(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Preset Colors"}),r.jsx("div",{className:"grid grid-cols-8 gap-2",children:l.map(s=>r.jsx("button",{onClick:()=>x(s),className:"w-8 h-8 rounded border border-gray-300 hover:scale-110 transition-transform relative",style:{backgroundColor:s},title:s,children:e===s&&r.jsx(i.Z,{className:"w-4 h-4 text-white absolute inset-0 m-auto drop-shadow-sm"})},s))})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Custom Color"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"color",value:c,onChange:e=>{let t=e.target.value;m(t),s(t)},className:"w-10 h-8 border border-gray-300 rounded cursor-pointer"}),r.jsx("input",{type:"text",value:c,onChange:e=>{m(e.target.value),e.target.value.match(/^#[0-9A-Fa-f]{6}$/)&&s(e.target.value)},placeholder:"#22c55e",className:"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"})]})]}),(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Brand Colors"}),r.jsx("div",{className:"grid grid-cols-4 gap-2",children:[{name:"Primary Green",color:"#22c55e"},{name:"Dark Green",color:"#16a34a"},{name:"Light Green",color:"#f0fdf4"},{name:"Green Accent",color:"#dcfce7"}].map(({name:e,color:s})=>(0,r.jsxs)("button",{onClick:()=>x(s),className:"flex flex-col items-center p-2 rounded hover:bg-gray-50 transition-colors",title:e,children:[r.jsx("div",{className:"w-6 h-6 rounded border border-gray-300 mb-1",style:{backgroundColor:s}}),r.jsx("span",{className:"text-xs text-gray-600 text-center",children:e})]},s))})]})]})]})};var f=t(24061),y=t(83855),j=t(33734);let v=(0,t(76557).Z)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);var N=t(98091),w=t(31215);let k=({isOpen:e,onClose:s,onSave:t})=>{let[l,n]=(0,a.useState)([]),[o,i]=(0,a.useState)(!1),[d,c]=(0,a.useState)(null),[m,x]=(0,a.useState)(!1),[h,g]=(0,a.useState)(null),[p,b]=(0,a.useState)({name:"",content:"",rating:5,image:"",position:"",company:"",isActive:!0,order:0});(0,a.useEffect)(()=>{e&&k()},[e]);let k=async()=>{try{i(!0);let e=await fetch("/api/testimonials"),s=await e.json();s.success&&n(s.data)}catch(e){console.error("Error fetching testimonials:",e),C("error","Failed to load testimonials")}finally{i(!1)}},C=(e,s)=>{g({type:e,message:s}),setTimeout(()=>g(null),3e3)},S=async e=>{e.preventDefault();try{i(!0);let e=d?`/api/testimonials/${d.id}`:"/api/testimonials",s=await fetch(e,{method:d?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)}),r=await s.json();r.success?(C("success",r.message),await k(),P(),t()):C("error",r.error)}catch(e){console.error("Error saving testimonial:",e),C("error","Failed to save testimonial")}finally{i(!1)}},T=async e=>{if(confirm("Are you sure you want to delete this testimonial?"))try{i(!0);let s=await fetch(`/api/testimonials/${e}`,{method:"DELETE"}),r=await s.json();r.success?(C("success","Testimonial deleted successfully"),await k(),t()):C("error",r.error)}catch(e){console.error("Error deleting testimonial:",e),C("error","Failed to delete testimonial")}finally{i(!1)}},B=e=>{c(e),b({name:e.name,content:e.content,rating:e.rating,image:e.image||"",position:e.position||"",company:e.company||"",isActive:e.isActive,order:e.order}),x(!0)},P=()=>{c(null),x(!1),b({name:"",content:"",rating:5,image:"",position:"",company:"",isActive:!0,order:0})};return e?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(f.Z,{className:"h-6 w-6 text-green-600 mr-2"}),r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Manage Testimonials"})]}),r.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:r.jsx(u.Z,{className:"h-6 w-6"})})]}),h&&r.jsx("div",{className:`p-4 ${"success"===h.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"}`,children:h.message}),r.jsx("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:m?(0,r.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:d?"Edit Testimonial":"Add New Testimonial"}),r.jsx("button",{type:"button",onClick:P,className:"text-gray-400 hover:text-gray-600",children:r.jsx(u.Z,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name *"}),r.jsx("input",{type:"text",required:!0,value:p.name,onChange:e=>b({...p,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Customer name"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating"}),r.jsx("select",{value:p.rating,onChange:e=>b({...p,rating:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("option",{value:e,children:[e," Star",1!==e?"s":""]},e))})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Testimonial Content *"}),r.jsx("textarea",{required:!0,rows:3,value:p.content,onChange:e=>b({...p,content:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"What did the customer say about your products?"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Position"}),r.jsx("input",{type:"text",value:p.position,onChange:e=>b({...p,position:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"e.g., Marketing Manager"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company"}),r.jsx("input",{type:"text",value:p.company,onChange:e=>b({...p,company:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"e.g., ABC Corp"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Image URL"}),r.jsx("input",{type:"url",value:p.image,onChange:e=>b({...p,image:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"https://example.com/image.jpg"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Order"}),r.jsx("input",{type:"number",min:"0",value:p.order,onChange:e=>b({...p,order:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"0"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",id:"isActive",checked:p.isActive,onChange:e=>b({...p,isActive:e.target.checked}),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("label",{htmlFor:"isActive",className:"ml-2 text-sm text-gray-700",children:"Active (show on website)"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[r.jsx("button",{type:"button",onClick:P,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",children:"Cancel"}),(0,r.jsxs)("button",{type:"submit",disabled:o,className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50",children:[r.jsx(w.Z,{className:"h-4 w-4 mr-2"}),o?"Saving...":d?"Update":"Create"]})]})]}):(0,r.jsxs)("div",{children:[r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("button",{onClick:()=>{c(null),b({name:"",content:"",rating:5,image:"",position:"",company:"",isActive:!0,order:l.length}),x(!0)},className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:[r.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Add New Testimonial"]})}),o?(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"}),r.jsx("p",{className:"text-gray-500 mt-2",children:"Loading testimonials..."})]}):0===l.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(f.Z,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500",children:"No testimonials found. Add your first testimonial!"})]}):r.jsx("div",{className:"grid gap-4",children:l.map(e=>r.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[r.jsx("h3",{className:"font-medium text-gray-900",children:e.name}),e.position&&e.company&&(0,r.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:[e.position," at ",e.company]}),r.jsx("div",{className:"ml-auto flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((s,t)=>r.jsx(j.Z,{className:`h-4 w-4 ${t<e.rating?"text-yellow-400 fill-current":"text-gray-300"}`},t))})]}),r.jsx("p",{className:"text-gray-600 text-sm mb-2",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[r.jsx("span",{className:`px-2 py-1 rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:e.isActive?"Active":"Inactive"}),(0,r.jsxs)("span",{className:"ml-2",children:["Order: ",e.order]})]})]}),(0,r.jsxs)("div",{className:"flex items-center ml-4 space-x-2",children:[r.jsx("button",{onClick:()=>B(e),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",children:r.jsx(v,{className:"h-4 w-4"})}),r.jsx("button",{onClick:()=>T(e.id),className:"p-2 text-gray-400 hover:text-red-600 transition-colors",children:r.jsx(N.Z,{className:"h-4 w-4"})})]})]})},e.id))})]})})]})}):null};function C(){let{data:e,status:s}=(0,l.useSession)();(0,n.useRouter)();let[t,u]=(0,a.useState)(!0),[x,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)(null),[y,j]=(0,a.useState)([]),[v,N]=(0,a.useState)(!1),[w,C]=(0,a.useState)({id:"",heroTitle:"Natural Skincare Essentials",heroSubtitle:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",heroCtaText:"Shop Collection",heroCtaLink:"/shop",heroSecondaryCtaText:"View Categories",heroSecondaryCtaLink:"/categories",heroBadgeText:"New Collection",heroBackgroundColor:"#f0fdf4",showHero:!0,trustIndicator1Value:"100%",trustIndicator1Label:"Natural",trustIndicator2Value:"500+",trustIndicator2Label:"Happy Customers",trustIndicator3Value:"50+",trustIndicator3Label:"Products",trustIndicator4Value:"4.8★",trustIndicator4Label:"Rating",productOfTheMonthId:null,showProductOfMonth:!0,bannerText:null,bannerCtaText:null,bannerCtaLink:null,bannerBackgroundColor:"#22c55e",showBanner:!0,showCategories:!0,productSectionBgColor:"#f0fdf4",bestsellerIds:[],showBestsellers:!0,newsletterTitle:"Stay Updated",newsletterSubtitle:"Get the latest updates on new products and exclusive offers",showNewsletter:!0,showTrustBadges:!0,flashSaleTitle:"Weekend Flash Sale",flashSaleSubtitle:"Get 25% off all natural skincare products",flashSaleEndDate:null,flashSaleBackgroundColor:"#16a34a",flashSalePercentage:25,showFlashSale:!0,testimonialsTitle:"What Our Customers Say",testimonialsSubtitle:"Real reviews from real customers who love our natural skincare",testimonialsBackgroundColor:"#f0fdf4",showTestimonials:!0,isActive:!0,createdAt:"",updatedAt:""}),S=(e,s)=>{f({type:e,message:s}),setTimeout(()=>f(null),3e3)},T=async()=>{h(!0);try{let e=await fetch("/api/homepage-settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(w)}),s=await e.json();s.success?(S("success","Homepage settings saved successfully"),s.data&&C(s.data)):S("error",s.error||"Failed to save settings")}catch(e){console.error("Error saving settings:",e),S("error","Failed to save settings")}finally{h(!1)}},B=(e,s)=>{C(t=>({...t,[e]:"flashSalePercentage"===e?parseFloat(s):s}))};return"loading"===s||t?r.jsx(o.fq,{}):(0,r.jsxs)("div",{className:"p-6",children:[p&&(0,r.jsxs)("div",{className:`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center ${"success"===p.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:["success"===p.type?r.jsx(i.Z,{className:"w-5 h-5 mr-2"}):r.jsx(d.Z,{className:"w-5 h-5 mr-2"}),p.message]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Homepage Settings"}),r.jsx("p",{className:"text-gray-600",children:"Customize your homepage content, featured products, and promotional banners"}),r.jsx("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100",children:(0,r.jsxs)("p",{className:"text-blue-800 text-sm",children:[r.jsx("span",{className:"font-medium",children:"Tip:"})," Changes made here will be reflected on your store's homepage immediately after saving. For best results, use high-quality images and compelling copy for your featured products."]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"Hero Section"}),(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:w.showHero,onChange:e=>B("showHero",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Show Hero Section"})]})]}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Configure the main hero section that appears at the top of your homepage."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Hero Title"}),r.jsx("input",{type:"text",value:w.heroTitle||"",onChange:e=>B("heroTitle",e.target.value),placeholder:"Natural Skincare Essentials",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Button Text"}),r.jsx("input",{type:"text",value:w.heroCtaText||"",onChange:e=>B("heroCtaText",e.target.value),placeholder:"Shop Collection",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Hero Subtitle"}),r.jsx("textarea",{value:w.heroSubtitle||"",onChange:e=>B("heroSubtitle",e.target.value),placeholder:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Button Link"}),r.jsx("input",{type:"text",value:w.heroCtaLink||"",onChange:e=>B("heroCtaLink",e.target.value),placeholder:"/shop",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Secondary CTA Text"}),r.jsx("input",{type:"text",value:w.heroSecondaryCtaText||"",onChange:e=>B("heroSecondaryCtaText",e.target.value),placeholder:"View Categories",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Secondary CTA Link"}),r.jsx("input",{type:"text",value:w.heroSecondaryCtaLink||"",onChange:e=>B("heroSecondaryCtaLink",e.target.value),placeholder:"/categories",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Badge Text"}),r.jsx("input",{type:"text",value:w.heroBadgeText||"",onChange:e=>B("heroBadgeText",e.target.value),placeholder:"New Collection",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx(b,{value:w.heroBackgroundColor||"#f0fdf4",onChange:e=>B("heroBackgroundColor",e),label:"Hero Background Color"}),r.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Background color for the hero section"})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Trust Indicators"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 1"}),r.jsx("input",{type:"text",value:w.trustIndicator1Value||"",onChange:e=>B("trustIndicator1Value",e.target.value),placeholder:"100%",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 1"}),r.jsx("input",{type:"text",value:w.trustIndicator1Label||"",onChange:e=>B("trustIndicator1Label",e.target.value),placeholder:"Natural",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 2"}),r.jsx("input",{type:"text",value:w.trustIndicator2Value||"",onChange:e=>B("trustIndicator2Value",e.target.value),placeholder:"500+",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 2"}),r.jsx("input",{type:"text",value:w.trustIndicator2Label||"",onChange:e=>B("trustIndicator2Label",e.target.value),placeholder:"Happy Customers",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 3"}),r.jsx("input",{type:"text",value:w.trustIndicator3Value||"",onChange:e=>B("trustIndicator3Value",e.target.value),placeholder:"50+",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 3"}),r.jsx("input",{type:"text",value:w.trustIndicator3Label||"",onChange:e=>B("trustIndicator3Label",e.target.value),placeholder:"Products",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 4"}),r.jsx("input",{type:"text",value:w.trustIndicator4Value||"",onChange:e=>B("trustIndicator4Value",e.target.value),placeholder:"4.8★",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 4"}),r.jsx("input",{type:"text",value:w.trustIndicator4Label||"",onChange:e=>B("trustIndicator4Label",e.target.value),placeholder:"Rating",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"Product of the Month"}),(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:w.showProductOfMonth,onChange:e=>B("showProductOfMonth",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Show Product of the Month"})]})]}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Select a featured product to highlight on the homepage. This product will be displayed prominently on the homepage."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search and Select Featured Product"}),r.jsx(g,{products:y,selectedProductId:w.productOfTheMonthId,onProductSelect:e=>B("productOfTheMonthId",e),placeholder:"Search and select a product..."}),r.jsx("p",{className:"text-gray-500 text-xs mt-2",children:'If no product is selected, the system will automatically feature the first product marked as "Featured" in the product settings.'})]}),w.productOfTheMonthId&&(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[r.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Selected Product Preview"}),(()=>{let e=y.find(e=>e.id===w.productOfTheMonthId);return e?(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[e.images?.[0]&&r.jsx("img",{src:e.images[0].url,alt:e.name,className:"w-16 h-16 object-cover rounded-lg"}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:["₹",e.price]})]})]}):null})()]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[r.jsx(b,{value:w.productSectionBgColor||"#f0fdf4",onChange:e=>B("productSectionBgColor",e),label:"Product Section Background Color"}),r.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Background color for the Product of the Month section"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"Promotional Banner"}),(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:w.showBanner,onChange:e=>B("showBanner",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Show Promotional Banner"})]})]}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Create an eye-catching banner to promote special offers, sales, or important announcements."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Banner Text"}),r.jsx("input",{type:"text",value:w.bannerText||"",onChange:e=>B("bannerText",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Limited time offer! Get 20% off on all herbal products"}),r.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Keep it short and compelling to grab attention"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Button Text"}),r.jsx("input",{type:"text",value:w.bannerCtaText||"",onChange:e=>B("bannerCtaText",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Shop Now"}),r.jsx("p",{className:"text-gray-500 text-xs mt-1",children:'Action-oriented text like "Shop Now", "Learn More", or "Get Offer"'})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Link"}),r.jsx("input",{type:"text",value:w.bannerCtaLink||"",onChange:e=>B("bannerCtaLink",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"/shop"}),r.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"URL where users should be redirected when clicking the CTA"})]}),(0,r.jsxs)("div",{children:[r.jsx(b,{value:w.bannerBackgroundColor||"#22c55e",onChange:e=>B("bannerBackgroundColor",e),label:"Banner Background Color"}),r.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Choose a color that matches your brand"})]})]}),w.bannerText&&(0,r.jsxs)("div",{className:"mt-6 p-4 rounded-lg text-white",style:{backgroundColor:w.bannerBackgroundColor||"#22c55e"},children:[r.jsx("h3",{className:"font-bold text-sm mb-1",children:w.bannerText}),w.bannerCtaText&&w.bannerCtaLink&&(0,r.jsxs)("a",{href:w.bannerCtaLink,className:"inline-flex items-center text-xs font-medium underline",children:[w.bannerCtaText,r.jsx(c.Z,{className:"ml-1 w-3 h-3"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"Bestseller Products"}),(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:w.showBestsellers,onChange:e=>B("showBestsellers",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Show Bestsellers Section"})]})]}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Manually select products to feature as bestsellers. If none are selected, the system will automatically show products with the most reviews."}),(0,r.jsxs)("div",{className:"space-y-4",children:[w.bestsellerIds&&w.bestsellerIds.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:["Selected Bestsellers (",w.bestsellerIds.length,"/4)"]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:w.bestsellerIds.map((e,s)=>{let t=y.find(s=>s.id===e);return t?(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[t.images?.[0]&&r.jsx("img",{src:t.images[0].url,alt:t.name,className:"w-12 h-12 object-cover rounded-lg"}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-900 text-sm",children:t.name}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:["₹",t.price]})]})]}),r.jsx("button",{onClick:()=>{B("bestsellerIds",w.bestsellerIds.filter(s=>s!==e))},className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},e):null})})]}),(!w.bestsellerIds||w.bestsellerIds.length<4)&&(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Add Bestseller Product"}),r.jsx(g,{products:y.filter(e=>!w.bestsellerIds?.includes(e.id)),selectedProductId:"",onProductSelect:e=>{e&&B("bestsellerIds",[...w.bestsellerIds||[],e])},placeholder:"Search and select a bestseller product..."})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Settings"}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Control the visibility and activation of homepage features"}),(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx("div",{className:"flex items-center h-5",children:r.jsx("input",{id:"isActive",type:"checkbox",checked:w.isActive,onChange:e=>B("isActive",e.target.checked),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"})}),(0,r.jsxs)("div",{className:"ml-3 text-sm",children:[r.jsx("label",{htmlFor:"isActive",className:"font-medium text-gray-700",children:"Enable Custom Homepage Settings"}),r.jsx("p",{className:"text-gray-500",children:"When enabled, your custom settings will be displayed on the homepage. When disabled, default content will be shown."})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"Newsletter Signup"}),(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:w.showNewsletter,onChange:e=>B("showNewsletter",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Show Newsletter Section"})]})]}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Customize the newsletter signup section that appears on the homepage"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Newsletter Title"}),r.jsx("input",{type:"text",value:w.newsletterTitle||"",onChange:e=>B("newsletterTitle",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Stay Updated"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Newsletter Subtitle"}),r.jsx("input",{type:"text",value:w.newsletterSubtitle||"",onChange:e=>B("newsletterSubtitle",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Get the latest updates on new products and exclusive offers"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Section Visibility"}),r.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Control which sections appear on your homepage"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50",children:[r.jsx("input",{type:"checkbox",checked:w.showCategories,onChange:e=>B("showCategories",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-3 text-sm font-medium text-gray-700",children:"Show Categories Section"})]}),(0,r.jsxs)("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50",children:[r.jsx("input",{type:"checkbox",checked:w.showTrustBadges,onChange:e=>B("showTrustBadges",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-3 text-sm font-medium text-gray-700",children:"Show Trust Badges Section"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Flash Sale Section"}),(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:w.showFlashSale,onChange:e=>B("showFlashSale",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Show Flash Sale"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Flash Sale Title"}),r.jsx("input",{type:"text",value:w.flashSaleTitle||"",onChange:e=>B("flashSaleTitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Weekend Flash Sale"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Flash Sale Subtitle"}),r.jsx("input",{type:"text",value:w.flashSaleSubtitle||"",onChange:e=>B("flashSaleSubtitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Get 25% off all natural skincare products"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Percentage"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:"number",min:"0",max:"100",value:w.flashSalePercentage||25,onChange:e=>B("flashSalePercentage",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 pr-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"25"}),r.jsx("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500",children:"%"})]}),r.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Store-wide discount percentage during flash sale"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date & Time"}),r.jsx("input",{type:"datetime-local",value:w.flashSaleEndDate||"",onChange:e=>B("flashSaleEndDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Background Color"}),r.jsx(b,{value:w.flashSaleBackgroundColor||"#16a34a",onChange:e=>B("flashSaleBackgroundColor",e)})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Testimonials Section"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>N(!0),className:"inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors",children:[r.jsx(m.Z,{className:"h-4 w-4 mr-1"}),"Manage Testimonials"]}),(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",checked:w.showTestimonials,onChange:e=>B("showTestimonials",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),r.jsx("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Show Testimonials"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Section Title"}),r.jsx("input",{type:"text",value:w.testimonialsTitle||"",onChange:e=>B("testimonialsTitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"What Our Customers Say"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Section Subtitle"}),r.jsx("input",{type:"text",value:w.testimonialsSubtitle||"",onChange:e=>B("testimonialsSubtitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Real reviews from real customers who love our natural skincare"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Background Color"}),r.jsx(b,{value:w.testimonialsBackgroundColor||"#f0fdf4",onChange:e=>B("testimonialsBackgroundColor",e)})]})]})]}),r.jsx("div",{className:"flex justify-end mt-8 pt-6 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[p&&r.jsx("div",{className:`mr-4 px-4 py-2 rounded-md text-sm ${"success"===p.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:p.message}),w.id&&(0,r.jsxs)("span",{className:"text-sm text-gray-500 mr-4",children:["Last saved: ",new Date(w.updatedAt).toLocaleString()]}),r.jsx("button",{type:"button",onClick:T,disabled:x,className:"inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:x?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving Changes..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(i.Z,{className:"h-5 w-5 mr-2"}),"Save Changes"]})})]})}),r.jsx(k,{isOpen:v,onClose:()=>N(!1),onSave:()=>{N(!1)}})]})}},87888:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},24230:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},32933:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},941:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},89392:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},83855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31215:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},88307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},33734:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},15849:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\homepage\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,3757,434,9536,1441],()=>t(59569));module.exports=r})();