(()=>{var e={};e.id=3,e.ids=[3],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},87301:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c}),s(66136),s(90596),s(37254),s(35866);var r=s(23191),a=s(88716),i=s(37922),l=s.n(i),n=s(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,66136)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\page.tsx"],m="/admin/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},75671:(e,t,s)=>{Promise.resolve().then(s.bind(s,2802))},94492:(e,t,s)=>{Promise.resolve().then(s.bind(s,88381))},2802:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var r=s(10326),a=s(17577),i=s.n(a),l=s(90434),n=s(35047),d=s(77109),c=s(24319),o=s(48705),m=s(34565),x=s(57671),h=s(24061),u=s(40765),g=s(40617),p=s(35351),y=s(6507),f=s(5932),b=s(71709),j=s(95920),v=s(88378),N=s(94019),k=s(90748),w=s(53080),Z=s(71810);let P=({children:e})=>{let t=(0,n.usePathname)(),[s,a]=i().useState(!1),[P,M]=i().useState(!1),C=async()=>{if(!P)try{M(!0),a(!1),await (0,d.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},q=[{href:"/admin",label:"Dashboard",icon:c.Z},{href:"/admin/products",label:"Products",icon:o.Z},{href:"/admin/categories",label:"Categories",icon:m.Z},{href:"/admin/orders",label:"Orders",icon:x.Z},{href:"/admin/customers",label:"Customers",icon:h.Z},{href:"/admin/coupons",label:"Coupons",icon:u.Z},{href:"/admin/reviews",label:"Reviews",icon:g.Z},{href:"/admin/enquiry",label:"Enquiries",icon:p.Z},{href:"/admin/notifications",label:"Notifications",icon:y.Z},{href:"/admin/newsletter",label:"Newsletter",icon:f.Z},{href:"/admin/media",label:"Media",icon:b.Z},{href:"/admin/homepage",label:"Homepage",icon:j.Z},{href:"/admin/settings",label:"Settings",icon:v.Z}],S=e=>"/admin"===e?"/admin"===t:t.startsWith(e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:r.jsx("button",{onClick:()=>a(!s),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:s?r.jsx(N.Z,{className:"w-6 h-6 text-gray-600"}):r.jsx(k.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,r.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${s?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[r.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:r.jsx(w.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),r.jsx("nav",{className:"mt-6 px-3",children:q.map(e=>{let t=e.icon,s=S(e.href);return(0,r.jsxs)(l.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${s?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>a(!1),children:[r.jsx(t,{className:`w-5 h-5 mr-3 ${s?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,r.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[r.jsx(l.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,r.jsxs)("button",{onClick:C,disabled:P,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx(Z.Z,{className:"w-4 h-4 mr-2"}),P?"Logging out...":"Logout"]})]})]}),r.jsx("main",{className:"lg:ml-64",children:r.jsx("div",{className:"p-4 lg:p-8",children:e})}),s&&r.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>a(!1)})]})}},88381:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(10326),a=s(17577),i=s(75290),l=s(87888),n=s(48705),d=s(34565),c=s(24061),o=s(33734),m=s(17069),x=s(29163),h=s(57671),u=s(9891);let g=()=>{let[e,t]=(0,a.useState)(null),[s,g]=(0,a.useState)(!0),[p,y]=(0,a.useState)(null);(0,a.useEffect)(()=>{f()},[]);let f=async()=>{try{g(!0);let e=await fetch("/api/dashboard/stats"),s=await e.json();s.success?t(s.data):y("Failed to fetch dashboard data")}catch(e){console.error("Error fetching dashboard data:",e),y("Failed to fetch dashboard data")}finally{g(!1)}};if(s)return r.jsx("div",{className:"min-h-screen bg-gray-50",children:r.jsx("div",{className:"p-6",children:r.jsx("div",{className:"flex items-center justify-center py-12",children:r.jsx(i.Z,{className:"w-8 h-8 animate-spin text-green-600"})})})});if(p)return r.jsx("div",{className:"min-h-screen bg-gray-50",children:r.jsx("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(l.Z,{className:"w-5 h-5 text-red-600"}),r.jsx("p",{className:"text-red-800",children:p})]}),r.jsx("button",{onClick:f,className:"mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium",children:"Try again"})]})})});let b=[{title:"Total Products",value:e?.overview.totalProducts||0,change:e?.growth.productsGrowth||"+0%",trend:"up",icon:n.Z},{title:"Total Categories",value:e?.overview.totalCategories||0,change:e?.growth.categoriesGrowth||"+0%",trend:"up",icon:d.Z},{title:"Total Users",value:e?.overview.totalUsers||0,change:e?.growth.usersGrowth||"+0%",trend:"up",icon:c.Z},{title:"Featured Products",value:e?.overview.featuredProducts||0,change:"+0%",trend:"up",icon:o.Z}],j=e?.recent.products||[];return r.jsx("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Welcome back! Here's what's happening with your store."})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:b.map((e,t)=>{let s=e.icon,a="up"===e.trend?m.Z:x.Z;return(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:e.title}),r.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]}),r.jsx(s,{className:"w-8 h-8 text-green-600"})]}),(0,r.jsxs)("div",{className:"flex items-center mt-4",children:[r.jsx(a,{className:`w-4 h-4 mr-1 ${"up"===e.trend?"text-green-600":"text-red-600"}`}),r.jsx("span",{className:`text-sm font-medium ${"up"===e.trend?"text-green-600":"text-red-600"}`,children:e.change}),r.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last month"})]})]},t)})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[r.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Orders"}),r.jsx("button",{className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"View all"})]})}),r.jsx("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(h.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders yet"}),r.jsx("p",{className:"text-gray-600",children:"Orders will appear here once customers start purchasing"})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[r.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Products"}),r.jsx("button",{className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"View all"})]})}),r.jsx("div",{className:"p-6",children:r.jsx("div",{className:"space-y-4",children:j.length>0?j.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center flex-1",children:[r.jsx("img",{src:e.images&&e.images.length>0&&e.images[0]?.url?e.images[0].url:"/images/default-product.jpg",alt:e.name,className:"w-12 h-12 rounded-lg object-cover mr-4"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center mt-1 space-x-3",children:[r.jsx("span",{className:"text-sm text-gray-600",children:e.category?.name||"No category"}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["Stock: ",e.quantity||0]})]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[r.jsx("p",{className:"font-medium text-gray-900",children:(0,u.T4)(e.price||0)}),e.isFeatured&&r.jsx("span",{className:"inline-flex px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-700 border border-green-200 mt-1",children:"Featured"})]})]},e._id||e.id)):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(n.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products yet"}),r.jsx("p",{className:"text-gray-600",children:"Add products to see them here"})]})})})]})]})]})})}},9891:(e,t,s)=>{"use strict";function r(e){return function(e,t=!0){if(isNaN(e))return"₹0";let s=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return`₹${s}`}(e,!0)}function a(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function i(e,t){return(e?a(e):a(t))||"product"}s.d(t,{GD:()=>a,T4:()=>r,w:()=>i})},87888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},35351:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},75290:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},33734:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},29163:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},17069:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35047:(e,t,s)=>{"use strict";var r=s(77389);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},90596:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)},66136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,3757,434,9536],()=>s(87301));module.exports=r})();