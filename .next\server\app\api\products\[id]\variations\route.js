"use strict";(()=>{var e={};e.id=1135,e.ids=[1135],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},47510:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>v,requestAsyncStorage:()=>E,routeModule:()=>g,serverHooks:()=>y,staticGenerationAsyncStorage:()=>O});var s={};r.r(s),r.d(s,{GET:()=>R,POST:()=>h});var o=r(49303),n=r(88716),a=r(60670),i=r(87070),u=r(75571),c=r(95306),d=r(89456),l=r(11185),p=r(81515),f=r(84875),m=r(54211);let R=(0,f.lm)(async(e,{params:t})=>{let r=t.id;m.kg.apiRequest("GET",`/api/products/${r}/variations`),await (0,d.Z)();let s=l.Types.ObjectId.isValid(r)?new l.Types.ObjectId(r):r,o=await p.Th.find({productId:s}).sort({name:1,value:1}).lean();return m.kg.info("Product variations fetched",{productId:r,count:o.length}),i.NextResponse.json({success:!0,data:o})}),h=(0,f.lm)(async(e,{params:t})=>{let r=t.id;m.kg.apiRequest("POST",`/api/products/${r}/variations`);let s=await (0,u.getServerSession)(c.L);if(!s?.user)throw new f._7;if("ADMIN"!==s.user.role)throw new f.M_;let{name:o,value:n,price:a,pricingMode:R}=await e.json();if(!o||!n)throw new f.p8("Name and value are required");let h=R&&["REPLACE","INCREMENT","FIXED"].includes(R)?R:"REPLACE";await (0,d.Z)();let g=l.Types.ObjectId.isValid(r)?new l.Types.ObjectId(r):r;if(!await p.xs.findById(g).lean())throw new f.dR("Product");if(await p.Th.findOne({productId:g,name:o,value:n}).lean())throw new f.AY("Variation with this name and value already exists");let E=null;if(null!=a&&""!==a){let e="string"==typeof a?parseFloat(a):Number(a);!isNaN(e)&&isFinite(e)&&(E=e)}let O=await p.Th.create({name:o.trim(),value:n.trim(),price:E,pricingMode:h,productId:g,createdAt:new Date,updatedAt:new Date});return m.kg.info("Product variation created",{productId:r,variationId:String(O._id),name:o,value:n,userId:s.user._id??s.user.id}),i.NextResponse.json({success:!0,data:O,message:"Variation created successfully"})}),g=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/products/[id]/variations/route",pathname:"/api/products/[id]/variations",filename:"route",bundlePath:"app/api/products/[id]/variations/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:E,staticGenerationAsyncStorage:O,serverHooks:y}=g,w="/api/products/[id]/variations/route";function v(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:O})}},84875:(e,t,r)=>{r.d(t,{AY:()=>d,M_:()=>u,_7:()=>i,dR:()=>c,gz:()=>n,lm:()=>f,p8:()=>a});var s=r(87070),o=r(29489);class n extends Error{constructor(e,t=500,r="INTERNAL_ERROR",s){super(e),this.statusCode=t,this.code=r,this.details=s,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class a extends n{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class i extends n{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class u extends n{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends n{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class d extends n{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class l extends n{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function p(e){let t={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return t[e]||t.INTERNAL_ERROR}function f(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){let t=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,error:e}),e instanceof n){let r={success:!1,error:{code:e.code,message:p(e.code),requestId:t,...!1}};return s.NextResponse.json(r,{status:e.statusCode})}if(e instanceof o.j){let r=new a("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),o={success:!1,error:{code:r.code,message:"Validation failed",requestId:t,...!1}};return s.NextResponse.json(o,{status:r.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let r=function(e){if(11e3===e.code||"MongoServerError"===e.name){let t=Object.keys(e.keyPattern||{})[0]||"field";return new d(`${t} already exists`)}return"ValidationError"===e.name?new a("Validation failed"):"CastError"===e.name?new a("Invalid data format"):"DocumentNotFoundError"===e.name?new c:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new l("Database connection failed"):new l("Database operation failed",{name:e.name,message:e.message})}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return s.NextResponse.json(o,{status:r.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let r=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new d(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new i("Invalid user session");return new a("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new a("Missing required relationship");case"P2000":return new a("Input value is too long");case"P2004":return new a("Data constraint violation");default:return new l("Database operation failed",{code:e.code,message:e.message})}}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return s.NextResponse.json(o,{status:r.statusCode})}}let r={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:t,...!1}};return s.NextResponse.json(r,{status:500})}(e)}}}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:o,context:n,error:a,userId:i,requestId:u}=e,c=s[r],d=`[${t}] ${c}: ${o}`;return i&&(d+=` | User: ${i}`),u&&(d+=` | Request: ${u}`),n&&Object.keys(n).length>0&&(d+=` | Context: ${JSON.stringify(n)}`),a&&(d+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(d+=`
Stack: ${a.stack}`)),d}log(e,t,r,s){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},n=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(o))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,o){this.info(`API ${e} ${t} - ${r}`,{...o,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,o){this.error(`API ${e} ${t} failed`,r,{...o,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new o},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=o?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(s,n,i):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,9489,5306],()=>r(47510));module.exports=s})();