(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{33527:function(e,s,t){Promise.resolve().then(t.bind(t,28250)),Promise.resolve().then(t.bind(t,13634))},13634:function(e,s,t){"use strict";t.d(s,{default:function(){return C}});var a=t(57437),l=t(29),r=t.n(l),n=t(2265),i=t(27648),d=t(76858),o=t(33327),c=t(86595),x=t(70525),m=t(89345),h=t(30401),u=t(82023),b=t(22252),g=e=>{let{title:s="Stay Updated",subtitle:t="Get the latest updates on new products and exclusive offers",backgroundColor:l="#f0fdf4",className:r="",source:i="homepage"}=e,[d,o]=(0,n.useState)(""),[c,x]=(0,n.useState)(""),[g,f]=(0,n.useState)(""),[p,j]=(0,n.useState)(!1),[v,y]=(0,n.useState)("idle"),[N,w]=(0,n.useState)(""),S=async e=>{if(e.preventDefault(),!d){y("error"),w("Please enter your email address");return}j(!0),y("idle");try{let e=await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d,name:c||void 0,whatsapp:g||void 0,source:i})}),s=await e.json();s.success?(y("success"),w(s.message||"Successfully subscribed to newsletter!"),o(""),x("")):(y("error"),w(s.error||"Failed to subscribe. Please try again."))}catch(e){y("error"),w("Network error. Please try again.")}finally{j(!1)}};return(0,a.jsx)("div",{className:"rounded-2xl p-6 lg:p-8 ".concat(r),style:{backgroundColor:l},children:(0,a.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(m.Z,{className:"w-8 h-8 text-green-600"})}),(0,a.jsx)("h3",{className:"text-xl lg:text-2xl font-bold text-gray-900 mb-2",children:s}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 text-sm lg:text-base",children:t}),(0,a.jsxs)("form",{onSubmit:S,className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,a.jsx)("input",{type:"text",placeholder:"Your name (optional)",value:c,onChange:e=>x(e.target.value),className:"w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm"}),(0,a.jsx)("input",{type:"tel",placeholder:"WhatsApp (optional)",value:g,onChange:e=>f(e.target.value),className:"w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"email",placeholder:"Enter your email address",value:d,onChange:e=>o(e.target.value),required:!0,className:"flex-1 px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm"}),(0,a.jsx)("button",{type:"submit",disabled:p||"success"===v,className:"px-6 py-2.5 rounded-lg font-semibold text-white transition-all duration-200 text-sm whitespace-nowrap ".concat("success"===v?"bg-green-600 cursor-default":p?"bg-green-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700 active:bg-green-800"),children:p?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"}),"Subscribing..."]}):"success"===v?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.Z,{className:"w-4 h-4 mr-1"}),"Subscribed!"]}):(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4 mr-1"}),"Subscribe"]})})]})]}),N&&(0,a.jsxs)("div",{className:"mt-4 p-3 rounded-lg flex items-center justify-center text-sm ".concat("success"===v?"bg-green-100 text-green-800 border border-green-200":"bg-red-100 text-red-800 border border-red-200"),children:["success"===v?(0,a.jsx)(h.Z,{className:"w-4 h-4 mr-2 flex-shrink-0"}):(0,a.jsx)(b.Z,{className:"w-4 h-4 mr-2 flex-shrink-0"}),N]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-4",children:"We respect your privacy. Unsubscribe at any time."})]})})},f=e=>{let{title:s="Natural Skincare Essentials",subtitle:t="Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",ctaText:l="Shop Collection",ctaLink:r="/shop",secondaryCtaText:n="View Categories",secondaryCtaLink:o="/categories",badgeText:c="New Collection",backgroundColor:x="#f0fdf4",trustIndicators:m={value1:"100%",label1:"Natural",value2:"5K+",label2:"Happy Lifetime Customers",value3:"25+",label3:"Products",value4:"4.8★",label4:"Rating"}}=e;return(0,a.jsx)("div",{className:"relative px-4 py-12 lg:px-8 lg:py-16",style:{backgroundColor:x},children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-6",children:[(0,a.jsx)(u.Z,{className:"w-3 h-3 mr-1"}),c]}),(0,a.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 leading-tight",children:s}),(0,a.jsx)("p",{className:"text-lg md:text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed",children:t}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,a.jsxs)(i.default,{href:r,className:"inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-full hover:bg-green-700 transition-colors duration-200 shadow-lg hover:shadow-xl",children:[l,(0,a.jsx)(d.Z,{className:"ml-2 w-4 h-4"})]}),(0,a.jsx)(i.default,{href:o,className:"inline-flex items-center px-6 py-3 border-2 border-green-600 text-green-600 font-semibold rounded-full hover:bg-green-50 transition-colors duration-200",children:n})]}),(0,a.jsxs)("div",{className:"mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:m.value1}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:m.label1})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:m.value2}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:m.label2})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:m.value3}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:m.label3})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:m.value4}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:m.label4})]})]})]})})},p=t(74656),j=t(92451),v=t(10407),y=t(9467),N=e=>{let{title:s="What Our Customers Say",subtitle:t="Real reviews from real customers who love our natural skincare",backgroundColor:l="#f0fdf4"}=e,[r,i]=(0,n.useState)([]),[d,o]=(0,n.useState)(!0),[x,m]=(0,y.Z)({loop:!0,align:"start"}),[h,u]=(0,n.useState)(0),[b,g]=(0,n.useState)([]),f=(0,n.useCallback)(()=>{m&&m.scrollPrev()},[m]),N=(0,n.useCallback)(()=>{m&&m.scrollNext()},[m]),w=(0,n.useCallback)(e=>{m&&m.scrollTo(e)},[m]),S=(0,n.useCallback)(()=>{m&&u(m.selectedScrollSnap())},[m,u]);return(0,n.useEffect)(()=>{m&&(S(),g(m.scrollSnapList()),m.on("select",S),m.on("reInit",S))},[m,S]),(0,n.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/testimonials?active=true"),s=await e.json();s.success&&i(s.data)}catch(e){console.error("Error fetching testimonials:",e)}finally{o(!1)}})()},[]),(0,a.jsx)("div",{className:"px-4 py-8 lg:px-8 lg:py-12",style:{backgroundColor:l},children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8 lg:mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2 lg:text-3xl",children:s}),(0,a.jsx)("p",{className:"text-gray-600 lg:text-lg",children:t})]}),d?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm animate-pulse",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded mb-4"}),(0,a.jsx)("div",{className:"flex space-x-1 mb-3",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded"},s))}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]}),(0,a.jsxs)("div",{className:"border-t border-gray-100 pt-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-1"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},s))}):0===r.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(p.Z,{className:"w-12 h-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No testimonials available at the moment."})]}):(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"overflow-hidden",ref:x,children:(0,a.jsx)("div",{className:"flex",children:r.map(e=>(0,a.jsx)("div",{className:"flex-shrink-0 w-full md:w-1/2 lg:w-1/4 p-3",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 h-full flex flex-col",children:[(0,a.jsx)("div",{className:"flex items-center mb-4",children:(0,a.jsx)(p.Z,{className:"w-8 h-8 text-green-500 opacity-50"})}),(0,a.jsx)("div",{className:"flex items-center mb-3",children:[void 0,void 0,void 0,void 0,void 0].map((s,t)=>(0,a.jsx)(c.Z,{className:"w-4 h-4 ".concat(t<e.rating?"text-yellow-400 fill-current":"text-gray-300")},t))}),(0,a.jsxs)("p",{className:"text-gray-700 text-sm mb-4 leading-relaxed flex-grow",children:['"',e.content,'"']}),(0,a.jsxs)("div",{className:"border-t border-gray-100 pt-3",children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900 text-sm",children:e.name}),e.position&&e.company&&(0,a.jsxs)("p",{className:"text-gray-500 text-xs",children:[e.position," at ",e.company]})]})]})},e.id))})}),(0,a.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 left-0 z-10 bg-white/80 rounded-full p-2 hover:bg-white transition-all duration-300 shadow-md",onClick:f,children:(0,a.jsx)(j.Z,{className:"h-6 w-6 text-gray-800"})}),(0,a.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 right-0 z-10 bg-white/80 rounded-full p-2 hover:bg-white transition-all duration-300 shadow-md",onClick:N,children:(0,a.jsx)(v.Z,{className:"h-6 w-6 text-gray-800"})})]}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsx)("div",{className:"flex justify-center items-center space-x-2",children:b.map((e,s)=>(0,a.jsx)("button",{className:"w-2 h-2 rounded-full ".concat(s===h?"bg-green-600":"bg-gray-300"),onClick:()=>w(s)},s))})})]})})},w=t(11239),S=t(91723),k=e=>{let{endDate:s,title:t="Limited Time Offer",subtitle:l,backgroundColor:r="#16a34a",discountPercentage:i=25}=e,[d,o]=(0,n.useState)({days:0,hours:0,minutes:0,seconds:0}),c=l||"Get ".concat(i,"% off all natural skincare products");return((0,n.useEffect)(()=>{let e=new Date(s||Date.now()+6048e5),t=setInterval(()=>{let s=new Date().getTime(),t=e.getTime()-s;t>0?o({days:Math.floor(t/864e5),hours:Math.floor(t%864e5/36e5),minutes:Math.floor(t%36e5/6e4),seconds:Math.floor(t%6e4/1e3)}):o({days:0,hours:0,minutes:0,seconds:0})},1e3);return()=>clearInterval(t)},[s]),0===d.days&&0===d.hours&&0===d.minutes&&0===d.seconds)?null:(0,a.jsxs)("div",{className:"mx-4 mt-4 rounded-2xl p-6 text-white shadow-lg lg:mx-8 lg:mt-6 lg:p-8",style:{backgroundColor:r},children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(w.Z,{className:"w-6 h-6 mr-2 animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-semibold uppercase tracking-wide lg:text-base",children:"Flash Sale"})]}),(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold mb-2 lg:text-2xl",children:t}),(0,a.jsx)("p",{className:"text-sm opacity-90 lg:text-base",children:c})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4 mb-6",children:[(0,a.jsx)(S.Z,{className:"w-5 h-5 opacity-80"}),(0,a.jsx)("span",{className:"text-sm font-medium lg:text-base",children:"Ends in:"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-3 max-w-sm mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white bg-opacity-20 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold lg:text-3xl",children:d.days}),(0,a.jsx)("div",{className:"text-xs opacity-80 lg:text-sm",children:"Days"})]}),(0,a.jsxs)("div",{className:"bg-white bg-opacity-20 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold lg:text-3xl",children:d.hours}),(0,a.jsx)("div",{className:"text-xs opacity-80 lg:text-sm",children:"Hours"})]}),(0,a.jsxs)("div",{className:"bg-white bg-opacity-20 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold lg:text-3xl",children:d.minutes}),(0,a.jsx)("div",{className:"text-xs opacity-80 lg:text-sm",children:"Min"})]}),(0,a.jsxs)("div",{className:"bg-white bg-opacity-20 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold lg:text-3xl",children:d.seconds}),(0,a.jsx)("div",{className:"text-xs opacity-80 lg:text-sm",children:"Sec"})]})]}),(0,a.jsx)("div",{className:"text-center mt-6",children:(0,a.jsxs)("button",{className:"bg-white text-gray-900 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg",children:["Shop Now & Save ",i,"%"]})})]})},C=()=>{var e,s,t;let[l,m]=(0,n.useState)([]),[h,u]=(0,n.useState)([]),[b,p]=(0,n.useState)(null),[j,v]=(0,n.useState)(!0),[y,w]=(0,n.useState)(!1),[S,C]=(0,n.useState)(25);(0,n.useEffect)(()=>{P()},[]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{j&&(console.warn("Homepage data fetch timeout, using fallback data"),v(!1),m([]),u([]),p({showHero:!0,showCategories:!0,showBestsellers:!0,showNewsletter:!0,showTestimonials:!0,showTrustBadges:!0,showFlashSale:!1,heroTitle:"Natural Skincare Essentials",heroSubtitle:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",heroCtaText:"Shop Collection",heroCtaLink:"/shop",heroBackgroundColor:"#f0fdf4"}))},5e3);return()=>clearTimeout(e)},[j]);let T=(e,s)=>Math.round((e-e*s/100)*100)/100,I=(e,s)=>e.map(e=>({...e,originalPrice:e.price,flashSalePrice:T(e.price,s),isFlashSale:!0})),P=async()=>{try{let s=await fetch("/api/homepage-settings",{cache:"no-store",headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(!s.ok)throw Error("HTTP error! status: ".concat(s.status));let t=await s.json();if(t.success){var e;let s=null===(e=t.data)||void 0===e?void 0:e.settings,a=(null==s?void 0:s.showFlashSale)===!0,l=(null==s?void 0:s.flashSalePercentage)||25;w(a),C(l);let r=[];t.data.featuredProduct&&r.push(t.data.featuredProduct),t.data.bestsellers&&r.push(...t.data.bestsellers),a&&r.length>0&&(r=I(r,l)),m(r),t.data.categories&&u(t.data.categories),s&&p(s)}else console.warn("Homepage API returned success: false",t),m([]),u([]),p({showHero:!0,showCategories:!0,showBestsellers:!0,showNewsletter:!0,showTestimonials:!0,showTrustBadges:!0,showFlashSale:!1,heroTitle:"Natural Skincare Essentials",heroSubtitle:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",heroCtaText:"Shop Collection",heroCtaLink:"/shop",heroBackgroundColor:"#f0fdf4"})}catch(e){console.error("Error fetching homepage data:",e),m([]),u([]),p({showHero:!0,showCategories:!0,showBestsellers:!0,showNewsletter:!0,showTestimonials:!0,showTrustBadges:!0,showFlashSale:!1,heroTitle:"Natural Skincare Essentials",heroSubtitle:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",heroCtaText:"Shop Collection",heroCtaLink:"/shop",heroBackgroundColor:"#f0fdf4"})}finally{v(!1)}},Z=l[0],E=l.slice(1,5);return j?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-t-4 border-green-600"})})}):(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 min-h-screen bg-gray-50",children:[(0,a.jsx)(r(),{id:"5261b8b3dd7b00c3",children:"@-webkit-keyframes fadeIn{from{opacity:0}to{opacity:1}}@-moz-keyframes fadeIn{from{opacity:0}to{opacity:1}}@-o-keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@-webkit-keyframes slideIn{from{-webkit-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideIn{from{-moz-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideIn{from{-o-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideIn{from{-webkit-transform:translatey(20px);-moz-transform:translatey(20px);-o-transform:translatey(20px);transform:translatey(20px);opacity:0}to{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}.animate-fadeIn.jsx-5261b8b3dd7b00c3{-webkit-animation:fadeIn.5s ease-in-out;-moz-animation:fadeIn.5s ease-in-out;-o-animation:fadeIn.5s ease-in-out;animation:fadeIn.5s ease-in-out}.animate-slideIn.jsx-5261b8b3dd7b00c3{-webkit-animation:slideIn.5s ease-out;-moz-animation:slideIn.5s ease-out;-o-animation:slideIn.5s ease-out;animation:slideIn.5s ease-out}"}),(null==b?void 0:b.showFlashSale)===!0&&(0,a.jsx)(k,{title:(null==b?void 0:b.flashSaleTitle)||"Weekend Flash Sale",subtitle:(null==b?void 0:b.flashSaleSubtitle)||"Get ".concat((null==b?void 0:b.flashSalePercentage)||25,"% off all natural skincare products"),endDate:null==b?void 0:b.flashSaleEndDate,backgroundColor:(null==b?void 0:b.flashSaleBackgroundColor)||"#16a34a",discountPercentage:(null==b?void 0:b.flashSalePercentage)||25}),(null==b?void 0:b.showHero)&&(0,a.jsx)(f,{title:null==b?void 0:b.heroTitle,subtitle:null==b?void 0:b.heroSubtitle,ctaText:null==b?void 0:b.heroCtaText,ctaLink:null==b?void 0:b.heroCtaLink,secondaryCtaText:null==b?void 0:b.heroSecondaryCtaText,secondaryCtaLink:null==b?void 0:b.heroSecondaryCtaLink,badgeText:null==b?void 0:b.heroBadgeText,backgroundColor:null==b?void 0:b.heroBackgroundColor,trustIndicators:{value1:null==b?void 0:b.trustIndicator1Value,label1:null==b?void 0:b.trustIndicator1Label,value2:null==b?void 0:b.trustIndicator2Value,label2:null==b?void 0:b.trustIndicator2Label,value3:null==b?void 0:b.trustIndicator3Value,label3:null==b?void 0:b.trustIndicator3Label,value4:null==b?void 0:b.trustIndicator4Value,label4:null==b?void 0:b.trustIndicator4Label}}),(null==b?void 0:b.showBanner)&&(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 px-4 mt-4 grid grid-cols-2 gap-3 lg:px-8 lg:mt-6 lg:gap-4",children:(null==b?void 0:b.bannerText)?(0,a.jsxs)("div",{style:{backgroundColor:b.bannerBackgroundColor||"#22c55e"},className:"jsx-5261b8b3dd7b00c3 rounded-xl p-4 text-white shadow-lg col-span-2",children:[(0,a.jsx)("h3",{className:"jsx-5261b8b3dd7b00c3 font-bold text-sm mb-1 lg:text-base",children:b.bannerText}),b.bannerCtaText&&b.bannerCtaLink&&(0,a.jsxs)(i.default,{href:b.bannerCtaLink,className:"inline-flex items-center text-xs font-medium underline lg:text-sm hover:opacity-90",children:[b.bannerCtaText,(0,a.jsx)(d.Z,{className:"ml-1 w-3 h-3"})]})]}):(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl p-4 text-white shadow-lg col-span-2",children:[(0,a.jsx)("h3",{className:"jsx-5261b8b3dd7b00c3 font-bold text-sm mb-1 lg:text-base",children:"Explore Our Collections"}),(0,a.jsx)("p",{className:"jsx-5261b8b3dd7b00c3 text-xs opacity-90 mb-2 lg:text-sm",children:"Find the perfect products for your needs."}),(0,a.jsxs)(i.default,{href:"/shop",className:"inline-flex items-center text-xs font-medium underline lg:text-sm hover:opacity-90",children:["Shop All",(0,a.jsx)(d.Z,{className:"ml-1 w-3 h-3"})]})]})}),(null==b?void 0:b.showProductOfMonth)&&Z&&(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 mx-4 mt-6 lg:mx-8",children:(0,a.jsxs)("div",{style:{backgroundColor:(null==b?void 0:b.productSectionBgColor)||"#f0fdf4"},className:"jsx-5261b8b3dd7b00c3 rounded-2xl p-4 lg:rounded-3xl lg:p-6",children:[(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center mb-2 lg:mb-3",children:[(0,a.jsx)(o.Z,{className:"w-4 h-4 mr-1 lg:w-5 lg:h-5 text-green-600"}),(0,a.jsx)("span",{className:"jsx-5261b8b3dd7b00c3 text-xs font-semibold lg:text-sm text-green-800",children:"PRODUCT OF THE MONTH"})]}),(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center space-x-3 lg:space-x-4",children:[(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center lg:w-20 lg:h-20",children:(null===(e=Z.images)||void 0===e?void 0:e[0])&&(0,a.jsx)("img",{src:Z.images[0].url,alt:Z.name,className:"jsx-5261b8b3dd7b00c3 w-full h-full object-cover rounded-lg"})}),(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex-1",children:[(0,a.jsx)("h3",{className:"jsx-5261b8b3dd7b00c3 font-bold text-gray-900 text-sm lg:text-base",children:Z.name}),(0,a.jsx)("p",{className:"jsx-5261b8b3dd7b00c3 text-gray-600 text-xs mt-1 line-clamp-2 lg:text-sm",children:Z.shortDescription||(null===(s=Z.description)||void 0===s?void 0:s.substring(0,80))+"..."}),(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center mt-2",children:(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center",children:[[void 0,void 0,void 0,void 0,void 0].map((e,s)=>{var t;return(0,a.jsx)(c.Z,{className:"w-3 h-3 ".concat(s<Math.floor((null===(t=Z._count)||void 0===t?void 0:t.reviews)/2)?"fill-yellow-400 text-yellow-400":"text-gray-300")},s)}),(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-xs ml-1 text-gray-600",children:["(",(null===(t=Z._count)||void 0===t?void 0:t.reviews)||0," reviews)"]})]})}),(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center justify-between mt-2",children:[(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center space-x-2",children:y&&Z.flashSalePrice?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-base font-bold lg:text-lg text-gray-900",children:["₹",Z.flashSalePrice]}),(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-sm text-gray-500 line-through",children:["₹",Z.originalPrice]}),(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-xs bg-red-500 text-white px-2 py-0.5 rounded-full",children:[S,"% OFF"]})]}):(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-base font-bold lg:text-lg text-gray-900",children:["₹",Z.price]})}),(0,a.jsx)(i.default,{href:"/product/".concat(Z.slug),className:"bg-green-600 text-white px-3 py-1 rounded-full text-xs font-medium hover:bg-green-700 transition-colors lg:px-4 lg:py-1.5 lg:text-sm",children:"View Details"})]})]})]})]})}),(null==b?void 0:b.showCategories)&&(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 px-4 mt-8 lg:px-8 lg:mt-10",children:[(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center justify-between mb-5 lg:mb-7",children:[(0,a.jsx)("h2",{className:"jsx-5261b8b3dd7b00c3 text-2xl font-bold text-gray-800 lg:text-3xl",children:"Shop by Category"}),(0,a.jsxs)(i.default,{href:"/categories",className:"inline-flex items-center text-green-600 text-sm font-medium hover:text-green-700 transition-colors lg:text-base",children:["See All",(0,a.jsx)(d.Z,{className:"ml-1 w-4 h-4"})]})]}),(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 grid grid-cols-2 gap-4 lg:grid-cols-3 lg:gap-5",children:h.map(e=>{var s;return(0,a.jsxs)(i.default,{href:"/shop?category=".concat(e.slug),className:"bg-white p-5 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 lg:p-6",children:[(0,a.jsx)("h3",{className:"jsx-5261b8b3dd7b00c3 font-semibold text-gray-800 text-base lg:text-lg mb-2",children:e.name}),(0,a.jsxs)("p",{className:"jsx-5261b8b3dd7b00c3 text-sm text-gray-600 mt-1 lg:text-base",children:[(null===(s=e._count)||void 0===s?void 0:s.products)||0," products"]}),(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 mt-3 inline-flex items-center text-xs text-green-600 font-medium",children:["Shop now",(0,a.jsx)(d.Z,{className:"ml-1 w-3 h-3"})]})]},e._id)})})]}),(null==b?void 0:b.showBestsellers)&&(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 px-4 mt-8 mb-8 lg:px-8 lg:mt-10 lg:mb-10",children:[(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center justify-between mb-5 lg:mb-7",children:[(0,a.jsx)("h2",{className:"jsx-5261b8b3dd7b00c3 text-2xl font-bold text-gray-800 lg:text-3xl",children:"Best Sellers"}),(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center text-amber-500",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4 mr-1 lg:w-5 lg:h-5"}),(0,a.jsx)("span",{className:"jsx-5261b8b3dd7b00c3 text-xs font-medium lg:text-sm",children:"Trending"})]})]}),(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 space-y-4 lg:space-y-5",children:E.map(e=>{var s,t;return(0,a.jsxs)(i.default,{href:"/product/".concat(e.slug),className:"flex items-center space-x-4 bg-white p-4 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 lg:p-5",children:[(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 w-16 h-16 bg-gray-100 rounded-xl flex-shrink-0 lg:w-20 lg:h-20",children:(null===(s=e.images)||void 0===s?void 0:s[0])&&(0,a.jsx)("img",{src:e.images[0].url,alt:e.name,className:"jsx-5261b8b3dd7b00c3 w-full h-full object-cover rounded-xl"})}),(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex-1",children:[(0,a.jsx)("h3",{className:"jsx-5261b8b3dd7b00c3 font-medium text-gray-800 text-base lg:text-lg",children:e.name}),(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center mt-1",children:(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center",children:[[void 0,void 0,void 0,void 0,void 0].map((s,t)=>{var l;return(0,a.jsx)(c.Z,{className:"w-3 h-3 ".concat(t<Math.floor((null===(l=e._count)||void 0===l?void 0:l.reviews)/2)?"fill-amber-400 text-amber-400":"text-gray-300")},t)}),(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-xs ml-1 text-gray-500",children:["(",(null===(t=e._count)||void 0===t?void 0:t.reviews)||0,")"]})]})}),(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 flex items-center space-x-2",children:y&&e.flashSalePrice?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-green-600 font-medium text-base lg:text-lg",children:["₹",e.flashSalePrice]}),(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-sm text-gray-500 line-through",children:["₹",e.originalPrice]}),(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-xs bg-red-500 text-white px-2 py-0.5 rounded-full",children:[S,"% OFF"]})]}):(0,a.jsxs)("span",{className:"jsx-5261b8b3dd7b00c3 text-green-600 font-medium text-base lg:text-lg",children:["₹",e.price]})})]}),(0,a.jsxs)("div",{className:"jsx-5261b8b3dd7b00c3 flex flex-col items-end",children:[(0,a.jsx)(d.Z,{className:"w-5 h-5 text-gray-400 lg:w-6 lg:h-6"}),(0,a.jsx)("span",{className:"jsx-5261b8b3dd7b00c3 text-xs text-gray-500 mt-2",children:"View Details"})]})]},e._id)})})]}),(null==b?void 0:b.showTestimonials)&&(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 mt-8 lg:mt-10",children:(0,a.jsx)(N,{title:(null==b?void 0:b.testimonialsTitle)||"What Our Customers Say",subtitle:(null==b?void 0:b.testimonialsSubtitle)||"Real reviews from real customers who love our natural skincare",backgroundColor:(null==b?void 0:b.testimonialsBackgroundColor)||"#f0fdf4"})}),(null==b?void 0:b.showNewsletter)&&(0,a.jsx)("div",{className:"jsx-5261b8b3dd7b00c3 px-4 mt-8 lg:px-8 lg:mt-12 pb-8",children:(0,a.jsx)(g,{title:(null==b?void 0:b.newsletterTitle)||"Stay Updated",subtitle:(null==b?void 0:b.newsletterSubtitle)||"Get the latest updates on new products and exclusive offers",backgroundColor:(null==b?void 0:b.productSectionBgColor)||"#f0fdf4",source:"homepage"})})]})}}},function(e){e.O(0,[7349,7648,5644,3999,4281,1682,8250,2971,2117,1744],function(){return e(e.s=33527)}),_N_E=e.O()}]);