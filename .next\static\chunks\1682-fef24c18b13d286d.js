"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1682],{53827:function(t,a,e){e.d(a,{CartProvider:function(){return m},j:function(){return h}});var o=e(57437),i=e(2265);let l=(0,i.createContext)(null),n="herbalicious_cart",s=t=>{try{let{isHydrated:a,...e}=t;localStorage.setItem(n,JSON.stringify(e))}catch(t){console.error("Error saving cart to localStorage:",t)}},r=()=>{try{{let t=localStorage.getItem(n);if(t)return{...JSON.parse(t),isHydrated:!0}}}catch(t){console.error("Error loading cart from localStorage:",t)}return null},u=(t,a)=>{if(!a||0===a.length)return t;let e=[...a].sort((t,a)=>t.name.localeCompare(a.name)).map(t=>"".concat(t.name,":").concat(t.value)).join("|");return"".concat(t,"__").concat(e)},c=t=>{var a;return t.variantKey||(null===(a=t.product)||void 0===a?void 0:a.id)||t.id},d=()=>({items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]},flashSaleDiscount:0,flashSaleActive:!1,flashSalePercentage:0,isHydrated:!1}),p=()=>{try{{let t=localStorage.getItem("flashSaleSettings");if(t){let a=JSON.parse(t),e=!0===a.showFlashSale,o=a.flashSalePercentage||25,i=a.flashSaleEndDate;if(i){let t=new Date,a=new Date(i);return{flashSaleActive:e&&t<a,flashSalePercentage:o}}return{flashSaleActive:e,flashSalePercentage:o}}}}catch(t){console.error("Error loading flash sale settings:",t)}return{flashSaleActive:!1,flashSalePercentage:0}},f=(t,a,e,o)=>{let i=0,l=0;t.forEach(t=>{let a=t.product.price;if(e&&o>0){let e=a*o/100;i+=(a-e)*t.quantity,l+=e*t.quantity}else i+=a*t.quantity});let n=t.reduce((t,a)=>t+a.quantity,0),s=a.reduce((t,a)=>t+a.discountAmount,0),r=i-s;return{subtotal:i,itemCount:n,total:i,finalTotal:r,flashSaleDiscount:l,couponDiscount:s}},v=(t,a)=>{let{flashSaleActive:e,flashSalePercentage:o}=p();switch(a.type){case"SET_CART_STATE":return{...a.payload,isHydrated:!0};case"ADD_ITEM":{let i;let l=u(a.payload.id,a.selectedVariants);if(t.items.find(t=>c(t)===l))i=t.items.map(t=>c(t)===l?{...t,quantity:t.quantity+1,variantKey:l}:t);else{let e={product:a.payload,quantity:1,selectedVariants:a.selectedVariants||[],variantKey:l};i=[...t.items,e]}let n=f(i,t.coupons.appliedCoupons,e,o),r={...t,items:i,subtotal:n.subtotal,itemCount:n.itemCount,total:n.total,finalTotal:n.finalTotal,coupons:{...t.coupons,totalDiscount:n.couponDiscount},flashSaleDiscount:n.flashSaleDiscount,flashSaleActive:e,flashSalePercentage:o};return s(r),r}case"REMOVE_ITEM":{let i=t.items.filter(t=>c(t)!==a.payload),l=f(i,t.coupons.appliedCoupons,e,o),n={...t,items:i,subtotal:l.subtotal,itemCount:l.itemCount,total:l.total,finalTotal:l.finalTotal,coupons:{...t.coupons,totalDiscount:l.couponDiscount},flashSaleDiscount:l.flashSaleDiscount,flashSaleActive:e,flashSalePercentage:o};return s(n),n}case"UPDATE_QUANTITY":{let i=t.items.map(t=>c(t)===a.payload.id?{...t,quantity:a.payload.quantity}:t).filter(t=>t.quantity>0),l=f(i,t.coupons.appliedCoupons,e,o),n={...t,items:i,subtotal:l.subtotal,itemCount:l.itemCount,total:l.total,finalTotal:l.finalTotal,coupons:{...t.coupons,totalDiscount:l.couponDiscount},flashSaleDiscount:l.flashSaleDiscount,flashSaleActive:e,flashSalePercentage:o};return s(n),n}case"APPLY_COUPON":{if(t.coupons.appliedCoupons.some(t=>t.coupon.id===a.payload.coupon.id)||t.coupons.appliedCoupons.some(t=>!t.coupon.isStackable)&&!a.payload.coupon.isStackable)return t;let i=[...t.coupons.appliedCoupons,a.payload],l=f(t.items,i,e,o),n={...t,subtotal:l.subtotal,itemCount:l.itemCount,total:l.total,finalTotal:l.finalTotal,coupons:{...t.coupons,appliedCoupons:i,totalDiscount:l.couponDiscount},flashSaleDiscount:l.flashSaleDiscount,flashSaleActive:e,flashSalePercentage:o};return s(n),n}case"REMOVE_COUPON":{let i=t.coupons.appliedCoupons.filter(t=>t.coupon.id!==a.payload),l=f(t.items,i,e,o),n={...t,subtotal:l.subtotal,itemCount:l.itemCount,total:l.total,finalTotal:l.finalTotal,coupons:{...t.coupons,appliedCoupons:i,totalDiscount:l.couponDiscount},flashSaleDiscount:l.flashSaleDiscount,flashSaleActive:e,flashSalePercentage:o};return s(n),n}case"CLEAR_COUPONS":{let a=f(t.items,[],e,o),i={...t,subtotal:a.subtotal,itemCount:a.itemCount,total:a.total,finalTotal:a.finalTotal,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]},flashSaleDiscount:a.flashSaleDiscount,flashSaleActive:e,flashSalePercentage:o};return s(i),i}case"CLEAR_CART":{let t={...d(),isHydrated:!0,flashSaleActive:e,flashSalePercentage:o};return s(t),t}default:return t}},m=t=>{let{children:a}=t,[e,n]=(0,i.useReducer)(v,d());return(0,i.useEffect)(()=>{let t=r();t?n({type:"SET_CART_STATE",payload:t}):n({type:"SET_CART_STATE",payload:d()})},[]),(0,i.useEffect)(()=>{e.isHydrated&&e.items.length>0&&n({type:"UPDATE_QUANTITY",payload:{id:e.items[0].variantKey||e.items[0].product.id,quantity:e.items[0].quantity}})},[e.isHydrated]),(0,i.useEffect)(()=>{if(!e.isHydrated)return;let t=t=>{"flashSaleSettings"===t.key&&e.items.length>0&&n({type:"UPDATE_QUANTITY",payload:{id:e.items[0].variantKey||e.items[0].product.id,quantity:e.items[0].quantity}})},a=()=>{e.items.length>0&&n({type:"UPDATE_QUANTITY",payload:{id:e.items[0].variantKey||e.items[0].product.id,quantity:e.items[0].quantity}})};return window.addEventListener("storage",t),window.addEventListener("flashSaleSettingsUpdated",a),()=>{window.removeEventListener("storage",t),window.removeEventListener("flashSaleSettingsUpdated",a)}},[e.isHydrated,e.items]),(0,o.jsx)(l.Provider,{value:{state:e,dispatch:n},children:a})},h=()=>{let t=(0,i.useContext)(l);if(!t)throw Error("useCart must be used within a CartProvider");return t}},19124:function(t,a,e){e.d(a,{NotificationProvider:function(){return r},z:function(){return s}});var o=e(57437),i=e(2265),l=e(80605);let n=(0,i.createContext)(void 0),s=()=>{let t=(0,i.useContext)(n);if(void 0===t)throw Error("useNotifications must be used within a NotificationProvider");return t},r=t=>{var a,e,s,r,u,c;let{children:d}=t,{data:p,status:f}=(0,l.useSession)(),[v,m]=(0,i.useState)([]),[h,y]=(0,i.useState)(0),[S,C]=(0,i.useState)(!1),[T,E]=(0,i.useState)(null),[g,D]=(0,i.useState)(0),w=(0,i.useCallback)(async function(){var t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)try{C(!0),E(null);let t=new URLSearchParams({page:(a.page||1).toString(),limit:(a.limit||10).toString(),...a.unreadOnly&&{unreadOnly:"true"}}),e=await fetch("/api/notifications?".concat(t)),o=await e.json();o.success?(m(o.data.notifications),y(o.data.unreadCount)):E(o.error||"Failed to fetch notifications")}catch(t){console.error("Error fetching notifications:",t),E("Failed to fetch notifications")}finally{C(!1)}},[null==p?void 0:null===(a=p.user)||void 0===a?void 0:a.id]),b=(0,i.useCallback)(async()=>{await w({limit:10})},[w]),A=(0,i.useCallback)(async()=>{var t;if(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)try{let t=await fetch("/api/notifications/unread-count"),a=await t.json();a.success&&(a.unreadCount>g&&w({limit:10}),D(a.unreadCount),y(a.unreadCount))}catch(t){console.error("Error fetching unread count:",t)}},[null==p?void 0:null===(e=p.user)||void 0===e?void 0:e.id]),P=(0,i.useCallback)(async t=>{var a;if(null==p?void 0:null===(a=p.user)||void 0===a?void 0:a.id)try{let a=await fetch("/api/notifications/".concat(t,"/read"),{method:"POST"}),e=await a.json();e.success?(m(a=>a.map(a=>a.id===t?{...a,isRead:!0}:a)),y(t=>Math.max(0,t-1))):E(e.error||"Failed to mark notification as read")}catch(t){console.error("Error marking notification as read:",t),E("Failed to mark notification as read")}},[null==p?void 0:null===(s=p.user)||void 0===s?void 0:s.id]),_=(0,i.useCallback)(async()=>{var t;if(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)try{let t=await fetch("/api/notifications/mark-all-read",{method:"POST"}),a=await t.json();a.success?(m(t=>t.map(t=>({...t,isRead:!0}))),y(0)):E(a.error||"Failed to mark all notifications as read")}catch(t){console.error("Error marking all notifications as read:",t),E("Failed to mark all notifications as read")}},[null==p?void 0:null===(r=p.user)||void 0===r?void 0:r.id]);return(0,i.useEffect)(()=>{var t;"authenticated"===f&&(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id)&&(w({limit:5}),A())},[f,null==p?void 0:null===(u=p.user)||void 0===u?void 0:u.id,w,A]),(0,i.useEffect)(()=>{var t;if(!(null==p?void 0:null===(t=p.user)||void 0===t?void 0:t.id))return;let a=setInterval(()=>{A()},3e4);return()=>clearInterval(a)},[null==p?void 0:null===(c=p.user)||void 0===c?void 0:c.id,A]),(0,o.jsx)(n.Provider,{value:{notifications:v,unreadCount:h,loading:S,error:T,fetchNotifications:w,markAsRead:P,markAllAsRead:_,refreshUnreadCount:A,refreshNotifications:b},children:d})}}}]);