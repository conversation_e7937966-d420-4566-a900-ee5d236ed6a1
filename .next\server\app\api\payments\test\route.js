"use strict";(()=>{var e={};e.id=4411,e.ids=[4411],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},72595:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>E,patchFetch:()=>h,requestAsyncStorage:()=>g,routeModule:()=>R,serverHooks:()=>y,staticGenerationAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>m});var n=r(49303),o=r(88716),a=r(60670),i=r(87070),c=r(75571),u=r(95306),d=r(62822),p=r(54211),l=r(84875);let m=(0,l.lm)(async e=>{p.kg.apiRequest("GET","/api/payments/test");let t=await (0,c.getServerSession)(u.L);if(!t?.user||"ADMIN"!==t.user.role)throw new l._7("Admin access required");try{let e=`TEST_${Date.now()}`,r=["RAZORPAY_KEY_ID","RAZORPAY_KEY_SECRET","NEXT_PUBLIC_RAZORPAY_KEY_ID"].filter(e=>!process.env[e]);if(r.length>0)return i.NextResponse.json({success:!1,error:"Missing environment variables",missingVars:r},{status:500});let s=(0,d.ml)(100),n=await (0,d.iP)({amount:100,currency:"INR",receipt:e,notes:{test:"true",userId:t.user.id,timestamp:new Date().toISOString()}});return p.kg.info("Payment test successful",{testOrderId:n.id,amount:100,receipt:e}),i.NextResponse.json({success:!0,message:"Payment integration test successful",test_results:{environment_variables:"OK",amount_validation:s?"OK":"FAILED",order_creation:"OK",razorpay_connection:"OK"},test_order:{id:n.id,amount:n.amount,currency:n.currency,receipt:n.receipt,status:n.status},configuration:{razorpay_key_id:"rzp_test_H8VYcEtWS9hwc8",environment:"production"}})}catch(e){return p.kg.error("Payment test failed",e),i.NextResponse.json({success:!1,error:"Payment integration test failed",details:e.message,test_results:{environment_variables:"UNKNOWN",amount_validation:"UNKNOWN",order_creation:"FAILED",razorpay_connection:"FAILED"}},{status:500})}}),R=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/payments/test/route",pathname:"/api/payments/test",filename:"route",bundlePath:"app/api/payments/test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\test\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:y}=R,E="/api/payments/test/route";function h(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:f})}},84875:(e,t,r)=>{r.d(t,{AY:()=>d,M_:()=>c,_7:()=>i,dR:()=>u,gz:()=>o,lm:()=>m,p8:()=>a});var s=r(87070),n=r(29489);class o extends Error{constructor(e,t=500,r="INTERNAL_ERROR",s){super(e),this.statusCode=t,this.code=r,this.details=s,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,o)}}class a extends o{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class i extends o{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class c extends o{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class u extends o{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class d extends o{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class p extends o{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function l(e){let t={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return t[e]||t.INTERNAL_ERROR}function m(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){let t=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,error:e}),e instanceof o){let r={success:!1,error:{code:e.code,message:l(e.code),requestId:t,...!1}};return s.NextResponse.json(r,{status:e.statusCode})}if(e instanceof n.j){let r=new a("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),n={success:!1,error:{code:r.code,message:"Validation failed",requestId:t,...!1}};return s.NextResponse.json(n,{status:r.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let r=function(e){if(11e3===e.code||"MongoServerError"===e.name){let t=Object.keys(e.keyPattern||{})[0]||"field";return new d(`${t} already exists`)}return"ValidationError"===e.name?new a("Validation failed"):"CastError"===e.name?new a("Invalid data format"):"DocumentNotFoundError"===e.name?new u:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new p("Database connection failed"):new p("Database operation failed",{name:e.name,message:e.message})}(e),n={success:!1,error:{code:r.code,message:l(r.code),requestId:t,...!1}};return s.NextResponse.json(n,{status:r.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let r=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new d(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new i("Invalid user session");return new a("Invalid reference to related record");case"P2025":case"P2001":return new u;case"P2014":return new a("Missing required relationship");case"P2000":return new a("Input value is too long");case"P2004":return new a("Data constraint violation");default:return new p("Database operation failed",{code:e.code,message:e.message})}}(e),n={success:!1,error:{code:r.code,message:l(r.code),requestId:t,...!1}};return s.NextResponse.json(n,{status:r.statusCode})}}let r={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:t,...!1}};return s.NextResponse.json(r,{status:500})}(e)}}}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>o}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class n{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:n,context:o,error:a,userId:i,requestId:c}=e,u=s[r],d=`[${t}] ${u}: ${n}`;return i&&(d+=` | User: ${i}`),c&&(d+=` | Request: ${c}`),o&&Object.keys(o).length>0&&(d+=` | Context: ${JSON.stringify(o)}`),a&&(d+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(d+=`
Stack: ${a.stack}`)),d}log(e,t,r,s){if(!this.shouldLog(e))return;let n={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},o=this.formatMessage(n);if(this.isDevelopment)switch(e){case 0:console.error(o);break;case 1:console.warn(o);break;case 2:console.info(o);break;case 3:console.debug(o)}else console.log(JSON.stringify(n))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,n){this.info(`API ${e} ${t} - ${r}`,{...n,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,n){this.error(`API ${e} ${t} failed`,r,{...n,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let o=new n},62822:(e,t,r)=>{r.d(t,{FT:()=>p,My:()=>R,iP:()=>d,mT:()=>m,ml:()=>g,sS:()=>l});var s=r(41212),n=r.n(s),o=r(54211),a=r(84875),i=r(84770),c=r.n(i);let u=new(n())({key_id:process.env.RAZORPAY_KEY_ID,key_secret:process.env.RAZORPAY_KEY_SECRET});async function d(e){try{o.kg.info("Creating Razorpay order",{amount:e.amount,currency:e.currency,receipt:e.receipt});let t=await u.orders.create({amount:e.amount,currency:e.currency,receipt:e.receipt,notes:e.notes||{}});return o.kg.info("Razorpay order created successfully",{orderId:t.id,amount:t.amount,receipt:t.receipt}),t}catch(e){throw o.kg.error("Failed to create Razorpay order",e),new a.gz("Failed to create payment order",500)}}function p(e){try{let t=c().createHmac("sha256",process.env.RAZORPAY_KEY_SECRET).update(`${e.razorpay_order_id}|${e.razorpay_payment_id}`).digest("hex")===e.razorpay_signature;return o.kg.info("Payment signature verification",{orderId:e.razorpay_order_id,paymentId:e.razorpay_payment_id,isValid:t}),t}catch(e){return o.kg.error("Payment signature verification failed",e),!1}}async function l(e){try{o.kg.info("Fetching payment details",{paymentId:e});let t=await u.payments.fetch(e);return o.kg.info("Payment details fetched successfully",{paymentId:t.id,status:t.status,amount:t.amount}),t}catch(e){throw o.kg.error("Failed to fetch payment details",e),new a.gz("Failed to fetch payment details",500)}}function m(e){return Math.round(100*e)}function R(e="ORDER"){let t=Date.now(),r=Math.random().toString(36).substring(2,8).toUpperCase();return`${e}_${t}_${r}`}function g(e){return e>=100&&e<=15e8&&Number.isInteger(e)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,9489,656,5306],()=>r(72595));module.exports=s})();