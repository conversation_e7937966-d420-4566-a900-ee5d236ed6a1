import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { HomepageSetting, Product, Category } from "@/app/lib/models";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/lib/auth";

// GET /api/homepage-settings - Get homepage settings
export async function GET() {
  try {
    await connectDB();

    // Get homepage settings
    const settings = await HomepageSetting.find().sort({ createdAt: -1 });
    const currentSettings = settings.length > 0 ? settings[0] : null;

    // Get categories with product counts
    const categories = await Category.aggregate([
      {
        $lookup: {
          from: 'product',
          localField: '_id',
          foreignField: 'categoryId',
          as: 'products'
        }
      },
      {
        $addFields: {
          '_count': {
            'products': { $size: '$products' }
          }
        }
      },
      { $limit: 6 }
    ]);

    // Get featured product (first active product or by settings)
    let featuredProduct = null;
    if (currentSettings?.productOfTheMonthId) {
      featuredProduct = await Product.findById(currentSettings.productOfTheMonthId)
        .where({ isActive: true })
        .lean();
    }
    if (!featuredProduct) {
      featuredProduct = await Product.findOne({ isActive: true, isFeatured: true })
        .sort({ createdAt: -1 })
        .lean();
    }

    // Get bestsellers (top 4 active products)
    const bestsellers = await Product.find({ isActive: true })
      .sort({ createdAt: -1 })
      .limit(4)
      .lean();

    return NextResponse.json({
      success: true,
      data: {
        settings: currentSettings,
        featuredProduct,
        bestsellers,
        categories,
      },
    });
  } catch (error) {
    console.error("Error fetching homepage settings:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch homepage settings" },
      { status: 500 },
    );
  }
}

// POST /api/homepage-settings - Create or update homepage settings
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session || (session.user as any).role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    const body = await request.json();
    const {
      // Hero Section
      heroTitle,
      heroSubtitle,
      heroCtaText,
      heroCtaLink,
      heroSecondaryCtaText,
      heroSecondaryCtaLink,
      heroBadgeText,
      heroBackgroundColor,
      showHero,

      // Hero Trust Indicators
      trustIndicator1Value,
      trustIndicator1Label,
      trustIndicator2Value,
      trustIndicator2Label,
      trustIndicator3Value,
      trustIndicator3Label,
      trustIndicator4Value,
      trustIndicator4Label,

      // Product of the Month
      productOfTheMonthId,
      showProductOfMonth,

      // Promotional Banner
      bannerText,
      bannerCtaText,
      bannerCtaLink,
      bannerBackgroundColor,
      showBanner,

      // Sections
      showCategories,
      productSectionBgColor,
      bestsellerIds,
      showBestsellers,

      // Newsletter
      newsletterTitle,
      newsletterSubtitle,
      showNewsletter,

      // Trust Badges
      showTrustBadges,

      // Flash Sale Section
      flashSaleTitle,
      flashSaleSubtitle,
      flashSaleEndDate,
      flashSaleBackgroundColor,
      flashSalePercentage,
      showFlashSale,

      // Testimonials Section
      testimonialsTitle,
      testimonialsSubtitle,
      testimonialsBackgroundColor,
      showTestimonials,

      isActive,
    } = body;

    await connectDB();
    
    // Check if homepage settings already exist
    let settings = await HomepageSetting.findOne({ id: "homepage-settings" });
    
    // Create or update homepage settings
    if (settings) {
      // Update existing settings
      settings = await HomepageSetting.findOneAndUpdate(
        { id: "homepage-settings" },
        {
          $set: {
            // Hero Section
            ...(heroTitle !== undefined && { heroTitle }),
            ...(heroSubtitle !== undefined && { heroSubtitle }),
            ...(heroCtaText !== undefined && { heroCtaText }),
            ...(heroCtaLink !== undefined && { heroCtaLink }),
            ...(heroSecondaryCtaText !== undefined && { heroSecondaryCtaText }),
            ...(heroSecondaryCtaLink !== undefined && { heroSecondaryCtaLink }),
            ...(heroBadgeText !== undefined && { heroBadgeText }),
            ...(heroBackgroundColor !== undefined && { heroBackgroundColor }),
            ...(showHero !== undefined && { showHero }),

            // Hero Trust Indicators
            ...(trustIndicator1Value !== undefined && { trustIndicator1Value }),
            ...(trustIndicator1Label !== undefined && { trustIndicator1Label }),
            ...(trustIndicator2Value !== undefined && { trustIndicator2Value }),
            ...(trustIndicator2Label !== undefined && { trustIndicator2Label }),
            ...(trustIndicator3Value !== undefined && { trustIndicator3Value }),
            ...(trustIndicator3Label !== undefined && { trustIndicator3Label }),
            ...(trustIndicator4Value !== undefined && { trustIndicator4Value }),
            ...(trustIndicator4Label !== undefined && { trustIndicator4Label }),

            // Product of the Month
            ...(productOfTheMonthId !== undefined && { productOfTheMonthId }),
            ...(showProductOfMonth !== undefined && { showProductOfMonth }),

            // Promotional Banner
            ...(bannerText !== undefined && { bannerText }),
            ...(bannerCtaText !== undefined && { bannerCtaText }),
            ...(bannerCtaLink !== undefined && { bannerCtaLink }),
            ...(bannerBackgroundColor !== undefined && { bannerBackgroundColor }),
            ...(showBanner !== undefined && { showBanner }),

            // Sections
            ...(showCategories !== undefined && { showCategories }),
            ...(productSectionBgColor !== undefined && { productSectionBgColor }),
            ...(bestsellerIds !== undefined && { bestsellerIds }),
            ...(showBestsellers !== undefined && { showBestsellers }),

            // Newsletter
            ...(newsletterTitle !== undefined && { newsletterTitle }),
            ...(newsletterSubtitle !== undefined && { newsletterSubtitle }),
            ...(showNewsletter !== undefined && { showNewsletter }),

            // Trust Badges
            ...(showTrustBadges !== undefined && { showTrustBadges }),

            // Flash Sale Section
            ...(flashSaleTitle !== undefined && { flashSaleTitle }),
            ...(flashSaleSubtitle !== undefined && { flashSaleSubtitle }),
            ...(flashSaleEndDate !== undefined && {
              flashSaleEndDate: flashSaleEndDate
                ? new Date(flashSaleEndDate)
                : null,
            }),
            ...(flashSaleBackgroundColor !== undefined && {
              flashSaleBackgroundColor,
            }),
            ...(flashSalePercentage !== undefined && { flashSalePercentage }),
            ...(showFlashSale !== undefined && { showFlashSale }),

            // Testimonials Section
            ...(testimonialsTitle !== undefined && { testimonialsTitle }),
            ...(testimonialsSubtitle !== undefined && { testimonialsSubtitle }),
            ...(testimonialsBackgroundColor !== undefined && {
              testimonialsBackgroundColor,
            }),
            ...(showTestimonials !== undefined && { showTestimonials }),

            ...(isActive !== undefined && { isActive }),
            updatedAt: new Date(),
          }
        },
        { new: true }
      );
    } else {
      // Create new settings
      settings = await HomepageSetting.create({
        id: "homepage-settings",
        // Hero Section
        heroTitle: heroTitle || "Natural Skincare Essentials",
        heroSubtitle:
          heroSubtitle ||
          "Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",
        heroCtaText: heroCtaText || "Shop Collection",
        heroCtaLink: heroCtaLink || "/shop",
        heroSecondaryCtaText: heroSecondaryCtaText || "View Categories",
        heroSecondaryCtaLink: heroSecondaryCtaLink || "/categories",
        heroBadgeText: heroBadgeText || "New Collection",
        heroBackgroundColor: heroBackgroundColor || "#f0fdf4",
        showHero: showHero !== undefined ? showHero : true,

        // Hero Trust Indicators
        trustIndicator1Value: trustIndicator1Value || "100%",
        trustIndicator1Label: trustIndicator1Label || "Natural",
        trustIndicator2Value: trustIndicator2Value || "500+",
        trustIndicator2Label: trustIndicator2Label || "Happy Customers",
        trustIndicator3Value: trustIndicator3Value || "50+",
        trustIndicator3Label: trustIndicator3Label || "Products",
        trustIndicator4Value: trustIndicator4Value || "4.8★",
        trustIndicator4Label: trustIndicator4Label || "Rating",

        // Product of the Month
        productOfTheMonthId,
        showProductOfMonth:
          showProductOfMonth !== undefined ? showProductOfMonth : true,

        // Promotional Banner
        bannerText,
        bannerCtaText,
        bannerCtaLink,
        bannerBackgroundColor: bannerBackgroundColor || "#22c55e",
        showBanner: showBanner !== undefined ? showBanner : true,

        // Sections
        showCategories: showCategories !== undefined ? showCategories : true,
        productSectionBgColor: productSectionBgColor || "#f0fdf4",
        bestsellerIds: bestsellerIds || [],
        showBestsellers: showBestsellers !== undefined ? showBestsellers : true,

        // Newsletter
        newsletterTitle: newsletterTitle || "Stay Updated",
        newsletterSubtitle:
          newsletterSubtitle ||
          "Get the latest updates on new products and exclusive offers",
        showNewsletter: showNewsletter !== undefined ? showNewsletter : true,

        // Trust Badges
        showTrustBadges: showTrustBadges !== undefined ? showTrustBadges : true,

        // Flash Sale Section
        flashSaleTitle: flashSaleTitle || "Weekend Flash Sale",
        flashSaleSubtitle:
          flashSaleSubtitle ||
          `Get ${flashSalePercentage ?? 25}% off all natural skincare products`,
        flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null,
        flashSaleBackgroundColor: flashSaleBackgroundColor || "#16a34a",
        flashSalePercentage: flashSalePercentage ?? 25,
        showFlashSale: showFlashSale !== undefined ? showFlashSale : true,

        // Testimonials Section
        testimonialsTitle: testimonialsTitle || "What Our Customers Say",
        testimonialsSubtitle:
          testimonialsSubtitle ||
          "Real reviews from real customers who love our natural skincare",
        testimonialsBackgroundColor: testimonialsBackgroundColor || "#f0fdf4",
        showTestimonials:
          showTestimonials !== undefined ? showTestimonials : true,

        isActive: isActive !== undefined ? isActive : true,
      });
    }

    return NextResponse.json({
      success: true,
      data: settings,
      message: "Homepage settings updated successfully",
    });
  } catch (error) {
    console.error("Error updating homepage settings:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update homepage settings" },
      { status: 500 },
    );
  }
}