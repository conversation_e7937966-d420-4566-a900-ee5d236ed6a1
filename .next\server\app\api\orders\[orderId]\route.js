"use strict";(()=>{var e={};e.id=3382,e.ids=[3382],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},33308:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>b,requestAsyncStorage:()=>N,routeModule:()=>w,serverHooks:()=>_,staticGenerationAsyncStorage:()=>y});var n={};t.r(n),t.d(n,{DELETE:()=>g,GET:()=>R,PATCH:()=>O});var a=t(49303),s=t(88716),o=t(60670),d=t(87070),i=t(65630),u=t(75571),c=t(95306),l=t(89456),p=t(81515),m=t(84875),f=t(54211),I=t(89585);let E=i.Ry({status:i.Km(["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"]).optional(),paymentStatus:i.Km(["PENDING","PAID","FAILED","REFUNDED"]).optional(),trackingNumber:i.Z_().optional(),notes:i.Z_().optional()}),R=(0,m.lm)(async(e,{params:r})=>{f.kg.apiRequest("GET",`/api/orders/${r.orderId}`);let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id)throw new m._7("Authentication required");let n="ADMIN"===t.user.role;try{await (0,l.Z)();let e=n?{}:{userId:t.user.id},a=await p.Order.findOne({_id:r.orderId,...e}).lean()||await p.Order.findOne({orderNumber:r.orderId,...e}).lean();if(!a)throw new m.dR("Order not found");let s=String(a._id),o=await p.Dd.find({orderId:s}).lean(),i=await Promise.all(o.map(async e=>{let r=await p.xs.findById(e.productId).select("_id name slug price images").lean();return{...e,id:String(e._id),product:r?{id:String(r._id),name:r.name,slug:r.slug,price:r.price,images:r.images||[]}:null}})),u=a.addressId?await p.kL.findById(a.addressId).lean():await p.kL.findOne({userId:a.userId}).lean(),c=n?await p.n5.findById(a.userId).select("_id name email phone").lean():null,I={...a,id:String(a._id),items:i,address:u,user:c?{id:String(c._id),name:c.name,email:c.email,phone:c.phone}:void 0};return f.kg.info("Order retrieved successfully",{orderId:r.orderId,userId:t.user.id,isAdmin:n}),d.NextResponse.json({success:!0,order:I})}catch(e){throw f.kg.error("Failed to retrieve order",e),e}}),O=(0,m.lm)(async(e,{params:r})=>{f.kg.apiRequest("PATCH",`/api/orders/${r.orderId}`);let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id||"ADMIN"!==t.user.role)throw new m._7("Admin access required");let n=await e.json(),a=E.parse(n);if("SHIPPED"===a.status&&!a.trackingNumber)throw new m.p8("Tracking number is required when marking order as shipped");try{await (0,l.Z)();let e=await p.Order.findOne({_id:r.orderId}).lean()||await p.Order.findOne({orderNumber:r.orderId}).lean();if(!e)throw new m.dR("Order not found");let n={updatedAt:new Date};a.status&&(n.status=a.status),a.paymentStatus&&(n.paymentStatus=a.paymentStatus),a.trackingNumber&&(n.trackingNumber=a.trackingNumber),a.notes&&(n.notes=a.notes),await p.Order.updateOne({_id:e._id},{$set:n});let s=await p.Order.findById(e._id).lean();if(a.status&&a.status!==e.status)try{let r=s?String(s._id):String(e._id),t=s?.orderNumber??e.orderNumber;switch(a.status){case"CONFIRMED":await I.aZ.orderConfirmed(e.userId,{orderId:r,orderNumber:t});break;case"PROCESSING":await I.aZ.orderProcessing(e.userId,{orderId:r,orderNumber:t});break;case"SHIPPED":await I.aZ.orderShipped(e.userId,{orderId:r,orderNumber:t,estimatedDelivery:s?.estimatedDelivery||void 0});break;case"DELIVERED":await I.aZ.orderDelivered(e.userId,{orderId:r,orderNumber:t}),s?.paymentMethod==="COD"&&s?.paymentStatus!=="PAID"&&await p.Order.updateOne({_id:e._id},{$set:{paymentStatus:"PAID"}});break;case"CANCELLED":await I.aZ.orderCancelled(e.userId,{orderId:r,orderNumber:t,reason:a.notes});break;case"REFUNDED":await I.aZ.orderCancelled(e.userId,{orderId:r,orderNumber:t,reason:a.notes||"Order has been refunded",refundAmount:s?.total,currency:s?.currency})}}catch(e){f.kg.error("Failed to send status update notification",e)}return f.kg.info("Order updated successfully",{orderId:r.orderId,updates:a,adminId:t.user.id}),d.NextResponse.json({success:!0,order:s})}catch(e){throw f.kg.error("Failed to update order",e),e}}),g=(0,m.lm)(async(e,{params:r})=>{f.kg.apiRequest("DELETE",`/api/orders/${r.orderId}`);let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id)throw new m._7("Authentication required");let n="ADMIN"===t.user.role;try{await (0,l.Z)();let e=await p.Order.findOne({_id:r.orderId,...n?{}:{userId:t.user.id}}).lean()||await p.Order.findOne({orderNumber:r.orderId,...n?{}:{userId:t.user.id}}).lean();if(!e)throw new m.dR("Order not found");if(!["PENDING","CONFIRMED"].includes(e.status))throw new m.p8("Order cannot be cancelled in current status");await p.Order.updateOne({_id:e._id},{$set:{status:"CANCELLED",updatedAt:new Date}});try{await I.aZ.orderCancelled(e.userId,{orderId:String(e._id),orderNumber:e.orderNumber,reason:"Cancelled by "+(n?"admin":"customer")})}catch(e){f.kg.error("Failed to send cancellation notification",e)}return f.kg.info("Order cancelled successfully",{orderId:r.orderId,userId:t.user.id,isAdmin:n}),d.NextResponse.json({success:!0,message:"Order cancelled successfully"})}catch(e){throw f.kg.error("Failed to cancel order",e),e}}),w=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/orders/[orderId]/route",pathname:"/api/orders/[orderId]",filename:"route",bundlePath:"app/api/orders/[orderId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\[orderId]\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:N,staticGenerationAsyncStorage:y,serverHooks:_}=w,h="/api/orders/[orderId]/route";function b(){return(0,o.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:y})}},84875:(e,r,t)=>{t.d(r,{AY:()=>c,M_:()=>i,_7:()=>d,dR:()=>u,gz:()=>s,lm:()=>m,p8:()=>o});var n=t(87070),a=t(29489);class s extends Error{constructor(e,r=500,t="INTERNAL_ERROR",n){super(e),this.statusCode=r,this.code=t,this.details=n,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,s)}}class o extends s{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class d extends s{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class i extends s{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class u extends s{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class c extends s{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class l extends s{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function p(e){let r={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return r[e]||r.INTERNAL_ERROR}function m(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){let r=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,error:e}),e instanceof s){let t={success:!1,error:{code:e.code,message:p(e.code),requestId:r,...!1}};return n.NextResponse.json(t,{status:e.statusCode})}if(e instanceof a.j){let t=new o("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),a={success:!1,error:{code:t.code,message:"Validation failed",requestId:r,...!1}};return n.NextResponse.json(a,{status:t.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let t=function(e){if(11e3===e.code||"MongoServerError"===e.name){let r=Object.keys(e.keyPattern||{})[0]||"field";return new c(`${r} already exists`)}return"ValidationError"===e.name?new o("Validation failed"):"CastError"===e.name?new o("Invalid data format"):"DocumentNotFoundError"===e.name?new u:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new l("Database connection failed"):new l("Database operation failed",{name:e.name,message:e.message})}(e),a={success:!1,error:{code:t.code,message:p(t.code),requestId:r,...!1}};return n.NextResponse.json(a,{status:t.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let t=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new c(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new d("Invalid user session");return new o("Invalid reference to related record");case"P2025":case"P2001":return new u;case"P2014":return new o("Missing required relationship");case"P2000":return new o("Input value is too long");case"P2004":return new o("Data constraint violation");default:return new l("Database operation failed",{code:e.code,message:e.message})}}(e),a={success:!1,error:{code:t.code,message:p(t.code),requestId:r,...!1}};return n.NextResponse.json(a,{status:t.statusCode})}}let t={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:r,...!1}};return n.NextResponse.json(t,{status:500})}(e)}}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var n={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var d=a?Object.getOwnPropertyDescriptor(e,s):null;d&&(d.get||d.set)?Object.defineProperty(n,s,d):n[s]=e[s]}return n.default=e,t&&t.set(e,n),n}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[9276,5972,8691,2830,9489,5630,5306,9585],()=>t(33308));module.exports=n})();