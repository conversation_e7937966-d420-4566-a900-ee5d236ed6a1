(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2626],{71370:function(e,s,r){Promise.resolve().then(r.bind(r,28250)),Promise.resolve().then(r.bind(r,82339))},82339:function(e,s,r){"use strict";var t=r(57437),a=r(2265),l=r(27648),n=r(99376),i=r(80605),o=r(89547),c=r(89345),d=r(66337),m=r(87769),u=r(42208);s.default=()=>{let e=(0,n.useRouter)(),{data:s,status:r}=(0,i.useSession)(),[x,h]=(0,a.useState)({email:"",password:""}),[f,g]=(0,a.useState)(!1),[p,b]=(0,a.useState)({}),[y,j]=(0,a.useState)(!1);a.useEffect(()=>{"authenticated"===r&&(null==s?void 0:s.user)&&("ADMIN"===s.user.role?e.push("/admin"):e.push("/profile"))},[r,s,e]);let w=e=>{let{name:s,value:r}=e.target;h(e=>({...e,[s]:r})),p[s]&&b(e=>({...e,[s]:""}))},N=()=>{let e={};return x.email?/\S+@\S+\.\S+/.test(x.email)||(e.email="Please enter a valid email"):e.email="Email is required",x.password||(e.password="Password is required"),b(e),0===Object.keys(e).length},v=async e=>{if(e.preventDefault(),N()){j(!0);try{let e=await (0,i.signIn)("credentials",{email:x.email,password:x.password,redirect:!1});if(null==e?void 0:e.error){b({general:"Invalid email or password"}),j(!1);return}(null==e?void 0:e.ok)&&(await (0,i.getSession)(),window.location.href="/profile")}catch(e){console.error("Login error:",e),b({general:"An error occurred during login"}),j(!1)}}},k=e=>{(0,i.signIn)(e,{callbackUrl:"/"})};return"loading"===r?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)("div",{className:"w-8 h-8 border-4 border-green-600 border-t-transparent rounded-full animate-spin"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center py-12 px-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(o.Z,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-3",children:"Welcome Back"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Sign in to your Herbalicious account"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-3xl p-8 shadow-lg border border-gray-100",children:[(0,t.jsxs)("form",{onSubmit:v,className:"space-y-6 mb-6",children:[p.general&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:p.general}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:"email",name:"email",value:x.email,onChange:w,placeholder:"Enter your email address",className:"w-full pl-12 pr-4 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ".concat(p.email?"border-red-500":"border-gray-300")})]}),p.email&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:p.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:f?"text":"password",name:"password",value:x.password,onChange:w,placeholder:"Enter your password",className:"w-full pl-12 pr-14 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ".concat(p.password?"border-red-500":"border-gray-300")}),(0,t.jsx)("button",{type:"button",onClick:()=>g(!f),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:f?(0,t.jsx)(m.Z,{className:"w-5 h-5"}):(0,t.jsx)(u.Z,{className:"w-5 h-5"})})]}),p.password&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:p.password})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",className:"rounded text-green-600 focus:ring-green-500"}),(0,t.jsx)("span",{className:"ml-2 text-gray-600",children:"Remember me"})]}),(0,t.jsx)(l.default,{href:"/forgot-password",className:"text-green-600 hover:text-green-700 font-medium",children:"Forgot password?"})]}),(0,t.jsx)("button",{type:"submit",disabled:y,className:"w-full bg-green-600 text-white py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center text-lg",children:y?(0,t.jsx)("div",{className:"w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"}):"Sign In"})]}),(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-4 bg-white text-gray-500",children:"Or continue with"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsx)("button",{onClick:()=>k("google"),className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors font-medium",children:"Google"}),(0,t.jsx)("button",{onClick:()=>k("facebook"),className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors font-medium",children:"Facebook"})]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(l.default,{href:"/signup",className:"text-green-600 font-semibold hover:text-green-700",children:"Create account"})]})})]})]})})}},87769:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(39763).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},42208:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89547:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(39763).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},66337:function(e,s,r){"use strict";r.d(s,{Z:function(){return t}});let t=(0,r(39763).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=71370)}),_N_E=e.O()}]);