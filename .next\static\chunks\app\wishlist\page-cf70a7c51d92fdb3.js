(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4456],{5679:function(e,s,t){Promise.resolve().then(t.bind(t,28250)),Promise.resolve().then(t.bind(t,96622))},96622:function(e,s,t){"use strict";var l=t(57437),i=t(2265),r=t(99376),a=t(80605),n=t(27648),c=t(33145),d=t(15863),o=t(32489),m=t(86595),h=t(16275),x=t(88997),u=t(53827),f=t(13905),g=t(92840),p=t(19124);s.default=()=>{let e=(0,r.useRouter)(),{data:s,status:t}=(0,a.useSession)(),{dispatch:j}=(0,u.j)(),{showToast:y}=(0,g.V)(),{refreshUnreadCount:v,refreshNotifications:w}=(0,p.z)(),[N,b]=(0,i.useState)([]),[k,E]=(0,i.useState)(!0),[Z,C]=(0,i.useState)(null),[A,_]=(0,i.useState)([]);(0,i.useEffect)(()=>{"unauthenticated"!==t||s||e.push("/login")},[t,e]),(0,i.useEffect)(()=>{(async()=>{var e;if("authenticated"!==t||!(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.id)){"loading"!==t&&E(!1);return}try{let e=await fetch("/api/wishlist");if(!e.ok)throw Error("Failed to fetch wishlist");let s=await e.json();b(s.items||[])}catch(e){console.error("Error fetching wishlist:",e),C("Failed to load wishlist")}finally{E(!1)}})()},[s,t]),(0,i.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/products?limit=4");if(e.ok){let s=(await e.json()).data.map(e=>{var s,t;return{id:e.id,name:e.name,slug:e.slug,price:e.price,shortDescription:e.shortDescription,image:(null===(t=e.images)||void 0===t?void 0:null===(s=t[0])||void 0===s?void 0:s.url)||"/placeholder-product.jpg",rating:e.rating||0,reviews:e.reviews||0,wishlistItemId:""}});_(s||[])}}catch(e){console.error("Error fetching recommendations:",e)}})()},[]);let z=async e=>{try{let s=await fetch("/api/wishlist?productId=".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to remove from wishlist")}b(s=>s.filter(s=>s.id!==e)),y("Item removed from wishlist","success"),v(),w()}catch(e){console.error("Error removing from wishlist:",e),y(e.message||"Could not remove item from wishlist.","error")}},D=e=>{let{wishlistItemId:s,...t}=e;j({type:"ADD_ITEM",payload:t}),y("".concat(t.name," added to cart"),"success")};return"loading"===t?(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsx)(d.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):"unauthenticated"===t?null:(0,l.jsx)("div",{className:"bg-gray-50 min-h-screen",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Wishlist"}),(0,l.jsxs)("span",{className:"text-gray-600",children:[N.length," items"]})]}),k?(0,l.jsx)(f.Gw,{}):Z?(0,l.jsxs)("div",{className:"text-center py-16",children:[(0,l.jsx)("div",{className:"w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,l.jsx)(o.Z,{className:"w-16 h-16 text-red-500"})}),(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Error loading wishlist"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:Z}),(0,l.jsx)("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Try Again"})]}):N.length>0?(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,l.jsx)("div",{className:"lg:col-span-2",children:(0,l.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4",children:[(0,l.jsx)(n.default,{href:"/product/".concat(e.slug||e.id),children:(0,l.jsx)("div",{className:"w-24 h-24 relative rounded-md overflow-hidden flex-shrink-0",children:(0,l.jsx)(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"96px"})})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)(n.default,{href:"/product/".concat(e.slug||e.id),children:(0,l.jsx)("h3",{className:"font-semibold text-gray-800 hover:text-green-600",children:e.name})}),(0,l.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["₹",e.price]}),(0,l.jsxs)("div",{className:"flex items-center mt-2",children:[(0,l.jsx)(m.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,l.jsxs)("span",{className:"text-sm text-gray-600 ml-1",children:[e.rating," (",e.reviews," reviews)"]})]})]}),(0,l.jsxs)("div",{className:"flex flex-col items-end space-y-2",children:[(0,l.jsx)("button",{onClick:()=>z(e.id),className:"p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors",children:(0,l.jsx)(o.Z,{className:"w-5 h-5"})}),(0,l.jsx)("button",{onClick:()=>D(e),className:"bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-green-200 transition-colors",children:"Add to Cart"})]})]},e.id))})}),(0,l.jsx)("div",{className:"lg:col-span-1",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 sticky top-24",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Wishlist Summary"}),(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Total Items:"}),(0,l.jsx)("span",{className:"font-semibold text-gray-900",children:N.length})]}),(0,l.jsxs)("button",{onClick:()=>{N.forEach(e=>{let{wishlistItemId:s,...t}=e;j({type:"ADD_ITEM",payload:t})}),y("All wishlist items added to cart","success")},className:"w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[(0,l.jsx)(h.Z,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Add All to Cart"})]}),(0,l.jsxs)("div",{className:"mt-8",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"You might also like"}),(0,l.jsx)("div",{className:"space-y-4",children:A.slice(0,2).map(e=>(0,l.jsxs)(n.default,{href:"/product/".concat(e.slug||e.id),className:"flex items-center space-x-3 group",children:[(0,l.jsx)("div",{className:"w-16 h-16 relative rounded-md overflow-hidden flex-shrink-0",children:(0,l.jsx)(c.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"64px"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-800 group-hover:text-green-600",children:e.name}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price]})]})]},e.id))})]})]})})]}):(0,l.jsxs)("div",{className:"text-center py-16",children:[(0,l.jsx)("div",{className:"w-32 h-32 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,l.jsx)(x.Z,{className:"w-16 h-16 text-green-500"})}),(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Your wishlist is empty"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Looks like you haven't added anything yet. Let's change that!"}),(0,l.jsx)(n.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Explore Products"})]})]})})}},65302:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},88997:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(39763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},15863:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},86595:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(39763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},45131:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,200,2971,2117,1744],function(){return e(e.s=5679)}),_N_E=e.O()}]);