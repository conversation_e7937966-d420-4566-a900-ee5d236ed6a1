"use strict";(()=>{var e={};e.id=5757,e.ids=[5757],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},13719:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>m,requestAsyncStorage:()=>v,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>d,PATCH:()=>f});var n=t(49303),i=t(88716),o=t(60670),a=t(87070),c=t(3474),p=t(75571),u=t(95306);async function d(e,{params:r}){try{let e=await (0,p.getServerSession)(u.L);if(!e?.user)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let t=e.user?.role==="ADMIN",s=e.user.id===r.id;if(!t&&!s)return a.NextResponse.json({success:!1,error:"Forbidden"},{status:403});await (0,c.uD)();let n=await c.n5.findById(r.id).lean(),i=n?.preferences||null;return i||(i={language:"en-US",theme:"light",orderUpdates:!0,promotions:!1,newsletter:!0,smsNotifications:!1,emailNotifications:!0,inAppNotifications:!0,orderNotifications:!0,wishlistNotifications:!0,reviewNotifications:!0,priceDropAlerts:!1,adminMessages:!0,broadcastMessages:!0},await c.n5.findByIdAndUpdate(r.id,{$set:{preferences:i}})),a.NextResponse.json({success:!0,data:i})}catch(e){return console.error("Error fetching user preferences:",e),a.NextResponse.json({success:!1,error:"Failed to fetch user preferences"},{status:500})}}async function f(e,{params:r}){try{let t=await (0,p.getServerSession)(u.L);if(!t?.user)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let s=t.user?.role==="ADMIN",n=t.user.id===r.id;if(!s&&!n)return a.NextResponse.json({success:!1,error:"Forbidden"},{status:403});let{language:i,theme:o,orderUpdates:d,promotions:f,newsletter:l,smsNotifications:v,emailNotifications:x,inAppNotifications:g,orderNotifications:y,wishlistNotifications:m,reviewNotifications:j,priceDropAlerts:h,adminMessages:w,broadcastMessages:N}=await e.json(),b={};void 0!==i&&(b["preferences.language"]=i),void 0!==o&&(b["preferences.theme"]=o),void 0!==d&&(b["preferences.orderUpdates"]=d),void 0!==f&&(b["preferences.promotions"]=f),void 0!==l&&(b["preferences.newsletter"]=l),void 0!==v&&(b["preferences.smsNotifications"]=v),void 0!==x&&(b["preferences.emailNotifications"]=x),void 0!==g&&(b["preferences.inAppNotifications"]=g),void 0!==y&&(b["preferences.orderNotifications"]=y),void 0!==m&&(b["preferences.wishlistNotifications"]=m),void 0!==j&&(b["preferences.reviewNotifications"]=j),void 0!==h&&(b["preferences.priceDropAlerts"]=h),void 0!==w&&(b["preferences.adminMessages"]=w),void 0!==N&&(b["preferences.broadcastMessages"]=N);let O={"preferences.language":"en-US","preferences.theme":"light","preferences.orderUpdates":!0,"preferences.promotions":!1,"preferences.newsletter":!0,"preferences.smsNotifications":!1,"preferences.emailNotifications":!0,"preferences.inAppNotifications":!0,"preferences.orderNotifications":!0,"preferences.wishlistNotifications":!0,"preferences.reviewNotifications":!0,"preferences.priceDropAlerts":!1,"preferences.adminMessages":!0,"preferences.broadcastMessages":!0},q=await c.n5.findByIdAndUpdate(r.id,{$setOnInsert:O,$set:b},{new:!0,upsert:!1,projection:{preferences:1}}).lean(),P=q?.preferences??await (async()=>await c.n5.exists({_id:r.id})?(await c.n5.findByIdAndUpdate(r.id,{$set:O}),(await c.n5.findById(r.id,{preferences:1}).lean())?.preferences):null)();return a.NextResponse.json({success:!0,data:P,message:"Preferences updated successfully"})}catch(e){return console.error("Error updating user preferences:",e),a.NextResponse.json({success:!1,error:"Failed to update user preferences"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/users/[id]/preferences/route",pathname:"/api/users/[id]/preferences",filename:"route",bundlePath:"app/api/users/[id]/preferences/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\preferences\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:v,staticGenerationAsyncStorage:x,serverHooks:g}=l,y="/api/users/[id]/preferences/route";function m(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:x})}},3474:(e,r,t)=>{t.d(r,{KM:()=>n.Order,WD:()=>n.WD,n5:()=>n.n5,uD:()=>s.Z,xs:()=>n.xs});var s=t(89456),n=t(81515)},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var n=t(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(s,i,a):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,2830,5306],()=>t(13719));module.exports=s})();