"use strict";(()=>{var e={};e.id=9407,e.ids=[9407],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},73571:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>f,patchFetch:()=>j,requestAsyncStorage:()=>q,routeModule:()=>m,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{DELETE:()=>x,PUT:()=>l});var i=s(49303),o=s(88716),n=s(60670),a=s(87070),u=s(45609),d=s(95306),p=s(89456),c=s(81515);async function l(e,{params:t}){try{let s=await (0,u.getServerSession)(d.L);if(!s?.user?.email)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});await (0,p.Z)();let r=await c.n5.findOne({email:s.user.email}).lean();if(!r||"ADMIN"!==r.role)return a.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{name:i,content:o,rating:n,image:l,position:x,company:m,isActive:q,order:g}=await e.json(),v=await c.gc.findByIdAndUpdate(t.id,{...void 0!==i&&{name:i},...void 0!==o&&{content:o},...void 0!==n&&{rating:n},...void 0!==l&&{image:l},...void 0!==x&&{position:x},...void 0!==m&&{company:m},...void 0!==q&&{isActive:q},...void 0!==g&&{order:g}},{new:!0});return a.NextResponse.json({success:!0,data:v,message:"Testimonial updated successfully"})}catch(e){return console.error("Error updating testimonial:",e),a.NextResponse.json({success:!1,error:"Failed to update testimonial"},{status:500})}}async function x(e,{params:t}){try{let e=await (0,u.getServerSession)(d.L);if(!e?.user?.email)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});await (0,p.Z)();let s=await c.n5.findOne({email:e.user.email}).lean();if(!s||"ADMIN"!==s.role)return a.NextResponse.json({success:!1,error:"Admin access required"},{status:403});return await c.gc.findByIdAndDelete(t.id),a.NextResponse.json({success:!0,message:"Testimonial deleted successfully"})}catch(e){return console.error("Error deleting testimonial:",e),a.NextResponse.json({success:!1,error:"Failed to delete testimonial"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/testimonials/[id]/route",pathname:"/api/testimonials/[id]",filename:"route",bundlePath:"app/api/testimonials/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:q,staticGenerationAsyncStorage:g,serverHooks:v}=m,f="/api/testimonials/[id]/route";function j(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,5972,8691,2830,5306],()=>s(73571));module.exports=r})();