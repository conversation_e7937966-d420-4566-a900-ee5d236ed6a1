"use strict";(()=>{var e={};e.id=5497,e.ids=[5497],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},84395:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>j,patchFetch:()=>y,requestAsyncStorage:()=>w,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>b,GET:()=>f,POST:()=>d});var n=r(49303),o=r(88716),a=r(60670),i=r(87070),u=r(75571),c=r(95306),l=r(89456),p=r(81515);async function d(e){try{let{email:t,name:r,whatsapp:s,source:n="homepage"}=await e.json();if(!t)return i.NextResponse.json({success:!1,error:"Email is required"},{status:400});await (0,l.Z)();let o=await p.mA.findOne({email:t}).lean();if(o){if(o.isActive)return i.NextResponse.json({success:!1,error:"Email is already subscribed"},{status:409});{let e=await p.mA.findOneAndUpdate({email:t},{$set:{isActive:!0,name:r||o.name,whatsapp:s||o.whatsapp,source:n,subscribedAt:new Date,unsubscribedAt:null}},{new:!0}).lean();return i.NextResponse.json({success:!0,data:e,message:"Successfully resubscribed to newsletter"})}}let a=await p.mA.create({email:t,name:r,whatsapp:s,source:n,isActive:!0});return a.toObject(),i.NextResponse.json({success:!0,data:a.toObject?a.toObject():a,message:"Successfully subscribed to newsletter"})}catch(e){return console.error("Error subscribing to newsletter:",e),i.NextResponse.json({success:!1,error:"Failed to subscribe to newsletter"},{status:500})}}async function f(e){try{let t=await (0,u.getServerSession)(c.L);if(!t||"ADMIN"!==t.user.role)return i.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),n=parseInt(r.get("limit")||"50"),o=r.get("active"),a=r.get("source"),d=(s-1)*n;await (0,l.Z)();let f={};null!==o&&(f.isActive="true"===o),a&&(f.source=a);let[b,m]=await Promise.all([p.mA.find(f).sort({createdAt:-1}).skip(d).limit(n).lean(),p.mA.countDocuments(f)]),w=await p.mA.countDocuments({isActive:!0}),x=await p.mA.countDocuments({isActive:!1});return i.NextResponse.json({success:!0,data:{subscribers:b,pagination:{page:s,limit:n,total:m,pages:Math.ceil(m/n)},stats:{total:m,active:w,inactive:x}}})}catch(e){return console.error("Error fetching newsletter subscribers:",e),i.NextResponse.json({success:!1,error:"Failed to fetch newsletter subscribers"},{status:500})}}async function b(e){try{let{searchParams:t}=new URL(e.url),r=t.get("email");if(!r)return i.NextResponse.json({success:!1,error:"Email is required"},{status:400});await (0,l.Z)();let s=await p.mA.findOneAndUpdate({email:r},{$set:{isActive:!1,unsubscribedAt:new Date}},{new:!0}).lean();return i.NextResponse.json({success:!0,data:s,message:"Successfully unsubscribed from newsletter"})}catch(e){return console.error("Error unsubscribing from newsletter:",e),i.NextResponse.json({success:!1,error:"Failed to unsubscribe from newsletter"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/newsletter/route",pathname:"/api/newsletter",filename:"route",bundlePath:"app/api/newsletter/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:x,serverHooks:g}=m,j="/api/newsletter/route";function y(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:x})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var n=r(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(s,o,i):s[o]=e[o]}return s.default=e,r&&r.set(e,s),s}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,5306],()=>r(84395));module.exports=s})();