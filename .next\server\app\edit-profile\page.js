(()=>{var e={};e.id=819,e.ids=[819],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26900:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),a(61012),a(37254),a(35866);var r=a(23191),t=a(88716),l=a(37922),n=a.n(l),i=a(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let o=["",{children:["edit-profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,61012)),"C:\\Users\\<USER>\\Desktop\\project\\app\\edit-profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\edit-profile\\page.tsx"],m="/edit-profile/page",x={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/edit-profile/page",pathname:"/edit-profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},99133:(e,s,a)=>{Promise.resolve().then(a.bind(a,23082))},23082:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var r=a(10326),t=a(17577),l=a(35047),n=a(77109),i=a(86333),d=a(79635),o=a(5932),c=a(42887),m=a(31215),x=a(77636),u=a(83855),h=a(69508),p=a(98091);let g=()=>{let e=(0,l.useRouter)(),{data:s,status:a,update:g}=(0,n.useSession)(),[y,f]=(0,t.useState)(!0),[j,b]=(0,t.useState)(!1),[v,N]=(0,t.useState)(null),[k,w]=(0,t.useState)([]),[P,Z]=(0,t.useState)({});(0,t.useEffect)(()=>{"loading"!==a&&"unauthenticated"===a&&e.push("/login")},[a,e]),(0,t.useEffect)(()=>{(async()=>{if(s?.user?.id)try{let e=await fetch(`/api/users/${s.user.id}`);if(e.ok){let s=await e.json();N({id:s.data.id,name:s.data.name||"",email:s.data.email,phone:s.data.phone||""}),w(s.data.addresses||[])}}catch(e){console.error("Error fetching user data:",e)}finally{f(!1)}})()},[s?.user?.id]);let S=(e,s)=>{v&&(N({...v,[e]:s}),P[e]&&Z(s=>({...s,[e]:""})))},C=()=>{let e={};return v?.name?.trim()||(e.name="Name is required"),v?.email?.trim()?/\S+@\S+\.\S+/.test(v.email)||(e.email="Please enter a valid email"):e.email="Email is required",v?.phone&&!/^\+?[\d\s\-\(\)]+$/.test(v.phone)&&(e.phone="Please enter a valid phone number"),Z(e),0===Object.keys(e).length},M=async()=>{if(C()&&v){b(!0);try{(await fetch(`/api/users/${v.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:v.name,email:v.email,phone:v.phone||null})})).ok?(await g({...s,user:{...s?.user,name:v.name,email:v.email}}),e.push("/profile")):Z({general:"Failed to update profile"})}catch(e){Z({general:"An error occurred while saving"})}finally{b(!1)}}},A=async e=>{if(confirm("Are you sure you want to delete this address?"))try{w(s=>s.filter(s=>s.id!==e))}catch(e){console.error("Error deleting address:",e)}},E=async e=>{try{w(s=>s.map(s=>({...s,isDefault:s.id===e})))}catch(e){console.error("Error setting default address:",e)}};return"loading"===a||y?r.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:r.jsx("div",{className:"max-w-4xl mx-auto px-4",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-8"}),r.jsx("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:r.jsx("div",{className:"space-y-4",children:[1,2,3].map(e=>r.jsx("div",{className:"h-16 bg-gray-200 rounded"},e))})})]})})}):"unauthenticated"===a?null:r.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[r.jsx("div",{className:"flex items-center mb-8",children:(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[r.jsx(i.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Back"})]})}),r.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Edit Profile"}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[r.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:r.jsx(d.Z,{className:"w-6 h-6 text-green-600"})}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:"Personal Information"})]}),P.general&&r.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:P.general}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),r.jsx("input",{type:"text",value:v?.name||"",onChange:e=>S("name",e.target.value),className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${P.name?"border-red-500":"border-gray-300"}`,placeholder:"Enter your full name"}),P.name&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:P.name})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(o.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"email",value:v?.email||"",onChange:e=>S("email",e.target.value),className:`w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${P.email?"border-red-500":"border-gray-300"}`,placeholder:"Enter your email address"})]}),P.email&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:P.email})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(c.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"tel",value:v?.phone||"",onChange:e=>S("phone",e.target.value),className:`w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${P.phone?"border-red-500":"border-gray-300"}`,placeholder:"Enter your phone number (optional)"})]}),P.phone&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:P.phone})]}),r.jsx("div",{className:"flex justify-end",children:(0,r.jsxs)("button",{onClick:M,disabled:j,className:"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors disabled:opacity-50",children:[r.jsx(m.Z,{className:"w-5 h-5"}),r.jsx("span",{children:j?"Saving...":"Save Changes"})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:r.jsx(x.Z,{className:"w-6 h-6 text-green-600"})}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:"Shipping Addresses"})]}),(0,r.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:[r.jsx(u.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Add Address"})]})]}),0===k.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(x.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-600",children:"No addresses added yet"}),r.jsx("button",{className:"mt-4 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Add Your First Address"})]}):r.jsx("div",{className:"space-y-4",children:k.map(e=>r.jsx("div",{className:"border border-gray-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&r.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[e.company&&r.jsx("p",{className:"font-medium",children:e.company}),r.jsx("p",{children:e.address1}),e.address2&&r.jsx("p",{children:e.address2}),(0,r.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),r.jsx("p",{children:e.country}),e.phone&&r.jsx("p",{className:"font-medium",children:e.phone})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&r.jsx("button",{onClick:()=>E(e.id),className:"px-3 py-1 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors",children:"Set as Default"}),r.jsx("button",{className:"p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",children:r.jsx(h.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>A(e.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:r.jsx(p.Z,{className:"w-4 h-4"})})]})]})},e.id))})]})]})]})})}},86333:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5932:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},77636:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},42887:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},83855:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31215:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},69508:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},98091:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},79635:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},35047:(e,s,a)=>{"use strict";var r=a(77389);a.o(r,"useParams")&&a.d(s,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},61012:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\edit-profile\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[9276,3757,9536],()=>a(26900));module.exports=r})();