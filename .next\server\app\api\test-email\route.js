"use strict";(()=>{var e={};e.id=5528,e.ids=[5528],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},194:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>q,requestAsyncStorage:()=>m,routeModule:()=>h,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{POST:()=>d});var i=r(49303),o=r(88716),n=r(60670),a=r(87070),u=r(75571),l=r(95306),p=r(95921),c=r(84875),f=r(54211);let d=(0,c.lm)(async e=>{f.kg.apiRequest("POST","/api/test-email");let t=await (0,u.getServerSession)(l.L);if(!t?.user)throw new c._7;if("ADMIN"!==t.user.role)throw new c.M_("Only admins can test email configuration");let{email:r}=await e.json();return r?(await (0,p.jC)(r),f.kg.info("Test email sent successfully",{email:r,userId:t.user.id}),a.NextResponse.json({success:!0,message:"Test email sent successfully"})):a.NextResponse.json({success:!1,error:"Email address is required"},{status:400})}),h=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/test-email/route",pathname:"/api/test-email",filename:"route",bundlePath:"app/api/test-email/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\test-email\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:g}=h,x="/api/test-email/route";function q(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>o}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class i{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:i,context:o,error:n,userId:a,requestId:u}=e,l=s[r],p=`[${t}] ${l}: ${i}`;return a&&(p+=` | User: ${a}`),u&&(p+=` | Request: ${u}`),o&&Object.keys(o).length>0&&(p+=` | Context: ${JSON.stringify(o)}`),n&&(p+=` | Error: ${n.message}`,this.isDevelopment&&n.stack&&(p+=`
Stack: ${n.stack}`)),p}log(e,t,r,s){if(!this.shouldLog(e))return;let i={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},o=this.formatMessage(i);if(this.isDevelopment)switch(e){case 0:console.error(o);break;case 1:console.warn(o);break;case 2:console.info(o);break;case 3:console.debug(o)}else console.log(JSON.stringify(i))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,i){this.info(`API ${e} ${t} - ${r}`,{...i,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,i){this.error(`API ${e} ${t} failed`,r,{...i,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let o=new i},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(s,o,a):s[o]=e[o]}return s.default=e,r&&r.set(e,s),s}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,9489,5245,5306,83],()=>r(194));module.exports=s})();