(()=>{var e={};e.id=2626,e.ids=[2626],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24640:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),s(93162),s(37254),s(35866);var t=s(23191),a=s(88716),l=s(37922),n=s.n(l),i=s(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let d=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93162)),"C:\\Users\\<USER>\\Desktop\\project\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\login\\page.tsx"],m="/login/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29721:(e,r,s)=>{Promise.resolve().then(s.bind(s,65090)),Promise.resolve().then(s.bind(s,7526))},7526:(e,r,s)=>{"use strict";s.d(r,{default:()=>p});var t=s(10326),a=s(17577),l=s.n(a),n=s(90434),i=s(35047),o=s(77109),d=s(53080),c=s(5932),m=s(9015),x=s(91216),u=s(12714);let p=()=>{let e=(0,i.useRouter)(),{data:r,status:s}=(0,o.useSession)(),[p,g]=(0,a.useState)({email:"",password:""}),[h,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)({}),[j,v]=(0,a.useState)(!1);l().useEffect(()=>{"authenticated"===s&&r?.user&&("ADMIN"===r.user.role?e.push("/admin"):e.push("/profile"))},[s,r,e]);let w=e=>{let{name:r,value:s}=e.target;g(e=>({...e,[r]:s})),y[r]&&b(e=>({...e,[r]:""}))},N=()=>{let e={};return p.email?/\S+@\S+\.\S+/.test(p.email)||(e.email="Please enter a valid email"):e.email="Email is required",p.password||(e.password="Password is required"),b(e),0===Object.keys(e).length},k=async e=>{if(e.preventDefault(),N()){v(!0);try{let e=await (0,o.signIn)("credentials",{email:p.email,password:p.password,redirect:!1});if(e?.error){b({general:"Invalid email or password"}),v(!1);return}e?.ok&&(await (0,o.getSession)(),window.location.href="/profile")}catch(e){console.error("Login error:",e),b({general:"An error occurred during login"}),v(!1)}}},P=e=>{(0,o.signIn)(e,{callbackUrl:"/"})};return"loading"===s?t.jsx("div",{className:"flex items-center justify-center min-h-screen",children:t.jsx("div",{className:"w-8 h-8 border-4 border-green-600 border-t-transparent rounded-full animate-spin"})}):t.jsx("div",{className:"min-h-screen flex items-center justify-center py-12 px-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[t.jsx("div",{className:"w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6",children:t.jsx(d.Z,{className:"w-10 h-10 text-white"})}),t.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-3",children:"Welcome Back"}),t.jsx("p",{className:"text-gray-600 text-lg",children:"Sign in to your Herbalicious account"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-3xl p-8 shadow-lg border border-gray-100",children:[(0,t.jsxs)("form",{onSubmit:k,className:"space-y-6 mb-6",children:[y.general&&t.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:y.general}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(c.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),t.jsx("input",{type:"email",name:"email",value:p.email,onChange:w,placeholder:"Enter your email address",className:`w-full pl-12 pr-4 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ${y.email?"border-red-500":"border-gray-300"}`})]}),y.email&&t.jsx("p",{className:"text-red-500 text-sm mt-1",children:y.email})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx(m.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),t.jsx("input",{type:h?"text":"password",name:"password",value:p.password,onChange:w,placeholder:"Enter your password",className:`w-full pl-12 pr-14 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ${y.password?"border-red-500":"border-gray-300"}`}),t.jsx("button",{type:"button",onClick:()=>f(!h),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:h?t.jsx(x.Z,{className:"w-5 h-5"}):t.jsx(u.Z,{className:"w-5 h-5"})})]}),y.password&&t.jsx("p",{className:"text-red-500 text-sm mt-1",children:y.password})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("label",{className:"flex items-center",children:[t.jsx("input",{type:"checkbox",className:"rounded text-green-600 focus:ring-green-500"}),t.jsx("span",{className:"ml-2 text-gray-600",children:"Remember me"})]}),t.jsx(n.default,{href:"/forgot-password",className:"text-green-600 hover:text-green-700 font-medium",children:"Forgot password?"})]}),t.jsx("button",{type:"submit",disabled:j,className:"w-full bg-green-600 text-white py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center text-lg",children:j?t.jsx("div",{className:"w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"}):"Sign In"})]}),(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-0 flex items-center",children:t.jsx("div",{className:"w-full border-t border-gray-300"})}),t.jsx("div",{className:"relative flex justify-center text-sm",children:t.jsx("span",{className:"px-4 bg-white text-gray-500",children:"Or continue with"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[t.jsx("button",{onClick:()=>P("google"),className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors font-medium",children:"Google"}),t.jsx("button",{onClick:()=>P("facebook"),className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors font-medium",children:"Facebook"})]})]}),t.jsx("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",t.jsx(n.default,{href:"/signup",className:"text-green-600 font-semibold hover:text-green-700",children:"Create account"})]})})]})]})})}},91216:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},53080:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},9015:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},40304:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)},93162:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(19510),a=s(40304);let l=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Login.tsx#default`);function n(){return t.jsx(a.Z,{children:t.jsx(l,{})})}}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[9276,3757,434,9694,9536,5090],()=>s(24640));module.exports=t})();