(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7460],{22499:function(e,t,s){Promise.resolve().then(s.bind(s,18651))},18651:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return h}});var a=s(57437),r=s(2265),n=s(80605),i=s(99376),l=s(15863),c=s(22252),o=s(99397),d=s(62720),u=s(87769),m=s(42208),x=s(15868),g=s(18930),p=s(32489);function h(){var e;let{data:t,status:s}=(0,n.useSession)(),h=(0,i.useRouter)(),[y,b]=(0,r.useState)([]),[f,v]=(0,r.useState)(!0),[j,N]=(0,r.useState)(!1),[C,k]=(0,r.useState)(!1),[w,E]=(0,r.useState)(null),[P,A]=(0,r.useState)([]),[S,T]=(0,r.useState)([]),[_,F]=(0,r.useState)({code:"",name:"",description:"",type:"STORE_WIDE",discountType:"PERCENTAGE",discountValue:0,minimumAmount:void 0,maximumDiscount:void 0,usageLimit:void 0,userUsageLimit:void 0,isActive:!0,isStackable:!1,showInModule:!1,validFrom:new Date().toISOString().split("T")[0],validUntil:void 0,applicableProducts:[],applicableCategories:[],excludedProducts:[],excludedCategories:[],customerSegments:[],buyQuantity:1,getQuantity:1,buyProducts:[],buyCategories:[],getProducts:[],getCategories:[],maxApplications:1,discountApplication:"FREE",getDiscountValue:0}),I=(0,r.useCallback)(async()=>{try{v(!0);let e=await fetch("/api/coupons?limit=100");if(e.ok){let t=await e.json();b(t.coupons)}}catch(e){console.error("Error fetching coupons:",e)}finally{v(!1)}},[]),U=(0,r.useCallback)(async()=>{try{let e=await fetch("/api/products?limit=100");if(e.ok){let t=await e.json();console.log("Products API response:",t);let s=t.data||[];console.log("Products list:",s),A(s)}else console.error("Failed to fetch products:",e.status)}catch(e){console.error("Error fetching products:",e)}},[]),D=(0,r.useCallback)(async()=>{try{let e=await fetch("/api/categories");if(e.ok){let t=await e.json();console.log("Categories API response:",t);let s=t.data||t.categories||[];console.log("Categories list:",s),T(s)}else console.error("Failed to fetch categories:",e.status)}catch(e){console.error("Error fetching categories:",e)}},[]);(0,r.useEffect)(()=>{var e;if("loading"!==s&&!j){if(!t||(null===(e=t.user)||void 0===e?void 0:e.role)!=="ADMIN"){h.push("/");return}N(!0),I(),U(),D()}},[t,s,j,I,U,D,h]);let M=async e=>{e.preventDefault();try{let e=w?"/api/coupons/".concat(w.id):"/api/coupons",t=await fetch(e,{method:w?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(_)});if(t.ok)await I(),Y(),k(!1),E(null);else{let e=await t.json();alert(e.error||"Failed to save coupon")}}catch(e){console.error("Error saving coupon:",e),alert("Failed to save coupon")}},R=async e=>{if(confirm("Are you sure you want to delete this coupon?"))try{(await fetch("/api/coupons/".concat(e),{method:"DELETE"})).ok?await I():alert("Failed to delete coupon")}catch(e){console.error("Error deleting coupon:",e),alert("Failed to delete coupon")}},G=async e=>{try{(await fetch("/api/coupons/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,isActive:!e.isActive})})).ok&&await I()}catch(e){console.error("Error updating coupon status:",e)}},Y=()=>{F({code:"",name:"",description:"",type:"STORE_WIDE",discountType:"PERCENTAGE",discountValue:0,minimumAmount:void 0,maximumDiscount:void 0,usageLimit:void 0,userUsageLimit:void 0,isActive:!0,isStackable:!1,showInModule:!1,validFrom:new Date().toISOString().split("T")[0],validUntil:void 0,applicableProducts:[],applicableCategories:[],excludedProducts:[],excludedCategories:[],customerSegments:[],buyQuantity:1,getQuantity:1,buyProducts:[],buyCategories:[],getProducts:[],getCategories:[],maxApplications:1,discountApplication:"FREE",getDiscountValue:0})},O=e=>{E(e),F({code:e.code,name:e.name,description:e.description||"",type:e.type,discountType:e.discountType,discountValue:e.discountValue,minimumAmount:e.minimumAmount||void 0,maximumDiscount:e.maximumDiscount||void 0,usageLimit:e.usageLimit||void 0,userUsageLimit:e.userUsageLimit||void 0,isActive:e.isActive,isStackable:e.isStackable,showInModule:e.showInModule,validFrom:e.validFrom.split("T")[0],validUntil:e.validUntil?e.validUntil.split("T")[0]:void 0,applicableProducts:e.applicableProducts,applicableCategories:e.applicableCategories,excludedProducts:e.excludedProducts,excludedCategories:e.excludedCategories,customerSegments:e.customerSegments,buyQuantity:e.buyQuantity||1,getQuantity:e.getQuantity||1,buyProducts:e.buyProducts||[],buyCategories:e.buyCategories||[],getProducts:e.getProducts||[],getCategories:e.getCategories||[],maxApplications:e.maxApplications||1,discountApplication:e.discountApplication||"FREE",getDiscountValue:e.getDiscountValue||0}),k(!0)},Z=e=>{if("BUY_X_GET_Y"===e.discountType){let t="FREE"===e.discountApplication?"FREE":"PERCENTAGE"===e.discountApplication?"".concat(e.getDiscountValue,"% OFF"):"₹".concat(e.getDiscountValue," OFF");return"Buy ".concat(e.buyQuantity," Get ").concat(e.getQuantity," ").concat(t)}switch(e.discountType){case"PERCENTAGE":return"".concat(e.discountValue,"% OFF");case"FIXED_AMOUNT":return"₹".concat(e.discountValue," OFF");case"FREE_SHIPPING":return"FREE SHIPPING";default:return"DISCOUNT"}},L=e=>{if(!e.isActive)return"bg-gray-100 text-gray-700 border-gray-200";let t=new Date,s=e.validUntil?new Date(e.validUntil):null;return s&&s<t?"bg-red-100 text-red-700 border-red-200":s&&s.getTime()-t.getTime()<2592e5?"bg-orange-100 text-orange-700 border-orange-200":"bg-green-100 text-green-700 border-green-200"},V=(e,t)=>{let s=_[t]||[],a=s.includes(e)?s.filter(t=>t!==e):[...s,e];F({..._,[t]:a})},B=(e,t)=>{let s=_[t]||[],a=s.includes(e)?s.filter(t=>t!==e):[...s,e];F({..._,[t]:a})};return"loading"===s?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(l.Z,{className:"w-8 h-8 animate-spin text-green-600"})})})}):t&&(null===(e=t.user)||void 0===e?void 0:e.role)==="ADMIN"?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Coupon Management"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Create and manage discount coupons"})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[y.length," ",1===y.length?"coupon":"coupons"," ","total"]}),(0,a.jsxs)("button",{onClick:()=>{Y(),k(!0)},className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Create Coupon"})]})]})}),f?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(l.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):0===y.length?(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No coupons found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Get started by creating your first coupon"}),(0,a.jsx)("button",{onClick:()=>{Y(),k(!0)},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:"Create Your First Coupon"})]})}):(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Coupon"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Discount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Usage"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Validity"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:y.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200",children:e.code}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.type.replace("_"," ")}),e.isStackable&&(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200",children:"Stackable"})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-semibold text-green-600",children:Z(e)}),e.minimumAmount&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Min: ₹",e.minimumAmount]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[e.usageCount,"/",e.usageLimit||"∞"]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.usageLimit?"".concat(Math.round(e.usageCount/e.usageLimit*100),"% used"):"Unlimited"})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:new Date(e.validFrom).toLocaleDateString()}),e.validUntil&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Until"," ",new Date(e.validUntil).toLocaleDateString()]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ".concat(L(e)),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>G(e),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:e.isActive?"Deactivate":"Activate",children:e.isActive?(0,a.jsx)(u.Z,{className:"w-5 h-5"}):(0,a.jsx)(m.Z,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>O(e),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:"Edit",children:(0,a.jsx)(x.Z,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>R(e.id),className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",title:"Delete",children:(0,a.jsx)(g.Z,{className:"w-5 h-5"})})]})})]},e.id))})]})})}),C&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:w?"Edit Coupon":"Create New Coupon"}),(0,a.jsx)("button",{onClick:()=>{k(!1),E(null),Y()},className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(p.Z,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("form",{onSubmit:M,className:"p-6",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Basic Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Coupon Code *"}),(0,a.jsx)("input",{type:"text",value:_.code,onChange:e=>F({..._,code:e.target.value.toUpperCase()}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Coupon Name *"}),(0,a.jsx)("input",{type:"text",value:_.name,onChange:e=>F({..._,name:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,a.jsx)("textarea",{value:_.description,onChange:e=>F({..._,description:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:3})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Discount Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Coupon Type *"}),(0,a.jsxs)("select",{value:_.type,onChange:e=>{let t=e.target.value,s={type:t};"BUY_X_GET_Y"===t?s.discountType="BUY_X_GET_Y":"BUY_X_GET_Y"===_.type&&"BUY_X_GET_Y"===_.discountType&&(s.discountType="PERCENTAGE"),F({..._,...s})},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"STORE_WIDE",children:"Store Wide"}),(0,a.jsx)("option",{value:"PRODUCT_SPECIFIC",children:"Product Specific"}),(0,a.jsx)("option",{value:"CATEGORY_SPECIFIC",children:"Category Specific"}),(0,a.jsx)("option",{value:"MINIMUM_PURCHASE",children:"Minimum Purchase"}),(0,a.jsx)("option",{value:"BUNDLE_DEAL",children:"Bundle Deal"}),(0,a.jsx)("option",{value:"FIRST_TIME_CUSTOMER",children:"First Time Customer"}),(0,a.jsx)("option",{value:"LOYALTY_REWARD",children:"Loyalty Reward"}),(0,a.jsx)("option",{value:"SEASONAL",children:"Seasonal"}),(0,a.jsx)("option",{value:"BUY_X_GET_Y",children:"Buy X Get Y"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Type *"}),(0,a.jsxs)("select",{value:_.discountType,onChange:e=>F({..._,discountType:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"PERCENTAGE",children:"Percentage"}),(0,a.jsx)("option",{value:"FIXED_AMOUNT",children:"Fixed Amount"}),(0,a.jsx)("option",{value:"FREE_SHIPPING",children:"Free Shipping"}),"BUY_X_GET_Y"===_.type&&(0,a.jsx)("option",{value:"BUY_X_GET_Y",children:"Buy X Get Y"})]})]})]}),"BUY_X_GET_Y"===_.discountType&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-3",children:"Buy X Get Y Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Buy Quantity *"}),(0,a.jsx)("input",{type:"number",value:_.buyQuantity,onChange:e=>F({..._,buyQuantity:parseInt(e.target.value)||1}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Get Quantity *"}),(0,a.jsx)("input",{type:"number",value:_.getQuantity,onChange:e=>F({..._,getQuantity:parseInt(e.target.value)||1}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Applications"}),(0,a.jsx)("input",{type:"number",value:_.maxApplications,onChange:e=>F({..._,maxApplications:parseInt(e.target.value)||1}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Application *"}),(0,a.jsxs)("select",{value:_.discountApplication,onChange:e=>F({..._,discountApplication:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"FREE",children:"Free"}),(0,a.jsx)("option",{value:"PERCENTAGE",children:"Percentage Off"}),(0,a.jsx)("option",{value:"FIXED_AMOUNT",children:"Fixed Amount Off"})]})]}),"FREE"!==_.discountApplication&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Value *"}),(0,a.jsx)("input",{type:"number",value:_.getDiscountValue,onChange:e=>F({..._,getDiscountValue:parseFloat(e.target.value)||0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01",required:!0})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Buy Products/Categories"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Buy Products"}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:P.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=_.buyProducts)||void 0===t?void 0:t.includes(e.id))||!1,onChange:()=>V(e.id,"buyProducts"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Buy Categories"}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:S.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=_.buyCategories)||void 0===t?void 0:t.includes(e.id))||!1,onChange:()=>B(e.id,"buyCategories"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Get Products/Categories"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Get Products"}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:P.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=_.getProducts)||void 0===t?void 0:t.includes(e.id))||!1,onChange:()=>V(e.id,"getProducts"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Get Categories"}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:S.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=_.getCategories)||void 0===t?void 0:t.includes(e.id))||!1,onChange:()=>B(e.id,"getCategories"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]})]})]})]}),"BUY_X_GET_Y"!==_.discountType&&"FREE_SHIPPING"!==_.discountType&&(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Value *"}),(0,a.jsx)("input",{type:"number",value:_.discountValue,onChange:e=>F({..._,discountValue:parseFloat(e.target.value)}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Amount"}),(0,a.jsx)("input",{type:"number",value:_.minimumAmount||"",onChange:e=>F({..._,minimumAmount:e.target.value?parseFloat(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Maximum Discount"}),(0,a.jsx)("input",{type:"number",value:_.maximumDiscount||"",onChange:e=>F({..._,maximumDiscount:e.target.value?parseFloat(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01"})]})]})]}),("PRODUCT_SPECIFIC"===_.type||"CATEGORY_SPECIFIC"===_.type)&&"BUY_X_GET_Y"!==_.discountType&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Applicable Items"}),"PRODUCT_SPECIFIC"===_.type&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Products"}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-3 max-h-48 overflow-y-auto",children:0===P.length?(0,a.jsx)("p",{className:"text-sm text-gray-500 text-center py-4",children:"No products available"}):P.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=_.applicableProducts)||void 0===t?void 0:t.includes(e.id))||!1,onChange:()=>V(e.id,"applicableProducts"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:[e.name," - ₹",e.price]})]},e.id)})})]}),"CATEGORY_SPECIFIC"===_.type&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Categories"}),(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-3 max-h-48 overflow-y-auto",children:S.map(e=>{var t;return(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null===(t=_.applicableCategories)||void 0===t?void 0:t.includes(e.id))||!1,onChange:()=>B(e.id,"applicableCategories"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Usage Limits"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Total Usage Limit"}),(0,a.jsx)("input",{type:"number",value:_.usageLimit||"",onChange:e=>F({..._,usageLimit:e.target.value?parseInt(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Per User Usage Limit"}),(0,a.jsx)("input",{type:"number",value:_.userUsageLimit||"",onChange:e=>F({..._,userUsageLimit:e.target.value?parseInt(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Validity Period"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Valid From *"}),(0,a.jsx)("input",{type:"date",value:_.validFrom,onChange:e=>F({..._,validFrom:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Valid Until"}),(0,a.jsx)("input",{type:"date",value:_.validUntil||"",onChange:e=>F({..._,validUntil:e.target.value||void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Settings"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:_.isActive,onChange:e=>F({..._,isActive:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Active"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:_.isStackable,onChange:e=>F({..._,isStackable:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Stackable"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:_.showInModule,onChange:e=>F({..._,showInModule:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Show in Module"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{k(!1),E(null),Y()},className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[w?"Update":"Create"," Coupon"]})]})]})]})})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c.Z,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:"You don't have permission to access this page."})]})})})})}},22252:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87769:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},42208:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15863:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},99397:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},15868:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},62720:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},18930:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},32489:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},99376:function(e,t,s){"use strict";var a=s(35475);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})}},function(e){e.O(0,[7349,2971,2117,1744],function(){return e(e.s=22499)}),_N_E=e.O()}]);