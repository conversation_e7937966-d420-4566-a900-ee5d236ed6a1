(()=>{var e={};e.id=799,e.ids=[799],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},90171:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>p,routeModule:()=>x,tree:()=>d}),t(4192),t(37254),t(35866);var r=t(23191),a=t(88716),i=t(37922),n=t.n(i),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d=["",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4192)),"C:\\Users\\<USER>\\Desktop\\project\\app\\faq\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],p=["C:\\Users\\<USER>\\Desktop\\project\\app\\faq\\page.tsx"],c="/faq/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/faq/page",pathname:"/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},35303:()=>{},4192:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(19510);t(71159);let a=()=>r.jsx("div",{className:"min-h-screen bg-white",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8",children:[r.jsx("h1",{className:"text-3xl font-extrabold text-gray-900",children:"Frequently Asked Questions"}),(0,r.jsxs)("div",{className:"mt-6 prose prose-indigo text-gray-500",children:[(0,r.jsxs)("div",{className:"py-6",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"What is your return policy?"}),r.jsx("p",{className:"mt-2",children:"We have a 30-day return policy, which means you have 30 days after receiving your item to request a return. To be eligible for a return, your item must be in the same condition that you received it, unworn or unused, with tags, and in its original packaging. You’ll also need the receipt or proof of purchase."})]}),(0,r.jsxs)("div",{className:"py-6",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"How long does shipping take?"}),r.jsx("p",{className:"mt-2",children:"Orders are typically processed within 1-2 business days. Shipping times may vary based on your location, but most orders arrive within 5-7 business days."})]}),(0,r.jsxs)("div",{className:"py-6",children:[r.jsx("h2",{className:"text-xl font-semibold",children:"Do you ship internationally?"}),r.jsx("p",{className:"mt-2",children:"Currently, we only ship within the country. We are working on expanding our shipping options to include international destinations in the near future."})]})]})]})})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,3757,9536],()=>t(90171));module.exports=r})();