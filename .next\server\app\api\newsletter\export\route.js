"use strict";(()=>{var e={};e.id=2480,e.ids=[2480],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},41346:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>j,requestAsyncStorage:()=>x,routeModule:()=>f,serverHooks:()=>v,staticGenerationAsyncStorage:()=>b});var n={};r.r(n),r.d(n,{GET:()=>d});var s=r(49303),o=r(88716),a=r(60670),i=r(87070),u=r(75571),p=r(95306),c=r(89456),l=r(81515);async function d(e){try{let t=await (0,u.getServerSession)(p.L);if(!t||"ADMIN"!==t.user.role)return i.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),n=r.get("active"),s=r.get("source"),o=r.get("format")||"csv";await (0,c.Z)();let a={};null!==n&&(a.isActive="true"===n),s&&(a.source=s);let d=await l.mA.find(a).sort({createdAt:-1}).lean();if("csv"===o){let e=d.map(e=>[e.email,e.name||"",e.isActive?"Active":"Inactive",e.source||"",e.createdAt?new Date(e.createdAt).toISOString():"",e.unsubscribedAt?new Date(e.unsubscribedAt).toISOString():""]),t=["Email,Name,Status,Source,Subscribed At,Unsubscribed At",...e.map(e=>e.map(e=>`"${e}"`).join(","))].join("\n");return new i.NextResponse(t,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="newsletter-subscribers-${new Date().toISOString().split("T")[0]}.csv"`}})}if("json"===o)return i.NextResponse.json({success:!0,data:{subscribers:d,exportedAt:new Date().toISOString(),total:d.length}});return i.NextResponse.json({success:!1,error:"Unsupported format. Use csv or json."},{status:400})}catch(e){return console.error("Error exporting newsletter subscribers:",e),i.NextResponse.json({success:!1,error:"Failed to export newsletter subscribers"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/newsletter/export/route",pathname:"/api/newsletter/export",filename:"route",bundlePath:"app/api/newsletter/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\export\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:x,staticGenerationAsyncStorage:b,serverHooks:v}=f,g="/api/newsletter/export/route";function j(){return(0,a.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:b})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=s?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,5972,8691,2830,5306],()=>r(41346));module.exports=n})();