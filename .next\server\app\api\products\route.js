"use strict";(()=>{var e={};e.id=9684,e.ids=[9684],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4926:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>f,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>S,staticGenerationAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{GET:()=>p,POST:()=>l});var a=r(49303),o=r(88716),n=r(60670),s=r(87070),d=r(89456),c=r(11185),u=r(81515);async function p(e){try{let t,r;await (0,d.Z)();let{searchParams:i}=new URL(e.url),a=parseInt(i.get("page")||"1"),o=parseInt(i.get("limit")||"10"),n=i.get("category"),p=i.get("search"),l=i.get("sort")||"random",m="true"===e.headers.get("x-admin-request"),g=(a-1)*o,y={};m||(y.isActive=!0);let S=[];if(n){let e=c.Types.ObjectId.isValid(n),t=null;if(e)t=new c.Types.ObjectId(n);else{let e=await u.WD.findOne({slug:n},{_id:1}).lean().exec();if(!e?._id)return s.NextResponse.json({success:!0,data:[],pagination:{page:a,limit:o,total:0,pages:0}});t=e._id}S.push({categoryId:t})}switch(p&&S.push({$or:[{name:{$regex:p,$options:"i"}},{description:{$regex:p,$options:"i"}}]}),S.length>0&&(y.$and=S),l){case"name_asc":t={name:1};break;case"name_desc":t={name:-1};break;case"price_asc":t={price:1};break;case"price_desc":t={price:-1};break;case"newest":t={createdAt:-1};break;case"oldest":t={createdAt:1};break;default:t=void 0}let h=await u.xs.countDocuments(y);if("random"===l){let e=[{$match:y},{$sample:{size:Math.min(o,h)}}];r=await u.xs.aggregate(e),g>0&&(r=r.slice(g,g+o))}else r=await u.xs.find(y).sort(t||{}).skip(g).limit(o).lean();let f=await Promise.all(r.map(async e=>{let t=e.categoryId?await u.WD.findById(e.categoryId).lean():null,r=new c.Types.ObjectId(e._id),[i,a,o]=await Promise.all([u.qN.find({productId:r}).sort({position:1}),u.Th.find({productId:r}),u.Cq.countDocuments({productId:r})]);return{...e,category:t,productCategories:[],images:i,variants:a,_count:{reviews:o}}}));return s.NextResponse.json({success:!0,data:f,pagination:{page:a,limit:o,total:h,pages:Math.ceil(h/o)}})}catch(e){return console.error("Error fetching products:",e),s.NextResponse.json({success:!1,error:"Failed to fetch products"},{status:500})}}async function l(e){try{await (0,d.Z)();let{name:t,slug:r,description:i,shortDescription:a,price:o,comparePrice:n,categoryId:c,categoryIds:p=[],images:l,isFeatured:m,variations:g=[]}=await e.json();if(!t)return s.NextResponse.json({success:!1,error:"Product name is required"},{status:400});let y=void 0!==o?parseFloat(o.toString()):null,S=null!==y&&y>=0,h=g&&g.length>0,f=[];if(!S&&!h)return s.NextResponse.json({success:!1,error:"Product must have either a base price (can be 0) or variations with pricing"},{status:400});0!==y||g&&0!==g.length||f.push("Product has zero base price and no variations. Consider adding variations for pricing."),0===y&&g&&g.length>0&&g.some(e=>!e.price||0===e.price)&&f.push("Some variations have zero price. Ensure all variations have valid pricing."),null===y&&(y=0);let I=await u.xs.create({name:t,slug:r,description:i,shortDescription:a,price:y,comparePrice:n?parseFloat(n.toString()):void 0,categoryId:c,isFeatured:!!m});if(l&&l.length>0){let e=l.map((e,r)=>({productId:I._id,url:e.url,alt:e.alt||t,position:r}));await u.qN.insertMany(e)}if(g.length>0){let e=g.map(e=>({productId:I._id,name:e.name,value:e.value,price:e.price||void 0,pricingMode:e.pricingMode||"INCREMENT"}));await u.Th.insertMany(e)}let[q,b,N]=await Promise.all([I.categoryId?u.WD.findById(I.categoryId):null,u.qN.find({productId:I._id}).sort({position:1}),u.Th.find({productId:I._id})]),w={...I.toObject(),category:q,productCategories:[],images:b,variants:N};return s.NextResponse.json({success:!0,data:w,message:"Product created successfully",warnings:f.length>0?f:void 0})}catch(e){return console.error("Error creating product:",e),s.NextResponse.json({success:!1,error:"Failed to create product"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:S}=m,h="/api/products/route";function f(){return(0,n.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:y})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>D,Dd:()=>x,Order:()=>T,P_:()=>R,Pp:()=>U,Th:()=>j,Vv:()=>B,WD:()=>v,gc:()=>O,hQ:()=>_,kL:()=>C,mA:()=>M,n5:()=>N,nW:()=>P,p1:()=>G,qN:()=>E,wV:()=>A,xs:()=>w});var i=r(11185),a=r.n(i);let o=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),n=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),s=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),c=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),u=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),g=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),y=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),I=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),q=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=a().models.User||a().model("User",o),w=a().models.Product||a().model("Product",n),v=a().models.Category||a().model("Category",s),T=a().models.Order||a().model("Order",d),P=a().models.HomepageSetting||a().model("HomepageSetting",c),O=a().models.Testimonial||a().model("Testimonial",u),E=a().models.ProductImage||a().model("ProductImage",p),j=a().models.ProductVariant||a().model("ProductVariant",l),D=a().models.Review||a().model("Review",m),C=a().models.Address||a().model("Address",g),x=a().models.OrderItem||a().model("OrderItem",y),R=a().models.Notification||a().model("Notification",S),A=a().models.Coupon||a().model("Coupon",h);a().models.Wishlist||a().model("Wishlist",f);let M=a().models.Newsletter||a().model("Newsletter",I),U=a().models.ProductCategory||a().model("ProductCategory",q),_=a().models.WishlistItem||a().model("WishlistItem",b),k=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),B=a().models.NotificationTemplate||a().model("NotificationTemplate",k),F=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),G=a().models.Enquiry||a().model("Enquiry",F)},89456:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(11185),a=r.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let s=async function(){if(n.conn)return n.conn;n.promise||(n.promise=a().connect(o,{bufferCommands:!1}));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(4926));module.exports=i})();