"use strict";(()=>{var e={};e.id=5436,e.ids=[5436],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},23038:(e,t,r)=>{let i;r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>h,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>S,staticGenerationAsyncStorage:()=>y});var o={};r.r(o),r.d(o,{POST:()=>p});var n=r(49303),s=r(88716),a=r(60670),d=r(87070),c=r(89456),u=r(81515),l=r(84875);let p=(i=async e=>{await (0,c.Z)();let{products:t}=await e.json()||{};if(!t||!Array.isArray(t))throw new l.p8("Invalid products data");let r={success:0,failed:0,errors:[]},i=await u.WD.find({isActive:!0}).lean(),o=new Map(i.map(e=>[String(e.name).toLowerCase(),String(e._id)]));for(let e of(i.forEach(e=>{let t=String(e.name).toLowerCase();"skincare"===t?(o.set("skin care",String(e._id)),o.set("skin",String(e._id))):"hair care"===t?(o.set("haircare",String(e._id)),o.set("hair",String(e._id))):"body care"===t&&(o.set("bodycare",String(e._id)),o.set("body",String(e._id)))}),t))try{let{name:t,slug:i,description:n,shortDescription:s,price:a,comparePrice:d,category:c,categoryNames:l=[],isFeatured:p,isActive:m,variations:g=[]}=e||{};if(!t){r.failed++,r.errors.push("Product missing required name field");continue}let y=g;if("string"==typeof g)try{y=JSON.parse(g)}catch{y=[]}Array.isArray(y)||(y=[]);let S=null!=a?parseFloat(a.toString()):null;if((null===S||Number.isNaN(S))&&y.length>0)S=0;else if((null===S||Number.isNaN(S))&&0===y.length){r.failed++,r.errors.push(`Product "${t}" missing required price field or variations with pricing`);continue}let f=[];c&&f.push(String(c)),Array.isArray(l)&&l.length>0&&f.push(...l.map(e=>String(e)));let h=[];for(let e of f){let i=o.get(e.toLowerCase());if(!i){let t=e.toLowerCase();t.includes("skin")?i=o.get("skincare"):t.includes("hair")?i=o.get("hair care"):t.includes("body")&&(i=o.get("body care"))}i?h.push(i):r.errors.push(`Category "${e}" not found for product "${t}"`)}if(0===h.length){r.failed++,r.errors.push(`Product "${t}" has no valid categories`);continue}let I=i||t.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"");if(await u.xs.findOne({slug:I}).lean()){r.failed++,r.errors.push(`Product with slug "${I}" already exists`);continue}let E=await u.xs.create({name:t,slug:I,description:n||"",shortDescription:s||"",price:S,comparePrice:null!=d?parseFloat(d.toString()):null,categoryId:h[0],isFeatured:!!p,isActive:!1!==m});if(h.length>0&&u.Pp){let e=h.map(e=>({productId:E._id,categoryId:e}));try{await u.Pp.insertMany(e,{ordered:!1})}catch(e){if(!(e?.writeErrors&&e.writeErrors.every(e=>11e3===e.code)))throw e}}if(y.length>0&&u.Th){let e=y.filter(e=>e&&e.name&&e.value).map(e=>({productId:E._id,name:String(e.name),value:String(e.value),price:void 0!==e.price&&null!==e.price?parseFloat(e.price.toString()):null,pricingMode:e.pricingMode&&["REPLACE","INCREMENT","FIXED"].includes(String(e.pricingMode))?String(e.pricingMode):"REPLACE",createdAt:new Date,updatedAt:new Date}));if(e.length>0)try{await u.Th.insertMany(e,{ordered:!1})}catch(e){if(e?.code===11e3||e?.writeErrors)r.errors.push(`Some duplicate variants for product "${t}" were skipped`);else throw e}}r.success++}catch(t){r.failed++,t?.code===11e3?r.errors.push(`Duplicate key error: ${t?.message||"duplicate detected"}`):t instanceof l.AY||t instanceof l.p8?r.errors.push(t.message):r.errors.push(`Failed to create product "${e?.name??"unknown"}": ${String(t)}`),console.error("Error creating product:",t)}return d.NextResponse.json({success:!0,data:r,message:`Import completed: ${r.success} successful, ${r.failed} failed`})},async(...e)=>{try{return await i(...e)}catch(e){return console.error(e),d.NextResponse.json({success:!1,error:"Internal Server Error"},{status:500})}}),m=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/import/route",pathname:"/api/products/import",filename:"route",bundlePath:"app/api/products/import/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\import\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:S}=m,f="/api/products/import/route";function h(){return(0,a.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:y})}},84875:(e,t,r)=>{r.d(t,{AY:()=>u,M_:()=>d,_7:()=>a,dR:()=>c,gz:()=>n,lm:()=>m,p8:()=>s});var i=r(87070),o=r(29489);class n extends Error{constructor(e,t=500,r="INTERNAL_ERROR",i){super(e),this.statusCode=t,this.code=r,this.details=i,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class s extends n{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class a extends n{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class d extends n{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends n{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class u extends n{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class l extends n{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function p(e){let t={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return t[e]||t.INTERNAL_ERROR}function m(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){let t=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,error:e}),e instanceof n){let r={success:!1,error:{code:e.code,message:p(e.code),requestId:t,...!1}};return i.NextResponse.json(r,{status:e.statusCode})}if(e instanceof o.j){let r=new s("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),o={success:!1,error:{code:r.code,message:"Validation failed",requestId:t,...!1}};return i.NextResponse.json(o,{status:r.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let r=function(e){if(11e3===e.code||"MongoServerError"===e.name){let t=Object.keys(e.keyPattern||{})[0]||"field";return new u(`${t} already exists`)}return"ValidationError"===e.name?new s("Validation failed"):"CastError"===e.name?new s("Invalid data format"):"DocumentNotFoundError"===e.name?new c:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new l("Database connection failed"):new l("Database operation failed",{name:e.name,message:e.message})}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return i.NextResponse.json(o,{status:r.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let r=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new u(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new a("Invalid user session");return new s("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new s("Missing required relationship");case"P2000":return new s("Input value is too long");case"P2004":return new s("Data constraint violation");default:return new l("Database operation failed",{code:e.code,message:e.message})}}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return i.NextResponse.json(o,{status:r.statusCode})}}let r={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:t,...!1}};return i.NextResponse.json(r,{status:500})}(e)}}}},81515:(e,t,r)=>{r.d(t,{Cq:()=>v,Dd:()=>D,Order:()=>b,P_:()=>j,Pp:()=>M,Th:()=>P,Vv:()=>k,WD:()=>q,gc:()=>T,hQ:()=>L,kL:()=>C,mA:()=>_,n5:()=>R,nW:()=>O,p1:()=>$,qN:()=>A,wV:()=>x,xs:()=>w});var i=r(11185),o=r.n(i);let n=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),s=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),a=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),c=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),u=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),g=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),y=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),f=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),h=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),I=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),E=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),N=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),R=o().models.User||o().model("User",n),w=o().models.Product||o().model("Product",s),q=o().models.Category||o().model("Category",a),b=o().models.Order||o().model("Order",d),O=o().models.HomepageSetting||o().model("HomepageSetting",c),T=o().models.Testimonial||o().model("Testimonial",u),A=o().models.ProductImage||o().model("ProductImage",l),P=o().models.ProductVariant||o().model("ProductVariant",p),v=o().models.Review||o().model("Review",m),C=o().models.Address||o().model("Address",g),D=o().models.OrderItem||o().model("OrderItem",y),j=o().models.Notification||o().model("Notification",S),x=o().models.Coupon||o().model("Coupon",f);o().models.Wishlist||o().model("Wishlist",h);let _=o().models.Newsletter||o().model("Newsletter",I),M=o().models.ProductCategory||o().model("ProductCategory",E),L=o().models.WishlistItem||o().model("WishlistItem",N),U=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),k=o().models.NotificationTemplate||o().model("NotificationTemplate",U),F=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),$=o().models.Enquiry||o().model("Enquiry",F)},89456:(e,t,r)=>{r.d(t,{Z:()=>a});var i=r(11185),o=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let a=async function(){if(s.conn)return s.conn;s.promise||(s.promise=o().connect(n,{bufferCommands:!1}));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,9489],()=>r(23038));module.exports=i})();