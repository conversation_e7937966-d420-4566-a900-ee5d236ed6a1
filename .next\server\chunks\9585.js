"use strict";exports.id=9585,exports.ids=[9585],exports.modules={54211:(e,t,r)=>{var i;r.d(t,{kg:()=>s}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class a{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:a,context:s,error:o,userId:n,requestId:d}=e,c=i[r],l=`[${t}] ${c}: ${a}`;return n&&(l+=` | User: ${n}`),d&&(l+=` | Request: ${d}`),s&&Object.keys(s).length>0&&(l+=` | Context: ${JSON.stringify(s)}`),o&&(l+=` | Error: ${o.message}`,this.isDevelopment&&o.stack&&(l+=`
Stack: ${o.stack}`)),l}log(e,t,r,i){if(!this.shouldLog(e))return;let a={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},s=this.formatMessage(a);if(this.isDevelopment)switch(e){case 0:console.error(s);break;case 1:console.warn(s);break;case 2:console.info(s);break;case 3:console.debug(s)}else console.log(JSON.stringify(a))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,a){this.info(`API ${e} ${t} - ${r}`,{...a,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,a){this.error(`API ${e} ${t} failed`,r,{...a,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let s=new a},89585:(e,t,r)=>{r.d(t,{$T:()=>n,aZ:()=>a,kg:()=>o,un:()=>s});var i=r(68602);let a={orderPlaced:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.ORDER_PLACED,title:"Order Placed Successfully",message:`Your order #${t.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,data:{orderId:t.orderId,orderNumber:t.orderNumber,amount:t.total,currency:t.currency,itemCount:t.itemCount},sendEmail:!0}),orderConfirmed:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.ORDER_CONFIRMED,title:"Order Confirmed",message:`Your order #${t.orderNumber} has been confirmed and is being prepared for shipment.${t.estimatedDelivery?` Estimated delivery: ${t.estimatedDelivery}`:""}`,data:{orderId:t.orderId,orderNumber:t.orderNumber,estimatedDelivery:t.estimatedDelivery},sendEmail:!0}),orderProcessing:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.ORDER_PROCESSING,title:"Order Being Processed",message:`Your order #${t.orderNumber} is currently being processed. We'll notify you once it's shipped.`,data:{orderId:t.orderId,orderNumber:t.orderNumber},sendEmail:!0}),orderShipped:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.ORDER_SHIPPED,title:"Order Shipped",message:`Great news! Your order #${t.orderNumber} has been shipped.${t.estimatedDelivery?` Estimated delivery: ${t.estimatedDelivery}`:""}`,data:{orderId:t.orderId,orderNumber:t.orderNumber,estimatedDelivery:t.estimatedDelivery},sendEmail:!0}),orderDelivered:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.ORDER_DELIVERED,title:"Order Delivered",message:`Your order #${t.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,data:{orderId:t.orderId,orderNumber:t.orderNumber,deliveredAt:t.deliveredAt},sendEmail:!0}),orderCancelled:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.ORDER_CANCELLED,title:"Order Cancelled",message:`Your order #${t.orderNumber} has been cancelled.${t.reason?` Reason: ${t.reason}`:""}${t.refundAmount?` A refund of ${t.currency} ${t.refundAmount} will be processed within 3-5 business days.`:""}`,data:{orderId:t.orderId,orderNumber:t.orderNumber,reason:t.reason,refundAmount:t.refundAmount,currency:t.currency},sendEmail:!0})},s={itemAdded:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.WISHLIST_ADDED,title:"Item Added to Wishlist",message:`${t.productName} has been added to your wishlist. We'll notify you of any price changes!`,data:{productId:t.productId,productName:t.productName,price:t.price,currency:t.currency},sendEmail:!1}),itemRemoved:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.WISHLIST_REMOVED,title:"Item Removed from Wishlist",message:`${t.productName} has been removed from your wishlist.`,data:{productId:t.productId,productName:t.productName},sendEmail:!1}),priceDropAlert:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.PRICE_DROP_ALERT,title:"Price Drop Alert!",message:`Great news! ${t.productName} is now ${t.discountPercentage}% off! Price dropped from ${t.currency} ${t.oldPrice} to ${t.currency} ${t.newPrice}.`,data:{productId:t.productId,productName:t.productName,oldPrice:t.oldPrice,newPrice:t.newPrice,currency:t.currency,discountPercentage:t.discountPercentage},sendEmail:!0,expiresAt:new Date(Date.now()+6048e5)})},o={async reviewRequest(e,t){let r=t.productNames.join(", ");return await i.B.createNotification({userId:e,type:i.k.REVIEW_REQUEST,title:"How was your experience?",message:`We'd love to hear about your experience with ${r}. Your review helps other customers make informed decisions!`,data:{orderId:t.orderId,orderNumber:t.orderNumber,productIds:t.productIds,productNames:t.productNames},sendEmail:!0,expiresAt:new Date(Date.now()+2592e6)})},reviewSubmitted:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.REVIEW_SUBMITTED,title:"Review Submitted",message:`Thank you for your ${t.rating}-star review of ${t.productName}! Your feedback is valuable to us and other customers.`,data:{productId:t.productId,productName:t.productName,rating:t.rating},sendEmail:!1})},n={adminMessage:async(e,t)=>await i.B.createNotification({userId:e,type:t.type||i.k.ADMIN_MESSAGE,title:t.title,message:t.content,data:{sentByAdmin:!0,sendEmail:!1!==t.sendEmail,sendInApp:!1!==t.sendInApp},sendEmail:!1!==t.sendEmail}),systemAlert:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.SYSTEM,title:t.title,message:t.message,data:{severity:t.severity},sendEmail:"critical"===t.severity||"high"===t.severity}),maintenanceNotice:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.SYSTEM,title:"Scheduled Maintenance",message:`Our system will be under maintenance from ${t.startTime} to ${t.endTime}. ${t.description||"We apologize for any inconvenience."}`,data:{maintenanceStart:t.startTime,maintenanceEnd:t.endTime},sendEmail:!0}),sendMessage:async(e,t)=>await i.B.createNotification({userId:e,type:i.k.ADMIN_MESSAGE,title:t.title,message:t.message,sendEmail:t.sendEmail||!1}),sendBroadcast:async e=>await i.B.sendBroadcast({type:i.k.BROADCAST,title:e.title,message:e.message,sendEmail:e.sendEmail||!1,userIds:e.userIds}),sendPromotion:async e=>await i.B.sendBroadcast({type:i.k.PROMOTIONAL,title:e.title,message:e.message,data:e.data,expiresAt:e.expiresAt,sendEmail:e.sendEmail||!1,userIds:e.userIds})}},68602:(e,t,r)=>{r.d(t,{B:()=>o,k:()=>i});var i,a=r(54211);!function(e){e.ORDER_PLACED="ORDER_PLACED",e.ORDER_CONFIRMED="ORDER_CONFIRMED",e.ORDER_PROCESSING="ORDER_PROCESSING",e.ORDER_SHIPPED="ORDER_SHIPPED",e.ORDER_DELIVERED="ORDER_DELIVERED",e.ORDER_CANCELLED="ORDER_CANCELLED",e.WISHLIST_ADDED="WISHLIST_ADDED",e.WISHLIST_REMOVED="WISHLIST_REMOVED",e.PRICE_DROP_ALERT="PRICE_DROP_ALERT",e.REVIEW_REQUEST="REVIEW_REQUEST",e.REVIEW_SUBMITTED="REVIEW_SUBMITTED",e.ADMIN_MESSAGE="ADMIN_MESSAGE",e.BROADCAST="BROADCAST",e.PROMOTIONAL="PROMOTIONAL",e.SYSTEM="SYSTEM"}(i||(i={}));class s{async createNotification(e){try{return a.kg.info(`Notification service temporarily disabled - would create notification for user ${e.userId}`),null}catch(e){throw a.kg.error("Error creating notification:",e),e}}async sendBroadcast(e){return a.kg.info("Broadcast service temporarily disabled"),[]}async markAsRead(e,t){return a.kg.info("Mark as read service temporarily disabled"),null}async markAllAsRead(e){return a.kg.info("Mark all as read service temporarily disabled"),{count:0}}async getUserNotifications(e,t={}){let{page:r=1,limit:i=20}=t;return a.kg.info("Get user notifications service temporarily disabled"),{notifications:[],total:0,page:r,limit:i,totalPages:0}}async getUnreadCount(e){return a.kg.info("Get unread count service temporarily disabled"),0}async cleanupOldNotifications(e=30){return a.kg.info("Cleanup notifications service temporarily disabled"),{count:0}}async sendEmailNotification(e){a.kg.info("Email notification service temporarily disabled")}shouldSendNotification(e,t){if(!t)return!0;if(!1===t.inAppNotifications)return!1;switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":case"ORDER_PROCESSING":case"ORDER_SHIPPED":case"ORDER_DELIVERED":case"ORDER_CANCELLED":return!1!==t.orderNotifications;case"WISHLIST_ADDED":case"WISHLIST_REMOVED":return!1!==t.wishlistNotifications;case"PRICE_DROP_ALERT":return!1!==t.priceDropAlerts;case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return!1!==t.reviewNotifications;case"ADMIN_MESSAGE":return!1!==t.adminMessages;case"BROADCAST":case"PROMOTIONAL":return!1!==t.broadcastMessages;default:return!0}}generateEmailContent(e){let t=process.env.NEXTAUTH_URL||"http://localhost:3000";return`
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #333; margin: 0 0 15px 0;">${e.title}</h2>
          <p style="color: #666; line-height: 1.6; margin: 0;">${e.message}</p>
        </div>
        
        ${e.data&&Object.keys(e.data).length>0?`
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 10px 0;">Details:</h3>
            ${this.formatNotificationData(e.data)}
          </div>
        `:""}
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${t}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Visit Herbalicious
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>You received this notification because you have an account with Herbalicious.</p>
          <p>You can manage your notification preferences in your account settings.</p>
        </div>
      </div>
    `}formatNotificationData(e){let t='<ul style="color: #666; line-height: 1.6;">';return e.orderNumber&&(t+=`<li><strong>Order Number:</strong> ${e.orderNumber}</li>`),e.productName&&(t+=`<li><strong>Product:</strong> ${e.productName}</li>`),e.amount&&e.currency&&(t+=`<li><strong>Amount:</strong> ${e.currency} ${e.amount}</li>`),t+="</ul>"}}let o=new s}};