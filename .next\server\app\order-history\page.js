(()=>{var e={};e.id=7947,e.ids=[7947],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},82142:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>o}),t(71447),t(37254),t(35866);var r=t(23191),a=t(88716),l=t(37922),n=t.n(l),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let o=["",{children:["order-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71447)),"C:\\Users\\<USER>\\Desktop\\project\\app\\order-history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\order-history\\page.tsx"],x="/order-history/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/order-history/page",pathname:"/order-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},24455:(e,s,t)=>{Promise.resolve().then(t.bind(t,65090)),Promise.resolve().then(t.bind(t,56579))},56579:(e,s,t)=>{"use strict";t.d(s,{default:()=>y});var r=t(10326),a=t(17577),l=t(35047),n=t(48998),i=t(54659),d=t(14228),o=t(91470),c=t(21405),x=t(75290),m=t(48705),h=t(86333),p=t(12714);let g=(0,t(76557).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var u=t(77109);let y=()=>{let e=(0,l.useRouter)(),{data:s,status:t}=(0,u.useSession)(),[y,b]=(0,a.useState)("all"),[j,f]=(0,a.useState)([]),[N,v]=(0,a.useState)(!0),[w,k]=(0,a.useState)(null),[P,C]=(0,a.useState)(1),[D,E]=(0,a.useState)(1),Z={PENDING:{icon:n.Z,color:"text-yellow-600",bg:"bg-yellow-100",label:"Pending"},CONFIRMED:{icon:i.Z,color:"text-blue-600",bg:"bg-blue-100",label:"Confirmed"},PROCESSING:{icon:n.Z,color:"text-green-600",bg:"bg-green-100",label:"Processing"},SHIPPED:{icon:d.Z,color:"text-green-600",bg:"bg-green-100",label:"Shipped"},DELIVERED:{icon:i.Z,color:"text-green-600",bg:"bg-green-100",label:"Delivered"},CANCELLED:{icon:o.Z,color:"text-red-600",bg:"bg-red-100",label:"Cancelled"},REFUNDED:{icon:c.Z,color:"text-purple-600",bg:"bg-purple-100",label:"Refunded"}};(0,a.useEffect)(()=>{"loading"!==t&&"unauthenticated"===t&&e.push("/login")},[t,e]),(0,a.useEffect)(()=>{s?.user&&S()},[P,y,s]);let S=async()=>{if(!s?.user){k("Please login to view your orders"),v(!1);return}try{v(!0),k(null);let e=new URLSearchParams({page:P.toString(),limit:"10"});"all"!==y&&e.append("status",y);let s=await fetch(`/api/orders?${e}`),t=await s.json();if(!s.ok)throw Error(t.error||"Failed to fetch orders");f(t.orders),E(t.pagination.totalPages)}catch(e){k(e instanceof Error?e.message:"Failed to load orders")}finally{v(!1)}},M=[{id:"all",label:"All Orders",count:j.length},{id:"PROCESSING",label:"Processing",count:j.filter(e=>"PROCESSING"===e.status).length},{id:"SHIPPED",label:"Shipped",count:j.filter(e=>"SHIPPED"===e.status).length},{id:"DELIVERED",label:"Delivered",count:j.filter(e=>"DELIVERED"===e.status).length}],O=async s=>{let t=s.items.map(e=>({id:e.product.id,name:e.product.name,price:e.product.price,quantity:e.quantity,slug:e.product.slug}));localStorage.setItem("cart",JSON.stringify(t)),e.push("/checkout")},R=s=>{e.push(`/orders/${s}`)};return"loading"===t||N?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(x.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):"unauthenticated"===t?null:w?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen px-4",children:[r.jsx(m.Z,{className:"w-16 h-16 text-gray-300 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Unable to load orders"}),r.jsx("p",{className:"text-gray-600 mb-6",children:w}),r.jsx("button",{onClick:S,className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Try Again"})]}):(0,r.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,r.jsxs)("div",{className:"lg:hidden",children:[r.jsx("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("button",{onClick:()=>e.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:r.jsx(h.Z,{className:"w-5 h-5"})}),r.jsx("h1",{className:"text-xl font-bold text-gray-800",children:"Order History"})]})}),r.jsx("div",{className:"px-4 py-4 bg-white border-b",children:r.jsx("div",{className:"flex space-x-2 overflow-x-auto",children:M.map(e=>(0,r.jsxs)("button",{onClick:()=>{b(e.id),C(1)},className:`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${y===e.id?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[r.jsx("span",{children:e.label}),r.jsx("span",{className:`px-2 py-0.5 rounded-full text-xs ${y===e.id?"bg-green-500 text-white":"bg-gray-200 text-gray-600"}`,children:e.count})]},e.id))})}),(0,r.jsxs)("div",{className:"px-4 py-6",children:[j.length>0?r.jsx("div",{className:"space-y-4",children:j.map(e=>{let s=Z[e.status].icon,t=Z[e.status];return(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-800",children:["#",e.orderNumber]}),r.jsx("p",{className:"text-sm text-gray-600",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-1 rounded-full ${t.bg}`,children:[r.jsx(s,{className:`w-4 h-4 ${t.color}`}),r.jsx("span",{className:`text-sm font-medium ${t.color}`,children:t.label})]})]}),r.jsx("div",{className:"space-y-2 mb-4",children:e.items.map(e=>(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.product.name]}),(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},e.id))}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["Total: ₹",e.total.toFixed(2)]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>R(e.id),className:"flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors",children:[r.jsx(p.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Details"})]}),"DELIVERED"===e.status&&(0,r.jsxs)("button",{onClick:()=>O(e),className:"flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium hover:bg-green-200 transition-colors",children:[r.jsx(g,{className:"w-4 h-4"}),r.jsx("span",{children:"Reorder"})]})]})]})]},e.id)})}):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(m.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:"No orders found"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"No orders match the selected filter"}),r.jsx("button",{onClick:()=>{b("all"),C(1)},className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"View All Orders"})]}),D>1&&(0,r.jsxs)("div",{className:"flex justify-center mt-6 space-x-2",children:[r.jsx("button",{onClick:()=>C(e=>Math.max(1,e-1)),disabled:1===P,className:`px-4 py-2 rounded-lg ${1===P?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Previous"}),(0,r.jsxs)("span",{className:"px-4 py-2 text-gray-700",children:["Page ",P," of ",D]}),r.jsx("button",{onClick:()=>C(e=>Math.min(D,e+1)),disabled:P===D,className:`px-4 py-2 rounded-lg ${P===D?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Next"})]})]})]}),r.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,r.jsxs)("div",{className:"py-8",children:[r.jsx("div",{className:"flex items-center mb-8",children:(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[r.jsx(h.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Back"})]})}),r.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Order History"}),r.jsx("div",{className:"flex space-x-4 mb-8",children:M.map(e=>(0,r.jsxs)("button",{onClick:()=>{b(e.id),C(1)},className:`flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-colors ${y===e.id?"bg-green-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"}`,children:[r.jsx("span",{children:e.label}),r.jsx("span",{className:`px-3 py-1 rounded-full text-sm ${y===e.id?"bg-green-500 text-white":"bg-gray-100 text-gray-600"}`,children:e.count})]},e.id))}),j.length>0?r.jsx("div",{className:"space-y-6",children:j.map(e=>{let s=Z[e.status].icon,t=Z[e.status];return(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:["Order #",e.orderNumber]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Ordered on"," ",new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,r.jsxs)("div",{className:`flex items-center space-x-3 px-4 py-2 rounded-xl ${t.bg}`,children:[r.jsx(s,{className:`w-5 h-5 ${t.color}`}),r.jsx("span",{className:`font-medium ${t.color}`,children:t.label})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-800 mb-3",children:"Items Ordered"}),r.jsx("div",{className:"space-y-2",children:e.items.map(e=>(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.product.name]}),(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},e.id))})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-800 mb-3",children:"Order Summary"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Subtotal"}),(0,r.jsxs)("span",{children:["₹",e.subtotal.toFixed(2)]})]}),e.couponDiscount>0&&(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Discount"}),(0,r.jsxs)("span",{className:"text-green-600",children:["-₹",e.couponDiscount.toFixed(2)]})]}),"COD"===e.paymentMethod&&(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"COD Charges"}),r.jsx("span",{children:"₹50.00"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Shipping"}),r.jsx("span",{children:"Free"})]}),(0,r.jsxs)("div",{className:"flex justify-between font-semibold text-gray-900 pt-2 border-t border-gray-200",children:[r.jsx("span",{children:"Total"}),(0,r.jsxs)("span",{children:["₹",e.total.toFixed(2)]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["Payment:"," ",r.jsx("span",{className:"font-medium",children:"COD"===e.paymentMethod?"Cash on Delivery":"Online"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>R(e.id),className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors",children:[r.jsx(p.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"View Details"})]}),"DELIVERED"===e.status&&(0,r.jsxs)("button",{onClick:()=>O(e),className:"flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-xl font-medium hover:bg-green-200 transition-colors",children:[r.jsx(g,{className:"w-4 h-4"}),r.jsx("span",{children:"Reorder"})]})]})]})]},e.id)})}):(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx(m.Z,{className:"w-24 h-24 text-gray-300 mx-auto mb-6"}),r.jsx("h3",{className:"text-2xl font-semibold text-gray-800 mb-4",children:"No orders found"}),r.jsx("p",{className:"text-gray-600 mb-8",children:"No orders match the selected filter"}),r.jsx("button",{onClick:()=>{b("all"),C(1)},className:"bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors",children:"View All Orders"})]}),D>1&&(0,r.jsxs)("div",{className:"flex justify-center mt-8 space-x-2",children:[r.jsx("button",{onClick:()=>C(e=>Math.max(1,e-1)),disabled:1===P,className:`px-6 py-3 rounded-xl font-medium ${1===P?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Previous"}),(0,r.jsxs)("span",{className:"px-6 py-3 text-gray-700 font-medium",children:["Page ",P," of ",D]}),r.jsx("button",{onClick:()=>C(e=>Math.min(D,e+1)),disabled:P===D,className:`px-6 py-3 rounded-xl font-medium ${P===D?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Next"})]})]})})]})}},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},75290:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},48705:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},40304:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)},71447:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\OrderHistory.tsx#default`);function n(){return r.jsx(a.Z,{children:r.jsx(l,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,3757,434,9694,9536,5090],()=>t(82142));module.exports=r})();