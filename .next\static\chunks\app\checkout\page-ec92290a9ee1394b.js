(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[285],{88546:function(e,s,t){Promise.resolve().then(t.bind(t,28250)),Promise.resolve().then(t.bind(t,27231))},27231:function(e,s,t){"use strict";t.d(s,{default:function(){return k}});var a,r,n=t(57437),l=t(2265),i=t(99376),d=t(80605),c=t(40340),o=t(88226),m=t(88906),x=t(32660),h=t(30401),u=t(92369),p=t(39763);let g=(0,p.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var y=t(2207);let b=(0,p.Z)("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]]),j=(0,p.Z)("Banknote",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M6 12h.01M18 12h.01",key:"113zkx"}]]);var f=t(76858),N=t(53827);(a=r||(r={}))[a.ERROR=0]="ERROR",a[a.WARN=1]="WARN",a[a.INFO=2]="INFO",a[a.DEBUG=3]="DEBUG";class v{shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:s,level:t,message:a,context:n,error:l,userId:i,requestId:d}=e,c=r[t],o="[".concat(s,"] ").concat(c,": ").concat(a);return i&&(o+=" | User: ".concat(i)),d&&(o+=" | Request: ".concat(d)),n&&Object.keys(n).length>0&&(o+=" | Context: ".concat(JSON.stringify(n))),l&&(o+=" | Error: ".concat(l.message),this.isDevelopment&&l.stack&&(o+="\nStack: ".concat(l.stack))),o}log(e,s,t,a){if(!this.shouldLog(e))return;let r={timestamp:new Date().toISOString(),level:e,message:s,context:t,error:a},n=this.formatMessage(r);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(r))}error(e,s,t){this.log(0,e,t,s)}warn(e,s){this.log(1,e,s)}info(e,s){this.log(2,e,s)}debug(e,s){this.log(3,e,s)}apiRequest(e,s,t,a){this.info("API ".concat(e," ").concat(s),{...a,userId:t,type:"api_request"})}apiResponse(e,s,t,a,r){this.info("API ".concat(e," ").concat(s," - ").concat(t),{...r,statusCode:t,duration:a,type:"api_response"})}apiError(e,s,t,a,r){this.error("API ".concat(e," ").concat(s," failed"),t,{...r,userId:a,type:"api_error"})}authSuccess(e,s,t){this.info("Authentication successful",{...t,userId:e,method:s,type:"auth_success"})}authFailure(e,s,t,a){this.warn("Authentication failed",{...a,email:e,method:s,reason:t,type:"auth_failure"})}dbQuery(e,s,t,a){this.debug("DB ".concat(e," on ").concat(s),{...a,operation:e,table:s,duration:t,type:"db_query"})}dbError(e,s,t,a){this.error("DB ".concat(e," on ").concat(s," failed"),t,{...a,operation:e,table:s,type:"db_error"})}securityEvent(e,s,t){this.log("high"===s?0:"medium"===s?1:2,"Security event: ".concat(e),{...t,severity:s,type:"security_event"})}rateLimitHit(e,s,t,a){this.warn("Rate limit exceeded",{...a,identifier:e,limit:s,window:t,type:"rate_limit"})}emailSent(e,s,t,a){this.info("Email sent",{...a,to:e,subject:s,template:t,type:"email_sent"})}emailError(e,s,t,a){this.error("Email failed to send",t,{...a,to:e,subject:s,type:"email_error"})}performance(e,s,t){let a=s>5e3?1:3;this.log(a,"Performance: ".concat(e," took ").concat(s,"ms"),{...t,operation:e,duration:s,type:"performance"})}constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}}let w=new v;function C(e){let{cartItems:s,shippingAddress:t,totalAmount:a,appliedCoupons:r=[],onSuccess:i,onError:c,className:o="",disabled:m=!1,flashSaleDiscount:x=0}=e,{data:h}=(0,d.useSession)(),[u,p]=(0,l.useState)(!1),g=()=>new Promise(e=>{let s=document.createElement("script");s.src="https://checkout.razorpay.com/v1/checkout.js",s.onload=()=>e(!0),s.onerror=()=>e(!1),document.body.appendChild(s)}),y=async()=>{if(!(null==h?void 0:h.user)){null==c||c("Please login to continue");return}if(0===s.length){null==c||c("Cart is empty");return}p(!0);try{if(!await g())throw Error("Failed to load payment gateway");let e=await fetch("/api/payments/create-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cartItems:s.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),shippingAddress:t,totalAmount:a,appliedCoupons:r,flashSaleDiscount:x})});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to create order")}let n=await e.json(),l={key:n.razorpayKeyId,amount:n.order.amount,currency:n.order.currency,name:"Herbalicious",description:"Order #".concat(n.order.orderNumber||"New Order"),order_id:n.order.razorpayOrderId,handler:async e=>{try{let s=await fetch("/api/payments/verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({razorpay_order_id:e.razorpay_order_id,razorpay_payment_id:e.razorpay_payment_id,razorpay_signature:e.razorpay_signature,order_id:n.order.id})});if(!s.ok){let e=await s.json();throw Error(e.message||"Payment verification failed")}let t=await s.json();w.info("Payment completed successfully",{orderId:t.order.id,orderNumber:t.order.orderNumber,paymentId:e.razorpay_payment_id}),null==i||i(t.order.id)}catch(e){console.error("Payment verification error:",e),null==c||c(e.message||"Payment verification failed")}},prefill:{name:"".concat(t.firstName," ").concat(t.lastName),email:h.user.email||"",contact:t.phone},notes:{address:"".concat(t.address1,", ").concat(t.city,", ").concat(t.state)},theme:{color:"#2d5a27"},modal:{ondismiss:()=>{p(!1),null==c||c("Payment cancelled")}}};new window.Razorpay(l).open()}catch(e){console.error("Payment error:",e),p(!1),null==c||c(e.message||"Payment failed")}};return(0,n.jsx)("button",{onClick:y,disabled:m||u||!(null==h?void 0:h.user),className:"\n        w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 \n        text-white font-semibold py-3 px-6 rounded-lg transition-colors\n        flex items-center justify-center space-x-2\n        ".concat(o,"\n      "),children:u?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,n.jsx)("span",{children:"Processing..."})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("span",{children:["Pay ₹",a.toFixed(2)]}),(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})]})})}var k=()=>{var e;let s=(0,i.useRouter)(),{data:t,status:a}=(0,d.useSession)(),{state:r,dispatch:p}=(0,N.j)(),[v,w]=(0,l.useState)(1),[k,S]=(0,l.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",state:"",zipCode:"",country:"India",paymentMethod:"online",billingDifferent:!1,billingAddress:"",billingCity:"",billingState:"",billingZipCode:""}),[M,D]=(0,l.useState)({}),[P,F]=(0,l.useState)(!1),[O,z]=(0,l.useState)([]),[Z,A]=(0,l.useState)(""),[E,I]=(0,l.useState)(!1),[_,q]=(0,l.useState)(!1),[L,R]=(0,l.useState)({email:"",password:""}),[B,T]=(0,l.useState)(""),[U,H]=(0,l.useState)(!1),[Y,W]=(0,l.useState)(!1);(0,l.useEffect)(()=>{(async()=>{var e,s,r;if((null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.id)&&"authenticated"===a){W(!0);try{let e=await fetch("/api/users/".concat(t.user.id));if(e.ok){let t=(await e.json()).data;z(t.addresses||[]);let a=(null===(s=t.addresses)||void 0===s?void 0:s.find(e=>e.isDefault))||(null===(r=t.addresses)||void 0===r?void 0:r[0]);a?(A(a.id),S(e=>({...e,firstName:a.firstName,lastName:a.lastName,email:t.email,phone:a.phone||t.phone||"",address:a.address1,city:a.city,state:a.state,zipCode:a.postalCode,country:a.country}))):S(e=>{var s,a;return{...e,email:t.email,phone:t.phone||"",firstName:(null===(s=t.name)||void 0===s?void 0:s.split(" ")[0])||"",lastName:(null===(a=t.name)||void 0===a?void 0:a.split(" ").slice(1).join(" "))||""}})}}catch(e){console.error("Error fetching user data:",e)}finally{W(!1)}}})()},[null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.id,a]);let G=[{id:1,name:"Shipping",icon:c.Z},{id:2,name:"Payment",icon:o.Z},{id:3,name:"Review",icon:m.Z}],J=e=>{let{name:s,value:t,type:a}=e.target;S(r=>({...r,[s]:"checkbox"===a?e.target.checked:t})),M[s]&&D(e=>({...e,[s]:""}))},Q=e=>{let s=O.find(s=>s.id===e);s&&(A(e),S(e=>({...e,firstName:s.firstName,lastName:s.lastName,phone:s.phone||e.phone,address:s.address1,city:s.city,state:s.state,zipCode:s.postalCode,country:s.country})),I(!1))},V=async e=>{e.preventDefault(),H(!0),T("");try{let e=await (0,d.signIn)("credentials",{email:L.email,password:L.password,redirect:!1});(null==e?void 0:e.error)?T("Invalid email or password"):(q(!1),R({email:"",password:""}))}catch(e){T("An error occurred during login")}finally{H(!1)}},K=e=>{let{name:s,value:t}=e.target;R(e=>({...e,[s]:t})),B&&T("")},X=e=>{let s={};return 1!==e||(k.firstName||(s.firstName="First name is required"),k.lastName||(s.lastName="Last name is required"),k.email||(s.email="Email is required"),k.phone||(s.phone="Phone is required"),k.address||(s.address="Address is required"),k.city||(s.city="City is required"),k.state||(s.state="State is required"),k.zipCode||(s.zipCode="ZIP code is required")),2!==e||k.paymentMethod||(s.paymentMethod="Please select a payment method"),D(s),0===Object.keys(s).length},$=()=>{X(v)&&w(e=>Math.min(e+1,3))},ee=()=>{w(e=>Math.max(e-1,1))},es=e=>{F(!1),p({type:"CLEAR_CART"}),s.push("/order-confirmation?orderId=".concat(e))},et=e=>{F(!1),D({payment:e})},ea=async()=>{if(X(2)){F(!0);try{let e=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cartItems:r.items.map(e=>({productId:e.product.id,quantity:e.quantity,price:e.product.price})),shippingAddress:{firstName:k.firstName,lastName:k.lastName,address1:k.address,address2:"",city:k.city,state:k.state,postalCode:k.zipCode,country:k.country,phone:k.phone},totalAmount:en,paymentMethod:"COD",appliedCoupons:[],flashSaleDiscount:r.flashSaleDiscount})});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to create order")}let s=await e.json();es(s.order.id)}catch(e){et(e.message||"Failed to create order")}}};r.subtotal;let er="cod"===k.paymentMethod?50:0,en=r.finalTotal+er;return 0===r.items.length?(0,n.jsx)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:(0,n.jsxs)("div",{className:"lg:col-span-12 text-center py-16",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Your cart is empty"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Add some products before checkout"}),(0,n.jsx)("button",{onClick:()=>s.push("/shop"),className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Continue Shopping"})]})}):(0,n.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,n.jsxs)("div",{className:"lg:hidden",children:[(0,n.jsxs)("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("button",{onClick:()=>s.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,n.jsx)(x.Z,{className:"w-5 h-5"})}),(0,n.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"Checkout"}),(0,n.jsx)("div",{})]}),(0,n.jsx)("div",{className:"flex items-center justify-between",children:G.map((e,s)=>(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(v>=e.id?"bg-green-600 text-white":"bg-gray-200 text-gray-500"),children:v>e.id?(0,n.jsx)(h.Z,{className:"w-4 h-4"}):(0,n.jsx)(e.icon,{className:"w-4 h-4"})}),(0,n.jsx)("span",{className:"ml-2 text-sm font-medium ".concat(v>=e.id?"text-green-600":"text-gray-500"),children:e.name}),s<G.length-1&&(0,n.jsx)("div",{className:"w-8 h-0.5 mx-2 ".concat(v>e.id?"bg-green-600":"bg-gray-200")})]},e.id))})]}),(0,n.jsxs)("div",{className:"px-4 py-6",children:["authenticated"!==a&&!_&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-blue-50 rounded-xl border border-blue-200",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)(u.Z,{className:"w-5 h-5 text-blue-600"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium text-blue-900",children:"Have an account?"}),(0,n.jsx)("p",{className:"text-sm text-blue-700",children:"Login to use saved addresses and faster checkout"})]})]}),(0,n.jsxs)("button",{onClick:()=>q(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors",children:[(0,n.jsx)(g,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:"Login"})]})]})}),_&&"authenticated"!==a&&(0,n.jsxs)("div",{className:"mb-6 p-4 bg-white rounded-xl border border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-800",children:"Login to Your Account"}),(0,n.jsx)("button",{onClick:()=>q(!1),className:"text-gray-500 hover:text-gray-700",children:"\xd7"})]}),(0,n.jsxs)("form",{onSubmit:V,className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,n.jsx)("input",{type:"email",name:"email",value:L.email,onChange:K,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,n.jsx)("input",{type:"password",name:"password",value:L.password,onChange:K,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),B&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:B}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsx)("button",{type:"submit",disabled:U,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50",children:U?"Logging in...":"Login"}),(0,n.jsx)("button",{type:"button",onClick:()=>q(!1),className:"px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"})]})]})]}),1===v&&(0,n.jsxs)("div",{className:"space-y-4",children:["authenticated"===a&&O.length>0&&!E&&(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-800",children:"Select Shipping Address"}),(0,n.jsxs)("button",{onClick:()=>I(!0),className:"flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium",children:[(0,n.jsx)(y.Z,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:"Edit Details"})]})]}),(0,n.jsx)("div",{className:"space-y-3",children:O.map(e=>(0,n.jsxs)("label",{className:"flex items-start p-4 border-2 rounded-xl cursor-pointer transition-all ".concat(Z===e.id?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"),children:[(0,n.jsx)("input",{type:"radio",name:"selectedAddress",value:e.id,checked:Z===e.id,onChange:()=>Q(e.id),className:"sr-only"}),(0,n.jsx)("div",{className:"w-5 h-5 rounded-full border-2 mr-3 mt-0.5 flex items-center justify-center ".concat(Z===e.id?"border-green-600":"border-gray-300"),children:Z===e.id&&(0,n.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-600"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,n.jsxs)("span",{className:"font-medium text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&(0,n.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:[e.address1,e.address2&&", ".concat(e.address2)]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:[e.city,", ",e.state," ",e.postalCode]}),e.phone&&(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.phone})]})]},e.id))})]}),("authenticated"!==a||0===O.length||E)&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"authenticated"===a&&O.length>0?"Edit Shipping Details":"Shipping Information"}),"authenticated"===a&&O.length>0&&E&&(0,n.jsx)("button",{onClick:()=>I(!1),className:"text-gray-600 hover:text-gray-800 font-medium",children:"Use Saved Address"})]}),Y&&(0,n.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,n.jsx)("div",{className:"w-6 h-6 border-2 border-green-600 border-t-transparent rounded-full animate-spin"}),(0,n.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading your details..."})]})]}),("authenticated"!==a||0===O.length||E)&&!Y&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),(0,n.jsx)("input",{type:"text",name:"firstName",value:k.firstName,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.firstName?"border-red-500":"border-gray-300")}),M.firstName&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.firstName})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),(0,n.jsx)("input",{type:"text",name:"lastName",value:k.lastName,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.lastName?"border-red-500":"border-gray-300")}),M.lastName&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.lastName})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,n.jsx)("input",{type:"email",name:"email",value:k.email,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.email?"border-red-500":"border-gray-300")}),M.email&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.email})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),(0,n.jsx)("input",{type:"tel",name:"phone",value:k.phone,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.phone?"border-red-500":"border-gray-300")}),M.phone&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.phone})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address"}),(0,n.jsx)("input",{type:"text",name:"address",value:k.address,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.address?"border-red-500":"border-gray-300")}),M.address&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.address})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City"}),(0,n.jsx)("input",{type:"text",name:"city",value:k.city,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.city?"border-red-500":"border-gray-300")}),M.city&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.city})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State"}),(0,n.jsx)("input",{type:"text",name:"state",value:k.state,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.state?"border-red-500":"border-gray-300")}),M.state&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.state})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ZIP Code"}),(0,n.jsx)("input",{type:"text",name:"zipCode",value:k.zipCode,onChange:J,className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.zipCode?"border-red-500":"border-gray-300")}),M.zipCode&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:M.zipCode})]})]})]}),2===v&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Payment Method"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{className:"flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all ".concat("online"===k.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"),children:[(0,n.jsx)("input",{type:"radio",name:"paymentMethod",value:"online",checked:"online"===k.paymentMethod,onChange:J,className:"sr-only"}),(0,n.jsx)("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ".concat("online"===k.paymentMethod?"border-green-600":"border-gray-300"),children:"online"===k.paymentMethod&&(0,n.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-600"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(b,{className:"w-5 h-5 text-gray-600"}),(0,n.jsx)("span",{className:"font-medium text-gray-800",children:"Online Payment"})]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Pay securely using Credit/Debit Card, UPI, Net Banking, or Wallets"})]})]}),(0,n.jsxs)("label",{className:"flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all ".concat("cod"===k.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"),children:[(0,n.jsx)("input",{type:"radio",name:"paymentMethod",value:"cod",checked:"cod"===k.paymentMethod,onChange:J,className:"sr-only"}),(0,n.jsx)("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ".concat("cod"===k.paymentMethod?"border-green-600":"border-gray-300"),children:"cod"===k.paymentMethod&&(0,n.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-600"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(j,{className:"w-5 h-5 text-gray-600"}),(0,n.jsx)("span",{className:"font-medium text-gray-800",children:"Cash on Delivery"})]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Pay when your order is delivered to your doorstep"})]})]})]}),M.paymentMethod&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-2",children:M.paymentMethod}),"online"===k.paymentMethod&&(0,n.jsx)("div",{className:"mt-4 p-4 bg-blue-50 rounded-xl",children:(0,n.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,n.jsx)("strong",{children:"Note:"})," You will be redirected to a secure payment gateway after reviewing your order."]})}),"cod"===k.paymentMethod&&(0,n.jsx)("div",{className:"mt-4 p-4 bg-yellow-50 rounded-xl",children:(0,n.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,n.jsx)("strong",{children:"Note:"})," Please keep exact change ready for delivery. Additional charges may apply for COD orders."]})})]}),3===v&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-800",children:"Review Your Order"}),(0,n.jsx)("div",{className:"space-y-4",children:r.items.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-lg"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h3",{className:"font-medium text-gray-800",children:e.product.name}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Qty: ",e.quantity]})]}),(0,n.jsxs)("span",{className:"font-medium text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]},e.product.id))}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,n.jsx)("span",{children:"Subtotal"}),(0,n.jsxs)("span",{children:["₹",r.subtotal.toFixed(2)]})]}),r.flashSaleDiscount>0&&(0,n.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,n.jsxs)("span",{children:["Flash Sale Discount (",r.flashSalePercentage,"% OFF)"]}),(0,n.jsxs)("span",{children:["-₹",r.flashSaleDiscount.toFixed(2)]})]}),r.coupons.totalDiscount>0&&(0,n.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,n.jsx)("span",{children:"Coupon Discount"}),(0,n.jsxs)("span",{children:["-₹",r.coupons.totalDiscount.toFixed(2)]})]}),(0,n.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,n.jsx)("span",{children:"Shipping"}),(0,n.jsx)("span",{children:"Free"})]}),"cod"===k.paymentMethod&&(0,n.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,n.jsx)("span",{children:"COD Charges"}),(0,n.jsxs)("span",{children:["₹",er.toFixed(2)]})]}),(0,n.jsx)("div",{className:"border-t border-gray-300 pt-2",children:(0,n.jsxs)("div",{className:"flex justify-between font-bold text-gray-900",children:[(0,n.jsx)("span",{children:"Total"}),(0,n.jsxs)("span",{children:["₹",en.toFixed(2)]})]})})]})]}),(0,n.jsxs)("div",{className:"flex justify-between mt-8 pt-6 border-t",children:[v>1&&(0,n.jsxs)("button",{onClick:ee,className:"flex items-center space-x-2 px-6 py-3 border border-gray-300 rounded-full font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,n.jsx)(x.Z,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:"Back"})]}),(0,n.jsx)("div",{className:"ml-auto",children:v<3?(0,n.jsxs)("button",{onClick:$,className:"flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:[(0,n.jsx)("span",{children:"Continue"}),(0,n.jsx)(f.Z,{className:"w-4 h-4"})]}):(0,n.jsxs)("div",{className:"space-y-3",children:["online"===k.paymentMethod?(0,n.jsx)(C,{cartItems:r.items.map(e=>({productId:e.product.id,quantity:e.quantity,price:e.product.price,product:{name:e.product.name,price:e.product.price}})),shippingAddress:{firstName:k.firstName,lastName:k.lastName,address1:k.address,address2:"",city:k.city,state:k.state,postalCode:k.zipCode,country:k.country,phone:k.phone},totalAmount:r.finalTotal,appliedCoupons:[],flashSaleDiscount:r.flashSaleDiscount,onSuccess:es,onError:et,disabled:P,className:"rounded-full"}):(0,n.jsx)("button",{onClick:ea,disabled:P,className:"flex items-center justify-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors disabled:opacity-50 w-full",children:P?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,n.jsx)("span",{children:"Processing..."})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{children:"Place COD Order"}),(0,n.jsx)(f.Z,{className:"w-4 h-4"})]})}),M.payment&&(0,n.jsx)("p",{className:"text-red-500 text-sm text-center",children:M.payment})]})})]})]})]}),(0,n.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,n.jsxs)("div",{className:"py-8",children:[(0,n.jsx)("div",{className:"flex items-center mb-8",children:(0,n.jsxs)("button",{onClick:()=>s.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,n.jsx)(x.Z,{className:"w-5 h-5"}),(0,n.jsx)("span",{children:"Back to Cart"})]})}),(0,n.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Checkout"}),"authenticated"!==a&&!_&&(0,n.jsx)("div",{className:"mb-8 p-6 bg-blue-50 rounded-xl border border-blue-200",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(u.Z,{className:"w-6 h-6 text-blue-600"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-blue-900",children:"Have an account?"}),(0,n.jsx)("p",{className:"text-blue-700",children:"Login to use saved addresses and faster checkout"})]})]}),(0,n.jsxs)("button",{onClick:()=>q(!0),className:"flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors",children:[(0,n.jsx)(g,{className:"w-5 h-5"}),(0,n.jsx)("span",{children:"Login"})]})]})}),_&&"authenticated"!==a&&(0,n.jsxs)("div",{className:"mb-8 p-6 bg-white rounded-xl border border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:"Login to Your Account"}),(0,n.jsx)("button",{onClick:()=>q(!1),className:"text-gray-500 hover:text-gray-700 text-xl",children:"\xd7"})]}),(0,n.jsxs)("form",{onSubmit:V,className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,n.jsx)("input",{type:"email",name:"email",value:L.email,onChange:K,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,n.jsx)("input",{type:"password",name:"password",value:L.password,onChange:K,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,n.jsxs)("div",{className:"col-span-2",children:[B&&(0,n.jsx)("p",{className:"text-red-500 text-sm mb-4",children:B}),(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)("button",{type:"submit",disabled:U,className:"bg-blue-600 text-white px-8 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors disabled:opacity-50",children:U?"Logging in...":"Login"}),(0,n.jsx)("button",{type:"button",onClick:()=>q(!1),className:"px-8 py-3 border border-gray-300 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"})]})]})]})]}),(0,n.jsx)("div",{className:"flex items-center justify-center mb-12",children:G.map((e,s)=>(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat(v>=e.id?"bg-green-600 text-white":"bg-gray-200 text-gray-500"),children:v>e.id?(0,n.jsx)(h.Z,{className:"w-6 h-6"}):(0,n.jsx)(e.icon,{className:"w-6 h-6"})}),(0,n.jsx)("span",{className:"ml-3 text-lg font-medium ".concat(v>=e.id?"text-green-600":"text-gray-500"),children:e.name}),s<G.length-1&&(0,n.jsx)("div",{className:"w-24 h-0.5 mx-8 ".concat(v>e.id?"bg-green-600":"bg-gray-200")})]},e.id))}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-12",children:[(0,n.jsxs)("div",{className:"col-span-2",children:[1===v&&(0,n.jsxs)("div",{className:"space-y-6",children:["authenticated"===a&&O.length>0&&!E&&(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold text-gray-800",children:"Select Shipping Address"}),(0,n.jsxs)("button",{onClick:()=>I(!0),className:"flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium",children:[(0,n.jsx)(y.Z,{className:"w-5 h-5"}),(0,n.jsx)("span",{children:"Edit Details"})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:O.map(e=>(0,n.jsxs)("label",{className:"flex items-start p-6 border-2 rounded-xl cursor-pointer transition-all ".concat(Z===e.id?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"),children:[(0,n.jsx)("input",{type:"radio",name:"selectedAddress",value:e.id,checked:Z===e.id,onChange:()=>Q(e.id),className:"sr-only"}),(0,n.jsx)("div",{className:"w-6 h-6 rounded-full border-2 mr-4 mt-1 flex items-center justify-center ".concat(Z===e.id?"border-green-600":"border-gray-300"),children:Z===e.id&&(0,n.jsx)("div",{className:"w-3.5 h-3.5 rounded-full bg-green-600"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,n.jsxs)("span",{className:"text-lg font-medium text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&(0,n.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-full",children:"Default"})]}),(0,n.jsxs)("p",{className:"text-gray-600 mb-1",children:[e.address1,e.address2&&", ".concat(e.address2)]}),(0,n.jsxs)("p",{className:"text-gray-600 mb-1",children:[e.city,", ",e.state," ",e.postalCode]}),e.phone&&(0,n.jsx)("p",{className:"text-gray-600",children:e.phone})]})]},e.id))})]}),("authenticated"!==a||0===O.length||E)&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"authenticated"===a&&O.length>0?"Edit Shipping Details":"Shipping Information"}),"authenticated"===a&&O.length>0&&E&&(0,n.jsx)("button",{onClick:()=>I(!1),className:"text-gray-600 hover:text-gray-800 font-medium",children:"Use Saved Address"})]}),Y&&(0,n.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,n.jsx)("div",{className:"w-8 h-8 border-2 border-green-600 border-t-transparent rounded-full animate-spin"}),(0,n.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading your details..."})]}),!Y&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),(0,n.jsx)("input",{type:"text",name:"firstName",value:k.firstName,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.firstName?"border-red-500":"border-gray-300")}),M.firstName&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.firstName})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),(0,n.jsx)("input",{type:"text",name:"lastName",value:k.lastName,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.lastName?"border-red-500":"border-gray-300")}),M.lastName&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.lastName})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,n.jsx)("input",{type:"email",name:"email",value:k.email,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.email?"border-red-500":"border-gray-300")}),M.email&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.email})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone"}),(0,n.jsx)("input",{type:"tel",name:"phone",value:k.phone,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.phone?"border-red-500":"border-gray-300")}),M.phone&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.phone})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Address"}),(0,n.jsx)("input",{type:"text",name:"address",value:k.address,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.address?"border-red-500":"border-gray-300")}),M.address&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.address})]}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),(0,n.jsx)("input",{type:"text",name:"city",value:k.city,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.city?"border-red-500":"border-gray-300")}),M.city&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.city})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"State"}),(0,n.jsx)("input",{type:"text",name:"state",value:k.state,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.state?"border-red-500":"border-gray-300")}),M.state&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.state})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ZIP Code"}),(0,n.jsx)("input",{type:"text",name:"zipCode",value:k.zipCode,onChange:J,className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(M.zipCode?"border-red-500":"border-gray-300")}),M.zipCode&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:M.zipCode})]})]})]})]})]}),2===v&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Payment Method"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("label",{className:"flex items-center p-6 border-2 rounded-xl cursor-pointer transition-all ".concat("online"===k.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"),children:[(0,n.jsx)("input",{type:"radio",name:"paymentMethod",value:"online",checked:"online"===k.paymentMethod,onChange:J,className:"sr-only"}),(0,n.jsx)("div",{className:"w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center ".concat("online"===k.paymentMethod?"border-green-600":"border-gray-300"),children:"online"===k.paymentMethod&&(0,n.jsx)("div",{className:"w-3.5 h-3.5 rounded-full bg-green-600"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)(b,{className:"w-6 h-6 text-gray-600"}),(0,n.jsx)("span",{className:"text-lg font-medium text-gray-800",children:"Online Payment"})]}),(0,n.jsx)("p",{className:"text-gray-600 mt-2",children:"Pay securely using Credit Card, Debit Card, UPI, Net Banking, or Digital Wallets"}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-2 mt-3",children:[(0,n.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"Credit Card"}),(0,n.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"Debit Card"}),(0,n.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"UPI"}),(0,n.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"Net Banking"})]})]})]}),(0,n.jsxs)("label",{className:"flex items-center p-6 border-2 rounded-xl cursor-pointer transition-all ".concat("cod"===k.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"),children:[(0,n.jsx)("input",{type:"radio",name:"paymentMethod",value:"cod",checked:"cod"===k.paymentMethod,onChange:J,className:"sr-only"}),(0,n.jsx)("div",{className:"w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center ".concat("cod"===k.paymentMethod?"border-green-600":"border-gray-300"),children:"cod"===k.paymentMethod&&(0,n.jsx)("div",{className:"w-3.5 h-3.5 rounded-full bg-green-600"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)(j,{className:"w-6 h-6 text-gray-600"}),(0,n.jsx)("span",{className:"text-lg font-medium text-gray-800",children:"Cash on Delivery"})]}),(0,n.jsx)("p",{className:"text-gray-600 mt-2",children:"Pay when your order is delivered to your doorstep"}),(0,n.jsx)("p",{className:"text-sm text-yellow-700 mt-2 bg-yellow-50 px-3 py-1 rounded-lg inline-block",children:"₹50 additional COD charges apply"})]})]})]}),M.paymentMethod&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-2",children:M.paymentMethod}),"online"===k.paymentMethod&&(0,n.jsxs)("div",{className:"mt-6 p-6 bg-blue-50 rounded-xl",children:[(0,n.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Secure Payment Gateway"}),(0,n.jsx)("p",{className:"text-blue-800",children:"You will be redirected to our secure payment partner after reviewing your order. All major payment methods are accepted."})]}),"cod"===k.paymentMethod&&(0,n.jsxs)("div",{className:"mt-6 p-6 bg-yellow-50 rounded-xl",children:[(0,n.jsx)("h3",{className:"font-medium text-yellow-900 mb-2",children:"Cash on Delivery Guidelines"}),(0,n.jsxs)("ul",{className:"text-yellow-800 space-y-1 list-disc list-inside",children:[(0,n.jsx)("li",{children:"Please keep exact change ready for smooth delivery"}),(0,n.jsx)("li",{children:"Additional ₹50 will be charged for COD orders"}),(0,n.jsx)("li",{children:"Order cannot be cancelled once out for delivery"})]})]})]}),3===v&&(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold text-gray-800",children:"Review Your Order"}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"Shipping Address"}),(0,n.jsxs)("p",{className:"text-gray-600",children:[k.firstName," ",k.lastName,(0,n.jsx)("br",{}),k.address,(0,n.jsx)("br",{}),k.city,", ",k.state," ",k.zipCode]})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"Payment Method"}),(0,n.jsx)("p",{className:"text-gray-600",children:"online"===k.paymentMethod?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(b,{className:"w-5 h-5 inline mr-2"}),"Online Payment - Secure Gateway"]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j,{className:"w-5 h-5 inline mr-2"}),"Cash on Delivery (COD)"]})})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-800",children:"Order Items"}),r.items.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-6 p-4 bg-gray-50 rounded-xl",children:[(0,n.jsx)("div",{className:"w-20 h-20 bg-gray-200 rounded-xl"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-800",children:e.product.name}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Quantity: ",e.quantity]})]}),(0,n.jsxs)("span",{className:"font-semibold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]},e.product.id))]})]}),(0,n.jsxs)("div",{className:"flex justify-between mt-12 pt-8 border-t",children:[v>1&&(0,n.jsxs)("button",{onClick:ee,className:"flex items-center space-x-2 px-8 py-4 border border-gray-300 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,n.jsx)(x.Z,{className:"w-5 h-5"}),(0,n.jsx)("span",{children:"Back"})]}),(0,n.jsx)("div",{className:"ml-auto",children:v<3?(0,n.jsxs)("button",{onClick:$,className:"flex items-center space-x-2 bg-green-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors text-lg",children:[(0,n.jsx)("span",{children:"Continue"}),(0,n.jsx)(f.Z,{className:"w-5 h-5"})]}):(0,n.jsxs)("div",{className:"space-y-3",children:["online"===k.paymentMethod?(0,n.jsx)(C,{cartItems:r.items.map(e=>({productId:e.product.id,quantity:e.quantity,price:e.product.price,product:{name:e.product.name,price:e.product.price}})),shippingAddress:{firstName:k.firstName,lastName:k.lastName,address1:k.address,address2:"",city:k.city,state:k.state,postalCode:k.zipCode,country:k.country,phone:k.phone},totalAmount:r.finalTotal,appliedCoupons:[],flashSaleDiscount:r.flashSaleDiscount,onSuccess:es,onError:et,disabled:P,className:"rounded-xl text-lg px-8 py-4"}):(0,n.jsx)("button",{onClick:ea,disabled:P,className:"flex items-center justify-center space-x-2 bg-green-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 text-lg w-full",children:P?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,n.jsx)("span",{children:"Processing..."})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{children:"Place COD Order"}),(0,n.jsx)(f.Z,{className:"w-5 h-5"})]})}),M.payment&&(0,n.jsx)("p",{className:"text-red-500 text-sm text-center",children:M.payment})]})})]})]}),(0,n.jsx)("div",{className:"col-span-1",children:(0,n.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Order Summary"}),(0,n.jsx)("div",{className:"space-y-4 mb-6",children:r.items.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("p",{className:"font-medium text-gray-800 text-sm line-clamp-1",children:e.product.name}),(0,n.jsxs)("p",{className:"text-gray-500 text-xs",children:["Qty: ",e.quantity]})]}),(0,n.jsxs)("span",{className:"font-medium text-gray-900 text-sm",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]},e.product.id))}),(0,n.jsxs)("div",{className:"space-y-3 mb-6 pt-4 border-t border-gray-200",children:[(0,n.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,n.jsx)("span",{children:"Subtotal"}),(0,n.jsxs)("span",{children:["₹",r.subtotal.toFixed(2)]})]}),r.flashSaleDiscount>0&&(0,n.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,n.jsxs)("span",{children:["Flash Sale Discount (",r.flashSalePercentage,"% OFF)"]}),(0,n.jsxs)("span",{children:["-₹",r.flashSaleDiscount.toFixed(2)]})]}),r.coupons.totalDiscount>0&&(0,n.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,n.jsx)("span",{children:"Coupon Discount"}),(0,n.jsxs)("span",{children:["-₹",r.coupons.totalDiscount.toFixed(2)]})]}),(0,n.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,n.jsx)("span",{children:"Shipping"}),(0,n.jsx)("span",{className:"text-green-600 font-medium",children:"Free"})]}),"cod"===k.paymentMethod&&(0,n.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,n.jsx)("span",{children:"COD Charges"}),(0,n.jsxs)("span",{children:["₹",er.toFixed(2)]})]}),(0,n.jsx)("div",{className:"border-t border-gray-200 pt-3",children:(0,n.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-lg",children:[(0,n.jsx)("span",{children:"Total"}),(0,n.jsxs)("span",{children:["₹",en.toFixed(2)]})]})})]})]})})]})]})})]})}},32660:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},76858:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},30401:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},88226:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2207:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=88546)}),_N_E=e.O()}]);