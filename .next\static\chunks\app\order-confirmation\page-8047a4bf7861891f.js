(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5329],{23332:function(e,s,a){Promise.resolve().then(a.bind(a,28250)),Promise.resolve().then(a.bind(a,69520))},69520:function(e,s,a){"use strict";a.d(s,{default:function(){return j}});var t=a(57437),r=a(2265),l=a(27648),d=a(99376),n=a(44794),i=a(65302),c=a(40340),x=a(99637),o=a(62720),h=a(89345),m=a(13041);let p=(0,a(39763).Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var j=()=>{let e=(0,d.useSearchParams)().get("orderId"),[s,a]=(0,r.useState)(null),[j,u]=(0,r.useState)(!0),[g,y]=(0,r.useState)(null);if((0,r.useEffect)(()=>{(async()=>{if(!e){y("Order ID not found"),u(!1);return}try{let s=await fetch("/api/orders/".concat(e));if(!s.ok)throw Error("Failed to fetch order details");let t=await s.json();a(t.order)}catch(e){y(e.message)}finally{u(!1)}})()},[e]),j)return(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading order details..."})]})});if(g||!s)return(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(n.Z,{className:"w-10 h-10 text-red-600"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Order Not Found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:g||"Unable to load order details"}),(0,t.jsx)(l.default,{href:"/shop",className:"bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Continue Shopping"})]})});let f=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),N=new Date(Date.now()+432e6).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),b=[{icon:i.Z,label:"Order Confirmed",status:"completed",date:"Today"},{icon:n.Z,label:"Processing",status:"current",date:"1-2 days"},{icon:c.Z,label:"Shipped",status:"pending",date:"2-3 days"},{icon:x.Z,label:"Delivered",status:"pending",date:"5-7 days"}];return(0,t.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsxs)("div",{className:"px-4 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(i.Z,{className:"w-10 h-10 text-green-600"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Order Confirmed!"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Thank you for your purchase"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[(0,t.jsx)("h2",{className:"font-semibold text-gray-800 mb-4",children:"Order Details"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Order Number"}),(0,t.jsx)("span",{className:"font-medium text-gray-800",children:s.orderNumber})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Order Date"}),(0,t.jsx)("span",{className:"font-medium text-gray-800",children:f(s.createdAt)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Amount"}),(0,t.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",s.total.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Estimated Delivery"}),(0,t.jsx)("span",{className:"font-medium text-green-600",children:N})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[(0,t.jsx)("h2",{className:"font-semibold text-gray-800 mb-4",children:"Delivery Timeline"}),(0,t.jsx)("div",{className:"space-y-4",children:b.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center ".concat("completed"===e.status?"bg-green-100 text-green-600":"current"===e.status?"bg-blue-100 text-blue-600":"bg-gray-100 text-gray-400"),children:(0,t.jsx)(e.icon,{className:"w-5 h-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-medium ".concat("completed"===e.status||"current"===e.status?"text-gray-800":"text-gray-500"),children:e.label}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.date})]}),"completed"===e.status&&(0,t.jsx)(i.Z,{className:"w-5 h-5 text-green-600"})]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[(0,t.jsx)("h2",{className:"font-semibold text-gray-800 mb-4",children:"Items Ordered"}),(0,t.jsxs)("div",{className:"space-y-3",children:[s.items.map(e=>(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-800",children:e.product.name}),(0,t.jsxs)("span",{className:"text-gray-600 ml-2",children:["x",e.quantity]})]}),(0,t.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.total.toFixed(2)]})]},e.id)),s.couponCodes.length>0&&(0,t.jsx)("div",{className:"border-t border-gray-200 pt-3 mt-3",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,t.jsx)("span",{children:"Subtotal"}),(0,t.jsxs)("span",{children:["₹",s.subtotal.toFixed(2)]})]}),s.couponCodes.map((e,a)=>(0,t.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,t.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o.Z,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:e})]}),(0,t.jsxs)("span",{children:["-₹",(s.couponDiscount/s.couponCodes.length).toFixed(2)]})]},a)),s.tax>0&&(0,t.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,t.jsx)("span",{children:"Tax"}),(0,t.jsxs)("span",{children:["₹",s.tax.toFixed(2)]})]}),s.shipping>0&&(0,t.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,t.jsx)("span",{children:"Shipping"}),(0,t.jsxs)("span",{children:["₹",s.shipping.toFixed(2)]})]})]})}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-3",children:(0,t.jsxs)("div",{className:"flex justify-between font-bold text-gray-900",children:[(0,t.jsx)("span",{children:"Total"}),(0,t.jsxs)("span",{children:["₹",s.total.toFixed(2)]})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[(0,t.jsx)("h2",{className:"font-semibold text-gray-800 mb-4",children:"Shipping Address"}),(0,t.jsxs)("div",{className:"text-gray-600",children:[(0,t.jsxs)("p",{className:"font-medium text-gray-800",children:[s.address.firstName," ",s.address.lastName]}),(0,t.jsx)("p",{children:s.address.address1}),s.address.address2&&(0,t.jsx)("p",{children:s.address.address2}),(0,t.jsxs)("p",{children:[s.address.city,", ",s.address.state," ",s.address.postalCode]}),(0,t.jsx)("p",{children:s.address.country}),s.address.phone&&(0,t.jsxs)("p",{children:["Phone: ",s.address.phone]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(l.default,{href:"/order-history",className:"w-full bg-green-600 text-white py-3 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center",children:"View Order History"}),(0,t.jsx)(l.default,{href:"/shop",className:"w-full border border-gray-300 text-gray-700 py-3 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center",children:"Continue Shopping"})]}),(0,t.jsxs)("div",{className:"mt-8 text-center",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Need help with your order?"}),(0,t.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,t.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center space-x-2 text-green-600 hover:text-green-700",children:[(0,t.jsx)(h.Z,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Email"})]}),(0,t.jsxs)("a",{href:"tel:+1234567890",className:"flex items-center space-x-2 text-green-600 hover:text-green-700",children:[(0,t.jsx)(m.Z,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Call"})]})]})]})]})}),(0,t.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,t.jsxs)("div",{className:"py-12",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("div",{className:"w-32 h-32 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(i.Z,{className:"w-16 h-16 text-green-600"})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:"Order Confirmed!"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Thank you for choosing Herbalicious. Your order has been successfully placed."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"col-span-2 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Order Summary"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Order Number"}),(0,t.jsx)("p",{className:"font-semibold text-gray-800 text-lg",children:s.orderNumber})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Order Date"}),(0,t.jsx)("p",{className:"font-medium text-gray-800",children:f(s.createdAt)})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Amount"}),(0,t.jsxs)("p",{className:"font-bold text-gray-900 text-xl",children:["₹",s.total.toFixed(2)]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Estimated Delivery"}),(0,t.jsx)("p",{className:"font-medium text-green-600",children:N})]})]})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"Items Ordered"}),(0,t.jsxs)("div",{className:"space-y-4",children:[s.items.map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-800",children:e.product.name}),(0,t.jsxs)("span",{className:"text-gray-600 ml-3",children:["Quantity: ",e.quantity]})]}),(0,t.jsxs)("span",{className:"font-semibold text-gray-800",children:["₹",e.total.toFixed(2)]})]},e.id)),s.couponCodes.length>0&&(0,t.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,t.jsx)("span",{children:"Subtotal"}),(0,t.jsxs)("span",{children:["₹",s.subtotal.toFixed(2)]})]}),s.couponCodes.map((e,a)=>(0,t.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,t.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.Z,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:["Coupon: ",e]})]}),(0,t.jsxs)("span",{children:["-₹",(s.couponDiscount/s.couponCodes.length).toFixed(2)]})]},a)),s.tax>0&&(0,t.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,t.jsx)("span",{children:"Tax"}),(0,t.jsxs)("span",{children:["₹",s.tax.toFixed(2)]})]}),s.shipping>0&&(0,t.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,t.jsx)("span",{children:"Shipping"}),(0,t.jsxs)("span",{children:["₹",s.shipping.toFixed(2)]})]})]})}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Total"}),(0,t.jsxs)("span",{className:"text-xl font-bold text-gray-900",children:["₹",s.total.toFixed(2)]})]})})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Delivery Timeline"}),(0,t.jsx)("div",{className:"space-y-6",children:b.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat("completed"===e.status?"bg-green-100 text-green-600":"current"===e.status?"bg-blue-100 text-blue-600":"bg-gray-100 text-gray-400"),children:(0,t.jsx)(e.icon,{className:"w-6 h-6"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg ".concat("completed"===e.status||"current"===e.status?"text-gray-800":"text-gray-500"),children:e.label}),(0,t.jsx)("p",{className:"text-gray-500",children:e.date})]}),"completed"===e.status&&(0,t.jsx)(i.Z,{className:"w-6 h-6 text-green-600"})]},s))})]})]}),(0,t.jsxs)("div",{className:"col-span-1 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Shipping Address"}),(0,t.jsxs)("div",{className:"text-gray-600 space-y-1",children:[(0,t.jsxs)("p",{className:"font-medium text-gray-800",children:[s.address.firstName," ",s.address.lastName]}),(0,t.jsx)("p",{children:s.address.address1}),s.address.address2&&(0,t.jsx)("p",{children:s.address.address2}),(0,t.jsxs)("p",{children:[s.address.city,", ",s.address.state," ",s.address.postalCode]}),(0,t.jsx)("p",{children:s.address.country}),s.address.phone&&(0,t.jsxs)("p",{className:"text-sm",children:["Phone: ",s.address.phone]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(l.default,{href:"/order-history",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center text-lg",children:"View Order History"}),(0,t.jsx)(l.default,{href:"/shop",className:"w-full border border-gray-300 text-gray-700 py-4 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center text-lg",children:"Continue Shopping"})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"Need Help?"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Our customer support team is here to help with any questions about your order."}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center space-x-3 text-green-600 hover:text-green-700 transition-colors",children:[(0,t.jsx)(h.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"<EMAIL>"})]}),(0,t.jsxs)("a",{href:"tel:+919987810707",className:"flex items-center space-x-3 text-green-600 hover:text-green-700 transition-colors",children:[(0,t.jsx)(m.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"+91 99878 10707"})]}),(0,t.jsxs)("a",{href:"https://wa.me/919987810707",target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-3 text-green-600 hover:text-green-700 transition-colors",children:[(0,t.jsx)(p,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"WhatsApp Support"})]})]})]})]})]})]})})]})}},65302:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},44794:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},13041:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},62720:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=23332)}),_N_E=e.O()}]);