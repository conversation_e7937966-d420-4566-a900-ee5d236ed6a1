(()=>{var e={};e.id=5311,e.ids=[5311],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13266:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c}),t(25176),t(37254),t(35866);var a=t(23191),r=t(88716),l=t(37922),n=t.n(l),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let c=["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25176)),"C:\\Users\\<USER>\\Desktop\\project\\app\\cart\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\cart\\page.tsx"],x="/cart/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},67729:(e,s,t)=>{Promise.resolve().then(t.bind(t,65090)),Promise.resolve().then(t.bind(t,60405))},60405:(e,s,t)=>{"use strict";t.d(s,{default:()=>Z});var a=t(10326),r=t(77626),l=t.n(r),n=t(17577),i=t.n(n),d=t(90434),c=t(46226),o=t(34565),x=t(24230),m=t(98091),h=t(76557);let p=(0,h.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var u=t(83855),g=t(94494),f=t(77109);let j=(0,h.Z)("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var y=t(71821);let b=(0,h.Z)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var N=t(40765),v=t(1572),w=t(54659),k=t(94019),C=t(87888),S=t(43810),P=t(48998),E=t(69719);let A=({cartItems:e,subtotal:s,userId:t,onCouponApply:r,onCouponRemove:l,appliedCoupons:d})=>{let{flashSaleSettings:c,isHydrated:o}=(0,E.u)(),[x,m]=(0,n.useState)([]),[h,p]=(0,n.useState)([]),[u,g]=(0,n.useState)(""),[f,A]=(0,n.useState)(!1),[Z,D]=(0,n.useState)(""),[M,F]=(0,n.useState)(!1),[T,U]=(0,n.useState)(!0),I=i().useMemo(()=>{if(!c||!c.showFlashSale)return!1;if(c.flashSaleEndDate){let e=new Date;return new Date(c.flashSaleEndDate)>=e}return!0},[c]);(0,n.useEffect)(()=>{_(),O()},[e,t,I]);let _=async()=>{try{U(!0);let e=new URLSearchParams({active:"true",limit:"20"});t&&e.append("userId",t);let s=await fetch(`/api/coupons?${e}`);if(s.ok){let e=await s.json(),t=R(e.coupons,!0);m(t)}}catch(e){console.error("Error fetching coupons:",e)}finally{U(!1)}},O=async()=>{try{let e=new URLSearchParams({active:"true",showInModule:"true",limit:"10"});t&&e.append("userId",t);let s=await fetch(`/api/coupons?${e}`);if(s.ok){let e=await s.json(),t=R(e.coupons,!1);p(t)}}catch(e){console.error("Error fetching featured coupons:",e)}},R=(t,a=!1)=>t.filter(t=>{if(d.some(e=>e.coupon.id===t.id)||a&&t.showInModule||t.minimumAmount&&s<t.minimumAmount)return!1;let r=e.map(e=>e.product.id),l=e.flatMap(e=>e.product.categories?.map(e=>e.id)||[]);switch(t.type){case"PRODUCT_SPECIFIC":return t.applicableProducts.some(e=>r.includes(e));case"CATEGORY_SPECIFIC":return t.applicableCategories.some(e=>l.includes(e));case"MINIMUM_PURCHASE":return s>=(t.minimumAmount||0);default:return!0}}),q=async a=>{if(a.trim()){A(!0),D("");try{let l=await fetch("/api/coupons/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({couponCode:a,cartItems:e,userId:t,subtotal:s})}),n=await l.json();if(n.isValid&&n.coupon){let e={coupon:n.coupon,discountAmount:n.discountAmount,isValid:!0};r(e),g(""),D("Coupon applied successfully!"),_(),O()}else D(n.errorMessage||"Invalid coupon code")}catch(e){D("Error validating coupon")}finally{A(!1)}}},V=async e=>{await q(e.code)},z=e=>{navigator.clipboard.writeText(e)},G=e=>{switch(e){case"PERCENTAGE":return a.jsx(j,{className:"w-4 h-4"});case"FIXED_AMOUNT":return a.jsx(y.Z,{className:"w-4 h-4"});case"FREE_SHIPPING":return a.jsx(b,{className:"w-4 h-4"});default:return a.jsx(N.Z,{className:"w-4 h-4"})}},H=e=>{switch(e.discountType){case"PERCENTAGE":return`${e.discountValue}% OFF`;case"FIXED_AMOUNT":return`₹${e.discountValue} OFF`;case"FREE_SHIPPING":return"FREE SHIPPING";default:return"DISCOUNT"}},K=e=>{if(!e.validUntil)return!1;let s=new Date(e.validUntil),t=new Date,a=Math.ceil((s.getTime()-t.getTime())/864e5);return a<=3&&a>0};if(T||!o)return a.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"h-16 bg-gray-200 rounded"}),a.jsx("div",{className:"h-16 bg-gray-200 rounded"})]})]})});if(I)return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[a.jsx(v.Z,{className:"w-5 h-5 text-green-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Coupons"})]}),(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(b,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),a.jsx("p",{className:"text-gray-600 font-medium mb-2",children:"Flash Sale Active!"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Coupons cannot be used during flash sale periods. Enjoy the flash sale discounts instead!"})]})]});let L=(()=>{let e=x.filter(e=>("PERCENTAGE"===e.discountType?e.discountValue>=10:e.discountValue>=50)||K(e));return e.length>=3?e.slice(0,5):x.slice(0,Math.min(5,x.length))})(),$=M?x:L;return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[a.jsx(v.Z,{className:"w-5 h-5 text-green-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Available Coupons"})]}),d.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-600 mb-3",children:"Applied Coupons"}),a.jsx("div",{className:"space-y-2",children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(w.Z,{className:"w-4 h-4 text-green-600"}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium text-green-800",children:e.coupon.code}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["-₹",e.discountAmount.toFixed(2)]})]})]}),a.jsx("button",{onClick:()=>l(e.coupon.id),className:"p-1 text-green-600 hover:bg-green-100 rounded-full transition-colors",children:a.jsx(k.Z,{className:"w-4 h-4"})})]},e.coupon.id))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("input",{type:"text",value:u,onChange:e=>g(e.target.value.toUpperCase()),placeholder:"Enter coupon code",className:"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&q(u)}),a.jsx("button",{onClick:()=>q(u),disabled:f||!u.trim(),className:"px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:f?"Applying...":"Apply"})]}),Z&&(0,a.jsxs)("div",{className:`mt-2 flex items-center space-x-2 text-sm ${Z.includes("successfully")?"text-green-600":"text-red-600"}`,children:[Z.includes("successfully")?a.jsx(w.Z,{className:"w-4 h-4"}):a.jsx(C.Z,{className:"w-4 h-4"}),a.jsx("span",{children:Z})]})]}),h.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-600 mb-4 flex items-center space-x-2",children:[a.jsx(v.Z,{className:"w-4 h-4 text-yellow-500"}),a.jsx("span",{children:"Featured Offers"})]}),a.jsx("div",{className:"space-y-4",children:h.map(e=>(0,a.jsxs)("div",{className:"relative bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow border-2 border-dashed border-green-400",children:[a.jsx("div",{className:"absolute top-3 right-3 bg-yellow-400 text-yellow-900 px-3 py-1 text-xs font-bold rounded-full z-10",children:"FEATURED"}),a.jsx("div",{className:"p-5 sm:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[a.jsx("div",{className:"bg-green-100 text-green-700 p-2 rounded-lg",children:G(e.discountType)}),(0,a.jsxs)("div",{className:"flex items-center flex-wrap gap-2",children:[a.jsx("span",{className:"text-2xl sm:text-3xl font-medium text-gray-900",children:H(e)}),K(e)&&a.jsx("span",{className:"px-2 py-0.5 bg-red-500 text-white text-xs rounded-full font-bold",children:"EXPIRES SOON"})]})]}),a.jsx("h5",{className:"font-medium text-gray-900 text-base sm:text-lg mb-1",children:e.name}),e.description&&a.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2 hidden sm:block",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 text-xs sm:text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-gray-100 px-2 py-1 rounded font-mono",children:[a.jsx(N.Z,{className:"w-3 h-3 text-gray-600"}),a.jsx("span",{className:"font-bold text-gray-900",children:e.code}),a.jsx("button",{onClick:()=>z(e.code),className:"p-0.5 hover:bg-gray-200 rounded",title:"Copy",children:a.jsx(S.Z,{className:"w-3 h-3 text-gray-600"})})]}),e.validUntil&&(0,a.jsxs)("span",{className:"text-gray-500 flex items-center gap-1",children:[a.jsx(P.Z,{className:"w-3 h-3"}),new Date(e.validUntil).toLocaleDateString()]}),e.minimumAmount&&(0,a.jsxs)("span",{className:"text-gray-500",children:["Min: ₹",e.minimumAmount]})]})]}),a.jsx("button",{onClick:()=>V(e),className:"w-full sm:w-auto px-6 py-2.5 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition-colors shadow hover:shadow-md",children:"APPLY"})]})})]},e.id))})]}),$.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-600",children:M?"All Available Coupons":"Recommended for You"}),x.length>$.length&&a.jsx("button",{onClick:()=>F(!M),className:"text-sm text-green-600 hover:text-green-700 font-medium",children:M?"Show Less":`View All (${x.length})`})]}),a.jsx("div",{className:"space-y-2",children:$.map(e=>(0,a.jsxs)("div",{className:"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow border border-gray-200",children:[a.jsx("div",{className:`absolute inset-0 border border-dashed ${K(e)?"border-orange-300":"border-gray-300"} rounded-lg m-1`}),a.jsx("div",{className:`absolute left-0 top-0 bottom-0 w-1 sm:w-1.5 ${K(e)?"bg-orange-500":"bg-green-500"}`}),a.jsx("div",{className:"p-3 pl-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[a.jsx("div",{className:`p-1.5 rounded ${K(e)?"bg-orange-100 text-orange-700":"bg-green-100 text-green-700"}`,children:G(e.discountType)}),a.jsx("span",{className:"text-lg sm:text-xl font-medium text-gray-900",children:H(e)}),K(e)&&a.jsx("span",{className:"px-1.5 py-0.5 bg-orange-500 text-white text-xs rounded font-bold",children:"EXPIRES"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 text-sm",children:[a.jsx("h5",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-gray-100 px-2 py-0.5 rounded text-xs font-mono",children:[a.jsx("span",{className:"font-bold text-gray-900",children:e.code}),a.jsx("button",{onClick:()=>z(e.code),className:"p-0.5 hover:bg-gray-200 rounded",title:"Copy",children:a.jsx(S.Z,{className:"w-3 h-3 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-500 mt-1",children:[e.validUntil&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[a.jsx(P.Z,{className:"w-3 h-3"}),new Date(e.validUntil).toLocaleDateString()]}),e.minimumAmount&&(0,a.jsxs)("span",{children:["Min: ₹",e.minimumAmount]})]})]}),a.jsx("button",{onClick:()=>V(e),className:"w-full sm:w-auto px-4 py-2 bg-green-600 text-white text-sm rounded-lg font-semibold hover:bg-green-700 transition-colors",children:"APPLY"})]})})]},e.id))})]}),0===x.length&&!T&&(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(b,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),a.jsx("p",{className:"text-gray-500",children:"No coupons available for your current cart"})]})]})},Z=()=>{let{state:e,dispatch:s}=(0,g.j)(),{data:t}=(0,f.useSession)(),r=(e,t)=>{t<=0?s({type:"REMOVE_ITEM",payload:e}):s({type:"UPDATE_QUANTITY",payload:{id:e,quantity:t}})},n=e=>{s({type:"REMOVE_ITEM",payload:e})},i=e=>{s({type:"APPLY_COUPON",payload:e})},h=e=>{s({type:"REMOVE_COUPON",payload:e})};return e.isHydrated?0===e.items.length?(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[a.jsx("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-8 text-center",children:[a.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:a.jsx(o.Z,{className:"w-12 h-12 text-gray-400"})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Your cart is empty"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Add some products to get started"}),(0,a.jsxs)(d.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:["Start Shopping",a.jsx(x.Z,{className:"ml-2 w-4 h-4"})]})]})}),a.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-16 text-center",children:[a.jsx("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:a.jsx(o.Z,{className:"w-16 h-16 text-gray-400"})}),a.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"Your cart is empty"}),a.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Discover our amazing products and start shopping"}),(0,a.jsxs)(d.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-green-700 transition-colors text-lg",children:["Start Shopping",a.jsx(x.Z,{className:"ml-3 w-5 h-5"})]})]})})]}):(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[a.jsx("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Shopping Cart"}),a.jsx("div",{className:"space-y-4 mb-6",children:e.items.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[a.jsx("div",{className:"w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0",children:a.jsx(c.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"80px"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:"font-semibold text-gray-800 mb-1 line-clamp-1",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&a.jsx("div",{className:"flex flex-wrap gap-1 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),a.jsx("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:e.product.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₹",e.product.price]}),a.jsx("button",{onClick:()=>n(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:a.jsx(m.Z,{className:"w-4 h-4"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-4 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity-1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(p,{className:"w-4 h-4"})}),a.jsx("span",{className:"font-medium text-gray-800 w-8 text-center",children:e.quantity}),a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity+1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(u.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]})]},e.variantKey||e.product.id))}),a.jsx("div",{className:"mb-6",children:a.jsx(A,{cartItems:e.items,subtotal:e.subtotal,userId:t?.user?.id,onCouponApply:i,onCouponRemove:h,appliedCoupons:e.coupons.appliedCoupons})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[a.jsx("h3",{className:"font-semibold text-gray-800 mb-4",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",e.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",e.subtotal.toFixed(2)]})]}),e.flashSaleDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,a.jsxs)("span",{children:["Flash Sale Discount (",e.flashSalePercentage,"% OFF)"]}),(0,a.jsxs)("span",{children:["-₹",e.flashSaleDiscount.toFixed(2)]})]}),e.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[a.jsx("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",e.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[a.jsx("span",{children:"Shipping"}),a.jsx("span",{children:"Free"})]}),a.jsx("div",{className:"border-t border-gray-200 pt-2",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-lg",children:[a.jsx("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",e.finalTotal.toFixed(2)]})]})})]})]}),(0,a.jsxs)(d.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[a.jsx("span",{children:"Proceed to Checkout"}),a.jsx(x.Z,{className:"w-5 h-5"})]})]})}),a.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[a.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Shopping Cart"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-2",children:[a.jsx("div",{className:"space-y-6",children:e.items.map(e=>a.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex space-x-6",children:[a.jsx("div",{className:"w-32 h-32 relative rounded-xl overflow-hidden flex-shrink-0",children:a.jsx(c.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"128px"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&a.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),a.jsx("p",{className:"text-gray-600",children:e.product.shortDescription})]}),a.jsx("button",{onClick:()=>n(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:a.jsx(m.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity-1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(p,{className:"w-5 h-5"})}),a.jsx("span",{className:"font-medium text-gray-800 w-12 text-center text-lg",children:e.quantity}),a.jsx("button",{onClick:()=>r(e.variantKey||e.product.id,e.quantity+1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:a.jsx(u.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["₹",e.product.price," each"]})]})]})]})]})},e.variantKey||e.product.id))}),a.jsx("div",{className:"mt-6",children:a.jsx(A,{cartItems:e.items,subtotal:e.subtotal,userId:t?.user?.id,onCouponApply:i,onCouponRemove:h,appliedCoupons:e.coupons.appliedCoupons})})]}),a.jsx("div",{className:"col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",e.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",e.subtotal.toFixed(2)]})]}),e.flashSaleDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,a.jsxs)("span",{children:["Flash Sale Discount (",e.flashSalePercentage,"% OFF)"]}),(0,a.jsxs)("span",{children:["-₹",e.flashSaleDiscount.toFixed(2)]})]}),e.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[a.jsx("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",e.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[a.jsx("span",{children:"Shipping"}),a.jsx("span",{className:"text-green-600 font-medium",children:"Free"})]}),a.jsx("div",{className:"border-t border-gray-200 pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-xl",children:[a.jsx("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",e.finalTotal.toFixed(2)]})]})})]}),(0,a.jsxs)(d.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 text-lg",children:[a.jsx("span",{children:"Proceed to Checkout"}),a.jsx(x.Z,{className:"w-5 h-5"})]}),a.jsx(d.default,{href:"/shop",className:"w-full mt-3 border border-gray-300 text-gray-700 py-4 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center",children:"Continue Shopping"})]})})]})]})})]}):(0,a.jsxs)("div",{className:"jsx-a6d7afc75062cb7a flex justify-center items-center h-96",children:[a.jsx("div",{className:"jsx-a6d7afc75062cb7a loader"}),a.jsx(l(),{id:"a6d7afc75062cb7a",children:".loader.jsx-a6d7afc75062cb7a{border:8px solid#f3f3f3;border-top:8px solid#3498db;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;width:60px;height:60px;-webkit-animation:spin 2s linear infinite;-moz-animation:spin 2s linear infinite;-o-animation:spin 2s linear infinite;animation:spin 2s linear infinite}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]})}},43810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},71821:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},83855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},40765:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},25176:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Cart.tsx#default`);function n(){return a.jsx(r.Z,{children:a.jsx(l,{})})}},40304:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,3757,434,9694,9265,9536,5090],()=>t(13266));module.exports=a})();