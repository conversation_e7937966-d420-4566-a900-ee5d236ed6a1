(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8606],{6963:function(e,t,s){Promise.resolve().then(s.bind(s,8488))},8488:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return C}});var a=s(57437),r=s(2265),n=s(91723),l=s(65302),i=s(44794),d=s(40340),c=s(45131),o=s(82431),x=s(15863),u=s(73247),m=s(92735),h=s(22252),p=s(39763);let g=(0,p.Z)("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]]),y=(0,p.Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var f=s(42208),N=s(2207),j=s(92451),v=s(10407),b=s(51380),w=s(80605),k=s(99376),C=()=>{var e,t,s;let{data:p,status:C}=(0,w.useSession)(),E=(0,k.useRouter)(),[D,S]=(0,r.useState)([]),[P,Z]=(0,r.useState)(!0),[I,L]=(0,r.useState)(null),[M,R]=(0,r.useState)(""),[O,A]=(0,r.useState)("all"),[F,T]=(0,r.useState)("all"),[G,q]=(0,r.useState)(null),[H,V]=(0,r.useState)(!1),[z,U]=(0,r.useState)(1),[_,B]=(0,r.useState)(1),[J,Q]=(0,r.useState)(0),[X,$]=(0,r.useState)([]),[K,W]=(0,r.useState)(!1),[Y,ee]=(0,r.useState)(!1);(0,r.useEffect)(()=>{var e;"loading"===C||p&&(null===(e=p.user)||void 0===e?void 0:e.role)==="ADMIN"||E.push("/")},[p,C,E]);let et=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{Z(!0),L(null);let t=new URLSearchParams({page:e.toString(),limit:"20",..."all"!==O&&{status:O},..."all"!==F&&{paymentStatus:F}}),s=await fetch("/api/orders?".concat(t)),a=await s.json();if(!s.ok)throw Error(a.message||"Failed to fetch orders");S(a.orders),B(a.pagination.totalPages),Q(a.pagination.totalCount),U(e),$([])}catch(e){L(e instanceof Error?e.message:"Failed to fetch orders")}finally{Z(!1)}};(0,r.useEffect)(()=>{var e;(null==p?void 0:null===(e=p.user)||void 0===e?void 0:e.role)==="ADMIN"&&et(1)},[p,O,F]);let es=D.filter(e=>{var t,s,a,r;if(!M)return!0;let n=M.toLowerCase();return e.orderNumber.toLowerCase().includes(n)||(null===(s=e.user)||void 0===s?void 0:null===(t=s.name)||void 0===t?void 0:t.toLowerCase().includes(n))||(null===(r=e.user)||void 0===r?void 0:null===(a=r.email)||void 0===a?void 0:a.toLowerCase().includes(n))||e.address.firstName.toLowerCase().includes(n)||e.address.lastName.toLowerCase().includes(n)}),ea=e=>{switch(e){case"PENDING":default:return(0,a.jsx)(n.Z,{className:"w-4 h-4"});case"CONFIRMED":case"DELIVERED":return(0,a.jsx)(l.Z,{className:"w-4 h-4"});case"PROCESSING":return(0,a.jsx)(i.Z,{className:"w-4 h-4"});case"SHIPPED":return(0,a.jsx)(d.Z,{className:"w-4 h-4"});case"CANCELLED":return(0,a.jsx)(c.Z,{className:"w-4 h-4"});case"REFUNDED":return(0,a.jsx)(o.Z,{className:"w-4 h-4"})}},er=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"CONFIRMED":return"bg-blue-100 text-blue-800";case"PROCESSING":return"bg-indigo-100 text-indigo-800";case"SHIPPED":return"bg-purple-100 text-purple-800";case"DELIVERED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},en=e=>{switch(e){case"PAID":return"text-green-600";case"PENDING":return"text-yellow-600";case"FAILED":return"text-red-600";default:return"text-gray-600"}},el=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),ei=async(e,t,s)=>{try{if("SHIPPED"===t&&!s){let e=prompt("Please enter the tracking number:");if(!e){alert("Tracking number is required when marking order as shipped");return}s=e}let a=await fetch("/api/orders/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t,...s&&{trackingNumber:s}})});if(!a.ok){let e=await a.json();throw Error(e.message||"Failed to update order status")}et(z),G&&G.id===e&&q({...G,status:t})}catch(e){console.error("Error updating order status:",e),alert("Failed to update order status")}},ed=async e=>{if(0!==X.length){if("SHIPPED"===e){alert("Cannot bulk update orders to Shipped status. Please update each order individually with tracking information.");return}ee(!0);try{let t=await fetch("/api/orders/bulk-update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderIds:X,updates:{status:e}})});if(!t.ok)throw Error("Failed to update orders");let s=await t.json();alert("Successfully updated ".concat(s.updatedCount," orders")),et(z),$([]),W(!1)}catch(e){console.error("Error updating orders:",e),alert("Failed to update orders")}finally{ee(!1)}}},ec=e=>{$(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return"loading"===C||P?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(x.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Orders"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage and track customer orders"})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search orders...",value:M,onChange:e=>R(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:O,onChange:e=>A(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"CONFIRMED",children:"Confirmed"}),(0,a.jsx)("option",{value:"PROCESSING",children:"Processing"}),(0,a.jsx)("option",{value:"SHIPPED",children:"Shipped"}),(0,a.jsx)("option",{value:"DELIVERED",children:"Delivered"}),(0,a.jsx)("option",{value:"CANCELLED",children:"Cancelled"}),(0,a.jsx)("option",{value:"REFUNDED",children:"Refunded"})]}),(0,a.jsxs)("select",{value:F,onChange:e=>T(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,a.jsx)("option",{value:"all",children:"All Payment Status"}),(0,a.jsx)("option",{value:"PENDING",children:"Payment Pending"}),(0,a.jsx)("option",{value:"PAID",children:"Paid"}),(0,a.jsx)("option",{value:"FAILED",children:"Failed"}),(0,a.jsx)("option",{value:"REFUNDED",children:"Refunded"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>et(z),className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"Refresh orders",children:(0,a.jsx)(o.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsx)("button",{onClick:()=>{let e=new Blob([["Order Number,Customer Name,Email,Date,Status,Payment Status,Total",...es.map(e=>{var t,s;return[e.orderNumber,(null===(t=e.user)||void 0===t?void 0:t.name)||"".concat(e.address.firstName," ").concat(e.address.lastName),(null===(s=e.user)||void 0===s?void 0:s.email)||"",el(e.createdAt),e.status,e.paymentStatus,e.total].join(",")})].join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="orders-".concat(new Date().toISOString().split("T")[0],".csv"),s.click()},className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"Export orders",children:(0,a.jsx)(m.Z,{className:"w-5 h-5 text-gray-600"})})]})]})}),X.length>0&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-green-800 font-medium",children:[X.length," order",X.length>1?"s":""," selected"]}),(0,a.jsx)("button",{onClick:()=>W(!K),className:"text-green-700 hover:text-green-900 font-medium",children:"Bulk Actions"})]}),(0,a.jsx)("button",{onClick:()=>$([]),className:"text-green-600 hover:text-green-800",children:"Clear Selection"})]}),K&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[(0,a.jsx)("button",{onClick:()=>ed("CONFIRMED"),disabled:Y,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Mark as Confirmed"}),(0,a.jsx)("button",{onClick:()=>ed("PROCESSING"),disabled:Y,className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50",children:"Mark as Processing"}),(0,a.jsx)("button",{onClick:()=>ed("DELIVERED"),disabled:Y,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50",children:"Mark as Delivered"}),(0,a.jsx)("button",{onClick:()=>ed("CANCELLED"),disabled:Y,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50",children:"Mark as Cancelled"})]})]}),I&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.Z,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:I})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("button",{onClick:()=>{X.length===es.length?$([]):$(es.map(e=>e.id))},className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900",children:X.length===es.length&&es.length>0?(0,a.jsx)(g,{className:"w-5 h-5"}):(0,a.jsx)(y,{className:"w-5 h-5"})})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===es.length?(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:8,className:"px-6 py-12 text-center text-gray-500",children:"No orders found"})}):es.map(e=>{var t,s;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("button",{onClick:()=>ec(e.id),className:"text-gray-600 hover:text-gray-900",children:X.includes(e.id)?(0,a.jsx)(g,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(y,{className:"w-5 h-5"})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("button",{onClick:()=>E.push("/admin/orders/".concat(e.id)),className:"text-left hover:text-green-600 transition-colors",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.orderNumber}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.items.length," items"]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:(null===(t=e.user)||void 0===t?void 0:t.name)||"".concat(e.address.firstName," ").concat(e.address.lastName)}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(null===(s=e.user)||void 0===s?void 0:s.email)||"Guest"})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:el(e.createdAt)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full items-center space-x-1 ".concat(er(e.status)),children:[ea(e.status),(0,a.jsx)("span",{className:"capitalize",children:e.status.toLowerCase()})]}),(0,a.jsx)("div",{className:"absolute left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsx)("button",{onClick:()=>ei(e.id,"CONFIRMED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Confirmed"}),(0,a.jsx)("button",{onClick:()=>ei(e.id,"PROCESSING"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Processing"}),(0,a.jsx)("button",{onClick:()=>ei(e.id,"SHIPPED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Shipped"}),(0,a.jsx)("button",{onClick:()=>ei(e.id,"DELIVERED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Delivered"}),(0,a.jsx)("button",{onClick:()=>ei(e.id,"CANCELLED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Cancelled"})]})})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium ".concat(en(e.paymentStatus)),children:e.paymentStatus.toLowerCase()}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"COD"===e.paymentMethod?"Cash on Delivery":e.paymentMethod||"Online"})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:(0,b.T4)(e.total)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>E.push("/admin/orders/".concat(e.id)),className:"text-green-600 hover:text-green-800 transition-colors",title:"View Details",children:(0,a.jsx)(f.Z,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>ei(e.id,"PROCESSING"),className:"text-blue-600 hover:text-blue-800 transition-colors",title:"Quick Update",children:(0,a.jsx)(N.Z,{className:"w-4 h-4"})})]})})]},e.id)})})]})})}),_>1&&(0,a.jsxs)("div",{className:"mt-6 flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(z-1)*20+1," to"," ",Math.min(20*z,J)," of ",J," orders"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>et(z-1),disabled:1===z,className:"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:(0,a.jsx)(j.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("span",{className:"px-3 py-2 text-gray-900",children:["Page ",z," of ",_]}),(0,a.jsx)("button",{onClick:()=>et(z+1),disabled:z===_,className:"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:(0,a.jsx)(v.Z,{className:"w-5 h-5"})})]})]}),H&&G&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Order Details"}),(0,a.jsx)("button",{onClick:()=>V(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.Z,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Information"}),(0,a.jsxs)("dl",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Order Number"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:G.orderNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Date"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:el(G.createdAt)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Status"}),(0,a.jsx)("dd",{children:(0,a.jsxs)("select",{value:G.status,onChange:e=>ei(G.id,e.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"CONFIRMED",children:"Confirmed"}),(0,a.jsx)("option",{value:"PROCESSING",children:"Processing"}),(0,a.jsx)("option",{value:"SHIPPED",children:"Shipped"}),(0,a.jsx)("option",{value:"DELIVERED",children:"Delivered"}),(0,a.jsx)("option",{value:"CANCELLED",children:"Cancelled"}),(0,a.jsx)("option",{value:"REFUNDED",children:"Refunded"})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Payment Status"}),(0,a.jsx)("dd",{className:"text-sm font-medium ".concat(en(G.paymentStatus)),children:G.paymentStatus})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Payment Method"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:"COD"===G.paymentMethod?"Cash on Delivery":"Online Payment"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Customer Information"}),(0,a.jsxs)("dl",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Name"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:(null===(e=G.user)||void 0===e?void 0:e.name)||"".concat(G.address.firstName," ").concat(G.address.lastName)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Email"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:(null===(t=G.user)||void 0===t?void 0:t.email)||"Guest"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Phone"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:(null===(s=G.user)||void 0===s?void 0:s.phone)||G.address.phone||"N/A"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Shipping Address"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[G.address.firstName," ",G.address.lastName,(0,a.jsx)("br",{}),G.address.address1,(0,a.jsx)("br",{}),G.address.address2&&(0,a.jsxs)(a.Fragment,{children:[G.address.address2,(0,a.jsx)("br",{})]}),G.address.city,", ",G.address.state," ",G.address.postalCode,(0,a.jsx)("br",{}),G.address.country]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Items"}),(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Product"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Quantity"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Price"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Total"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:G.items.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.product.name}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.quantity}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:(0,b.T4)(e.price)}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:(0,b.T4)(e.total)})]},e.id))})]})})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,a.jsxs)("dl",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Subtotal"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:(0,b.T4)(G.subtotal)})]}),G.couponDiscount&&G.couponDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"Discount"}),(0,a.jsxs)("dd",{className:"text-sm font-medium text-green-600",children:["-",(0,b.T4)(G.couponDiscount)]})]}),"COD"===G.paymentMethod&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("dt",{className:"text-sm text-gray-500",children:"COD Charges"}),(0,a.jsx)("dd",{className:"text-sm font-medium text-gray-900",children:(0,b.T4)(50)})]}),(0,a.jsxs)("div",{className:"flex justify-between border-t border-gray-200 pt-2",children:[(0,a.jsx)("dt",{className:"text-base font-medium text-gray-900",children:"Total"}),(0,a.jsx)("dd",{className:"text-base font-medium text-gray-900",children:(0,b.T4)(G.total)})]})]})})]})]})})]})}},51380:function(e,t,s){"use strict";function a(e){return function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(isNaN(e))return"₹0";let s=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return"₹".concat(s)}(e,!0)}function r(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function n(e,t){return(e?r(e):r(t))||"product"}s.d(t,{GD:function(){return r},T4:function(){return a},w:function(){return n}})},22252:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},65302:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},92451:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},10407:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},91723:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},92735:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},42208:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15863:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44794:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},2207:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]])},82431:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},73247:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},40340:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},45131:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},99376:function(e,t,s){"use strict";var a=s(35475);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})}},function(e){e.O(0,[7349,2971,2117,1744],function(){return e(e.s=6963)}),_N_E=e.O()}]);