"use strict";(()=>{var e={};e.id=9100,e.ids=[9100],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},81612:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>O,requestAsyncStorage:()=>w,routeModule:()=>j,serverHooks:()=>m,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>y,GET:()=>h,POST:()=>g});var i=t(49303),o=t(88716),n=t(60670),u=t(87070),a=t(75571),d=t(95306),p=t(89585),c=t(89456),l=t(81515),f=t(11185);async function h(e){try{let e=await (0,a.getServerSession)(d.L);if(!e?.user?.id)return u.NextResponse.json({error:"Unauthorized"},{status:401});await (0,c.Z)();let r=f.Types.ObjectId.isValid(e.user.id)?new f.Types.ObjectId(e.user.id):e.user.id,t=(await l.hQ.find({userId:r}).populate([{path:"product",select:"name slug price shortDescription images rating reviews"}]).sort({createdAt:-1}).lean()||[]).map(e=>({_id:String(e.product?._id??""),name:e.product?.name??"",slug:e.product?.slug??"",price:e.product?.price??0,shortDescription:e.product?.shortDescription??"",image:(Array.isArray(e.product?.images)&&e.product.images.length>0?e.product.images[0]?.url:void 0)||"/placeholder-product.jpg",rating:e.product?.rating??0,reviews:e.product?.reviews??0,wishlistItemId:String(e._id)}));return u.NextResponse.json({items:t})}catch(e){return console.error("Error fetching wishlist:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let r=await (0,a.getServerSession)(d.L);if(!r?.user?.id)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{productId:t}=await e.json();if(!t)return u.NextResponse.json({error:"Product ID is required"},{status:400});await (0,c.Z)();let s=f.Types.ObjectId.isValid(r.user.id)?new f.Types.ObjectId(r.user.id):r.user.id,i=f.Types.ObjectId.isValid(t)?new f.Types.ObjectId(t):t,o=await l.xs.findById(i).select("_id name price").lean();if(!o)return u.NextResponse.json({error:"Product not found"},{status:404});if(await l.hQ.findOne({userId:s,productId:i}).select("_id").lean())return u.NextResponse.json({error:"Item already in wishlist"},{status:400});let n=await l.hQ.create({userId:s,productId:i});try{await p.un.itemAdded(String(s),{productId:String(o._id),productName:o.name??"",price:o.price??void 0,currency:"INR"})}catch(e){console.error("Failed to send wishlist added notification:",e)}let h=n.toObject();return u.NextResponse.json({success:!0,item:{_id:String(h._id),userId:String(h.userId),productId:String(h.productId)}})}catch(e){return console.error("Error adding to wishlist:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){try{let r=await (0,a.getServerSession)(d.L);if(!r?.user?.id)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("productId");if(!s)return u.NextResponse.json({error:"Product ID is required"},{status:400});await (0,c.Z)();let i=f.Types.ObjectId.isValid(r.user.id)?new f.Types.ObjectId(r.user.id):r.user.id,o=f.Types.ObjectId.isValid(s)?new f.Types.ObjectId(s):s,n=await l.hQ.findOne({userId:i,productId:o}).populate([{path:"product",select:"name"}]).lean();if(!n)return u.NextResponse.json({error:"Item not found in wishlist"},{status:404});await l.hQ.deleteOne({_id:n._id});try{n.product&&await p.un.itemRemoved(String(i),{productId:String(n.product._id??n.product.id??o),productName:n.product.name??""})}catch(e){console.error("Failed to send wishlist removed notification:",e)}return u.NextResponse.json({success:!0})}catch(e){return console.error("Error removing from wishlist:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let j=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/wishlist/route",pathname:"/api/wishlist",filename:"route",bundlePath:"app/api/wishlist/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\wishlist\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:x,serverHooks:m}=j,v="/api/wishlist/route";function O(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:x})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var u=i?Object.getOwnPropertyDescriptor(e,o):null;u&&(u.get||u.set)?Object.defineProperty(s,o,u):s[o]=e[o]}return s.default=e,t&&t.set(e,s),s}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,2830,5306,9585],()=>t(81612));module.exports=s})();