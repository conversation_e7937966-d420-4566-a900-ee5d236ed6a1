(()=>{var e={};e.id=4331,e.ids=[4331],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93077:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),a(14285),a(90596),a(37254),a(35866);var r=a(23191),s=a(88716),i=a(37922),l=a.n(i),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(t,c);let d=["",{children:["admin",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,14285)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\categories\\page.tsx"],x="/admin/categories/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/admin/categories/page",pathname:"/admin/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},67319:(e,t,a)=>{Promise.resolve().then(a.bind(a,8957))},75671:(e,t,a)=>{Promise.resolve().then(a.bind(a,2802))},8957:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(10326),s=a(17577),i=a.n(s),l=a(94019),n=a(88307),c=a(83855),d=a(75290),o=a(87888);let x=(0,a(76557).Z)("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);var u=a(48705),h=a(69508),m=a(98091);let g=()=>{let[e,t]=(0,s.useState)(""),[a,g]=(0,s.useState)(!1),[p,y]=(0,s.useState)(!1),[f,b]=(0,s.useState)(null),[j,v]=(0,s.useState)([]),[N,w]=(0,s.useState)(!0),[k,Z]=(0,s.useState)(null);(0,s.useEffect)(()=>{C()},[]);let C=async()=>{try{w(!0);let e=await fetch("/api/categories",{headers:{"x-admin-request":"true"}}),t=await e.json();t.success?v(t.data):Z("Failed to fetch categories")}catch(e){console.error("Error fetching categories:",e),Z("Failed to fetch categories")}finally{w(!1)}},M=j.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())),P=e=>{b(e),y(!0)},S=async e=>{let t=j.find(t=>t.id===e);if(!t)return;let a=(t._count.products||0)+(t._count.productCategories||0);if(confirm(a>0?`This category has ${a} product(s). You need to remove or reassign these products before deleting the category. Continue anyway?`:"Are you sure you want to delete this category?"))try{let t=await fetch(`/api/categories/${e}`,{method:"DELETE"}),a=await t.json();t.ok?(await C(),alert("Category deleted successfully")):alert(a.error||"Failed to delete category")}catch(e){console.error("Error deleting category:",e),alert("Failed to delete category")}};return r.jsx("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Categories"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your product categories"})]}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[r.jsx("div",{className:"flex items-center gap-4",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Search categories...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),(0,r.jsxs)("button",{onClick:()=>{g(!0)},className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[r.jsx(c.Z,{className:"w-5 h-5"}),"Add Category"]})]})}),N?r.jsx("div",{className:"flex items-center justify-center py-12",children:r.jsx(d.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):k?r.jsx("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(o.Z,{className:"w-5 h-5 text-red-600"}),r.jsx("p",{className:"text-red-800",children:k})]})}):0===M.length?r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(x,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No categories found"}),r.jsx("p",{className:"text-gray-600",children:"Get started by creating your first category."})]})}):r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[r.jsx("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Products"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),r.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:M.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),r.jsx("div",{className:"text-xs text-gray-500",children:e.slug})]})}),r.jsx("td",{className:"px-6 py-4",children:r.jsx("div",{className:"text-sm text-gray-600",children:e.description||"-"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(u.Z,{className:"w-4 h-4 text-gray-400"}),r.jsx("span",{className:"text-sm text-gray-900",children:(e._count.products||0)+(e._count.productCategories||0)})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-3 py-1 text-xs font-medium rounded-full ${e.isActive?"bg-green-100 text-green-700 border border-green-200":"bg-gray-100 text-gray-700 border border-gray-200"}`,children:e.isActive?"Active":"Inactive"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-600",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right",children:[r.jsx("button",{onClick:()=>P(e),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors mr-2",children:r.jsx(h.Z,{className:"w-5 h-5"})}),r.jsx("button",{onClick:()=>S(e.id),className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:r.jsx(m.Z,{className:"w-5 h-5"})})]})]},e.id))})]})})}),r.jsx(()=>{let[e,t]=(0,s.useState)({name:"",description:""}),[i,n]=(0,s.useState)(!1),c=async a=>{a.preventDefault(),n(!0);try{let a=await fetch("/api/categories",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(a.ok){let e=await a.json();e.success?(v(t=>[...t,e.data]),g(!1),t({name:"",description:""})):alert("Failed to create category: "+e.error)}else alert("Failed to create category")}catch(e){console.error("Error creating category:",e),alert("Failed to create category")}finally{n(!1)}};return a?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl max-w-md w-full",children:[r.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Add New Category"}),r.jsx("button",{onClick:()=>g(!1),className:"text-gray-500 hover:text-gray-700",children:r.jsx(l.Z,{className:"w-6 h-6"})})]})}),(0,r.jsxs)("form",{onSubmit:c,className:"p-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),r.jsx("input",{type:"text",value:e.name,onChange:a=>t({...e,name:a.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),r.jsx("textarea",{value:e.description,onChange:a=>t({...e,description:a.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:4})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[r.jsx("button",{type:"button",onClick:()=>g(!1),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Cancel"}),r.jsx("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50",disabled:i,children:i?"Creating...":"Add Category"})]})]})]})}):null},{}),r.jsx(()=>{let[e,t]=(0,s.useState)({name:f?.name||"",description:f?.description||""}),[a,n]=(0,s.useState)(!1);i().useEffect(()=>{f&&t({name:f.name,description:f.description||""})},[f]);let c=async t=>{if(t.preventDefault(),f){n(!0);try{let t=await fetch(`/api/categories/${f.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(t.ok){let e=await t.json();e.success?(v(t=>t.map(t=>t.id===f.id?e.data:t)),y(!1),b(null)):alert("Failed to update category: "+e.error)}else alert("Failed to update category")}catch(e){console.error("Error updating category:",e),alert("Failed to update category")}finally{n(!1)}}};return p?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl max-w-md w-full",children:[r.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Edit Category"}),r.jsx("button",{onClick:()=>{y(!1),b(null)},className:"text-gray-500 hover:text-gray-700",children:r.jsx(l.Z,{className:"w-6 h-6"})})]})}),(0,r.jsxs)("form",{onSubmit:c,className:"p-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),r.jsx("input",{type:"text",value:e.name,onChange:a=>t({...e,name:a.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),r.jsx("textarea",{value:e.description,onChange:a=>t({...e,description:a.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:4})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[r.jsx("button",{type:"button",onClick:()=>{y(!1),b(null)},className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Cancel"}),r.jsx("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50",disabled:a,children:a?"Saving...":"Save Changes"})]})]})]})}):null},{})]})})}},2802:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>C});var r=a(10326),s=a(17577),i=a.n(s),l=a(90434),n=a(35047),c=a(77109),d=a(24319),o=a(48705),x=a(34565),u=a(57671),h=a(24061),m=a(40765),g=a(40617),p=a(35351),y=a(6507),f=a(5932),b=a(71709),j=a(95920),v=a(88378),N=a(94019),w=a(90748),k=a(53080),Z=a(71810);let C=({children:e})=>{let t=(0,n.usePathname)(),[a,s]=i().useState(!1),[C,M]=i().useState(!1),P=async()=>{if(!C)try{M(!0),s(!1),await (0,c.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},S=[{href:"/admin",label:"Dashboard",icon:d.Z},{href:"/admin/products",label:"Products",icon:o.Z},{href:"/admin/categories",label:"Categories",icon:x.Z},{href:"/admin/orders",label:"Orders",icon:u.Z},{href:"/admin/customers",label:"Customers",icon:h.Z},{href:"/admin/coupons",label:"Coupons",icon:m.Z},{href:"/admin/reviews",label:"Reviews",icon:g.Z},{href:"/admin/enquiry",label:"Enquiries",icon:p.Z},{href:"/admin/notifications",label:"Notifications",icon:y.Z},{href:"/admin/newsletter",label:"Newsletter",icon:f.Z},{href:"/admin/media",label:"Media",icon:b.Z},{href:"/admin/homepage",label:"Homepage",icon:j.Z},{href:"/admin/settings",label:"Settings",icon:v.Z}],q=e=>"/admin"===e?"/admin"===t:t.startsWith(e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:r.jsx("button",{onClick:()=>s(!a),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:a?r.jsx(N.Z,{className:"w-6 h-6 text-gray-600"}):r.jsx(w.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,r.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${a?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[r.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:r.jsx(k.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),r.jsx("nav",{className:"mt-6 px-3",children:S.map(e=>{let t=e.icon,a=q(e.href);return(0,r.jsxs)(l.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${a?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>s(!1),children:[r.jsx(t,{className:`w-5 h-5 mr-3 ${a?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,r.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[r.jsx(l.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,r.jsxs)("button",{onClick:P,disabled:C,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx(Z.Z,{className:"w-4 h-4 mr-2"}),C?"Logging out...":"Logout"]})]})]}),r.jsx("main",{className:"lg:ml-64",children:r.jsx("div",{className:"p-4 lg:p-8",children:e})}),a&&r.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>s(!1)})]})}},87888:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6507:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},35351:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},95920:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},75290:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},83855:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88307:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88378:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69508:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},40765:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},24061:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35047:(e,t,a)=>{"use strict";var r=a(77389);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},14285:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\categories\page.tsx#default`)},90596:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[9276,3757,434,9536],()=>a(93077));module.exports=r})();