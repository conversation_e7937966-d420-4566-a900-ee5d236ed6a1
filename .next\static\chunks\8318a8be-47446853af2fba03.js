"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[411],{71853:function(e,a,t){t.d(a,{UX3:function(){return y},eqj:function(){return i}});var s=t(11602),o=t(57201);t(2171);var n=t(4598),r=t(99538),c=t(32349),d=t(72838);let y=async(e,a)=>{let t;let s=(0,o.cu)(e,a),r=(0,n.UI)({},n.gj,{[H]:e[q],[R]:e[K],[W]:e[g],[F]:e[Y],[X]:[()=>(0,n.gj)(e[T]),()=>e[T].toString()]});s.bp("/"),s.p("Bucket",()=>e.Bucket,"{Bucket}",!1);let c=(0,n.UI)({[_]:[,""]});return s.m("GET").h(r).q(c).b(t),s.build()},i=async(e,a)=>{if(200!==e.statusCode&&e.statusCode>=300)return l(e,a);let t=(0,n.UI)({$metadata:f(e),[K]:[,e.headers[R]],[g]:[,e.headers[W]],[Y]:[,e.headers[F]],[T]:[()=>void 0!==e.headers[X],()=>(0,n.gx)(e.headers[X])]}),o=(0,n.CE)((0,n.Wh)(await (0,s.he)(e.body,a)),"body");return null!=o[C]&&(t[C]=N(o[C],a)),t},l=async(e,a)=>{let t={...e,body:await (0,s.OH)(e.body,a)},o=(0,s.Js)(e,t.body);switch(o){case"NoSuchUpload":case"com.amazonaws.s3#NoSuchUpload":throw await U(t,a);case"ObjectNotInActiveTierError":case"com.amazonaws.s3#ObjectNotInActiveTierError":throw await x(t,a);case"BucketAlreadyExists":case"com.amazonaws.s3#BucketAlreadyExists":throw await w(t,a);case"BucketAlreadyOwnedByYou":case"com.amazonaws.s3#BucketAlreadyOwnedByYou":throw await m(t,a);case"NoSuchBucket":case"com.amazonaws.s3#NoSuchBucket":throw await S(t,a);case"InvalidObjectState":case"com.amazonaws.s3#InvalidObjectState":throw await I(t,a);case"NoSuchKey":case"com.amazonaws.s3#NoSuchKey":throw await k(t,a);case"NotFound":case"com.amazonaws.s3#NotFound":throw await E(t,a);case"EncryptionTypeMismatch":case"com.amazonaws.s3#EncryptionTypeMismatch":throw await b(t,a);case"InvalidRequest":case"com.amazonaws.s3#InvalidRequest":throw await p(t,a);case"InvalidWriteOffset":case"com.amazonaws.s3#InvalidWriteOffset":throw await z(t,a);case"TooManyParts":case"com.amazonaws.s3#TooManyParts":throw await $(t,a);case"IdempotencyParameterMismatch":case"com.amazonaws.s3#IdempotencyParameterMismatch":throw await h(t,a);case"ObjectAlreadyInActiveTierError":case"com.amazonaws.s3#ObjectAlreadyInActiveTierError":throw await v(t,a);default:return u({output:e,parsedBody:t.body,errorCode:o})}},u=(0,n.PC)(d.k),w=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new r.SH({$metadata:f(e),...t});return(0,n.to)(s,e.body)},m=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new r.M0({$metadata:f(e),...t});return(0,n.to)(s,e.body)},b=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new c.Xc({$metadata:f(e),...t});return(0,n.to)(s,e.body)},h=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new c.K$({$metadata:f(e),...t});return(0,n.to)(s,e.body)},I=async(e,a)=>{let t=(0,n.UI)({}),s=e.body;null!=s[B]&&(t[B]=(0,n.pY)(s[B])),null!=s[j]&&(t[j]=(0,n.pY)(s[j]));let o=new r.oN({$metadata:f(e),...t});return(0,n.to)(o,e.body)},p=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new c.sd({$metadata:f(e),...t});return(0,n.to)(s,e.body)},z=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new c.Nx({$metadata:f(e),...t});return(0,n.to)(s,e.body)},S=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new r.mn({$metadata:f(e),...t});return(0,n.to)(s,e.body)},k=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new r.Yw({$metadata:f(e),...t});return(0,n.to)(s,e.body)},U=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new r.wi({$metadata:f(e),...t});return(0,n.to)(s,e.body)},E=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new r.TX({$metadata:f(e),...t});return(0,n.to)(s,e.body)},v=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new c.T8({$metadata:f(e),...t});return(0,n.to)(s,e.body)},x=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new r.Vn({$metadata:f(e),...t});return(0,n.to)(s,e.body)},$=async(e,a)=>{let t=(0,n.UI)({});e.body;let s=new c.g0({$metadata:f(e),...t});return(0,n.to)(s,e.body)},N=(e,a)=>{let t={};return null!=e[A]&&(t[A]=(0,n.pY)(e[A])),null!=e[M]&&(t[M]=(0,n.pY)(e[M])),null!=e[P]&&(t[P]=(0,n.pY)(e[P])),null!=e[O]&&(t[O]=(0,n.CE)((0,n.aH)(e[O]))),t},f=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),A="AccessKeyId",B="AccessTier",T="BucketKeyEnabled",C="Credentials",O="Expiration",M="SecretAccessKey",j="StorageClass",q="SessionMode",K="ServerSideEncryption",Y="SSEKMSEncryptionContext",g="SSEKMSKeyId",P="SessionToken",_="session",H="x-amz-create-session-mode",R="x-amz-server-side-encryption",W="x-amz-server-side-encryption-aws-kms-key-id",X="x-amz-server-side-encryption-bucket-key-enabled",F="x-amz-server-side-encryption-context"}}]);