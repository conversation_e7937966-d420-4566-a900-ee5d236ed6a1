"use strict";(()=>{var e={};e.id=9361,e.ids=[9361],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},11500:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>O,requestAsyncStorage:()=>x,routeModule:()=>j,serverHooks:()=>b,staticGenerationAsyncStorage:()=>v});var n={};r.r(n),r.d(n,{DELETE:()=>y,PUT:()=>f});var s=r(49303),o=r(88716),i=r(60670),a=r(87070),u=r(75571),d=r(95306),p=r(89456),c=r(11185),l=r(81515);async function f(e,{params:t}){try{let r=await (0,u.getServerSession)(d.L);if(!r?.user||"ADMIN"!==r.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});await (0,p.Z)();let{id:n,variationId:s}=t,{name:o,value:i,price:f,pricingMode:y}=await e.json();if(!o||!i)return a.NextResponse.json({error:"Name and value are required"},{status:400});let j=y&&["REPLACE","INCREMENT","FIXED"].includes(y)?y:"INCREMENT",x=await l.Th.findById(c.Types.ObjectId.isValid(s)?new c.Types.ObjectId(s):s).lean();if(!x)return a.NextResponse.json({error:"Variation not found"},{status:404});if(String(x.productId)!==String(n))return a.NextResponse.json({error:"Variation does not belong to this product"},{status:400});if(await l.Th.findOne({_id:{$ne:c.Types.ObjectId.isValid(s)?new c.Types.ObjectId(s):s},productId:c.Types.ObjectId.isValid(n)?new c.Types.ObjectId(n):n,name:o,value:i}).lean())return a.NextResponse.json({error:"Another variation with this name and value already exists"},{status:400});let v=null;if(null!=f&&""!==f){let e="string"==typeof f?parseFloat(f):Number(f);!isNaN(e)&&isFinite(e)&&(v=e)}let b=await l.Th.findByIdAndUpdate(c.Types.ObjectId.isValid(s)?new c.Types.ObjectId(s):s,{name:o.trim(),value:i.trim(),price:v,pricingMode:j,updatedAt:new Date},{new:!0}).lean();return a.NextResponse.json({success:!0,data:b,message:"Variation updated successfully"})}catch(e){return console.error("Error updating variation:",e),a.NextResponse.json({success:!1,error:"Failed to update variation"},{status:500})}}async function y(e,{params:t}){try{let e=await (0,u.getServerSession)(d.L);if(!e?.user||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});await (0,p.Z)();let{id:r,variationId:n}=t,s=await l.Th.findById(c.Types.ObjectId.isValid(n)?new c.Types.ObjectId(n):n).lean();if(!s)return a.NextResponse.json({error:"Variation not found"},{status:404});if(String(s.productId)!==String(r))return a.NextResponse.json({error:"Variation does not belong to this product"},{status:400});return await l.Th.deleteOne({_id:c.Types.ObjectId.isValid(n)?new c.Types.ObjectId(n):n}),a.NextResponse.json({success:!0,message:"Variation deleted successfully"})}catch(e){return console.error("Error deleting variation:",e),a.NextResponse.json({success:!1,error:"Failed to delete variation"},{status:500})}}let j=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/products/[id]/variations/[variationId]/route",pathname:"/api/products/[id]/variations/[variationId]",filename:"route",bundlePath:"app/api/products/[id]/variations/[variationId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\[variationId]\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:x,staticGenerationAsyncStorage:v,serverHooks:b}=j,g="/api/products/[id]/variations/[variationId]/route";function O(){return(0,i.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:v})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,5972,8691,2830,5306],()=>r(11500));module.exports=n})();