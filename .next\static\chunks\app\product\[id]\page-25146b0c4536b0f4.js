(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[188],{20157:function(e,s,t){Promise.resolve().then(t.bind(t,28250)),Promise.resolve().then(t.bind(t,65271))},55497:function(e,s,t){"use strict";t.d(s,{Z:function(){return y}});var a=t(57437);t(2265);var l=t(27648),r=t(89547),i=t(82023),n=t(66927),c=t(88997),d=t(44794),o=t(48757),x=t(86595),m=t(42208),h=t(12805),u=t(38220),g=t(11239),j=t(9646),p=t(80512),f=t(93609),b=t(94394),v=t(33741),y=e=>{let{product:s,showAsLinks:t=!1,className:y="",maxCategories:N,size:w="xs"}=e,S=function(e){let s=[];return e.productCategories&&e.productCategories.length>0&&e.productCategories.forEach(e=>{e.category&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug})}),e.category&&!s.some(s=>s.id===e.category.id)&&s.push({id:e.category.id,name:e.category.name,slug:e.category.slug}),s}(s),k=N?S.slice(0,N):S;if(0===k.length)return null;let Z={xs:"text-xs",sm:"text-sm",base:"text-base",lg:"text-lg"},C={xs:"w-3 h-3",sm:"w-4 h-4",base:"w-5 h-5",lg:"w-6 h-6"},E=e=>{let s=C[w],t=e.toLowerCase(),l={skincare:(0,a.jsx)(r.Z,{className:s}),"hair care":(0,a.jsx)(i.Z,{className:s}),"hair oils":(0,a.jsx)(n.Z,{className:s}),"body care":(0,a.jsx)(c.Z,{className:s}),"all products":(0,a.jsx)(d.Z,{className:s}),"cleansers & face wash":(0,a.jsx)(o.Z,{className:s}),"combo & complete care":(0,a.jsx)(x.Z,{className:s}),"creams & moisturizers":(0,a.jsx)(n.Z,{className:s}),"eye & lip care":(0,a.jsx)(m.Z,{className:s}),"facial kits":(0,a.jsx)(h.Z,{className:s}),"facial oils & elixirs":(0,a.jsx)(u.Z,{className:s}),"gels & serums":(0,a.jsx)(g.Z,{className:s}),"hair masks":(0,a.jsx)(j.Z,{className:s}),"massage & body oils":(0,a.jsx)(p.Z,{className:s}),"scrubs & exfoliants":(0,a.jsx)(i.Z,{className:s}),toners:(0,a.jsx)(f.Z,{className:s}),"ubtan & masks":(0,a.jsx)(b.Z,{className:s}),cleanser:(0,a.jsx)(o.Z,{className:s}),serum:(0,a.jsx)(g.Z,{className:s}),moisturizer:(0,a.jsx)(n.Z,{className:s}),mask:(0,a.jsx)(j.Z,{className:s}),exfoliator:(0,a.jsx)(i.Z,{className:s}),"eye-care":(0,a.jsx)(m.Z,{className:s}),oil:(0,a.jsx)(n.Z,{className:s}),toner:(0,a.jsx)(f.Z,{className:s}),kit:(0,a.jsx)(h.Z,{className:s}),ubtan:(0,a.jsx)(b.Z,{className:s}),baby:(0,a.jsx)(v.Z,{className:s})};if(l[t])return l[t];for(let[e,s]of Object.entries(l))if(t.includes(e)||e.includes(t))return s;return(0,a.jsx)(r.Z,{className:s})};return(0,a.jsx)("div",{className:"flex flex-wrap gap-3 ".concat(y),children:k.map(e=>{let s=E(e.name);return t?(0,a.jsxs)(l.default,{href:"/shop?category=".concat(e.slug),className:"inline-flex items-center gap-1.5 ".concat(Z[w]," font-medium text-green-600 hover:text-green-700 transition-colors"),children:[s,(0,a.jsx)("span",{children:e.name})]},e.id):(0,a.jsxs)("span",{className:"inline-flex items-center gap-1.5 ".concat(Z[w]," font-medium text-green-600"),children:[s,(0,a.jsx)("span",{children:e.name})]},e.id)})})}},65271:function(e,s,t){"use strict";t.d(s,{default:function(){return R}});var a=t(57437),l=t(2265),r=t(99376),i=t(80605),n=t(32660),c=t(88997),d=t(68919),o=t(86595),x=t(30401),m=t(16275),h=t(53827),u=t(22135),g=t(40875),j=e=>{let{productId:s}=e,[t,r]=(0,l.useState)([]),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null);(0,l.useEffect)(()=>{o()},[s]);let o=async()=>{try{n(!0);let e=await fetch("/api/products/".concat(s,"/faqs")),t=await e.json();t.success&&r(t.data)}catch(e){console.error("Error fetching FAQs:",e)}finally{n(!1)}},x=e=>{d(c===e?null:e)};return i?(0,a.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-100 rounded"})]},e))}):0===t.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("div",{className:"text-gray-500",children:"No frequently asked questions available for this product."})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Frequently Asked Questions"}),(0,a.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,a.jsx)("button",{onClick:()=>x(e.id),className:"w-full px-6 py-4 text-left bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-inset",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 pr-4",children:e.question}),(0,a.jsx)("div",{className:"flex-shrink-0",children:c===e.id?(0,a.jsx)(u.Z,{className:"w-5 h-5 text-gray-500"}):(0,a.jsx)(g.Z,{className:"w-5 h-5 text-gray-500"})})]})}),c===e.id&&(0,a.jsx)("div",{className:"px-6 pb-4 bg-gray-50",children:(0,a.jsx)("div",{className:"pt-2 text-gray-700 whitespace-pre-wrap leading-relaxed",children:e.answer})})]},e.id))}),(0,a.jsx)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"Have a question that's not answered here?"})," Feel free to contact our customer support team for more information about this product."]})})]})},p=e=>{let{productId:s,basePrice:t,onVariationChange:r}=e,[i,n]=(0,l.useState)([]),[c,d]=(0,l.useState)(!0),[o,x]=(0,l.useState)({});(0,l.useEffect)(()=>{m()},[s]),(0,l.useEffect)(()=>{if(i.length>0&&0===Object.keys(o).length){let e=i.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),s={};Object.entries(e).forEach(e=>{let[t,a]=e;s[t]=a[a.length-1]}),x(s);let t=u(Object.values(s)),a=1===Object.keys(s).length?Object.values(s)[0]:null;r&&r(a,t)}},[i]);let m=async()=>{try{d(!0);let e=await fetch("/api/products/".concat(s,"/variations")),t=await e.json();t.success&&n(t.data)}catch(e){console.error("Error fetching variations:",e)}finally{d(!1)}},h=i.reduce((e,s)=>(e[s.name]||(e[s.name]=[]),e[s.name].push(s),e),{}),u=e=>{if(0===e.length)return 0;if(1===e.length){let s=e[0];if(void 0!==s.price&&null!==s.price&&s.price>0)return s.price}let s=e.filter(e=>void 0!==e.price&&null!==e.price&&e.price>0);if(s.length>0){var t;return null!==(t=s.reduce((e,s)=>{var t,a;return(null!==(t=s.price)&&void 0!==t?t:0)>(null!==(a=e.price)&&void 0!==a?a:0)?s:e}).price)&&void 0!==t?t:0}return 0},g=(e,s)=>{let t={...o,[e]:s};x(t);let a=u(Object.values(t)),l=1===Object.keys(t).length?Object.values(t)[0]:null;r&&r(l,a)},j=e=>!0;return c?(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),(0,a.jsx)("div",{className:"flex space-x-2",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"h-10 w-16 bg-gray-200 rounded"},e))})]})}):0===i.length?null:((()=>{let e=Object.values(o);return 0===e.length?null:{totalPrice:u(e),selectedValues:e}})(),(0,a.jsxs)("div",{className:"space-y-6",children:[Object.entries(h).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 capitalize",children:s}),o[s]&&(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Selected: ",o[s].value]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>{var t;let l=(null===(t=o[s])||void 0===t?void 0:t.id)===e.id,r=j(e);return(0,a.jsx)("button",{onClick:()=>r&&g(s,e),disabled:!r,className:"\n                    px-4 py-2 border rounded-lg text-sm font-medium transition-colors\n                    ".concat(l?"border-green-500 bg-green-50 text-green-700":r?"border-gray-300 bg-white text-gray-700 hover:border-gray-400":"border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed","\n                  "),children:(0,a.jsx)("span",{children:e.value})},e.id)})})]},s)}),Object.keys(h).length>0&&Object.keys(o).length<Object.keys(h).length&&(0,a.jsx)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-yellow-800",children:"Please select all required options before adding to cart."})})]}))},f=t(43391),b=t(92451),v=t(10407),y=t(32489),N=e=>{let{images:s,productName:t}=e,[r,i]=(0,l.useState)(0),[n,c]=(0,l.useState)(!1),[d,o]=(0,l.useState)(!0),[x,m]=(0,l.useState)(null),[h,u]=(0,l.useState)(null),g=[...s].sort((e,s)=>e.position-s.position);(0,l.useEffect)(()=>{g.length>0&&o(!1)},[g]);let j=e=>{i(e)},p=()=>{i(e=>0===e?g.length-1:e-1)},N=()=>{i(e=>e===g.length-1?0:e+1)},w=()=>{c(!1)},S=e=>{n&&("Escape"===e.key&&w(),"ArrowLeft"===e.key&&p(),"ArrowRight"===e.key&&N())};if((0,l.useEffect)(()=>(document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)),[n]),!g||0===g.length)return(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)("div",{className:"aspect-square bg-gray-200 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-300 rounded-lg mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No images available"})]})})});let k=g[r];return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"relative mb-4 group",children:(0,a.jsxs)("div",{className:"aspect-square relative overflow-hidden rounded-lg bg-gray-100",onTouchStart:e=>{u(null),m(e.targetTouches[0].clientX)},onTouchMove:e=>{u(e.targetTouches[0].clientX)},onTouchEnd:()=>{if(!x||!h)return;let e=x-h;e>50&&g.length>1&&N(),e<-50&&g.length>1&&p()},children:[d&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse rounded-lg"}),(0,a.jsx)("img",{src:k.url,alt:k.alt||"".concat(t," - Image ").concat(r+1),className:"w-full h-full object-cover cursor-zoom-in transition-transform duration-300 hover:scale-105 select-none",onClick:()=>{c(!0)},onLoad:()=>o(!1),draggable:!1}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)("div",{className:"bg-white bg-opacity-90 p-2 rounded-full",children:(0,a.jsx)(f.Z,{className:"w-6 h-6 text-gray-700"})})})}),g.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:p,className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:(0,a.jsx)(b.Z,{className:"w-5 h-5 text-gray-700"})}),(0,a.jsx)("button",{onClick:N,className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity hidden md:block",children:(0,a.jsx)(v.Z,{className:"w-5 h-5 text-gray-700"})})]}),g.length>1&&(0,a.jsxs)("div",{className:"absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded",children:[r+1," / ",g.length]})]})}),g.length>1&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"hidden md:flex space-x-2 overflow-x-auto pb-2",children:g.map((e,s)=>(0,a.jsx)("button",{onClick:()=>j(s),className:"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ".concat(s===r?"border-green-500 ring-2 ring-green-200":"border-gray-200 hover:border-gray-300"),children:(0,a.jsx)("img",{src:e.url,alt:e.alt||"".concat(t," thumbnail ").concat(s+1),className:"w-full h-full object-cover"})},e.id||s))}),(0,a.jsx)("div",{className:"md:hidden flex space-x-2 overflow-x-auto pb-2",children:g.map((e,s)=>(0,a.jsx)("button",{onClick:()=>j(s),className:"flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all ".concat(s===r?"border-green-500 ring-1 ring-green-200":"border-gray-200"),children:(0,a.jsx)("img",{src:e.url,alt:e.alt||"".concat(t," thumbnail ").concat(s+1),className:"w-full h-full object-cover"})},e.id||s))}),(0,a.jsxs)("div",{className:"md:hidden flex justify-center space-x-4",children:[(0,a.jsx)("button",{onClick:p,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:(0,a.jsx)(b.Z,{className:"w-5 h-5 text-gray-700"})}),(0,a.jsx)("button",{onClick:N,className:"bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors",children:(0,a.jsx)(v.Z,{className:"w-5 h-5 text-gray-700"})})]})]}),n&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"relative max-w-4xl max-h-full",children:[(0,a.jsx)("button",{onClick:w,className:"absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full z-10",children:(0,a.jsx)(y.Z,{className:"w-6 h-6"})}),g.length>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:p,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:(0,a.jsx)(b.Z,{className:"w-6 h-6"})}),(0,a.jsx)("button",{onClick:N,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full z-10",children:(0,a.jsx)(v.Z,{className:"w-6 h-6"})})]}),(0,a.jsx)("img",{src:k.url,alt:k.alt||"".concat(t," - Image ").concat(r+1),className:"max-w-full max-h-full object-contain"}),(0,a.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-center",children:[r+1," of ",g.length,k.alt&&" - ".concat(k.alt)]})})]})})]})},w=t(55497),S=t(91723),k=t(92369),Z=t(15863),C=e=>{let{productId:s,onReviewSubmitted:t}=e,{data:r}=(0,i.useSession)(),[n,c]=(0,l.useState)(0),[d,x]=(0,l.useState)(""),[m,h]=(0,l.useState)(""),[u,g]=(0,l.useState)(!1),[j,p]=(0,l.useState)(null),[f,b]=(0,l.useState)(!1),v=async e=>{if(e.preventDefault(),!(null==r?void 0:r.user)){p("Please sign in to leave a review");return}if(0===n){p("Please select a rating");return}g(!0),p(null);try{let e=await fetch("/api/products/".concat(s,"/reviews"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({rating:n,title:d,content:m})}),a=await e.json();a.success?(b(!0),c(0),x(""),h(""),null==t||t()):p(a.error||"Failed to submit review")}catch(e){p("Failed to submit review")}finally{g(!1)}};return(null==r?void 0:r.user)?f?null:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Write a Review"}),(0,a.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rating *"}),(0,a.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>c(e),className:"p-1 hover:scale-110 transition-transform",children:(0,a.jsx)(o.Z,{className:"w-6 h-6 ".concat(e<=n?"text-yellow-400 fill-current":"text-gray-300")})},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Title"}),(0,a.jsx)("input",{type:"text",value:d,onChange:e=>x(e.target.value),placeholder:"Summarize your experience",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:100})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Review Details"}),(0,a.jsx)("textarea",{value:m,onChange:e=>h(e.target.value),placeholder:"Tell us about your experience with this product",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",maxLength:1e3})]}),j&&(0,a.jsx)("div",{className:"text-red-600 text-sm",children:j}),(0,a.jsx)("button",{type:"submit",disabled:u||0===n,className:"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Z.Z,{className:"w-4 h-4 inline mr-2 animate-spin"}),"Submitting..."]}):"Submit Review"})]})]}):(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Please sign in to leave a review"})})},E=e=>{var s;let{productId:t}=e,{data:r}=(0,i.useSession)(),[n,c]=(0,l.useState)([]),[d,x]=(0,l.useState)(!0),[m,h]=(0,l.useState)(null),[u,g]=(0,l.useState)(!1),[j,p]=(0,l.useState)(!1);(0,l.useEffect)(()=>{f()},[t]);let f=async()=>{try{x(!0);let e=await fetch("/api/products/".concat(t,"/reviews")),s=await e.json();s.success?c(s.data):h(s.error||"Failed to fetch reviews")}catch(e){h("Failed to fetch reviews")}finally{x(!1)}},b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),v=n.filter(e=>"APPROVED"===e.status),y=v.length>0?(v.reduce((e,s)=>e+s.rating,0)/v.length).toFixed(1):0,N=n.find(e=>{var s,t;return"PENDING"===e.status&&(null===(s=e.user)||void 0===s?void 0:s.id)===(null==r?void 0:null===(t=r.user)||void 0===t?void 0:t.id)});return d?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900",children:["Customer Reviews (",v.length,")"]}),!N&&(0,a.jsx)("button",{onClick:()=>g(!u),className:"text-green-600 hover:text-green-700 font-medium",children:"Write a Review"})]}),v.length>0&&(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(e=>(0,a.jsx)(o.Z,{className:"w-5 h-5 ".concat(e<=Math.round(Number(y))?"text-yellow-400 fill-current":"text-gray-300")},e))}),(0,a.jsx)("span",{className:"text-lg font-semibold",children:y}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",v.length," reviews)"]})]})}),j&&N&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(S.Z,{className:"w-5 h-5 text-green-600 mt-0.5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-green-800 font-medium",children:"Thank you for your review!"}),(0,a.jsx)("p",{className:"text-green-600 text-sm mt-1",children:"Your review has been submitted and is pending approval. It will appear below once approved by our team."})]})]})}),u&&(0,a.jsx)(C,{productId:t,onReviewSubmitted:()=>{g(!1),p(!0),f(),setTimeout(()=>p(!1),5e3)}}),N&&(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("div",{className:"bg-yellow-50 rounded-lg shadow-sm border border-yellow-200 p-4",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center",children:(null===(s=N.user)||void 0===s?void 0:s.avatar)?(0,a.jsx)("img",{src:N.user.avatar,alt:N.user.name||"User",className:"w-10 h-10 rounded-full"}):(0,a.jsx)(k.Z,{className:"w-5 h-5 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:"Your Review"}),(0,a.jsxs)("span",{className:"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full flex items-center space-x-1",children:[(0,a.jsx)(S.Z,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Pending Approval"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(e=>(0,a.jsx)(o.Z,{className:"w-4 h-4 ".concat(e<=N.rating?"text-yellow-400 fill-current":"text-gray-300")},e))}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:b(N.createdAt)})]}),N.title&&(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:N.title}),N.content&&(0,a.jsx)("p",{className:"text-gray-700 text-sm",children:N.content})]})]})})})}),0!==v.length||N?(0,a.jsx)("div",{className:"space-y-4",children:v.map(e=>{var s,t;return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center",children:(null===(s=e.user)||void 0===s?void 0:s.avatar)?(0,a.jsx)("img",{src:e.user.avatar,alt:e.user.name||"User",className:"w-10 h-10 rounded-full"}):(0,a.jsx)(k.Z,{className:"w-5 h-5 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(null===(t=e.user)||void 0===t?void 0:t.name)||"Anonymous"}),e.isVerified&&(0,a.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Verified Purchase"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(s=>(0,a.jsx)(o.Z,{className:"w-4 h-4 ".concat(s<=e.rating?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:b(e.createdAt)})]}),e.title&&(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:e.title}),e.content&&(0,a.jsx)("p",{className:"text-gray-700 text-sm",children:e.content})]})]})})},e.id)})}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(o.Z,{className:"w-12 h-12 mx-auto"})}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"No reviews yet"}),(0,a.jsx)("button",{onClick:()=>g(!0),className:"text-green-600 hover:text-green-700 font-medium",children:"Be the first to write a review"})]})]})},F=t(13905),P=t(1396),O=t(8329),D=t(92840),A=t(19124);let T=e=>{var s,t;let a=e.reviews||[],l=a.length>0?a.reduce((e,s)=>e+s.rating,0)/a.length:0,r=[];if(e.description){let s=e.description.match(/benefits?:\s*([^.]+)/i);s&&r.push(...s[1].split(",").map(e=>e.trim()).filter(e=>e))}return{id:e.id,name:e.name,description:e.description||"",shortDescription:e.shortDescription||"",price:e.price||0,image:(null===(s=e.images[0])||void 0===s?void 0:s.url)||"/images/default-product.jpg",images:e.images.map(s=>({id:s.id,url:s.url,alt:s.alt||e.name,position:s.position||0})),category:(null===(t=e.category)||void 0===t?void 0:t.slug)||"skincare",featured:e.isFeatured,benefits:r,rating:Math.round(10*l)/10,reviews:a.length,_raw:e}};var R=e=>{let{id:s}=e,t=(0,r.useRouter)(),{data:u}=(0,i.useSession)(),{dispatch:g}=(0,h.j)(),{flashSaleSettings:f}=(0,P.u)(),{showToast:b}=(0,D.V)(),{refreshUnreadCount:v,refreshNotifications:y}=(0,A.z)(),[S,k]=(0,l.useState)("description"),[Z,C]=(0,l.useState)(!1),[R,L]=(0,l.useState)(null),[I,z]=(0,l.useState)(1),[q,M]=(0,l.useState)(null),[_,B]=(0,l.useState)(!0),[V,Q]=(0,l.useState)(null),[U,J]=(0,l.useState)(0),[W,G]=(0,l.useState)(!1),[H,X]=(0,l.useState)(!1),[Y,K]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=async()=>{try{B(!0);let e=await fetch("/api/products/".concat(s)),t=await e.json();if(t.success){let e=T(t.data);M(e);let s=t.data.variants||[];K(s.length>0),0===s.length&&J(e.price)}else Q("Product not found")}catch(e){console.error("Error fetching product:",e),Q("Failed to load product")}finally{B(!1)}};s&&e()},[s]),(0,l.useEffect)(()=>{(async()=>{var e;if((null==u?void 0:null===(e=u.user)||void 0===e?void 0:e.id)&&(null==q?void 0:q.id))try{let e=await fetch("/api/wishlist");if(e.ok){let s=(await e.json()).items.some(e=>e.id===q.id);G(s)}}catch(e){console.error("Error checking wishlist status:",e)}})()},[u,q]);let $=(e,s)=>{L(e),J(s)},ee=()=>{if(!q)return;let e=(0,O.FO)(U,f);g({type:"ADD_ITEM",payload:{...q,price:e.salePrice},selectedVariants:R?[{id:R.id,name:R.name,value:R.value,price:R.price}]:void 0}),C(!0),setTimeout(()=>C(!1),2e3)},es=async()=>{var e;if(!(null==u?void 0:null===(e=u.user)||void 0===e?void 0:e.id)){t.push("/login");return}if(null==q?void 0:q.id){X(!0);try{if(W){let e=await fetch("/api/wishlist?productId=".concat(q.id),{method:"DELETE"});if(e.ok)G(!1),b("Removed from wishlist","success"),v(),y();else{let s=await e.json();b(s.error||"Failed to remove from wishlist","error")}}else{let e=await fetch("/api/wishlist",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:q.id})});if(e.ok)G(!0),b("Added to wishlist","success"),v(),y();else{let s=await e.json();b(s.error||"Failed to add to wishlist","error")}}}catch(e){console.error("Error updating wishlist:",e),b("An unexpected error occurred.","error")}finally{X(!1)}}},et=async()=>{let e={title:(null==q?void 0:q.name)||"Check out this product",text:(null==q?void 0:q.shortDescription)||"Amazing product from Herbalicious",url:window.location.href};try{navigator.share&&navigator.canShare(e)?await navigator.share(e):(await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!"))}catch(e){console.error("Error sharing:",e);try{await navigator.clipboard.writeText(window.location.href),alert("Product link copied to clipboard!")}catch(e){console.error("Clipboard error:",e),alert("Unable to share. Please copy the URL manually.")}}};if(_)return(0,a.jsx)(F.QW,{});if(V||!q)return(0,a.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:V||"Product not found"}),(0,a.jsx)("button",{onClick:()=>t.back(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Go Back"})]})});let ea=(0,O.FO)(U,f),el=ea.salePrice,er=ea.isOnSale&&U>0;return(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden bg-white",children:[(0,a.jsxs)("div",{className:"sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b",children:[(0,a.jsx)("button",{onClick:()=>t.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(n.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:es,disabled:H,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(c.Z,{className:"w-5 h-5 ".concat(W?"text-red-500 fill-current":"text-gray-600")})}),(0,a.jsx)("button",{onClick:et,className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(d.Z,{className:"w-5 h-5 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"relative p-4",children:[(0,a.jsx)(N,{images:q.images||[],productName:q.name}),(0,a.jsx)("div",{className:"absolute top-8 right-8 bg-white rounded-full p-2 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(o.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:q.rating})]})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:q.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:q.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(w.Z,{product:q._raw||q,showAsLinks:!0,size:"base"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(o.Z,{className:"w-4 h-4 ".concat(s<Math.floor((null==q?void 0:q.rating)||0)?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",(null==q?void 0:q.reviews)||0," reviews)"]})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:el>0?I>1?"₹".concat((el*I).toFixed(0)):"₹".concat(el):"Loading..."}),er&&(0,a.jsx)("span",{className:"text-xl text-gray-500 line-through",children:I>1?"₹".concat((U*I).toFixed(0)):"₹".concat(U)})]}),I>1&&el>0&&(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["₹",el," \xd7 ",I]}),ea.isOnSale&&ea.discountPercentage>0&&(0,a.jsxs)("span",{className:"text-sm text-red-600 font-semibold mt-1",children:["Save ",ea.discountPercentage,"% - Flash Sale!"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(o.Z,{className:"w-4 h-4 ".concat(s<Math.floor((null==q?void 0:q.rating)||0)?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",(null==q?void 0:q.reviews)||0," reviews)"]})]})]})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("div",{className:"flex border-b border-gray-200",children:[{id:"description",label:"Description"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>(0,a.jsx)("button",{onClick:()=>k(e.id),className:"px-4 py-2 text-sm font-medium transition-colors ".concat(S===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"),children:e.label},e.id))}),(0,a.jsxs)("div",{className:"py-4",children:["description"===S&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:q.description}),q.benefits.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Benefits:"}),(0,a.jsx)("ul",{className:"space-y-1",children:q.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e})]},s))})]})]}),"reviews"===S&&(0,a.jsx)("div",{children:(0,a.jsx)(E,{productId:q.id})}),"faqs"===S&&(0,a.jsx)("div",{children:(0,a.jsx)(j,{productId:q.id})})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(p,{productId:q.id,basePrice:q.price,onVariationChange:$})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>z(Math.max(1,I-1)),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"-"}),(0,a.jsx)("span",{className:"w-10 text-center font-medium",children:I}),(0,a.jsx)("button",{onClick:()=>z(I+1),className:"w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50",children:"+"})]}),(0,a.jsx)("button",{onClick:ee,disabled:Z||0===U,className:"w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ".concat(Z?"bg-green-500 text-white":0===U?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-green-600 text-white hover:bg-green-700"),children:Z?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Add to Cart"})]})})]})]})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsxs)("button",{onClick:()=>t.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors mb-8",children:[(0,a.jsx)(n.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Back to Products"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(N,{images:q.images||[],productName:q.name}),(0,a.jsx)("div",{className:"absolute top-6 right-6 bg-white rounded-full p-3 shadow-lg",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5 text-yellow-400 fill-current"}),(0,a.jsx)("span",{className:"font-medium text-gray-700",children:q.rating})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:q.name}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-6",children:q.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(w.Z,{product:q._raw||q,showAsLinks:!0,size:"lg"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(o.Z,{className:"w-5 h-5 ".concat(s<Math.floor((null==q?void 0:q.rating)||0)?"text-yellow-400 fill-current":"text-gray-300")},s))}),(0,a.jsxs)("span",{className:"text-gray-600",children:["(",(null==q?void 0:q.reviews)||0," reviews)"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-gray-900",children:el>0?I>1?"₹".concat((el*I).toFixed(0)):"₹".concat(el):"Loading..."}),er&&(0,a.jsx)("span",{className:"text-2xl text-gray-500 line-through",children:I>1?"₹".concat((U*I).toFixed(0)):"₹".concat(U)})]}),I>1&&el>0&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["₹",el," \xd7 ",I]}),ea.isOnSale&&ea.discountPercentage>0&&(0,a.jsxs)("div",{className:"text-lg text-red-600 font-semibold mt-2",children:["Save ",ea.discountPercentage,"% - Flash Sale!"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:es,disabled:H,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,a.jsx)(c.Z,{className:"w-6 h-6 ".concat(W?"text-red-500 fill-current":"text-gray-600")})}),(0,a.jsx)("button",{onClick:et,className:"p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,a.jsx)(d.Z,{className:"w-6 h-6 text-gray-600"})})]})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(p,{productId:q.id,basePrice:q.price,onVariationChange:$})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:"Quantity:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>z(Math.max(1,I-1)),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg transition-colors",children:"-"}),(0,a.jsx)("span",{className:"w-12 text-center font-medium text-lg",children:I}),(0,a.jsx)("button",{onClick:()=>z(I+1),className:"w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg transition-colors",children:"+"})]})]}),(0,a.jsx)("button",{onClick:ee,disabled:Z||0===U,className:"w-full py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-3 text-lg ".concat(Z?"bg-green-500 text-white":0===U?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-green-600 text-white hover:bg-green-700"),children:Z?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.Z,{className:"w-6 h-6"}),(0,a.jsx)("span",{children:"Added!"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{className:"w-6 h-6"}),(0,a.jsx)("span",{children:"Add to Cart"})]})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex border-b border-gray-200 mb-6",children:[{id:"description",label:"Description"},{id:"reviews",label:"Reviews"},{id:"faqs",label:"FAQs"}].map(e=>(0,a.jsx)("button",{onClick:()=>k(e.id),className:"px-6 py-3 font-medium transition-colors ".concat(S===e.id?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"),children:e.label},e.id))}),(0,a.jsxs)("div",{children:["description"===S&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 mb-6 leading-relaxed",children:q.description}),q.benefits.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Benefits:"}),(0,a.jsx)("ul",{className:"space-y-3",children:q.benefits.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.Z,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("span",{className:"text-gray-600",children:e})]},s))})]})]}),"reviews"===S&&(0,a.jsx)("div",{children:(0,a.jsx)(E,{productId:q.id})}),"faqs"===S&&(0,a.jsx)("div",{children:(0,a.jsx)(j,{productId:q.id})})]})]})]})]})]})})]})}},1396:function(e,s,t){"use strict";t.d(s,{FlashSaleProvider:function(){return i},u:function(){return n}});var a=t(57437),l=t(2265);let r=(0,l.createContext)(void 0);function i(e){let{children:s}=e,[t,i]=(0,l.useState)(null),[n,c]=(0,l.useState)(!0),[d,o]=(0,l.useState)(!1),x=async()=>{try{let e=await fetch("/api/homepage-settings"),s=await e.json();if(s.success&&s.data.settings){let e=s.data.settings,t={showFlashSale:e.showFlashSale,flashSaleEndDate:e.flashSaleEndDate,flashSalePercentage:e.flashSalePercentage,flashSaleTitle:e.flashSaleTitle,flashSaleSubtitle:e.flashSaleSubtitle,flashSaleBackgroundColor:e.flashSaleBackgroundColor};i(t),localStorage.setItem("flashSaleSettings",JSON.stringify(t)),window.dispatchEvent(new Event("flashSaleSettingsUpdated"))}}catch(e){console.error("Error fetching flash sale settings:",e)}finally{c(!1)}};(0,l.useEffect)(()=>{x().then(()=>{o(!0)})},[]);let m=async()=>{c(!0),await x()};return(0,a.jsx)(r.Provider,{value:{flashSaleSettings:t,loading:n,isHydrated:d,refreshSettings:m},children:s})}function n(){let e=(0,l.useContext)(r);if(void 0===e)throw Error("useFlashSale must be used within a FlashSaleProvider");return e}},8329:function(e,s,t){"use strict";function a(e){return!!e&&!!e.showFlashSale&&(!e.flashSaleEndDate||new Date<new Date(e.flashSaleEndDate))}function l(e,s){let t=a(s),l=(null==s?void 0:s.flashSalePercentage)||0;return{isOnSale:t,salePrice:t?Math.round(e-e*l/100):e,originalPrice:e,discountPercentage:t?l:0}}t.d(s,{FO:function(){return l},LR:function(){return a}})}},function(e){e.O(0,[7349,7648,5644,8040,1682,8250,200,2971,2117,1744],function(){return e(e.s=20157)}),_N_E=e.O()}]);