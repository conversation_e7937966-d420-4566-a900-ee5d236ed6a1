(()=>{var e={};e.id=3021,e.ids=[3021],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17322:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d}),t(15392),t(37254),t(35866);var a=t(23191),r=t(88716),l=t(37922),i=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["shop",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15392)),"C:\\Users\\<USER>\\Desktop\\project\\app\\shop\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\shop\\page.tsx"],x="/shop/page",h={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/shop/page",pathname:"/shop",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},86890:(e,s,t)=>{Promise.resolve().then(t.bind(t,65090)),Promise.resolve().then(t.bind(t,15986))},15986:(e,s,t)=>{"use strict";t.d(s,{default:()=>A});var a=t(10326),r=t(17577),l=t(35047),i=t(88307),n=t(76557);let c=(0,n.Z)("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var d=t(94019),o=t(924),x=t(29389);let h=(0,n.Z)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var m=t(90434),u=t(77109),p=t(46226),g=t(67427),f=t(33734),y=t(83855),j=t(57671),v=t(94494);function b(e){return!e||e.includes("placeholder")||e.includes("api/placeholder")?"/images/default-product.jpg":e}var N=t(19922),w=t(69719),k=t(39108),C=t(47537),S=t(52807);let P=({product:e,featured:s=!1,viewMode:t="grid"})=>{let{dispatch:i}=(0,v.j)(),{flashSaleSettings:n}=(0,w.u)(),{data:c}=(0,u.useSession)(),d=(0,l.useRouter)(),{showToast:o}=(0,C.V)(),{refreshUnreadCount:x,refreshNotifications:h}=(0,S.z)(),[P,Z]=(0,r.useState)(!1),[$,_]=(0,r.useState)(!1),[M,E]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{if(c?.user?.id&&e?.id)try{let s=await fetch("/api/wishlist");if(s.ok){let t=(await s.json()).items.some(s=>s.id===e.id);Z(t)}}catch(e){console.error("Error checking wishlist status:",e)}})()},[c,e]);let F=s=>{s.preventDefault(),s.stopPropagation();let t=e.price||0,a=e.variants;if(0===e.price&&a&&a.length>0){let e=a.map(e=>e.price??0).filter(e=>e>0);e.length>0&&(t=Math.max(...e))}i({type:"ADD_ITEM",payload:{...e,price:t}})},L=async s=>{if(s.preventDefault(),s.stopPropagation(),!c?.user?.id){d.push("/login");return}if(e?.id){_(!0);try{if(P){let s=await fetch(`/api/wishlist?productId=${e.id}`,{method:"DELETE"});if(s.ok)Z(!1),o("Removed from wishlist","success"),x(),h();else{let e=await s.json();o(e.error||"Failed to remove from wishlist","error")}}else{let s=await fetch("/api/wishlist",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:e.id})});if(s.ok)Z(!0),o("Added to wishlist","success"),x(),h();else{let e=await s.json();o(e.error||"Failed to add to wishlist","error")}}}catch(e){console.error("Error updating wishlist:",e),o("An unexpected error occurred.","error")}finally{_(!1)}}},A=e.variants,D=e.price||0,O="",q="",R=!1;if(A&&A.length>0){let s=[e.price||0,...A.map(e=>e.price??0)].filter(e=>e>0);if(s.length>0){let e=Math.min(...s),t=Math.max(...s);if(e!==t){R=!0;let s=(0,k.FO)(e,n),a=(0,k.FO)(t,n);s.isOnSale?(O=`₹${s.salePrice} - ₹${a.salePrice}`,q=`₹${e} - ₹${t}`):O=`₹${e} - ₹${t}`}else D=e}}if(!R){let e=(0,k.FO)(D,n);e.isOnSale?(O=`₹${e.salePrice}`,q=`₹${e.originalPrice}`):O=`₹${D}`}return"list"===t?a.jsx(m.default,{href:`/product/${e.slug||e.id}`,className:"block group",children:a.jsx("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-green-200",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"relative w-28 h-28 sm:w-40 sm:h-40 flex-shrink-0",children:(0,a.jsxs)("div",{className:"w-full h-full relative overflow-hidden rounded-l-2xl",children:[!M&&a.jsx("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),a.jsx(p.default,{src:b(e.image),alt:e.name,fill:!0,className:`object-cover group-hover:scale-105 transition-transform duration-300 ${M?"opacity-100":"opacity-0"}`,sizes:"(max-width: 640px) 112px, 160px",onLoad:()=>E(!0)})]})}),(0,a.jsxs)("div",{className:"flex-1 p-3 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-1 line-clamp-1 text-base sm:text-lg",children:e.name}),a.jsx("div",{className:"mb-2",children:a.jsx(N.Z,{product:e._raw||e,maxCategories:1})})]}),a.jsx("button",{onClick:L,className:`p-1.5 sm:p-2 rounded-full transition-colors ml-2 ${P?"text-red-500 bg-red-50 hover:bg-red-100":"text-gray-400 hover:text-red-500 hover:bg-red-50"}`,children:a.jsx(g.Z,{className:`w-3.5 h-3.5 sm:w-4 sm:h-4 ${P?"fill-current":""}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-base sm:text-lg font-bold text-gray-900",children:O}),q&&a.jsx("span",{className:"text-sm sm:text-base text-gray-500 line-through",children:q})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(f.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),a.jsx("span",{className:"text-xs text-gray-600 ml-1",children:e.rating})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.reviews," reviews)"]})]})]}),(0,a.jsxs)("button",{onClick:F,className:"bg-green-600 text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl hover:bg-green-700 transition-colors shadow-sm hover:shadow-md flex items-center space-x-1.5 sm:space-x-2",children:[a.jsx(y.Z,{className:"w-3.5 h-3.5 sm:w-4 sm:h-4"}),a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:"Add"})]})]})]})]})})}):a.jsx(m.default,{href:`/product/${e.slug||e.id}`,className:"block group",children:(0,a.jsxs)("div",{className:`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-green-200 ${s?"w-72 flex-shrink-0 lg:w-80":"w-full"}`,children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"w-full aspect-square relative overflow-hidden",children:[!M&&a.jsx("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"}),a.jsx(p.default,{src:b(e.image),alt:e.name,fill:!0,className:`object-cover group-hover:scale-105 transition-transform duration-300 ${M?"opacity-100":"opacity-0"}`,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",onLoad:()=>E(!0)})]}),a.jsx("div",{className:"absolute top-3 right-3 flex flex-col space-y-2",children:a.jsx("button",{onClick:L,className:`p-2 rounded-full shadow-sm transition-all duration-200 ${P?"bg-red-500 text-white":"bg-white text-gray-600 hover:text-red-500 hover:bg-red-50"}`,children:a.jsx(g.Z,{className:`w-4 h-4 ${P?"fill-current":""}`})})}),a.jsx("div",{className:"absolute top-3 left-3 bg-white rounded-full px-2 py-1 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(f.Z,{className:"w-3 h-3 text-yellow-400 fill-current"}),a.jsx("span",{className:"text-xs font-medium text-gray-700",children:e.rating})]})}),q&&n?.flashSalePercentage&&(0,a.jsxs)("div",{className:"absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-sm",children:[n.flashSalePercentage,"% OFF"]})]}),(0,a.jsxs)("div",{className:"p-4 lg:p-5",children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-1 text-base lg:text-lg leading-tight",children:e.name}),a.jsx("div",{className:"mb-4",children:a.jsx(N.Z,{product:e._raw||e,className:"mb-2",maxCategories:1})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("span",{className:"text-base lg:text-lg font-bold text-gray-900",children:O}),q&&a.jsx("span",{className:"text-sm lg:text-base text-gray-500 line-through",children:q})]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.reviews," reviews"]})]}),a.jsx("button",{onClick:F,className:"bg-green-600 text-white p-2.5 lg:p-3 rounded-full hover:bg-green-700 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105 active:scale-95",children:a.jsx(j.Z,{className:"w-4 h-4 lg:w-5 lg:h-5"})})]})]})]})})};var Z=t(75290);let $=({hasMore:e,loading:s,onLoadMore:t,threshold:l=100,children:i})=>{let n=(0,r.useRef)(null),c=(0,r.useRef)(!1),d=(0,r.useCallback)(a=>{let[r]=a;r.isIntersecting&&e&&!s&&!c.current&&(c.current=!0,t(),setTimeout(()=>{c.current=!1},1e3))},[e,s,t]);return(0,r.useEffect)(()=>{let e=n.current;if(!e)return;let s=new IntersectionObserver(d,{threshold:.1,rootMargin:`${l}px`});return s.observe(e),()=>{e&&s.unobserve(e)}},[d,l]),(0,a.jsxs)("div",{children:[i,(0,a.jsxs)("div",{ref:n,className:"w-full py-8",children:[s&&a.jsx("div",{className:"flex items-center justify-center",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[a.jsx(Z.Z,{className:"w-5 h-5 animate-spin"}),a.jsx("span",{className:"text-sm font-medium",children:"Loading more products..."})]})}),!e&&!s&&a.jsx("div",{className:"text-center py-8",children:a.jsx("div",{className:"inline-flex items-center px-4 py-2 bg-gray-100 rounded-full",children:a.jsx("span",{className:"text-sm text-gray-600 font-medium",children:"You've reached the end of our collection"})})})]})]})};var _=t(11417),M=t(3634),E=t(48998);function F(){let{flashSaleSettings:e}=(0,w.u)(),[s,t]=(0,r.useState)("");return e&&(0,k.LR)(e)?(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-lg p-4 mb-6",style:{backgroundColor:e.flashSaleBackgroundColor||"#16a34a"},children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[a.jsx("div",{className:"absolute -top-4 -right-4 w-24 h-24 bg-white rounded-full"}),a.jsx("div",{className:"absolute -bottom-4 -left-4 w-32 h-32 bg-white rounded-full"})]}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between flex-wrap gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"bg-white bg-opacity-20 p-2 rounded-full",children:a.jsx(M.Z,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-white font-bold text-lg",children:e.flashSaleTitle||"Flash Sale"}),a.jsx("p",{className:"text-white text-opacity-90 text-sm",children:e.flashSaleSubtitle||`Get ${e.flashSalePercentage}% off all products`})]})]}),s&&e.flashSaleEndDate&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 bg-white bg-opacity-20 px-4 py-2 rounded-full",children:[a.jsx(E.Z,{className:"w-4 h-4 text-white"}),a.jsx("span",{className:"text-white font-semibold text-sm",children:"Ended"===s?"Sale Ended":`Ends in: ${s}`})]})]}),e.flashSalePercentage&&(0,a.jsxs)("div",{className:"mt-3 inline-flex items-center bg-white bg-opacity-20 px-3 py-1 rounded-full",children:[(0,a.jsxs)("span",{className:"text-white font-bold text-2xl",children:[e.flashSalePercentage,"% OFF"]}),a.jsx("span",{className:"text-white text-opacity-90 ml-2",children:"Store-wide Discount"})]})]})]}):null}let L=e=>{let s=e.category?.slug||"skincare";if(e.price,e.variants&&e.variants.length>0){let s=[e.price||0,...e.variants.map(e=>e.price??0)].filter(e=>e>0);s.length>0&&([...s],[...s])}return{id:e.id,slug:e.slug,name:e.name,description:e.description,shortDescription:e.shortDescription,price:e.price||0,image:e.images[0]?.url||"/placeholde.jpg",images:e.images.map(e=>({id:e.id,url:e.url,alt:e.alt,position:e.position||0})),category:s,featured:e.isFeatured,ingredients:[],benefits:[],rating:0,reviews:0,variants:e.variants,_raw:e}},A=()=>{let e=(0,l.useSearchParams)(),[s,t]=(0,r.useState)("all"),[n,m]=(0,r.useState)("random"),[u,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)([]),[y,j]=(0,r.useState)([]),[v,b]=(0,r.useState)([]),[N,w]=(0,r.useState)([]),[k,C]=(0,r.useState)(!0),[S,Z]=(0,r.useState)(!1),[M,E]=(0,r.useState)(null),[A,D]=(0,r.useState)(""),[O,q]=(0,r.useState)("grid"),[R,T]=(0,r.useState)([0,1e4]),[U,I]=(0,r.useState)(!1),[z,G]=(0,r.useState)(1),[H,V]=(0,r.useState)(!0),B=(0,r.useRef)(null);(0,r.useEffect)(()=>{let s=e.get("category");s&&t(s)},[e]),(0,r.useEffect)(()=>{(async()=>{try{C(!0);let e=new URLSearchParams({limit:"1000",sort:n}),[s,t]=await Promise.all([fetch(`/api/products?${e.toString()}`),fetch("/api/categories")]),[a,r]=await Promise.all([s.json(),t.json()]);if(a.success&&r.success){let e=a.data.filter(e=>e.isActive).map(e=>({...L(e),_raw:e}));if(f(e),j(e),0===N.length){let e=[{id:"all",name:"All Products"},...r.data.map(e=>({id:e.slug,name:e.name}))];w(e)}}else E("Failed to fetch data")}catch(e){console.error("Error fetching data:",e),E("Failed to load products")}finally{C(!1)}})()},[n]),(0,r.useEffect)(()=>{let e=g;if("all"!==s&&(e=e.filter(e=>{let t=e._raw;return t.category?.slug===s||(t.productCategories?.some(e=>e.category.slug===s)??!1)})),A.trim()){let s=A.toLowerCase().trim();e=e.filter(e=>e.name.toLowerCase().includes(s)||e.description.toLowerCase().includes(s)||e.shortDescription.toLowerCase().includes(s))}j(e=e.filter(e=>{let s=e.price||0;return s>=R[0]&&s<=R[1]})),G(1),V(e.length>12)},[s,g,A,R]),(0,r.useEffect)(()=>{let e=12*z;b(y.slice(0,e)),V(e<y.length)},[y,z]);let J=()=>{!S&&H&&(Z(!0),setTimeout(()=>{G(e=>e+1),Z(!1)},500))};(0,r.useEffect)(()=>{U&&B.current&&B.current.focus()},[U]);let W=e=>{t(e)};return k?a.jsx(_.sW,{}):M?a.jsx("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-red-600 mb-4",children:M}),a.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:"Try Again"})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsxs)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:[(0,a.jsxs)("div",{className:"px-4 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Shop"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>I(!U),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors",children:a.jsx(i.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("button",{onClick:()=>p(!u),className:"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[a.jsx(c,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm font-medium",children:"Filters"})]})]})]}),a.jsx("div",{className:`transition-all duration-300 ${U?"max-h-20 opacity-100 mb-3":"max-h-0 opacity-0 overflow-hidden"}`,children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(i.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),a.jsx("input",{ref:B,type:"text",placeholder:"Search products...",value:A,onChange:e=>D(e.target.value),className:"w-full pl-10 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),A&&a.jsx("button",{onClick:()=>D(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a.jsx(d.Z,{className:"w-4 h-4"})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[y.length," products found"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>q("grid"),className:`p-1.5 rounded ${"grid"===O?"bg-green-100 text-green-600":"text-gray-400"}`,children:a.jsx(o.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>q("list"),className:`p-1.5 rounded ${"list"===O?"bg-green-100 text-green-600":"text-gray-400"}`,children:a.jsx(x.Z,{className:"w-4 h-4"})})]})]})]}),a.jsx("div",{className:`border-t bg-white transition-all duration-300 ${u?"max-h-screen opacity-100":"max-h-0 opacity-0 overflow-hidden"}`,children:(0,a.jsxs)("div",{className:"px-4 py-4 space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Sort by"}),(0,a.jsxs)("select",{value:n,onChange:e=>m(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-sm",children:[a.jsx("option",{value:"random",children:"Random (Default)"}),a.jsx("option",{value:"name_asc",children:"Name (A-Z)"}),a.jsx("option",{value:"name_desc",children:"Name (Z-A)"}),a.jsx("option",{value:"price_asc",children:"Price (Low to High)"}),a.jsx("option",{value:"price_desc",children:"Price (High to Low)"}),a.jsx("option",{value:"newest",children:"Newest First"}),a.jsx("option",{value:"oldest",children:"Oldest First"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Price Range"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"number",placeholder:"Min",value:R[0],onChange:e=>T([parseInt(e.target.value)||0,R[1]]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),a.jsx("span",{className:"text-gray-400",children:"-"}),a.jsx("input",{type:"number",placeholder:"Max",value:R[1],onChange:e=>T([R[0],parseInt(e.target.value)||1e4]),className:"flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["₹",R[0]," - ₹",R[1]]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-semibold text-gray-900 mb-2",children:"Categories"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:N.map(e=>a.jsx("button",{onClick:()=>W(e.id),className:`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${s===e.id?"bg-green-600 text-white shadow-md scale-105":"bg-gray-100 text-gray-700 hover:bg-gray-200 active:scale-95"}`,children:e.name},e.id))})]}),a.jsx("button",{onClick:()=>{t("all"),D(""),T([0,1e4]),m("random")},className:"w-full py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors",children:"Clear All Filters"})]})})]}),a.jsx("div",{className:"px-4 pt-4",children:a.jsx(F,{})}),a.jsx("div",{className:"px-4 py-6",children:0===y.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:a.jsx(i.Z,{className:"w-8 h-8 text-gray-400"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),a.jsx("p",{className:"text-gray-500 mb-4",children:"Try adjusting your search or filters"}),a.jsx("button",{onClick:()=>{t("all"),D(""),T([0,1e4])},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):a.jsx($,{hasMore:H,loading:S,onLoadMore:J,children:a.jsx("div",{className:`${"grid"===O?"grid grid-cols-2 gap-4":"space-y-4"}`,children:v.map(e=>a.jsx(P,{product:e,viewMode:O},e.id))})})})]}),(0,a.jsxs)("div",{className:"hidden lg:block",children:[a.jsx("div",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,a.jsxs)("div",{className:"relative w-80",children:[a.jsx(i.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:"text",placeholder:"Search products...",value:A,onChange:e=>D(e.target.value),className:"w-full pl-12 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"}),A&&a.jsx("button",{onClick:()=>D(""),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a.jsx(d.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 flex-1 justify-center",children:[a.jsx("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Price Range:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"number",placeholder:"Min",value:R[0],onChange:e=>T([parseInt(e.target.value)||0,R[1]]),className:"w-24 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"}),a.jsx("span",{className:"text-gray-400",children:"-"}),a.jsx("input",{type:"number",placeholder:"Max",value:R[1],onChange:e=>T([R[0],parseInt(e.target.value)||1e4]),className:"w-24 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(h,{className:"w-5 h-5 text-gray-600"}),(0,a.jsxs)("select",{value:n,onChange:e=>m(e.target.value),className:"px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white",children:[a.jsx("option",{value:"random",children:"Random (Default)"}),a.jsx("option",{value:"name_asc",children:"Name (A-Z)"}),a.jsx("option",{value:"name_desc",children:"Name (Z-A)"}),a.jsx("option",{value:"price_asc",children:"Price (Low to High)"}),a.jsx("option",{value:"price_desc",children:"Price (High to Low)"}),a.jsx("option",{value:"newest",children:"Newest First"}),a.jsx("option",{value:"oldest",children:"Oldest First"})]})]}),(0,a.jsxs)("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[a.jsx("button",{onClick:()=>q("grid"),className:`p-2 rounded ${"grid"===O?"bg-white shadow-sm text-green-600":"text-gray-500"}`,children:a.jsx(o.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>q("list"),className:`p-2 rounded ${"list"===O?"bg-white shadow-sm text-green-600":"text-gray-500"}`,children:a.jsx(x.Z,{className:"w-4 h-4"})})]})]})]}),a.jsx("div",{className:"flex justify-center mb-8",children:a.jsx("div",{className:"flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100",children:N.map(e=>a.jsx("button",{onClick:()=>W(e.id),className:`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${s===e.id?"bg-green-600 text-white shadow-md scale-105":"text-gray-700 hover:bg-gray-100 active:scale-95"}`,children:e.name},e.id))})})]})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[a.jsx(F,{}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("p",{className:"text-gray-600",children:[y.length," products found"]}),a.jsx("button",{onClick:()=>{t("all"),D(""),T([0,1e4]),m("random")},className:"text-sm text-gray-500 hover:text-gray-700 underline",children:"Clear all filters"})]}),0===y.length?(0,a.jsxs)("div",{className:"text-center py-16",children:[a.jsx("div",{className:"w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center",children:a.jsx(i.Z,{className:"w-10 h-10 text-gray-400"})}),a.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"No products found"}),a.jsx("p",{className:"text-gray-500 mb-6",children:"Try adjusting your search or filters"}),a.jsx("button",{onClick:()=>{t("all"),D(""),T([0,1e4])},className:"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"Clear Filters"})]}):a.jsx($,{hasMore:H,loading:S,onLoadMore:J,children:a.jsx("div",{className:`${"grid"===O?"grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6":"grid grid-cols-1 lg:grid-cols-2 gap-6"}`,children:v.map(e=>a.jsx(P,{product:e,viewMode:O},e.id))})})]})]})]})}},924:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},29389:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},83855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},15392:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,dynamic:()=>n});var a=t(19510),r=t(71159),l=t(40304);let i=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Shop.tsx#default`),n="force-dynamic";function c(){return a.jsx(i,{})}function d(){return a.jsx(l.Z,{children:a.jsx(r.Suspense,{fallback:a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading products..."})]})}),children:a.jsx(c,{})})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,3757,434,9694,9536,5090,561,389],()=>t(17322));module.exports=a})();