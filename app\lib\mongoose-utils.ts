import {
  Product,
  Category,
  ProductImage,
  ProductVariant,
  Review,
  User,
  Order,
  OrderItem,
  Address,
  ProductCategory,
  WishlistItem,
} from "./models";

/**
 * Utility functions to translate Prisma include patterns to Mongoose populate
 */

// Product population helpers
export const productPopulationHelpers = {
  /**
   * Get product with full relationship population (equivalent to Prisma include)
   * @param productId - The product ID or slug
   * @param bySlug - Whether to search by slug instead of ID
   */
  async getProductWithRelations(productId: string, bySlug: boolean = false) {
    const query = bySlug ? { slug: productId } : { _id: productId };

    // Get the base product
    const product = await Product.findOne(query).lean();
    if (!product) return null;

    // Manually populate relations using separate queries (flattened approach)
    const [category, images, variants, reviews, productCategories] =
      await Promise.all([
        // Get primary category
        (product as any).categoryId
          ? Category.findById((product as any).categoryId).lean()
          : null,

        // Get images sorted by position
        ProductImage.find({ productId: String((product as any)._id) })
          .sort({ position: 1 })
          .lean(),

        // Get variants
        ProductVariant.find({ productId: String((product as any)._id) }).lean(),

        // Get reviews with user data
        Review.find({ productId: String((product as any)._id), isApproved: true })
          .sort({ createdAt: -1 })
          .lean()
          .then(async (reviews) => {
            // Populate user data for each review
            const reviewsWithUsers = await Promise.all(
              reviews.map(async (review) => {
                const user = await User.findById(review.userId)
                  .select("name avatar")
                  .lean();
                return { ...review, user };
              }),
            );
            return reviewsWithUsers;
          }),

        // Get product categories (many-to-many relationship)
        ProductCategory.find({ productId: String((product as any)._id) })
          .lean()
          .then(async (productCats) => {
            // Populate category data for each relationship
            const productCategoriesWithCategories = await Promise.all(
              productCats.map(async (pc) => {
                const category = await Category.findById(pc.categoryId).lean();
                return { ...pc, category };
              }),
            );
            return productCategoriesWithCategories;
          }),
      ]);

    return {
      ...product,
      category,
      images,
      variants,
      reviews,
      productCategories,
    };
  },

  /**
   * Alternative approach using Mongoose populate (requires proper refs in schema)
   * Note: This would require updating schemas to use ObjectId references
   */
  async getProductWithPopulate(productId: string, bySlug: boolean = false) {
    const query = bySlug ? { slug: productId } : { _id: productId };

    return await Product.findOne(query)
      .populate("categoryId", "name slug description") // Populate main category
      .populate({
        path: "images",
        options: { sort: { position: 1 } },
      })
      .populate("variants")
      .populate({
        path: "reviews",
        match: { isApproved: true },
        populate: {
          path: "userId",
          select: "name avatar",
        },
        options: { sort: { createdAt: -1 } },
      })
      .lean();
  },
};

// Order population helpers
export const orderPopulationHelpers = {
  /**
   * Get order with full relationship population
   * @param orderId - Order ID or order number
   * @param userId - User ID (for access control)
   * @param isAdmin - Whether the requester is an admin
   */
  async getOrderWithRelations(
    orderId: string,
    userId?: string,
    isAdmin: boolean = false,
  ) {
    // Support both ID and order number lookup
    const query: any = {
      $or: [{ _id: orderId }, { orderNumber: orderId }],
    };

    // Non-admin users can only see their own orders
    if (!isAdmin && userId) {
      query.userId = userId;
    }

    const order = await Order.findOne(query).lean();
    if (!order) return null;

    // Manually populate relations
    const [orderItems, address, user] = await Promise.all([
      // Get order items with product data
      OrderItem.find({ orderId: String((order as any)._id) })
        .lean()
        .then(async (items) => {
          const itemsWithProducts = await Promise.all(
            items.map(async (item) => {
              const product = await Product.findById(item.productId)
                .select("name slug price")
                .lean();

              // Get first product image
              const images = await ProductImage.find({
                productId: item.productId,
              })
                .sort({ position: 1 })
                .limit(1)
                .lean();

              return {
                ...item,
                product: product ? { ...product, images } : null,
              };
            }),
          );
          return itemsWithProducts;
        }),

      // Get address data
      (order as any).addressId ? Address.findById((order as any).addressId).lean() : null,

      // Get user data (only for admins)
      isAdmin && (order as any).userId
        ? User.findById((order as any).userId).select("name email phone").lean()
        : null,
    ]);

    return {
      ...order,
      items: orderItems,
      address,
      ...(isAdmin && user && { user }),
    };
  },
};

// Wishlist population helpers
export const wishlistPopulationHelpers = {
  /**
   * Get user's wishlist with populated product data
   * @param userId - User ID
   */
  async getUserWishlistWithProducts(userId: string) {
    const wishlistItems = await WishlistItem.find({ userId }).lean();

    // Populate product data for each wishlist item
    const itemsWithProducts = await Promise.all(
      wishlistItems.map(async (item) => {
        const [product, images, productCategories] = await Promise.all([
          Product.findById(item.productId).lean(),
          ProductImage.find({ productId: item.productId })
            .sort({ position: 1 })
            .limit(1)
            .lean(),
          ProductCategory.find({ productId: item.productId })
            .lean()
            .then(async (productCats) => {
              const categoriesWithData = await Promise.all(
                productCats.map(async (pc) => {
                  const category = await Category.findById(
                    pc.categoryId,
                  ).lean();
                  return { ...pc, category };
                }),
              );
              return categoriesWithData;
            }),
        ]);

        return {
          ...item,
          product: product
            ? {
                ...product,
                images,
                productCategories,
              }
            : null,
        };
      }),
    );

    return itemsWithProducts.filter((item) => item.product !== null);
  },
};

/**
 * Generic helper for manual population when Mongoose populate isn't suitable
 */
export const manualPopulationHelpers = {
  /**
   * Populate a single field with data from another collection
   */
  async populateField(
    items: any[],
    fieldName: string,
    targetModel: any,
    selectFields?: string,
  ) {
    const ids = items.map((item) => item[fieldName]).filter(Boolean);
    const populatedData = await targetModel
      .find({ _id: { $in: ids } })
      .select(selectFields)
      .lean();

    const dataMap = new Map(
      populatedData.map((doc: any) => [doc._id.toString(), doc]),
    );

    return items.map((item) => ({
      ...item,
      [fieldName]: item[fieldName]
        ? dataMap.get(item[fieldName].toString())
        : null,
    }));
  },

  /**
   * Populate many-to-many relationships
   */
  async populateManyToMany(
    parentItems: any[],
    joinModel: any,
    targetModel: any,
    parentField: string,
    joinParentField: string,
    joinTargetField: string,
    targetFieldName: string,
  ) {
    const parentIds = parentItems.map((item) => item._id);

    // Get join table entries
    const joinEntries = await joinModel
      .find({
        [joinParentField]: { $in: parentIds },
      })
      .lean();

    // Get target data
    const targetIds = joinEntries.map((entry: any) => entry[joinTargetField]);
    const targetData = await targetModel
      .find({
        _id: { $in: targetIds },
      })
      .lean();

    // Create maps for efficient lookup
    const targetMap = new Map(
      targetData.map((doc: any) => [doc._id.toString(), doc]),
    );
    const joinMap = new Map();

    joinEntries.forEach((entry: any) => {
      const parentId = entry[joinParentField].toString();
      if (!joinMap.has(parentId)) {
        joinMap.set(parentId, []);
      }
      const target = targetMap.get(entry[joinTargetField].toString());
      if (target) {
        joinMap.get(parentId).push({
          ...entry,
          [targetFieldName]: target,
        });
      }
    });

    // Add populated data to parent items
    return parentItems.map((item) => ({
      ...item,
      [targetFieldName + "s"]: joinMap.get(item._id.toString()) || [],
    }));
  },
};

export default {
  productPopulationHelpers,
  orderPopulationHelpers,
  wishlistPopulationHelpers,
  manualPopulationHelpers,
};
