(()=>{var e={};e.id=5193,e.ids=[5193],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},69165:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c}),s(90834),s(37254),s(35866);var a=s(23191),r=s(88716),n=s(37922),i=s.n(n),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90834)),"C:\\Users\\<USER>\\Desktop\\project\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\notifications\\page.tsx"],x="/notifications/page",h={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},30518:(e,t,s)=>{Promise.resolve().then(s.bind(s,20112))},20112:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(10326),r=s(17577),n=s(35047),i=s(77109),l=s(34565),o=s(54659),c=s(48705),d=s(14228),x=s(91470),h=s(67427),m=s(29163),u=s(33734),g=s(40617),p=s(1572),y=s(6507),f=s(75290),b=s(86333),j=s(76557);let N=(0,j.Z)("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]);var E=s(87888);let w=(0,j.Z)("BellOff",[["path",{d:"M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5",key:"o7mx20"}],["path",{d:"M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7",key:"16f1lm"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var R=s(65090),k=s(52807);let v=()=>{let e=(0,n.useRouter)(),{data:t,status:s}=(0,i.useSession)(),{unreadCount:j,markAsRead:v,markAllAsRead:D}=(0,k.z)(),[_,S]=(0,r.useState)([]),[C,Z]=(0,r.useState)(!0),[P,M]=(0,r.useState)(null),[O,A]=(0,r.useState)(1),[I,L]=(0,r.useState)(1),[T,U]=(0,r.useState)(0);(0,r.useEffect)(()=>{"unauthenticated"===s&&e.push("/login")},[s,e]);let q=async(e=1)=>{Z(!0),M(null);try{let t=new URLSearchParams({page:e.toString(),limit:"20"}),s=await fetch(`/api/notifications?${t}`),a=await s.json();a.success?(S(a.data.notifications),L(a.data.pagination.totalPages),U(a.data.pagination.totalCount),A(e)):M(a.message||"Failed to fetch notifications.")}catch(e){console.error("Error fetching notifications:",e),M("An unexpected error occurred.")}finally{Z(!1)}};(0,r.useEffect)(()=>{t?.user?.id&&q(1)},[t?.user?.id]);let B=e=>{switch(e){case"ORDER_PLACED":return a.jsx(l.Z,{className:"w-5 h-5"});case"ORDER_CONFIRMED":case"ORDER_DELIVERED":return a.jsx(o.Z,{className:"w-5 h-5"});case"ORDER_PROCESSING":return a.jsx(c.Z,{className:"w-5 h-5"});case"ORDER_SHIPPED":return a.jsx(d.Z,{className:"w-5 h-5"});case"ORDER_CANCELLED":return a.jsx(x.Z,{className:"w-5 h-5"});case"WISHLIST_ADDED":case"WISHLIST_REMOVED":return a.jsx(h.Z,{className:"w-5 h-5"});case"PRICE_DROP_ALERT":return a.jsx(m.Z,{className:"w-5 h-5"});case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return a.jsx(u.Z,{className:"w-5 h-5"});case"ADMIN_MESSAGE":case"BROADCAST":return a.jsx(g.Z,{className:"w-5 h-5"});case"PROMOTIONAL":return a.jsx(p.Z,{className:"w-5 h-5"});default:return a.jsx(y.Z,{className:"w-5 h-5"})}},H=e=>{switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":case"ORDER_PROCESSING":case"PRICE_DROP_ALERT":case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return{iconBg:"bg-green-600",iconColor:"text-white",badge:"bg-green-100 text-green-700 border-green-200"};case"ORDER_SHIPPED":case"ORDER_DELIVERED":case"ADMIN_MESSAGE":case"BROADCAST":case"PROMOTIONAL":return{iconBg:"bg-green-700",iconColor:"text-white",badge:"bg-green-100 text-green-800 border-green-200"};case"ORDER_CANCELLED":return{iconBg:"bg-gray-600",iconColor:"text-white",badge:"bg-gray-100 text-gray-700 border-gray-200"};case"WISHLIST_ADDED":case"WISHLIST_REMOVED":return{iconBg:"bg-green-500",iconColor:"text-white",badge:"bg-green-50 text-green-700 border-green-200"};default:return{iconBg:"bg-gray-500",iconColor:"text-white",badge:"bg-gray-100 text-gray-700 border-gray-200"}}},V=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/1e3);return s<60?"Just now":s<3600?`${Math.floor(s/60)}m ago`:s<86400?`${Math.floor(s/3600)}h ago`:s<604800?`${Math.floor(s/86400)}d ago`:t.toLocaleDateString("en-US",{month:"short",day:"numeric"})},W=async t=>{t.isRead||await v(t.id),t.type.startsWith("ORDER_")?t.data?.orderId&&e.push("/order-history"):"PRICE_DROP_ALERT"===t.type&&t.data?.productId&&e.push(`/product/${t.data.productId}`)};return"loading"===s?a.jsx(R.default,{children:a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx(f.Z,{className:"w-8 h-8 animate-spin text-green-600"})})}):t?.user?a.jsx(R.default,{children:a.jsx("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("button",{onClick:()=>e.back(),className:"inline-flex items-center space-x-2 text-gray-500 hover:text-gray-800 mb-4 group",children:[a.jsx(b.Z,{className:"w-4 h-4 group-hover:-translate-x-1 transition-transform"}),a.jsx("span",{className:"font-medium text-sm",children:"Back"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),j>0&&(0,a.jsxs)("span",{className:"px-3 py-1 text-xs font-bold text-white bg-green-600 rounded-full",children:[j," New"]})]}),_.length>0&&(0,a.jsxs)("button",{onClick:D,className:"flex items-center space-x-2 px-3 py-1.5 bg-white text-gray-600 hover:text-gray-800 hover:bg-gray-50 border border-gray-200 rounded-lg transition-all duration-200 text-sm font-medium",children:[a.jsx(N,{className:"w-4 h-4"}),a.jsx("span",{children:"Mark all as read"})]})]})]}),a.jsx("div",{className:"space-y-4",children:C?(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-12 text-center",children:[a.jsx(f.Z,{className:"w-12 h-12 animate-spin text-green-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600 text-lg",children:"Loading your notifications..."})]}):P?(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-12 text-center",children:[a.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(E.Z,{className:"w-8 h-8 text-red-500"})}),a.jsx("p",{className:"text-gray-900 font-semibold text-lg mb-2",children:"Oops! Something went wrong"}),a.jsx("p",{className:"text-gray-600 mb-6",children:P}),a.jsx("button",{onClick:()=>q(O),className:"px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors font-medium",children:"Try again"})]}):0===_.length?(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-16 text-center",children:[a.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:a.jsx(w,{className:"w-12 h-12 text-gray-400"})}),a.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"No notifications yet"}),a.jsx("p",{className:"text-gray-600 text-lg mb-8",children:"We'll notify you when something important happens"}),a.jsx("button",{onClick:()=>e.push("/shop"),className:"px-8 py-3 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors font-medium",children:"Start Shopping"})]}):a.jsx(a.Fragment,{children:_.map(e=>{let t=H(e.type);return a.jsx("div",{onClick:()=>W(e),className:`bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border ${e.isRead?"border-gray-200":"border-green-500"}`,children:a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("div",{className:`w-10 h-10 rounded-lg ${t.iconBg} flex items-center justify-center flex-shrink-0`,children:a.jsx("div",{className:t.iconColor,children:B(e.type)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[a.jsx("h3",{className:"text-md font-semibold text-gray-800 group-hover:text-green-600 transition-colors",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isRead&&a.jsx("div",{className:"w-2.5 h-2.5 bg-green-500 rounded-full flex-shrink-0"}),a.jsx("span",{className:"text-xs text-gray-500",children:V(e.createdAt)})]})]}),a.jsx("p",{className:"text-sm text-gray-600 leading-snug",children:e.message})]})]})})},e.id)})})}),I>1&&a.jsx("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Page ",O," of ",I]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>q(O-1),disabled:1===O,className:"px-3 py-1.5 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200 text-sm font-medium",children:"Previous"}),a.jsx("button",{onClick:()=>q(O+1),disabled:O===I,className:"px-3 py-1.5 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200 text-sm font-medium",children:"Next"})]})]})})]})})}):null}},87888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},67427:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},75290:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},1572:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},33734:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},29163:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},90834:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\notifications\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,3757,434,9694,9536,5090],()=>s(69165));module.exports=a})();