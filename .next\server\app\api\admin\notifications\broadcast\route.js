"use strict";(()=>{var e={};e.id=3350,e.ids=[3350],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},68840:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>D,requestAsyncStorage:()=>R,routeModule:()=>m,serverHooks:()=>O,staticGenerationAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{POST:()=>g});var n=r(49303),o=r(88716),a=r(60670),s=r(87070),c=r(75571),l=r(95306),u=r(68602),d=r(54211),p=r(65630),f=r(29489);let E=p.Ry({title:p.Z_().min(1,"Title is required"),content:p.Z_().min(1,"Content is required"),type:p.Km(["BROADCAST","PROMOTIONAL","SYSTEM"]).default("BROADCAST"),priority:p.Km(["LOW","NORMAL","HIGH","URGENT"]).default("NORMAL"),sendEmail:p.O7().default(!0),sendInApp:p.O7().default(!0)});async function g(e){try{let t=await (0,c.getServerSession)(l.L);if(!t?.user?.id||"ADMIN"!==t.user.role)return s.NextResponse.json({error:"Admin access required"},{status:401});let r=await e.json(),i=E.parse(r);d.kg.info("Admin sending broadcast notification",{adminId:t.user.id,type:i.type,priority:i.priority,title:i.title});let n=await u.B.sendBroadcast({type:i.type,title:i.title,message:i.content,sendEmail:i.sendEmail,data:{sentBy:t.user.id,sentAt:new Date().toISOString(),priority:i.priority}});return d.kg.info("Broadcast notification sent successfully",{adminId:t.user.id,userCount:n.length}),s.NextResponse.json({success:!0,message:`Broadcast notification sent to ${n.length} users`,userCount:n.length})}catch(e){if(e instanceof f.j)return s.NextResponse.json({error:"Validation error",details:e.issues},{status:400});return d.kg.error("Failed to send broadcast notification",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/notifications/broadcast/route",pathname:"/api/admin/notifications/broadcast",filename:"route",bundlePath:"app/api/admin/notifications/broadcast/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\broadcast\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:R,staticGenerationAsyncStorage:y,serverHooks:O}=m,h="/api/admin/notifications/broadcast/route";function D(){return(0,a.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:y})}},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>o}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class n{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:n,context:o,error:a,userId:s,requestId:c}=e,l=i[r],u=`[${t}] ${l}: ${n}`;return s&&(u+=` | User: ${s}`),c&&(u+=` | Request: ${c}`),o&&Object.keys(o).length>0&&(u+=` | Context: ${JSON.stringify(o)}`),a&&(u+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(u+=`
Stack: ${a.stack}`)),u}log(e,t,r,i){if(!this.shouldLog(e))return;let n={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},o=this.formatMessage(n);if(this.isDevelopment)switch(e){case 0:console.error(o);break;case 1:console.warn(o);break;case 2:console.info(o);break;case 3:console.debug(o)}else console.log(JSON.stringify(n))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,n){this.info(`API ${e} ${t} - ${r}`,{...n,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,n){this.error(`API ${e} ${t} failed`,r,{...n,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let o=new n},68602:(e,t,r)=>{r.d(t,{B:()=>a,k:()=>i});var i,n=r(54211);!function(e){e.ORDER_PLACED="ORDER_PLACED",e.ORDER_CONFIRMED="ORDER_CONFIRMED",e.ORDER_PROCESSING="ORDER_PROCESSING",e.ORDER_SHIPPED="ORDER_SHIPPED",e.ORDER_DELIVERED="ORDER_DELIVERED",e.ORDER_CANCELLED="ORDER_CANCELLED",e.WISHLIST_ADDED="WISHLIST_ADDED",e.WISHLIST_REMOVED="WISHLIST_REMOVED",e.PRICE_DROP_ALERT="PRICE_DROP_ALERT",e.REVIEW_REQUEST="REVIEW_REQUEST",e.REVIEW_SUBMITTED="REVIEW_SUBMITTED",e.ADMIN_MESSAGE="ADMIN_MESSAGE",e.BROADCAST="BROADCAST",e.PROMOTIONAL="PROMOTIONAL",e.SYSTEM="SYSTEM"}(i||(i={}));class o{async createNotification(e){try{return n.kg.info(`Notification service temporarily disabled - would create notification for user ${e.userId}`),null}catch(e){throw n.kg.error("Error creating notification:",e),e}}async sendBroadcast(e){return n.kg.info("Broadcast service temporarily disabled"),[]}async markAsRead(e,t){return n.kg.info("Mark as read service temporarily disabled"),null}async markAllAsRead(e){return n.kg.info("Mark all as read service temporarily disabled"),{count:0}}async getUserNotifications(e,t={}){let{page:r=1,limit:i=20}=t;return n.kg.info("Get user notifications service temporarily disabled"),{notifications:[],total:0,page:r,limit:i,totalPages:0}}async getUnreadCount(e){return n.kg.info("Get unread count service temporarily disabled"),0}async cleanupOldNotifications(e=30){return n.kg.info("Cleanup notifications service temporarily disabled"),{count:0}}async sendEmailNotification(e){n.kg.info("Email notification service temporarily disabled")}shouldSendNotification(e,t){if(!t)return!0;if(!1===t.inAppNotifications)return!1;switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":case"ORDER_PROCESSING":case"ORDER_SHIPPED":case"ORDER_DELIVERED":case"ORDER_CANCELLED":return!1!==t.orderNotifications;case"WISHLIST_ADDED":case"WISHLIST_REMOVED":return!1!==t.wishlistNotifications;case"PRICE_DROP_ALERT":return!1!==t.priceDropAlerts;case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return!1!==t.reviewNotifications;case"ADMIN_MESSAGE":return!1!==t.adminMessages;case"BROADCAST":case"PROMOTIONAL":return!1!==t.broadcastMessages;default:return!0}}generateEmailContent(e){let t=process.env.NEXTAUTH_URL||"http://localhost:3000";return`
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #333; margin: 0 0 15px 0;">${e.title}</h2>
          <p style="color: #666; line-height: 1.6; margin: 0;">${e.message}</p>
        </div>
        
        ${e.data&&Object.keys(e.data).length>0?`
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 10px 0;">Details:</h3>
            ${this.formatNotificationData(e.data)}
          </div>
        `:""}
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${t}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Visit Herbalicious
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>You received this notification because you have an account with Herbalicious.</p>
          <p>You can manage your notification preferences in your account settings.</p>
        </div>
      </div>
    `}formatNotificationData(e){let t='<ul style="color: #666; line-height: 1.6;">';return e.orderNumber&&(t+=`<li><strong>Order Number:</strong> ${e.orderNumber}</li>`),e.productName&&(t+=`<li><strong>Product:</strong> ${e.productName}</li>`),e.amount&&e.currency&&(t+=`<li><strong>Amount:</strong> ${e.currency} ${e.amount}</li>`),t+="</ul>"}}let a=new o},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var n=r(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=n?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(i,o,s):i[o]=e[o]}return i.default=e,r&&r.set(e,i),i}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,2830,9489,5630,5306],()=>r(68840));module.exports=i})();