(()=>{var e={};e.id=1659,e.ids=[1659],e.modules={98860:e=>{"use strict";e.exports=require("jsdom")},11185:e=>{"use strict";e.exports=require("mongoose")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},86624:e=>{"use strict";e.exports=require("querystring")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},88811:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>N,patchFetch:()=>O,requestAsyncStorage:()=>R,routeModule:()=>A,serverHooks:()=>b,staticGenerationAsyncStorage:()=>w});var n={};r.r(n),r.d(n,{GET:()=>y,POST:()=>_});var o=r(49303),i=r(88716),a=r(60670),s=r(87070),l=r(75571),c=r(95306),u=r(89456),d=r(81515),p=r(84875),m=r(54211),f=r(89585),g=r(96468),h=r.n(g);let E={ALLOWED_TAGS:["b","i","em","strong","a","p","br","ul","ol","li"],ALLOWED_ATTR:["href","target","rel"],ALLOW_DATA_ATTR:!1,ALLOWED_URI_REGEXP:/^(?:(?:https?|mailto):|[^a-z]|[a-z+.-]+(?:[^a-z+.\-:]|$))/i},T={ALLOWED_TAGS:[],ALLOWED_ATTR:[],ALLOW_DATA_ATTR:!1},y=(0,p.lm)(async(e,{params:t})=>{let r=t.id;m.kg.apiRequest("GET",`/api/products/${r}/reviews`);let n=await (0,l.getServerSession)(c.L),o=n?.user?.id;await (0,u.Z)();let i=[{status:"APPROVED"}];o&&i.push({status:"PENDING",userId:o});let a=await d.Cq.find({productId:r,$or:i}).sort({createdAt:-1}).lean(),p=Array.from(new Set(a.map(e=>String(e.userId)))),f=new Map((await d.n5.find({_id:{$in:p}}).select("_id name avatar").lean()).map(e=>[String(e._id),e])),g=a.map(e=>{let t=f.get(String(e.userId));return{...e,user:t?{id:String(t._id),name:t.name??"",avatar:t.avatar}:null}});return m.kg.info("Product reviews fetched",{productId:r,count:g.length,includesPending:o?"yes":"no"}),s.NextResponse.json({success:!0,data:g})}),_=(0,p.lm)(async(e,{params:t})=>{let r=t.id;m.kg.apiRequest("POST",`/api/products/${r}/reviews`);let n=await (0,l.getServerSession)(c.L);if(!n?.user)throw new p._7;let{rating:o,title:i,content:a}=await e.json();if(!o||o<1||o>5)throw new p.p8("Rating must be between 1 and 5");if(!n.user.id&&!n.user.email)throw new p._7("Invalid session. Please log out and log back in.");await (0,u.Z)();let g=null;n.user.id?g=await d.n5.findById(n.user.id).select("_id email name").lean():n.user.email&&(g=await d.n5.findOne({email:n.user.email}).select("_id email name").lean());let y=g?._id;if(!g||!y)throw new p._7("User not found. Please log out and log back in.");let _=await d.xs.findById(r).select("_id name").lean();if(!_)return s.NextResponse.json({success:!1,error:"Product not found"},{status:404});if(await d.Cq.findOne({userId:y,productId:r}).lean())throw new p.AY("You have already reviewed this product");let A=await d.Order.findOne({userId:y,paymentStatus:"PAID",_id:{$in:await d.Dd.find({productId:r}).distinct("orderId")}}).lean(),R=i?function(e){return e?h().sanitize(e,T).replace(/[<>]/g,"").trim():""}(i):null,w=a?function(e,t=E){if(!e)return"";let r=e.replace(/javascript:/gi,"").replace(/on\w+\s*=/gi,"").replace(/<script[^>]*>[\s\S]*?<\/script>/gi,"").replace(/<iframe[^>]*>[\s\S]*?<\/iframe>/gi,"");return h().sanitize(r,t)}(a):null,b=await d.Cq.create({rating:o,title:R,content:w,isVerified:!!A,status:"PENDING",userId:y,productId:r,createdAt:new Date,updatedAt:new Date}),N={...b.toObject(),user:{id:g._id,name:g.name,avatar:g.avatar}};m.kg.info("Review created",{reviewId:String(b._id),productId:r,userId:y,rating:o,isVerified:!!A});try{await f.kg.reviewSubmitted(y,{productId:r,productName:_?.name??"",rating:o})}catch(e){m.kg.error("Failed to send review submitted notification",e)}return s.NextResponse.json({success:!0,data:N,message:"Review submitted for approval"})}),A=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/products/[id]/reviews/route",pathname:"/api/products/[id]/reviews",filename:"route",bundlePath:"app/api/products/[id]/reviews/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\reviews\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:R,staticGenerationAsyncStorage:w,serverHooks:b}=A,N="/api/products/[id]/reviews/route";function O(){return(0,a.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:w})}},84875:(e,t,r)=>{"use strict";r.d(t,{AY:()=>u,M_:()=>l,_7:()=>s,dR:()=>c,gz:()=>i,lm:()=>m,p8:()=>a});var n=r(87070),o=r(29489);class i extends Error{constructor(e,t=500,r="INTERNAL_ERROR",n){super(e),this.statusCode=t,this.code=r,this.details=n,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,i)}}class a extends i{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class s extends i{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class l extends i{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class c extends i{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class u extends i{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class d extends i{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function p(e){let t={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return t[e]||t.INTERNAL_ERROR}function m(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){let t=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,error:e}),e instanceof i){let r={success:!1,error:{code:e.code,message:p(e.code),requestId:t,...!1}};return n.NextResponse.json(r,{status:e.statusCode})}if(e instanceof o.j){let r=new a("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),o={success:!1,error:{code:r.code,message:"Validation failed",requestId:t,...!1}};return n.NextResponse.json(o,{status:r.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let r=function(e){if(11e3===e.code||"MongoServerError"===e.name){let t=Object.keys(e.keyPattern||{})[0]||"field";return new u(`${t} already exists`)}return"ValidationError"===e.name?new a("Validation failed"):"CastError"===e.name?new a("Invalid data format"):"DocumentNotFoundError"===e.name?new c:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new d("Database connection failed"):new d("Database operation failed",{name:e.name,message:e.message})}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return n.NextResponse.json(o,{status:r.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let r=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new u(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new s("Invalid user session");return new a("Invalid reference to related record");case"P2025":case"P2001":return new c;case"P2014":return new a("Missing required relationship");case"P2000":return new a("Input value is too long");case"P2004":return new a("Data constraint violation");default:return new d("Database operation failed",{code:e.code,message:e.message})}}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return n.NextResponse.json(o,{status:r.statusCode})}}let r={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:t,...!1}};return n.NextResponse.json(r,{status:500})}(e)}}}},56141:e=>{"use strict";let{entries:t,setPrototypeOf:r,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:i}=Object,{freeze:a,seal:s,create:l}=Object,{apply:c,construct:u}="undefined"!=typeof Reflect&&Reflect;a||(a=function(e){return e}),s||(s=function(e){return e}),c||(c=function(e,t,r){return e.apply(t,r)}),u||(u=function(e,t){return new e(...t)});let d=N(Array.prototype.forEach),p=N(Array.prototype.lastIndexOf),m=N(Array.prototype.pop),f=N(Array.prototype.push),g=N(Array.prototype.splice),h=N(String.prototype.toLowerCase),E=N(String.prototype.toString),T=N(String.prototype.match),y=N(String.prototype.replace),_=N(String.prototype.indexOf),A=N(String.prototype.trim),R=N(Object.prototype.hasOwnProperty),w=N(RegExp.prototype.test),b=(X=TypeError,function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u(X,t)});function N(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var r=arguments.length,n=Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return c(e,t,n)}}function O(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:h;r&&r(e,null);let i=t.length;for(;i--;){let r=t[i];if("string"==typeof r){let e=o(r);e!==r&&(n(t)||(t[i]=e),r=e)}e[r]=!0}return e}function S(e){let r=l(null);for(let[n,o]of t(e))R(e,n)&&(Array.isArray(o)?r[n]=function(e){for(let t=0;t<e.length;t++)R(e,t)||(e[t]=null);return e}(o):o&&"object"==typeof o&&o.constructor===Object?r[n]=S(o):r[n]=o);return r}function v(e,t){for(;null!==e;){let r=i(e,t);if(r){if(r.get)return N(r.get);if("function"==typeof r.value)return N(r.value)}e=o(e)}return function(){return null}}let x=a(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),I=a(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),D=a(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),L=a(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),C=a(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),k=a(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),M=a(["#text"]),P=a(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),U=a(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),j=a(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),z=a(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),F=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),H=s(/<%[\w\W]*|[\w\W]*%>/gm),q=s(/\$\{[\w\W]*/gm),W=s(/^data-[\-\w.\u00B7-\uFFFF]+$/),G=s(/^aria-[\-\w]+$/),B=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Y=s(/^(?:\w+script|data):/i),$=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),V=s(/^html$/i);var X,Z=Object.freeze({__proto__:null,ARIA_ATTR:G,ATTR_WHITESPACE:$,CUSTOM_ELEMENT:s(/^[a-z][.\w]*(-[.\w]+)+$/i),DATA_ATTR:W,DOCTYPE_NAME:V,ERB_EXPR:H,IS_ALLOWED_URI:B,IS_SCRIPT_OR_DATA:Y,MUSTACHE_EXPR:F,TMPLIT_EXPR:q});let K={element:1,text:3,progressingInstruction:7,comment:8,document:9},J=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let r=null,n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(r=t.getAttribute(n));let o="dompurify"+(r?"#"+r:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}},Q=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};var ee=function e(){let r,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window,o=t=>e(t);if(o.version="3.2.6",o.removed=[],!n||!n.document||n.document.nodeType!==K.document||!n.Element)return o.isSupported=!1,o;let{document:i}=n,s=i,c=s.currentScript,{DocumentFragment:u,HTMLTemplateElement:N,Node:F,Element:H,NodeFilter:q,NamedNodeMap:W=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:G,DOMParser:Y,trustedTypes:$}=n,X=H.prototype,ee=v(X,"cloneNode"),et=v(X,"remove"),er=v(X,"nextSibling"),en=v(X,"childNodes"),eo=v(X,"parentNode");if("function"==typeof N){let e=i.createElement("template");e.content&&e.content.ownerDocument&&(i=e.content.ownerDocument)}let ei="",{implementation:ea,createNodeIterator:es,createDocumentFragment:el,getElementsByTagName:ec}=i,{importNode:eu}=s,ed=Q();o.isSupported="function"==typeof t&&"function"==typeof eo&&ea&&void 0!==ea.createHTMLDocument;let{MUSTACHE_EXPR:ep,ERB_EXPR:em,TMPLIT_EXPR:ef,DATA_ATTR:eg,ARIA_ATTR:eh,IS_SCRIPT_OR_DATA:eE,ATTR_WHITESPACE:eT,CUSTOM_ELEMENT:ey}=Z,{IS_ALLOWED_URI:e_}=Z,eA=null,eR=O({},[...x,...I,...D,...C,...M]),ew=null,eb=O({},[...P,...U,...j,...z]),eN=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),eO=null,eS=null,ev=!0,ex=!0,eI=!1,eD=!0,eL=!1,eC=!0,ek=!1,eM=!1,eP=!1,eU=!1,ej=!1,ez=!1,eF=!0,eH=!1,eq=!0,eW=!1,eG={},eB=null,eY=O({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),e$=null,eV=O({},["audio","video","img","source","image","track"]),eX=null,eZ=O({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),eK="http://www.w3.org/1998/Math/MathML",eJ="http://www.w3.org/2000/svg",eQ="http://www.w3.org/1999/xhtml",e0=eQ,e1=!1,e5=null,e8=O({},[eK,eJ,eQ],E),e9=O({},["mi","mo","mn","ms","mtext"]),e2=O({},["annotation-xml"]),e6=O({},["title","style","font","a","script"]),e4=null,e7=["application/xhtml+xml","text/html"],e3=null,te=null,tt=i.createElement("form"),tr=function(e){return e instanceof RegExp||e instanceof Function},tn=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!te||te!==e){if(e&&"object"==typeof e||(e={}),e=S(e),e3="application/xhtml+xml"===(e4=-1===e7.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE)?E:h,eA=R(e,"ALLOWED_TAGS")?O({},e.ALLOWED_TAGS,e3):eR,ew=R(e,"ALLOWED_ATTR")?O({},e.ALLOWED_ATTR,e3):eb,e5=R(e,"ALLOWED_NAMESPACES")?O({},e.ALLOWED_NAMESPACES,E):e8,eX=R(e,"ADD_URI_SAFE_ATTR")?O(S(eZ),e.ADD_URI_SAFE_ATTR,e3):eZ,e$=R(e,"ADD_DATA_URI_TAGS")?O(S(eV),e.ADD_DATA_URI_TAGS,e3):eV,eB=R(e,"FORBID_CONTENTS")?O({},e.FORBID_CONTENTS,e3):eY,eO=R(e,"FORBID_TAGS")?O({},e.FORBID_TAGS,e3):S({}),eS=R(e,"FORBID_ATTR")?O({},e.FORBID_ATTR,e3):S({}),eG=!!R(e,"USE_PROFILES")&&e.USE_PROFILES,ev=!1!==e.ALLOW_ARIA_ATTR,ex=!1!==e.ALLOW_DATA_ATTR,eI=e.ALLOW_UNKNOWN_PROTOCOLS||!1,eD=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,eL=e.SAFE_FOR_TEMPLATES||!1,eC=!1!==e.SAFE_FOR_XML,ek=e.WHOLE_DOCUMENT||!1,eU=e.RETURN_DOM||!1,ej=e.RETURN_DOM_FRAGMENT||!1,ez=e.RETURN_TRUSTED_TYPE||!1,eP=e.FORCE_BODY||!1,eF=!1!==e.SANITIZE_DOM,eH=e.SANITIZE_NAMED_PROPS||!1,eq=!1!==e.KEEP_CONTENT,eW=e.IN_PLACE||!1,e_=e.ALLOWED_URI_REGEXP||B,e0=e.NAMESPACE||eQ,e9=e.MATHML_TEXT_INTEGRATION_POINTS||e9,e2=e.HTML_INTEGRATION_POINTS||e2,eN=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&tr(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(eN.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&tr(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(eN.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(eN.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),eL&&(ex=!1),ej&&(eU=!0),eG&&(eA=O({},M),ew=[],!0===eG.html&&(O(eA,x),O(ew,P)),!0===eG.svg&&(O(eA,I),O(ew,U),O(ew,z)),!0===eG.svgFilters&&(O(eA,D),O(ew,U),O(ew,z)),!0===eG.mathMl&&(O(eA,C),O(ew,j),O(ew,z))),e.ADD_TAGS&&(eA===eR&&(eA=S(eA)),O(eA,e.ADD_TAGS,e3)),e.ADD_ATTR&&(ew===eb&&(ew=S(ew)),O(ew,e.ADD_ATTR,e3)),e.ADD_URI_SAFE_ATTR&&O(eX,e.ADD_URI_SAFE_ATTR,e3),e.FORBID_CONTENTS&&(eB===eY&&(eB=S(eB)),O(eB,e.FORBID_CONTENTS,e3)),eq&&(eA["#text"]=!0),ek&&O(eA,["html","head","body"]),eA.table&&(O(eA,["tbody"]),delete eO.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw b('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw b('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ei=(r=e.TRUSTED_TYPES_POLICY).createHTML("")}else void 0===r&&(r=J($,c)),null!==r&&"string"==typeof ei&&(ei=r.createHTML(""));a&&a(e),te=e}},to=O({},[...I,...D,...L]),ti=O({},[...C,...k]),ta=function(e){let t=eo(e);t&&t.tagName||(t={namespaceURI:e0,tagName:"template"});let r=h(e.tagName),n=h(t.tagName);return!!e5[e.namespaceURI]&&(e.namespaceURI===eJ?t.namespaceURI===eQ?"svg"===r:t.namespaceURI===eK?"svg"===r&&("annotation-xml"===n||e9[n]):!!to[r]:e.namespaceURI===eK?t.namespaceURI===eQ?"math"===r:t.namespaceURI===eJ?"math"===r&&e2[n]:!!ti[r]:e.namespaceURI===eQ?(t.namespaceURI!==eJ||!!e2[n])&&(t.namespaceURI!==eK||!!e9[n])&&!ti[r]&&(e6[r]||!to[r]):"application/xhtml+xml"===e4&&!!e5[e.namespaceURI])},ts=function(e){f(o.removed,{element:e});try{eo(e).removeChild(e)}catch(t){et(e)}},tl=function(e,t){try{f(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){f(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e){if(eU||ej)try{ts(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}}},tc=function(e){let t=null,n=null;if(eP)e="<remove></remove>"+e;else{let t=T(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===e4&&e0===eQ&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");let o=r?r.createHTML(e):e;if(e0===eQ)try{t=new Y().parseFromString(o,e4)}catch(e){}if(!t||!t.documentElement){t=ea.createDocument(e0,"template",null);try{t.documentElement.innerHTML=e1?ei:o}catch(e){}}let a=t.body||t.documentElement;return(e&&n&&a.insertBefore(i.createTextNode(n),a.childNodes[0]||null),e0===eQ)?ec.call(t,ek?"html":"body")[0]:ek?t.documentElement:a},tu=function(e){return es.call(e.ownerDocument||e,e,q.SHOW_ELEMENT|q.SHOW_COMMENT|q.SHOW_TEXT|q.SHOW_PROCESSING_INSTRUCTION|q.SHOW_CDATA_SECTION,null)},td=function(e){return e instanceof G&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof W)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},tp=function(e){return"function"==typeof F&&e instanceof F};function tm(e,t,r){d(e,e=>{e.call(o,t,r,te)})}let tf=function(e){let t=null;if(tm(ed.beforeSanitizeElements,e,null),td(e))return ts(e),!0;let r=e3(e.nodeName);if(tm(ed.uponSanitizeElement,e,{tagName:r,allowedTags:eA}),eC&&e.hasChildNodes()&&!tp(e.firstElementChild)&&w(/<[/\w!]/g,e.innerHTML)&&w(/<[/\w!]/g,e.textContent)||e.nodeType===K.progressingInstruction||eC&&e.nodeType===K.comment&&w(/<[/\w]/g,e.data))return ts(e),!0;if(!eA[r]||eO[r]){if(!eO[r]&&th(r)&&(eN.tagNameCheck instanceof RegExp&&w(eN.tagNameCheck,r)||eN.tagNameCheck instanceof Function&&eN.tagNameCheck(r)))return!1;if(eq&&!eB[r]){let t=eo(e)||e.parentNode,r=en(e)||e.childNodes;if(r&&t){let n=r.length;for(let o=n-1;o>=0;--o){let n=ee(r[o],!0);n.__removalCount=(e.__removalCount||0)+1,t.insertBefore(n,er(e))}}}return ts(e),!0}return e instanceof H&&!ta(e)||("noscript"===r||"noembed"===r||"noframes"===r)&&w(/<\/no(script|embed|frames)/i,e.innerHTML)?(ts(e),!0):(eL&&e.nodeType===K.text&&(t=e.textContent,d([ep,em,ef],e=>{t=y(t,e," ")}),e.textContent!==t&&(f(o.removed,{element:e.cloneNode()}),e.textContent=t)),tm(ed.afterSanitizeElements,e,null),!1)},tg=function(e,t,r){if(eF&&("id"===t||"name"===t)&&(r in i||r in tt))return!1;if(ex&&!eS[t]&&w(eg,t));else if(ev&&w(eh,t));else if(!ew[t]||eS[t]){if(!(th(e)&&(eN.tagNameCheck instanceof RegExp&&w(eN.tagNameCheck,e)||eN.tagNameCheck instanceof Function&&eN.tagNameCheck(e))&&(eN.attributeNameCheck instanceof RegExp&&w(eN.attributeNameCheck,t)||eN.attributeNameCheck instanceof Function&&eN.attributeNameCheck(t))||"is"===t&&eN.allowCustomizedBuiltInElements&&(eN.tagNameCheck instanceof RegExp&&w(eN.tagNameCheck,r)||eN.tagNameCheck instanceof Function&&eN.tagNameCheck(r))))return!1}else if(eX[t]);else if(w(e_,y(r,eT,"")));else if(("src"===t||"xlink:href"===t||"href"===t)&&"script"!==e&&0===_(r,"data:")&&e$[e]);else if(eI&&!w(eE,y(r,eT,"")));else if(r)return!1;return!0},th=function(e){return"annotation-xml"!==e&&T(e,ey)},tE=function(e){tm(ed.beforeSanitizeAttributes,e,null);let{attributes:t}=e;if(!t||td(e))return;let n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ew,forceKeepAttr:void 0},i=t.length;for(;i--;){let{name:a,namespaceURI:s,value:l}=t[i],c=e3(a),u="value"===a?l:A(l);if(n.attrName=c,n.attrValue=u,n.keepAttr=!0,n.forceKeepAttr=void 0,tm(ed.uponSanitizeAttribute,e,n),u=n.attrValue,eH&&("id"===c||"name"===c)&&(tl(a,e),u="user-content-"+u),eC&&w(/((--!?|])>)|<\/(style|title)/i,u)){tl(a,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr||!eD&&w(/\/>/i,u)){tl(a,e);continue}eL&&d([ep,em,ef],e=>{u=y(u,e," ")});let p=e3(e.nodeName);if(!tg(p,c,u)){tl(a,e);continue}if(r&&"object"==typeof $&&"function"==typeof $.getAttributeType){if(s);else switch($.getAttributeType(p,c)){case"TrustedHTML":u=r.createHTML(u);break;case"TrustedScriptURL":u=r.createScriptURL(u)}}if(u!==l)try{s?e.setAttributeNS(s,a,u):e.setAttribute(a,u),td(e)?ts(e):m(o.removed)}catch(t){tl(a,e)}}tm(ed.afterSanitizeAttributes,e,null)},tT=function e(t){let r=null,n=tu(t);for(tm(ed.beforeSanitizeShadowDOM,t,null);r=n.nextNode();)tm(ed.uponSanitizeShadowNode,r,null),tf(r),tE(r),r.content instanceof u&&e(r.content);tm(ed.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,i=null,a=null,l=null;if((e1=!e)&&(e="<!-->"),"string"!=typeof e&&!tp(e)){if("function"==typeof e.toString){if("string"!=typeof(e=e.toString()))throw b("dirty is not a string, aborting")}else throw b("toString is not a function")}if(!o.isSupported)return e;if(eM||tn(t),o.removed=[],"string"==typeof e&&(eW=!1),eW){if(e.nodeName){let t=e3(e.nodeName);if(!eA[t]||eO[t])throw b("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof F)(i=(n=tc("<!---->")).ownerDocument.importNode(e,!0)).nodeType===K.element&&"BODY"===i.nodeName?n=i:"HTML"===i.nodeName?n=i:n.appendChild(i);else{if(!eU&&!eL&&!ek&&-1===e.indexOf("<"))return r&&ez?r.createHTML(e):e;if(!(n=tc(e)))return eU?null:ez?ei:""}n&&eP&&ts(n.firstChild);let c=tu(eW?e:n);for(;a=c.nextNode();)tf(a),tE(a),a.content instanceof u&&tT(a.content);if(eW)return e;if(eU){if(ej)for(l=el.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(ew.shadowroot||ew.shadowrootmode)&&(l=eu.call(s,l,!0)),l}let p=ek?n.outerHTML:n.innerHTML;return ek&&eA["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&w(V,n.ownerDocument.doctype.name)&&(p="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+p),eL&&d([ep,em,ef],e=>{p=y(p,e," ")}),r&&ez?r.createHTML(p):p},o.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};tn(e),eM=!0},o.clearConfig=function(){te=null,eM=!1},o.isValidAttribute=function(e,t,r){return te||tn({}),tg(e3(e),e3(t),r)},o.addHook=function(e,t){"function"==typeof t&&f(ed[e],t)},o.removeHook=function(e,t){if(void 0!==t){let r=p(ed[e],t);return -1===r?void 0:g(ed[e],r,1)[0]}return m(ed[e])},o.removeHooks=function(e){ed[e]=[]},o.removeAllHooks=function(){ed=Q()},o}();e.exports=ee},96468:(e,t,r)=>{function n(e){return e&&e.default||e}e.exports=global.DOMPurify=global.DOMPurify||("undefined"!=typeof window?n(r(56141)):function(){let e=n(r(56141)),{JSDOM:t}=n(r(98860)),{window:o}=new t("<!DOCTYPE html>");return e(o)}())},69955:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,5972,8691,2830,9489,5306,9585],()=>r(88811));module.exports=n})();