(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4331],{69812:function(e,t,r){Promise.resolve().then(r.bind(r,11947))},11947:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return g}});var a=r(57437),s=r(2265),n=r(32489),c=r(73247),i=r(99397),l=r(15863),o=r(22252);let d=(0,r(39763).Z)("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);var u=r(44794),x=r(15868),m=r(18930),g=()=>{let[e,t]=(0,s.useState)(""),[r,g]=(0,s.useState)(!1),[h,p]=(0,s.useState)(!1),[y,f]=(0,s.useState)(null),[b,j]=(0,s.useState)([]),[N,v]=(0,s.useState)(!0),[w,k]=(0,s.useState)(null);(0,s.useEffect)(()=>{C()},[]);let C=async()=>{try{v(!0);let e=await fetch("/api/categories",{headers:{"x-admin-request":"true"}}),t=await e.json();t.success?j(t.data):k("Failed to fetch categories")}catch(e){console.error("Error fetching categories:",e),k("Failed to fetch categories")}finally{v(!1)}},Z=b.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())),S=e=>{f(e),p(!0)},A=async e=>{let t=b.find(t=>t.id===e);if(!t)return;let r=(t._count.products||0)+(t._count.productCategories||0);if(confirm(r>0?"This category has ".concat(r," product(s). You need to remove or reassign these products before deleting the category. Continue anyway?"):"Are you sure you want to delete this category?"))try{let t=await fetch("/api/categories/".concat(e),{method:"DELETE"}),r=await t.json();t.ok?(await C(),alert("Category deleted successfully")):alert(r.error||"Failed to delete category")}catch(e){console.error("Error deleting category:",e),alert("Failed to delete category")}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Categories"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your product categories"})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search categories...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),(0,a.jsxs)("button",{onClick:()=>{g(!0)},className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,a.jsx)(i.Z,{className:"w-5 h-5"}),"Add Category"]})]})}),N?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(l.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):w?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:w})]})}):0===Z.length?(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No categories found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Get started by creating your first category."})]})}):(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Products"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:Z.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.slug})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description||"-"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:(e._count.products||0)+(e._count.productCategories||0)})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-3 py-1 text-xs font-medium rounded-full ".concat(e.isActive?"bg-green-100 text-green-700 border border-green-200":"bg-gray-100 text-gray-700 border border-gray-200"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-600",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right",children:[(0,a.jsx)("button",{onClick:()=>S(e),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors mr-2",children:(0,a.jsx)(x.Z,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>A(e.id),className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:(0,a.jsx)(m.Z,{className:"w-5 h-5"})})]})]},e.id))})]})})}),(0,a.jsx)(()=>{let[e,t]=(0,s.useState)({name:"",description:""}),[c,i]=(0,s.useState)(!1),l=async r=>{r.preventDefault(),i(!0);try{let r=await fetch("/api/categories",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(r.ok){let e=await r.json();e.success?(j(t=>[...t,e.data]),g(!1),t({name:"",description:""})):alert("Failed to create category: "+e.error)}else alert("Failed to create category")}catch(e){console.error("Error creating category:",e),alert("Failed to create category")}finally{i(!1)}};return r?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl max-w-md w-full",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Add New Category"}),(0,a.jsx)("button",{onClick:()=>g(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(n.Z,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("form",{onSubmit:l,className:"p-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,a.jsx)("input",{type:"text",value:e.name,onChange:r=>t({...e,name:r.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,a.jsx)("textarea",{value:e.description,onChange:r=>t({...e,description:r.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:4})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{type:"button",onClick:()=>g(!1),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50",disabled:c,children:c?"Creating...":"Add Category"})]})]})]})}):null},{}),(0,a.jsx)(()=>{let[e,t]=(0,s.useState)({name:(null==y?void 0:y.name)||"",description:(null==y?void 0:y.description)||""}),[r,c]=(0,s.useState)(!1);s.useEffect(()=>{y&&t({name:y.name,description:y.description||""})},[y]);let i=async t=>{if(t.preventDefault(),y){c(!0);try{let t=await fetch("/api/categories/".concat(y.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||null})});if(t.ok){let e=await t.json();e.success?(j(t=>t.map(t=>t.id===y.id?e.data:t)),p(!1),f(null)):alert("Failed to update category: "+e.error)}else alert("Failed to update category")}catch(e){console.error("Error updating category:",e),alert("Failed to update category")}finally{c(!1)}}};return h?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl max-w-md w-full",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Edit Category"}),(0,a.jsx)("button",{onClick:()=>{p(!1),f(null)},className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(n.Z,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("form",{onSubmit:i,className:"p-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,a.jsx)("input",{type:"text",value:e.name,onChange:r=>t({...e,name:r.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,a.jsx)("textarea",{value:e.description,onChange:r=>t({...e,description:r.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:4})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{p(!1),f(null)},className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50",disabled:r,children:r?"Saving...":"Save Changes"})]})]})]})}):null},{})]})})}},39763:function(e,t,r){"use strict";r.d(t,{Z:function(){return c}});var a=r(2265),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),c=(e,t)=>{let r=(0,a.forwardRef)((r,c)=>{let{color:i="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:u="",children:x,...m}=r;return(0,a.createElement)("svg",{ref:c,...s,width:l,height:l,stroke:i,strokeWidth:d?24*Number(o)/Number(l):o,className:["lucide","lucide-".concat(n(e)),u].join(" "),...m},[...t.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(x)?x:[x]])});return r.displayName="".concat(e),r}},22252:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},15863:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44794:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},99397:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},73247:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},15868:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},18930:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},32489:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(39763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=69812)}),_N_E=e.O()}]);