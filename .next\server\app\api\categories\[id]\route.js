"use strict";(()=>{var e={};e.id=4831,e.ids=[4831],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},42349:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>I,requestAsyncStorage:()=>y,routeModule:()=>l,serverHooks:()=>S,staticGenerationAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{DELETE:()=>m,GET:()=>c,PATCH:()=>p});var o=r(49303),n=r(88716),a=r(60670),s=r(87070),d=r(89456),u=r(81515);async function c(e,{params:t}){try{await (0,d.Z)();let e=await u.WD.findById(t.id).lean();if(!e)return s.NextResponse.json({success:!1,error:"Category not found"},{status:404});let r=await u.xs.find({categoryId:t.id},{_id:1,name:1,slug:1}).lean(),i=e.parentId?await u.WD.findById(e.parentId).lean():null,o=await u.WD.find({parentId:t.id}).lean(),n=await u.xs.countDocuments({categoryId:t.id}),a={...e,products:r,parent:i,children:o,_count:{products:n}};return s.NextResponse.json({success:!0,data:a})}catch(e){return console.error("Error fetching category:",e),s.NextResponse.json({success:!1,error:"Failed to fetch category"},{status:500})}}async function p(e,{params:t}){try{await (0,d.Z)();let{name:r,slug:i,description:o,image:n,isActive:a,parentId:c}=await e.json(),p={};void 0!==r&&(p.name=r),void 0!==i&&(p.slug=i),void 0!==o&&(p.description=o),void 0!==n&&(p.image=n),"boolean"==typeof a&&(p.isActive=a),void 0!==c&&(p.parentId=c);let m=await u.WD.findByIdAndUpdate(t.id,{$set:p},{new:!0,lean:!0});if(!m)return s.NextResponse.json({success:!1,error:"Category not found"},{status:404});let l=m.parentId?await u.WD.findById(m.parentId).lean():null,y=await u.xs.countDocuments({categoryId:t.id});return s.NextResponse.json({success:!0,data:{...m,parent:l,_count:{products:y}},message:"Category updated successfully"})}catch(e){return console.error("Error updating category:",e),s.NextResponse.json({success:!1,error:"Failed to update category"},{status:500})}}async function m(e,{params:t}){try{await (0,d.Z)();let e=await u.xs.countDocuments({categoryId:t.id}),r=0;try{u.Pp&&(r=await u.Pp.countDocuments({categoryId:t.id}))}catch(e){console.log("ProductCategory collection not found, skipping many-to-many check")}let i=e+r;if(i>0)return s.NextResponse.json({success:!1,error:`Cannot delete category. ${i} product(s) are still assigned to this category.`},{status:400});let o=await u.WD.countDocuments({parentId:t.id});if(o>0)return s.NextResponse.json({success:!1,error:`Cannot delete category. ${o} subcategory(ies) exist under this category.`},{status:400});return await u.WD.deleteOne({_id:t.id}),s.NextResponse.json({success:!0,message:"Category deleted successfully"})}catch(e){return console.error("Error deleting category:",e),s.NextResponse.json({success:!1,error:"Failed to delete category"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:S}=l,f="/api/categories/[id]/route";function I(){return(0,a.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:g})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>j,Dd:()=>x,Order:()=>P,P_:()=>R,Pp:()=>B,Th:()=>O,Vv:()=>F,WD:()=>D,gc:()=>E,hQ:()=>M,kL:()=>v,mA:()=>U,n5:()=>b,nW:()=>T,p1:()=>G,qN:()=>C,wV:()=>A,xs:()=>w});var i=r(11185),o=r.n(i);let n=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),a=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),s=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),c=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),f=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),I=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),h=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),q=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),N=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),b=o().models.User||o().model("User",n),w=o().models.Product||o().model("Product",a),D=o().models.Category||o().model("Category",s),P=o().models.Order||o().model("Order",d),T=o().models.HomepageSetting||o().model("HomepageSetting",u),E=o().models.Testimonial||o().model("Testimonial",c),C=o().models.ProductImage||o().model("ProductImage",p),O=o().models.ProductVariant||o().model("ProductVariant",m),j=o().models.Review||o().model("Review",l),v=o().models.Address||o().model("Address",y),x=o().models.OrderItem||o().model("OrderItem",g),R=o().models.Notification||o().model("Notification",S),A=o().models.Coupon||o().model("Coupon",f);o().models.Wishlist||o().model("Wishlist",I);let U=o().models.Newsletter||o().model("Newsletter",h),B=o().models.ProductCategory||o().model("ProductCategory",q),M=o().models.WishlistItem||o().model("WishlistItem",N),W=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),F=o().models.NotificationTemplate||o().model("NotificationTemplate",W),k=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),G=o().models.Enquiry||o().model("Enquiry",k)},89456:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(11185),o=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let s=async function(){if(a.conn)return a.conn;a.promise||(a.promise=o().connect(n,{bufferCommands:!1}));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(42349));module.exports=i})();