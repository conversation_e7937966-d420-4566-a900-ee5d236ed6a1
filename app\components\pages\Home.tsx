"use client";

import React, { useState, useEffect } from "react";
import <PERSON> from "next/link";
import {
  ArrowRight,
  Heart,
  Shield,
  TrendingUp,
  Award,
  Star,
  Leaf,
  Truck,
  RotateCcw,
} from "lucide-react";
import NewsletterSignup from "../NewsletterSignup";
import Hero from "../Hero";
import TestimonialsSection from "../TestimonialsSection";
import CountdownBanner from "../CountdownBanner";

const Home: React.FC = () => {
  const [products, setProducts] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [settings, setSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [flashSaleActive, setFlashSaleActive] = useState(false);
  const [flashSalePercentage, setFlashSalePercentage] = useState(25);

  useEffect(() => {
    fetchHomepageData();
  }, []);

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.warn("Homepage data fetch timeout, using fallback data");
        setLoading(false);
        setProducts([]);
        setCategories([]);
        setSettings({
          showHero: true,
          showCategories: true,
          showBestsellers: true,
          showNewsletter: true,
          showTestimonials: true,
          showTrustBadges: true,
          showFlashSale: false,
          heroTitle: "Natural Skincare Essentials",
          heroSubtitle:
            "Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",
          heroCtaText: "Shop Collection",
          heroCtaLink: "/shop",
          heroBackgroundColor: "#f0fdf4",
        });
      }
    }, 5000);

    return () => clearTimeout(timeout);
  }, [loading]);

  const calculateFlashSalePrice = (
    originalPrice: number,
    discountPercentage: number,
  ): number => {
    const discountAmount = (originalPrice * discountPercentage) / 100;
    return Math.round((originalPrice - discountAmount) * 100) / 100;
  };

  const applyFlashSalePricing = (
    products: any[],
    discountPercentage: number,
  ): any[] => {
    return products.map((product) => ({
      ...product,
      originalPrice: product.price,
      flashSalePrice: calculateFlashSalePrice(
        product.price,
        discountPercentage,
      ),
      isFlashSale: true,
    }));
  };

  const fetchHomepageData = async () => {
    try {
      // Fetch all homepage data from the new endpoint with cache busting
      const homepageRes = await fetch("/api/homepage-settings", {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (!homepageRes.ok) {
        throw new Error(`HTTP error! status: ${homepageRes.status}`);
      }

      const homepageData = await homepageRes.json();

      if (homepageData.success) {
        const settings = homepageData.data?.settings;
        const isFlashSaleActive = settings?.showFlashSale === true;
        const discountPercentage = settings?.flashSalePercentage || 25;

        setFlashSaleActive(isFlashSaleActive);
        setFlashSalePercentage(discountPercentage);

        // Create a combined array of products
        let allProducts = [];

        // Add featured product (Product of the Month) if available
        if (homepageData.data.featuredProduct) {
          allProducts.push(homepageData.data.featuredProduct);
        }

        // Add bestsellers
        if (homepageData.data.bestsellers) {
          allProducts.push(...homepageData.data.bestsellers);
        }

        // Apply flash sale pricing if active
        if (isFlashSaleActive && allProducts.length > 0) {
          allProducts = applyFlashSalePricing(allProducts, discountPercentage);
        }

        setProducts(allProducts);

        // Set categories
        if (homepageData.data.categories) {
          setCategories(homepageData.data.categories);
        }

        // Set homepage settings
        if (settings) {
          setSettings(settings);
        }
      } else {
        console.warn("Homepage API returned success: false", homepageData);
        // Use fallback data
        setProducts([]);
        setCategories([]);
        setSettings({
          showHero: true,
          showCategories: true,
          showBestsellers: true,
          showNewsletter: true,
          showTestimonials: true,
          showTrustBadges: true,
          showFlashSale: false,
          heroTitle: "Natural Skincare Essentials",
          heroSubtitle:
            "Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",
          heroCtaText: "Shop Collection",
          heroCtaLink: "/shop",
          heroBackgroundColor: "#f0fdf4",
        });
      }
    } catch (error) {
      console.error("Error fetching homepage data:", error);
      // Use fallback data on error
      setProducts([]);
      setCategories([]);
      setSettings({
        showHero: true,
        showCategories: true,
        showBestsellers: true,
        showNewsletter: true,
        showTestimonials: true,
        showTrustBadges: true,
        showFlashSale: false,
        heroTitle: "Natural Skincare Essentials",
        heroSubtitle:
          "Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",
        heroCtaText: "Shop Collection",
        heroCtaLink: "/shop",
        heroBackgroundColor: "#f0fdf4",
      });
    } finally {
      setLoading(false);
    }
  };

  // Get Product of the Month (first product)
  const productOfTheMonth = products[0];

  // Get Bestsellers (next 4 products)
  const bestSellers = products.slice(1, 5);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-green-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }
        @keyframes slideIn {
          from {
            transform: translateY(20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        .animate-fadeIn {
          animation: fadeIn 0.5s ease-in-out;
        }
        .animate-slideIn {
          animation: slideIn 0.5s ease-out;
        }
      `}</style>

      {/* Countdown Banner */}
      {settings?.showFlashSale === true && (
        <CountdownBanner
          title={settings?.flashSaleTitle || "Weekend Flash Sale"}
          subtitle={
            settings?.flashSaleSubtitle ||
            `Get ${settings?.flashSalePercentage || 25}% off all natural skincare products`
          }
          endDate={settings?.flashSaleEndDate}
          backgroundColor={settings?.flashSaleBackgroundColor || "#16a34a"}
          discountPercentage={settings?.flashSalePercentage || 25}
        />
      )}

      {/* Hero Section */}
      {settings?.showHero && (
        <Hero
          title={settings?.heroTitle}
          subtitle={settings?.heroSubtitle}
          ctaText={settings?.heroCtaText}
          ctaLink={settings?.heroCtaLink}
          secondaryCtaText={settings?.heroSecondaryCtaText}
          secondaryCtaLink={settings?.heroSecondaryCtaLink}
          badgeText={settings?.heroBadgeText}
          backgroundColor={settings?.heroBackgroundColor}
          trustIndicators={{
            value1: settings?.trustIndicator1Value,
            label1: settings?.trustIndicator1Label,
            value2: settings?.trustIndicator2Value,
            label2: settings?.trustIndicator2Label,
            value3: settings?.trustIndicator3Value,
            label3: settings?.trustIndicator3Label,
            value4: settings?.trustIndicator4Value,
            label4: settings?.trustIndicator4Label,
          }}
        />
      )}

      {/* Promotional Banners */}
      {settings?.showBanner && (
        <div className="px-4 mt-4 grid grid-cols-2 gap-3 lg:px-8 lg:mt-6 lg:gap-4">
          {settings?.bannerText ? (
            <div
              className="rounded-xl p-4 text-white shadow-lg col-span-2"
              style={{
                backgroundColor: settings.bannerBackgroundColor || "#22c55e",
              }}
            >
              <h3 className="font-bold text-sm mb-1 lg:text-base">
                {settings.bannerText}
              </h3>
              {settings.bannerCtaText && settings.bannerCtaLink && (
                <Link
                  href={settings.bannerCtaLink}
                  className="inline-flex items-center text-xs font-medium underline lg:text-sm hover:opacity-90"
                >
                  {settings.bannerCtaText}
                  <ArrowRight className="ml-1 w-3 h-3" />
                </Link>
              )}
            </div>
          ) : (
            <div className="bg-gradient-to-r from-green-500 to-teal-500 rounded-xl p-4 text-white shadow-lg col-span-2">
              <h3 className="font-bold text-sm mb-1 lg:text-base">
                Explore Our Collections
              </h3>
              <p className="text-xs opacity-90 mb-2 lg:text-sm">
                Find the perfect products for your needs.
              </p>
              <Link
                href="/shop"
                className="inline-flex items-center text-xs font-medium underline lg:text-sm hover:opacity-90"
              >
                Shop All
                <ArrowRight className="ml-1 w-3 h-3" />
              </Link>
            </div>
          )}
        </div>
      )}

      {/* Product of the Month */}
      {settings?.showProductOfMonth && productOfTheMonth && (
        <div className="mx-4 mt-6 lg:mx-8">
          <div
            className="rounded-2xl p-4 lg:rounded-3xl lg:p-6"
            style={{
              backgroundColor: settings?.productSectionBgColor || "#f0fdf4",
            }}
          >
            <div className="flex items-center mb-2 lg:mb-3">
              <Award className="w-4 h-4 mr-1 lg:w-5 lg:h-5 text-green-600" />
              <span className="text-xs font-semibold lg:text-sm text-green-800">
                PRODUCT OF THE MONTH
              </span>
            </div>
            <div className="flex items-center space-x-3 lg:space-x-4">
              <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center lg:w-20 lg:h-20">
                {productOfTheMonth.images?.[0] && (
                  <img
                    src={productOfTheMonth.images[0].url}
                    alt={productOfTheMonth.name}
                    className="w-full h-full object-cover rounded-lg"
                  />
                )}
              </div>
              <div className="flex-1">
                <h3 className="font-bold text-gray-900 text-sm lg:text-base">
                  {productOfTheMonth.name}
                </h3>
                <p className="text-gray-600 text-xs mt-1 line-clamp-2 lg:text-sm">
                  {productOfTheMonth.shortDescription ||
                    productOfTheMonth.description?.substring(0, 80) + "..."}
                </p>
                <div className="flex items-center mt-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-3 h-3 ${i < Math.floor(productOfTheMonth._count?.reviews / 2) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                      />
                    ))}
                    <span className="text-xs ml-1 text-gray-600">
                      ({productOfTheMonth._count?.reviews || 0} reviews)
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center space-x-2">
                    {flashSaleActive && productOfTheMonth.flashSalePrice ? (
                      <>
                        <span className="text-base font-bold lg:text-lg text-gray-900">
                          ₹{productOfTheMonth.flashSalePrice}
                        </span>
                        <span className="text-sm text-gray-500 line-through">
                          ₹{productOfTheMonth.originalPrice}
                        </span>
                        <span className="text-xs bg-red-500 text-white px-2 py-0.5 rounded-full">
                          {flashSalePercentage}% OFF
                        </span>
                      </>
                    ) : (
                      <span className="text-base font-bold lg:text-lg text-gray-900">
                        ₹{productOfTheMonth.price}
                      </span>
                    )}
                  </div>
                  <Link
                    href={`/product/${productOfTheMonth.slug}`}
                    className="bg-green-600 text-white px-3 py-1 rounded-full text-xs font-medium hover:bg-green-700 transition-colors lg:px-4 lg:py-1.5 lg:text-sm"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Categories Section */}
      {settings?.showCategories && (
        <div className="px-4 mt-8 lg:px-8 lg:mt-10">
          <div className="flex items-center justify-between mb-5 lg:mb-7">
            <h2 className="text-2xl font-bold text-gray-800 lg:text-3xl">
              Shop by Category
            </h2>
            <Link
              href="/categories"
              className="inline-flex items-center text-green-600 text-sm font-medium hover:text-green-700 transition-colors lg:text-base"
            >
              See All
              <ArrowRight className="ml-1 w-4 h-4" />
            </Link>
          </div>

          <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 lg:gap-5">
            {categories.map((category: any) => (
              <Link
                key={category._id}
                href={`/shop?category=${category.slug}`}
                className="bg-white p-5 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 lg:p-6"
              >
                <h3 className="font-semibold text-gray-800 text-base lg:text-lg mb-2">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-600 mt-1 lg:text-base">
                  {category._count?.products || 0} products
                </p>
                <div className="mt-3 inline-flex items-center text-xs text-green-600 font-medium">
                  Shop now
                  <ArrowRight className="ml-1 w-3 h-3" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Best Sellers */}
      {settings?.showBestsellers && (
        <div className="px-4 mt-8 mb-8 lg:px-8 lg:mt-10 lg:mb-10">
          <div className="flex items-center justify-between mb-5 lg:mb-7">
            <h2 className="text-2xl font-bold text-gray-800 lg:text-3xl">
              Best Sellers
            </h2>
            <div className="flex items-center text-amber-500">
              <TrendingUp className="w-4 h-4 mr-1 lg:w-5 lg:h-5" />
              <span className="text-xs font-medium lg:text-sm">Trending</span>
            </div>
          </div>

          <div className="space-y-4 lg:space-y-5">
            {bestSellers.map((product: any) => (
              <Link
                key={product._id}
                href={`/product/${product.slug}`}
                className="flex items-center space-x-4 bg-white p-4 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 lg:p-5"
              >
                <div className="w-16 h-16 bg-gray-100 rounded-xl flex-shrink-0 lg:w-20 lg:h-20">
                  {product.images?.[0] && (
                    <img
                      src={product.images[0].url}
                      alt={product.name}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-800 text-base lg:text-lg">
                    {product.name}
                  </h3>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-3 h-3 ${i < Math.floor(product._count?.reviews / 2) ? "fill-amber-400 text-amber-400" : "text-gray-300"}`}
                        />
                      ))}
                      <span className="text-xs ml-1 text-gray-500">
                        ({product._count?.reviews || 0})
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {flashSaleActive && product.flashSalePrice ? (
                      <>
                        <span className="text-green-600 font-medium text-base lg:text-lg">
                          ₹{product.flashSalePrice}
                        </span>
                        <span className="text-sm text-gray-500 line-through">
                          ₹{product.originalPrice}
                        </span>
                        <span className="text-xs bg-red-500 text-white px-2 py-0.5 rounded-full">
                          {flashSalePercentage}% OFF
                        </span>
                      </>
                    ) : (
                      <span className="text-green-600 font-medium text-base lg:text-lg">
                        ₹{product.price}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex flex-col items-end">
                  <ArrowRight className="w-5 h-5 text-gray-400 lg:w-6 lg:h-6" />
                  <span className="text-xs text-gray-500 mt-2">
                    View Details
                  </span>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Testimonials Section */}
      {settings?.showTestimonials && (
        <div className="mt-8 lg:mt-10">
          <TestimonialsSection
            title={settings?.testimonialsTitle || "What Our Customers Say"}
            subtitle={
              settings?.testimonialsSubtitle ||
              "Real reviews from real customers who love our natural skincare"
            }
            backgroundColor={settings?.testimonialsBackgroundColor || "#f0fdf4"}
          />
        </div>
      )}

      {/* Newsletter Signup - Moved to bottom */}
      {settings?.showNewsletter && (
        <div className="px-4 mt-8 lg:px-8 lg:mt-12 pb-8">
          <NewsletterSignup
            title={settings?.newsletterTitle || "Stay Updated"}
            subtitle={
              settings?.newsletterSubtitle ||
              "Get the latest updates on new products and exclusive offers"
            }
            backgroundColor={settings?.productSectionBgColor || "#f0fdf4"}
            source="homepage"
          />
        </div>
      )}
    </div>
  );
};

export default Home;
