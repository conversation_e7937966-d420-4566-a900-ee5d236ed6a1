exports.id=9536,exports.ids=[9536],exports.modules={76982:(t,e,a)=>{Promise.resolve().then(a.bind(a,94494)),Promise.resolve().then(a.bind(a,69719)),Promise.resolve().then(a.bind(a,52807)),Promise.resolve().then(a.bind(a,67520)),Promise.resolve().then(a.bind(a,47537))},10138:(t,e,a)=>{Promise.resolve().then(a.t.bind(a,12994,23)),Promise.resolve().then(a.t.bind(a,96114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,79671,23)),Promise.resolve().then(a.t.bind(a,41868,23)),Promise.resolve().then(a.t.bind(a,84759,23))},94494:(t,e,a)=>{"use strict";a.d(e,{CartProvider:()=>f,j:()=>h});var o=a(10326),s=a(17577);let i=(0,s.createContext)(null),r=t=>{try{let{isHydrated:e,...a}=t}catch(t){console.error("Error saving cart to localStorage:",t)}},n=()=>null,l=(t,e)=>{if(!e||0===e.length)return t;let a=[...e].sort((t,e)=>t.name.localeCompare(e.name)).map(t=>`${t.name}:${t.value}`).join("|");return`${t}__${a}`},u=t=>t.variantKey||t.product?.id||t.id,c=()=>({items:[],total:0,subtotal:0,itemCount:0,finalTotal:0,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]},flashSaleDiscount:0,flashSaleActive:!1,flashSalePercentage:0,isHydrated:!1}),d=(t,e,a,o)=>{let s=0,i=0;t.forEach(t=>{let e=t.product.price;if(a&&o>0){let a=e*o/100;s+=(e-a)*t.quantity,i+=a*t.quantity}else s+=e*t.quantity});let r=t.reduce((t,e)=>t+e.quantity,0),n=e.reduce((t,e)=>t+e.discountAmount,0),l=s-n;return{subtotal:s,itemCount:r,total:s,finalTotal:l,flashSaleDiscount:i,couponDiscount:n}},p=(t,e)=>{let{flashSaleActive:a,flashSalePercentage:o}={flashSaleActive:!1,flashSalePercentage:0};switch(e.type){case"SET_CART_STATE":return{...e.payload,isHydrated:!0};case"ADD_ITEM":{let s;let i=l(e.payload.id,e.selectedVariants);if(t.items.find(t=>u(t)===i))s=t.items.map(t=>u(t)===i?{...t,quantity:t.quantity+1,variantKey:i}:t);else{let a={product:e.payload,quantity:1,selectedVariants:e.selectedVariants||[],variantKey:i};s=[...t.items,a]}let n=d(s,t.coupons.appliedCoupons,a,o),c={...t,items:s,subtotal:n.subtotal,itemCount:n.itemCount,total:n.total,finalTotal:n.finalTotal,coupons:{...t.coupons,totalDiscount:n.couponDiscount},flashSaleDiscount:n.flashSaleDiscount,flashSaleActive:a,flashSalePercentage:o};return r(c),c}case"REMOVE_ITEM":{let s=t.items.filter(t=>u(t)!==e.payload),i=d(s,t.coupons.appliedCoupons,a,o),n={...t,items:s,subtotal:i.subtotal,itemCount:i.itemCount,total:i.total,finalTotal:i.finalTotal,coupons:{...t.coupons,totalDiscount:i.couponDiscount},flashSaleDiscount:i.flashSaleDiscount,flashSaleActive:a,flashSalePercentage:o};return r(n),n}case"UPDATE_QUANTITY":{let s=t.items.map(t=>u(t)===e.payload.id?{...t,quantity:e.payload.quantity}:t).filter(t=>t.quantity>0),i=d(s,t.coupons.appliedCoupons,a,o),n={...t,items:s,subtotal:i.subtotal,itemCount:i.itemCount,total:i.total,finalTotal:i.finalTotal,coupons:{...t.coupons,totalDiscount:i.couponDiscount},flashSaleDiscount:i.flashSaleDiscount,flashSaleActive:a,flashSalePercentage:o};return r(n),n}case"APPLY_COUPON":{if(t.coupons.appliedCoupons.some(t=>t.coupon.id===e.payload.coupon.id)||t.coupons.appliedCoupons.some(t=>!t.coupon.isStackable)&&!e.payload.coupon.isStackable)return t;let s=[...t.coupons.appliedCoupons,e.payload],i=d(t.items,s,a,o),n={...t,subtotal:i.subtotal,itemCount:i.itemCount,total:i.total,finalTotal:i.finalTotal,coupons:{...t.coupons,appliedCoupons:s,totalDiscount:i.couponDiscount},flashSaleDiscount:i.flashSaleDiscount,flashSaleActive:a,flashSalePercentage:o};return r(n),n}case"REMOVE_COUPON":{let s=t.coupons.appliedCoupons.filter(t=>t.coupon.id!==e.payload),i=d(t.items,s,a,o),n={...t,subtotal:i.subtotal,itemCount:i.itemCount,total:i.total,finalTotal:i.finalTotal,coupons:{...t.coupons,appliedCoupons:s,totalDiscount:i.couponDiscount},flashSaleDiscount:i.flashSaleDiscount,flashSaleActive:a,flashSalePercentage:o};return r(n),n}case"CLEAR_COUPONS":{let e=d(t.items,[],a,o),s={...t,subtotal:e.subtotal,itemCount:e.itemCount,total:e.total,finalTotal:e.finalTotal,coupons:{appliedCoupons:[],totalDiscount:0,availableCoupons:[]},flashSaleDiscount:e.flashSaleDiscount,flashSaleActive:a,flashSalePercentage:o};return r(s),s}case"CLEAR_CART":{let t={...c(),isHydrated:!0,flashSaleActive:a,flashSalePercentage:o};return r(t),t}default:return t}},f=({children:t})=>{let[e,a]=(0,s.useReducer)(p,c());return(0,s.useEffect)(()=>{let t=n();t?a({type:"SET_CART_STATE",payload:t}):a({type:"SET_CART_STATE",payload:c()})},[]),(0,s.useEffect)(()=>{e.isHydrated&&e.items.length>0&&a({type:"UPDATE_QUANTITY",payload:{id:e.items[0].variantKey||e.items[0].product.id,quantity:e.items[0].quantity}})},[e.isHydrated]),(0,s.useEffect)(()=>{if(!e.isHydrated)return;let t=t=>{"flashSaleSettings"===t.key&&e.items.length>0&&a({type:"UPDATE_QUANTITY",payload:{id:e.items[0].variantKey||e.items[0].product.id,quantity:e.items[0].quantity}})},o=()=>{e.items.length>0&&a({type:"UPDATE_QUANTITY",payload:{id:e.items[0].variantKey||e.items[0].product.id,quantity:e.items[0].quantity}})};return window.addEventListener("storage",t),window.addEventListener("flashSaleSettingsUpdated",o),()=>{window.removeEventListener("storage",t),window.removeEventListener("flashSaleSettingsUpdated",o)}},[e.isHydrated,e.items]),o.jsx(i.Provider,{value:{state:e,dispatch:a},children:t})},h=()=>{let t=(0,s.useContext)(i);if(!t)throw Error("useCart must be used within a CartProvider");return t}},69719:(t,e,a)=>{"use strict";a.d(e,{FlashSaleProvider:()=>r,u:()=>n});var o=a(10326),s=a(17577);let i=(0,s.createContext)(void 0);function r({children:t}){let[e,a]=(0,s.useState)(null),[r,n]=(0,s.useState)(!0),[l,u]=(0,s.useState)(!1),c=async()=>{try{let t=await fetch("/api/homepage-settings"),e=await t.json();if(e.success&&e.data.settings){let t=e.data.settings,o={showFlashSale:t.showFlashSale,flashSaleEndDate:t.flashSaleEndDate,flashSalePercentage:t.flashSalePercentage,flashSaleTitle:t.flashSaleTitle,flashSaleSubtitle:t.flashSaleSubtitle,flashSaleBackgroundColor:t.flashSaleBackgroundColor};a(o)}}catch(t){console.error("Error fetching flash sale settings:",t)}finally{n(!1)}},d=async()=>{n(!0),await c()};return o.jsx(i.Provider,{value:{flashSaleSettings:e,loading:r,isHydrated:l,refreshSettings:d},children:t})}function n(){let t=(0,s.useContext)(i);if(void 0===t)throw Error("useFlashSale must be used within a FlashSaleProvider");return t}},52807:(t,e,a)=>{"use strict";a.d(e,{NotificationProvider:()=>l,z:()=>n});var o=a(10326),s=a(17577),i=a(77109);let r=(0,s.createContext)(void 0),n=()=>{let t=(0,s.useContext)(r);if(void 0===t)throw Error("useNotifications must be used within a NotificationProvider");return t},l=({children:t})=>{let{data:e,status:a}=(0,i.useSession)(),[n,l]=(0,s.useState)([]),[u,c]=(0,s.useState)(0),[d,p]=(0,s.useState)(!1),[f,h]=(0,s.useState)(null),[m,C]=(0,s.useState)(0),x=(0,s.useCallback)(async(t={})=>{if(e?.user?.id)try{p(!0),h(null);let e=new URLSearchParams({page:(t.page||1).toString(),limit:(t.limit||10).toString(),...t.unreadOnly&&{unreadOnly:"true"}}),a=await fetch(`/api/notifications?${e}`),o=await a.json();o.success?(l(o.data.notifications),c(o.data.unreadCount)):h(o.error||"Failed to fetch notifications")}catch(t){console.error("Error fetching notifications:",t),h("Failed to fetch notifications")}finally{p(!1)}},[e?.user?.id]),S=(0,s.useCallback)(async()=>{await x({limit:10})},[x]),y=(0,s.useCallback)(async()=>{if(e?.user?.id)try{let t=await fetch("/api/notifications/unread-count"),e=await t.json();e.success&&(e.unreadCount>m&&x({limit:10}),C(e.unreadCount),c(e.unreadCount))}catch(t){console.error("Error fetching unread count:",t)}},[e?.user?.id]),v=(0,s.useCallback)(async t=>{if(e?.user?.id)try{let e=await fetch(`/api/notifications/${t}/read`,{method:"POST"}),a=await e.json();a.success?(l(e=>e.map(e=>e.id===t?{...e,isRead:!0}:e)),c(t=>Math.max(0,t-1))):h(a.error||"Failed to mark notification as read")}catch(t){console.error("Error marking notification as read:",t),h("Failed to mark notification as read")}},[e?.user?.id]),w=(0,s.useCallback)(async()=>{if(e?.user?.id)try{let t=await fetch("/api/notifications/mark-all-read",{method:"POST"}),e=await t.json();e.success?(l(t=>t.map(t=>({...t,isRead:!0}))),c(0)):h(e.error||"Failed to mark all notifications as read")}catch(t){console.error("Error marking all notifications as read:",t),h("Failed to mark all notifications as read")}},[e?.user?.id]);return(0,s.useEffect)(()=>{"authenticated"===a&&e?.user?.id&&(x({limit:5}),y())},[a,e?.user?.id,x,y]),(0,s.useEffect)(()=>{if(!e?.user?.id)return;let t=setInterval(()=>{y()},3e4);return()=>clearInterval(t)},[e?.user?.id,y]),o.jsx(r.Provider,{value:{notifications:n,unreadCount:u,loading:d,error:f,fetchNotifications:x,markAsRead:v,markAllAsRead:w,refreshUnreadCount:y,refreshNotifications:S},children:t})}},67520:(t,e,a)=>{"use strict";a.d(e,{default:()=>i});var o=a(10326),s=a(77109);function i({children:t}){return o.jsx(s.SessionProvider,{children:t})}},47537:(t,e,a)=>{"use strict";a.d(e,{ToastProvider:()=>m,V:()=>C});var o=a(10326),s=a(17577);let i=()=>{let[t,e]=(0,s.useState)([]);return{toasts:t,showToast:(0,s.useCallback)((t,a="info")=>{let o=Date.now();e(e=>[...e,{id:o,message:t,type:a}])},[]),hideToast:(0,s.useCallback)(t=>{e(e=>e.filter(e=>e.id!==t))},[])}};var r=a(54659),n=a(91470),l=a(18019),u=a(94019);let c={success:o.jsx(r.Z,{className:"w-5 h-5 text-white"}),error:o.jsx(n.Z,{className:"w-5 h-5 text-white"}),info:o.jsx(l.Z,{className:"w-5 h-5 text-white"})},d={success:"bg-green-500",error:"bg-red-500",info:"bg-blue-500"},p=({message:t,type:e,onClose:a})=>((0,s.useEffect)(()=>{let t=setTimeout(()=>{a()},5e3);return()=>{clearTimeout(t)}},[a]),(0,o.jsxs)("div",{className:`fixed bottom-5 right-5 flex items-center p-4 rounded-lg shadow-lg text-white ${d[e]} z-50 transition-transform transform animate-slide-in`,children:[o.jsx("div",{className:"mr-3",children:c[e]}),o.jsx("div",{className:"flex-1",children:t}),o.jsx("button",{onClick:a,className:"ml-4 p-1 rounded-full hover:bg-white/20",children:o.jsx(u.Z,{className:"w-4 h-4"})})]})),f=({toasts:t,hideToast:e})=>o.jsx("div",{className:"fixed bottom-5 right-5 z-50",children:t.map(t=>o.jsx(p,{message:t.message,type:t.type,onClose:()=>e(t.id)},t.id))}),h=(0,s.createContext)(void 0),m=({children:t})=>{let{toasts:e,showToast:a,hideToast:s}=i();return(0,o.jsxs)(h.Provider,{value:{showToast:a},children:[t,o.jsx(f,{toasts:e,hideToast:s})]})},C=()=>{let t=(0,s.useContext)(h);if(void 0===t)throw Error("useToastContext must be used within a ToastProvider");return t}},37254:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>h,metadata:()=>p,viewport:()=>f});var o=a(19510),s=a(77366),i=a.n(s);a(67272);var r=a(68570);let n=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`);(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);let u=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`);let c=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#FlashSaleProvider`);(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#useFlashSale`);let d=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\ToastContext.tsx#ToastProvider`);(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\ToastContext.tsx#useToastContext`);let p={title:"Herbalicious - Natural Skincare",description:"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."},f={width:"device-width",initialScale:1,themeColor:"#16a34a"};function h({children:t}){return o.jsx("html",{lang:"en",children:o.jsx("body",{className:i().className,children:o.jsx(l,{children:o.jsx(u,{children:o.jsx(c,{children:o.jsx(n,{children:o.jsx(d,{children:t})})})})})})})}},67272:()=>{}};