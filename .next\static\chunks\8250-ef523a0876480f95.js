"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8250],{28250:function(e,s,t){t.d(s,{default:function(){return H}});var a=t(57437),l=t(2265),r=t(27648),i=t(33145),n=t(99376),c=t(80605),x=t(58293),o=t(94766),d=t(16275),h=t(92369),m=t(99637),f=t(42449),u=t(53827),g=t(19124),j=t(33245),p=t(89345),N=t(88906),b=t(48736),y=t(40340),v=t(70112),w=t(32489),Z=t(73247),C=t(37760),k=t(79219),S=t(42351),A=e=>{var s,t;let{isOpen:l,onClose:i}=e,{data:n}=(0,c.useSession)();if(!l)return null;let x=[{href:"/",icon:(0,a.jsx)(m.Z,{className:"w-5 h-5 text-white"}),label:"Home"},{href:"/shop",icon:(0,a.jsx)(f.Z,{className:"w-5 h-5 text-white"}),label:"Shop"},{href:"/about",icon:(0,a.jsx)(j.Z,{className:"w-5 h-5 text-white"}),label:"About"},{href:"/contact",icon:(0,a.jsx)(p.Z,{className:"w-5 h-5 text-white"}),label:"Contact"}],o=[{href:"/privacy",icon:(0,a.jsx)(N.Z,{className:"w-5 h-5 text-white"}),label:"Privacy Policy"},{href:"/terms",icon:(0,a.jsx)(b.Z,{className:"w-5 h-5 text-white"}),label:"Terms and Conditions"},{href:"/shipping",icon:(0,a.jsx)(y.Z,{className:"w-5 h-5 text-white"}),label:"Shippings and Returns"},{href:"/faq",icon:(0,a.jsx)(v.Z,{className:"w-5 h-5 text-white"}),label:"FAQs"}];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden",children:(0,a.jsx)("div",{className:"fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-60",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"p-4 flex items-center justify-between border-b",children:[(0,a.jsxs)("h2",{className:"font-bold text-lg",children:["Welcome, ",(null==n?void 0:null===(t=n.user)||void 0===t?void 0:null===(s=t.name)||void 0===s?void 0:s.split(" ")[0])||"User"]}),(0,a.jsx)("button",{onClick:i,className:"p-2 rounded-full hover:bg-gray-100",children:(0,a.jsx)(w.Z,{className:"w-5 h-5 text-gray-600"})})]}),(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(Z.Z,{className:"absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search products...",className:"w-full bg-gray-100 border border-gray-200 rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-green-500"})]})}),(0,a.jsxs)("nav",{className:"flex-1 overflow-y-auto p-4 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2",children:"Main"}),(0,a.jsx)("ul",{className:"space-y-1",children:x.map(e=>(0,a.jsx)("li",{children:(0,a.jsxs)(r.default,{href:e.href,onClick:i,className:"flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium",children:[(0,a.jsx)("div",{className:"mr-3 bg-green-600 p-2 rounded-full",children:e.icon}),e.label]})},e.href))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2",children:"Information"}),(0,a.jsx)("ul",{className:"space-y-1",children:o.map(e=>(0,a.jsx)("li",{children:(0,a.jsxs)(r.default,{href:e.href,onClick:i,className:"flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium",children:[(0,a.jsx)("div",{className:"mr-3 bg-green-600 p-2 rounded-full",children:e.icon}),e.label]})},e.href))})]})]}),(0,a.jsx)("div",{className:"p-4 border-t",children:(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,a.jsx)("a",{href:"#",className:"p-2 text-gray-500 hover:text-green-600",children:(0,a.jsx)(C.Z,{className:"w-6 h-6"})}),(0,a.jsx)("a",{href:"#",className:"p-2 text-gray-500 hover:text-green-600",children:(0,a.jsx)(k.Z,{className:"w-6 h-6"})}),(0,a.jsx)("a",{href:"#",className:"p-2 text-gray-500 hover:text-green-600",children:(0,a.jsx)(S.Z,{className:"w-6 h-6"})})]})})]})})})},H=e=>{let{children:s}=e,t=(0,n.usePathname)(),{data:j}=(0,c.useSession)(),{state:p}=(0,u.j)(),{unreadCount:N}=(0,g.z)(),[b,y]=(0,l.useState)(!1),[v,w]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{y(!0)},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[(0,a.jsx)(A,{isOpen:v,onClose:()=>w(!1)}),(0,a.jsx)("header",{className:"bg-white shadow-sm sticky top-0 z-40",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto lg:px-8",children:[(0,a.jsxs)("div",{className:"max-w-md mx-auto lg:hidden px-4 py-3 flex items-center justify-between",children:[(0,a.jsx)("button",{onClick:()=>w(!0),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(x.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsx)(r.default,{href:"/",className:"flex items-center",children:(0,a.jsx)(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[40px] w-auto"})}),(0,a.jsxs)(r.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5 text-gray-600"}),b&&(null==j?void 0:j.user)&&N>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:N>99?"99+":N})]})]}),(0,a.jsxs)("div",{className:"hidden lg:flex px-4 py-3 items-center justify-between",children:[(0,a.jsx)(r.default,{href:"/",className:"flex items-center",children:(0,a.jsx)(i.default,{src:"/logo.svg",alt:"Herbalicious Logo",width:60,height:60,className:"h-[60px] w-auto"})}),(0,a.jsxs)("nav",{className:"flex items-center space-x-8",children:[(0,a.jsx)(r.default,{href:"/",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Home"}),(0,a.jsx)(r.default,{href:"/shop",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Shop"}),(0,a.jsx)(r.default,{href:"/about",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"About"}),(0,a.jsx)(r.default,{href:"/contact",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"Contact"}),(0,a.jsx)(r.default,{href:"/faq",className:"text-gray-600 hover:text-green-600 font-medium transition-colors",children:"FAQs"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(r.default,{href:"/notifications",className:"p-2 rounded-full hover:bg-gray-100 transition-colors relative",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5 text-gray-600"}),b&&(null==j?void 0:j.user)&&N>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:N>99?"99+":N})]}),(0,a.jsxs)(r.default,{href:"/cart",className:"relative p-2 rounded-full hover:bg-gray-100 transition-colors",children:[(0,a.jsx)(d.Z,{className:"w-5 h-5 text-gray-600"}),b&&p.itemCount>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:p.itemCount})]}),(0,a.jsx)(r.default,{href:"/profile",className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(h.Z,{className:"w-5 h-5 text-gray-600"})})]})]})]})}),(0,a.jsx)("main",{className:"flex-1 pb-20 lg:pb-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto lg:px-8",children:(0,a.jsx)("div",{className:"max-w-md mx-auto lg:max-w-none",children:s})})}),(0,a.jsx)("nav",{className:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 lg:hidden",children:(0,a.jsx)("div",{className:"max-w-md mx-auto px-4 py-2",children:(0,a.jsxs)("div",{className:"flex justify-around",children:[(0,a.jsxs)(r.default,{href:"/",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors ".concat("/"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(m.Z,{className:"w-6 h-6 mb-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Home"})]}),(0,a.jsxs)(r.default,{href:"/shop",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors ".concat("/shop"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(f.Z,{className:"w-6 h-6 mb-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Shop"})]}),(0,a.jsxs)(r.default,{href:"/cart",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors relative ".concat("/cart"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(d.Z,{className:"w-6 h-6 mb-1"}),b&&p.itemCount>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:p.itemCount}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Cart"})]}),(0,a.jsxs)(r.default,{href:"/profile",className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors ".concat("/profile"===t?"text-green-600 bg-green-50":"text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(h.Z,{className:"w-6 h-6 mb-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Profile"})]})]})})}),(0,a.jsx)("footer",{className:"hidden lg:block bg-white border-t border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["\xa9 ",new Date().getFullYear()," Herbalicious. All rights reserved."]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)(r.default,{href:"/privacy",className:"text-sm text-gray-500 hover:text-green-600",children:"Privacy Policy"}),(0,a.jsx)(r.default,{href:"/terms",className:"text-sm text-gray-500 hover:text-green-600",children:"Terms and Conditions"}),(0,a.jsx)(r.default,{href:"/shipping",className:"text-sm text-gray-500 hover:text-green-600",children:"Shippings and Returns"}),(0,a.jsx)(r.default,{href:"/faq",className:"text-sm text-gray-500 hover:text-green-600",children:"FAQs"})]})]})})})]})}}}]);