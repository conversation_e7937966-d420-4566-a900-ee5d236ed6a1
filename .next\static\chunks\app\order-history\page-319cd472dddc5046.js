(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7947],{10719:function(e,s,t){Promise.resolve().then(t.bind(t,28250)),Promise.resolve().then(t.bind(t,80896))},80896:function(e,s,t){"use strict";t.d(s,{default:function(){return b}});var a=t(57437),r=t(2265),l=t(99376),n=t(91723),c=t(65302),i=t(40340),d=t(45131),o=t(82431),x=t(15863),m=t(44794),h=t(32660),u=t(42208);let g=(0,t(39763).Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var p=t(80605),b=()=>{let e=(0,l.useRouter)(),{data:s,status:t}=(0,p.useSession)(),[b,y]=(0,r.useState)("all"),[f,j]=(0,r.useState)([]),[N,v]=(0,r.useState)(!0),[w,k]=(0,r.useState)(null),[C,E]=(0,r.useState)(1),[Z,D]=(0,r.useState)(1),S={PENDING:{icon:n.Z,color:"text-yellow-600",bg:"bg-yellow-100",label:"Pending"},CONFIRMED:{icon:c.Z,color:"text-blue-600",bg:"bg-blue-100",label:"Confirmed"},PROCESSING:{icon:n.Z,color:"text-green-600",bg:"bg-green-100",label:"Processing"},SHIPPED:{icon:i.Z,color:"text-green-600",bg:"bg-green-100",label:"Shipped"},DELIVERED:{icon:c.Z,color:"text-green-600",bg:"bg-green-100",label:"Delivered"},CANCELLED:{icon:d.Z,color:"text-red-600",bg:"bg-red-100",label:"Cancelled"},REFUNDED:{icon:o.Z,color:"text-purple-600",bg:"bg-purple-100",label:"Refunded"}};(0,r.useEffect)(()=>{"loading"!==t&&"unauthenticated"===t&&e.push("/login")},[t,e]),(0,r.useEffect)(()=>{(null==s?void 0:s.user)&&P()},[C,b,s]);let P=async()=>{if(!(null==s?void 0:s.user)){k("Please login to view your orders"),v(!1);return}try{v(!0),k(null);let e=new URLSearchParams({page:C.toString(),limit:"10"});"all"!==b&&e.append("status",b);let s=await fetch("/api/orders?".concat(e)),t=await s.json();if(!s.ok)throw Error(t.error||"Failed to fetch orders");j(t.orders),D(t.pagination.totalPages)}catch(e){k(e instanceof Error?e.message:"Failed to load orders")}finally{v(!1)}},O=[{id:"all",label:"All Orders",count:f.length},{id:"PROCESSING",label:"Processing",count:f.filter(e=>"PROCESSING"===e.status).length},{id:"SHIPPED",label:"Shipped",count:f.filter(e=>"SHIPPED"===e.status).length},{id:"DELIVERED",label:"Delivered",count:f.filter(e=>"DELIVERED"===e.status).length}],M=async s=>{let t=s.items.map(e=>({id:e.product.id,name:e.product.name,price:e.product.price,quantity:e.quantity,slug:e.product.slug}));localStorage.setItem("cart",JSON.stringify(t)),e.push("/checkout")},R=s=>{e.push("/orders/".concat(s))};return"loading"===t||N?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(x.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):"unauthenticated"===t?null:w?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen px-4",children:[(0,a.jsx)(m.Z,{className:"w-16 h-16 text-gray-300 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Unable to load orders"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:w}),(0,a.jsx)("button",{onClick:P,className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Try Again"})]}):(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>e.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:(0,a.jsx)(h.Z,{className:"w-5 h-5"})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"Order History"})]})}),(0,a.jsx)("div",{className:"px-4 py-4 bg-white border-b",children:(0,a.jsx)("div",{className:"flex space-x-2 overflow-x-auto",children:O.map(e=>(0,a.jsxs)("button",{onClick:()=>{y(e.id),E(1)},className:"flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ".concat(b===e.id?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,a.jsx)("span",{children:e.label}),(0,a.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs ".concat(b===e.id?"bg-green-500 text-white":"bg-gray-200 text-gray-600"),children:e.count})]},e.id))})}),(0,a.jsxs)("div",{className:"px-4 py-6",children:[f.length>0?(0,a.jsx)("div",{className:"space-y-4",children:f.map(e=>{let s=S[e.status].icon,t=S[e.status];return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-800",children:["#",e.orderNumber]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 rounded-full ".concat(t.bg),children:[(0,a.jsx)(s,{className:"w-4 h-4 ".concat(t.color)}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(t.color),children:t.label})]})]}),(0,a.jsx)("div",{className:"space-y-2 mb-4",children:e.items.map(e=>(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.product.name]}),(0,a.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},e.id))}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["Total: ₹",e.total.toFixed(2)]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>R(e.id),className:"flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Details"})]}),"DELIVERED"===e.status&&(0,a.jsxs)("button",{onClick:()=>M(e),className:"flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium hover:bg-green-200 transition-colors",children:[(0,a.jsx)(g,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Reorder"})]})]})]})]},e.id)})}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:"No orders found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"No orders match the selected filter"}),(0,a.jsx)("button",{onClick:()=>{y("all"),E(1)},className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"View All Orders"})]}),Z>1&&(0,a.jsxs)("div",{className:"flex justify-center mt-6 space-x-2",children:[(0,a.jsx)("button",{onClick:()=>E(e=>Math.max(1,e-1)),disabled:1===C,className:"px-4 py-2 rounded-lg ".concat(1===C?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Previous"}),(0,a.jsxs)("span",{className:"px-4 py-2 text-gray-700",children:["Page ",C," of ",Z]}),(0,a.jsx)("button",{onClick:()=>E(e=>Math.min(Z,e+1)),disabled:C===Z,className:"px-4 py-2 rounded-lg ".concat(C===Z?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Next"})]})]})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsx)("div",{className:"flex items-center mb-8",children:(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(h.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Back"})]})}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Order History"}),(0,a.jsx)("div",{className:"flex space-x-4 mb-8",children:O.map(e=>(0,a.jsxs)("button",{onClick:()=>{y(e.id),E(1)},className:"flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-colors ".concat(b===e.id?"bg-green-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"),children:[(0,a.jsx)("span",{children:e.label}),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm ".concat(b===e.id?"bg-green-500 text-white":"bg-gray-100 text-gray-600"),children:e.count})]},e.id))}),f.length>0?(0,a.jsx)("div",{className:"space-y-6",children:f.map(e=>{let s=S[e.status].icon,t=S[e.status];return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:["Order #",e.orderNumber]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Ordered on"," ",new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 px-4 py-2 rounded-xl ".concat(t.bg),children:[(0,a.jsx)(s,{className:"w-5 h-5 ".concat(t.color)}),(0,a.jsx)("span",{className:"font-medium ".concat(t.color),children:t.label})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Items Ordered"}),(0,a.jsx)("div",{className:"space-y-2",children:e.items.map(e=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[e.quantity,"x ",e.product.name]}),(0,a.jsxs)("span",{className:"font-medium text-gray-800",children:["₹",e.price]})]},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Subtotal"}),(0,a.jsxs)("span",{children:["₹",e.subtotal.toFixed(2)]})]}),e.couponDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Discount"}),(0,a.jsxs)("span",{className:"text-green-600",children:["-₹",e.couponDiscount.toFixed(2)]})]}),"COD"===e.paymentMethod&&(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"COD Charges"}),(0,a.jsx)("span",{children:"₹50.00"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{children:"Free"})]}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold text-gray-900 pt-2 border-t border-gray-200",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",e.total.toFixed(2)]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Payment:"," ",(0,a.jsx)("span",{className:"font-medium",children:"COD"===e.paymentMethod?"Cash on Delivery":"Online"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{onClick:()=>R(e.id),className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View Details"})]}),"DELIVERED"===e.status&&(0,a.jsxs)("button",{onClick:()=>M(e),className:"flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-xl font-medium hover:bg-green-200 transition-colors",children:[(0,a.jsx)(g,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Reorder"})]})]})]})]},e.id)})}):(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)(m.Z,{className:"w-24 h-24 text-gray-300 mx-auto mb-6"}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-gray-800 mb-4",children:"No orders found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"No orders match the selected filter"}),(0,a.jsx)("button",{onClick:()=>{y("all"),E(1)},className:"bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors",children:"View All Orders"})]}),Z>1&&(0,a.jsxs)("div",{className:"flex justify-center mt-8 space-x-2",children:[(0,a.jsx)("button",{onClick:()=>E(e=>Math.max(1,e-1)),disabled:1===C,className:"px-6 py-3 rounded-xl font-medium ".concat(1===C?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Previous"}),(0,a.jsxs)("span",{className:"px-6 py-3 text-gray-700 font-medium",children:["Page ",C," of ",Z]}),(0,a.jsx)("button",{onClick:()=>E(e=>Math.min(Z,e+1)),disabled:C===Z,className:"px-6 py-3 rounded-xl font-medium ".concat(C===Z?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Next"})]})]})})]})}},32660:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},65302:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},91723:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},42208:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15863:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44794:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},82431:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},45131:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=10719)}),_N_E=e.O()}]);