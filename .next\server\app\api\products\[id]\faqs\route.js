"use strict";(()=>{var e={};e.id=6948,e.ids=[6948],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},17888:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>m,serverHooks:()=>S,staticGenerationAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{GET:()=>c,POST:()=>p});var o=r(49303),s=r(88716),n=r(60670),a=r(87070),d=r(89456),u=r(81515);async function c(e,{params:t}){try{await (0,d.Z)();let e=await u.xs.findById(t.id).select("faqs").lean(),r=(e?.faqs||[]).filter(e=>!1!==e.isActive).sort((e,t)=>(e.position??0)-(t.position??0));return a.NextResponse.json({success:!0,data:r})}catch(e){return console.error("Error fetching FAQs:",e),a.NextResponse.json({success:!1,error:"Failed to fetch FAQs"},{status:500})}}async function p(e,{params:t}){try{await (0,d.Z)();let{question:r,answer:i,position:o}=await e.json();if(!r||!i)return a.NextResponse.json({success:!1,error:"Question and answer are required"},{status:400});let s=o;if(void 0===s){let e=await u.xs.findById(t.id).select("faqs").lean();s=(e?.faqs||[]).reduce((e,t)=>Math.max(e,t.position??-1),-1)+1}let n=await u.xs.findByIdAndUpdate(t.id,{$push:{faqs:{question:r,answer:i,position:s,isActive:!0,createdAt:new Date,updatedAt:new Date}}},{new:!0,projection:{faqs:1}}).lean(),c=(n?.faqs||[]).find(e=>e.position===s)||{question:r,answer:i,position:s,isActive:!0};return a.NextResponse.json({success:!0,data:c,message:"FAQ created successfully"})}catch(e){return console.error("Error creating FAQ:",e),a.NextResponse.json({success:!1,error:"Failed to create FAQ"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/[id]/faqs/route",pathname:"/api/products/[id]/faqs",filename:"route",bundlePath:"app/api/products/[id]/faqs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:l,staticGenerationAsyncStorage:y,serverHooks:S}=m,g="/api/products/[id]/faqs/route";function f(){return(0,n.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:y})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>j,Dd:()=>C,Order:()=>P,P_:()=>x,Pp:()=>B,Th:()=>D,Vv:()=>G,WD:()=>T,gc:()=>E,hQ:()=>F,kL:()=>A,mA:()=>U,n5:()=>N,nW:()=>O,p1:()=>L,qN:()=>v,wV:()=>R,xs:()=>w});var i=r(11185),o=r.n(i);let s=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),n=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),a=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),c=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),S=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),g=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),f=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),q=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),h=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),I=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=o().models.User||o().model("User",s),w=o().models.Product||o().model("Product",n),T=o().models.Category||o().model("Category",a),P=o().models.Order||o().model("Order",d),O=o().models.HomepageSetting||o().model("HomepageSetting",u),E=o().models.Testimonial||o().model("Testimonial",c),v=o().models.ProductImage||o().model("ProductImage",p),D=o().models.ProductVariant||o().model("ProductVariant",m),j=o().models.Review||o().model("Review",l),A=o().models.Address||o().model("Address",y),C=o().models.OrderItem||o().model("OrderItem",S),x=o().models.Notification||o().model("Notification",g),R=o().models.Coupon||o().model("Coupon",f);o().models.Wishlist||o().model("Wishlist",q);let U=o().models.Newsletter||o().model("Newsletter",h),B=o().models.ProductCategory||o().model("ProductCategory",I),F=o().models.WishlistItem||o().model("WishlistItem",b),M=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),G=o().models.NotificationTemplate||o().model("NotificationTemplate",M),k=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),L=o().models.Enquiry||o().model("Enquiry",k)},89456:(e,t,r)=>{r.d(t,{Z:()=>a});var i=r(11185),o=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let a=async function(){if(n.conn)return n.conn;n.promise||(n.promise=o().connect(s,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(17888));module.exports=i})();