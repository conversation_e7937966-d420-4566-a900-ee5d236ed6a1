(()=>{var e={};e.id=1327,e.ids=[1327],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},70703:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c}),r(38932),r(37254),r(35866);var t=r(23191),a=r(88716),n=r(37922),l=r.n(n),i=r(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(s,o);let c=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38932)),"C:\\Users\\<USER>\\Desktop\\project\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\contact\\page.tsx"],m="/contact/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78135:(e,s,r)=>{Promise.resolve().then(r.bind(r,65090)),Promise.resolve().then(r.bind(r,63775))},63775:(e,s,r)=>{"use strict";r.d(s,{default:()=>o});var t=r(10326),a=r(17577),n=r(5932),l=r(42887),i=r(69436);function o(){let[e,s]=(0,a.useState)({name:"",email:"",subject:"",message:""}),[r,o]=(0,a.useState)(!1),[c,d]=(0,a.useState)("idle"),m=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t}))},x=async r=>{r.preventDefault(),o(!0),d("idle");try{let r=await fetch("/api/enquiries",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await r.json();if(!r.ok)throw Error(t.error||"Failed to submit enquiry");d("success"),s({name:"",email:"",subject:"",message:""})}catch(e){console.error("Error submitting form:",e),d("error")}finally{o(!1)}};return t.jsx("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[t.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Contact Us"}),t.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Have questions or need assistance? We're here to help. Reach out to us through any of the channels below."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-8",children:[t.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Get in Touch"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(n.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900",children:"Email"}),t.jsx("p",{className:"text-gray-600",children:"<EMAIL>"}),t.jsx("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(l.Z,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium text-gray-900",children:"Phone"}),t.jsx("p",{className:"text-gray-600",children:"+91 99878 10707"}),t.jsx("p",{className:"text-gray-600",children:"WhatsApp Available"})]})]})]})]}),(0,t.jsxs)("div",{className:"mt-6 bg-green-50 rounded-2xl p-6",children:[t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Need Quick Answers?"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Check out our FAQ section for answers to common questions."}),t.jsx("a",{href:"/faq",className:"text-green-600 font-medium hover:text-green-700",children:"Visit FAQ →"})]})]}),t.jsx("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-8",children:[t.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Send us a Message"}),(0,t.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name"}),t.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"John Doe"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),t.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,t.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[t.jsx("option",{value:"",children:"Select a subject"}),t.jsx("option",{value:"general",children:"General Inquiry"}),t.jsx("option",{value:"order",children:"Order Support"}),t.jsx("option",{value:"product",children:"Product Information"}),t.jsx("option",{value:"technical",children:"Technical Support"}),t.jsx("option",{value:"partnership",children:"Partnership Opportunities"}),t.jsx("option",{value:"other",children:"Other"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),t.jsx("textarea",{id:"message",name:"message",value:e.message,onChange:m,required:!0,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none",placeholder:"Tell us how we can help you..."})]}),t.jsx("div",{children:t.jsx("button",{type:"submit",disabled:r,className:"w-full md:w-auto px-8 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:r?(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),t.jsx("span",{children:"Sending..."})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(i.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Send Message"})]})})}),"success"===c&&t.jsx("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:t.jsx("p",{className:"text-green-800",children:"Thank you for your message! We'll get back to you within 24 hours."})}),"error"===c&&t.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:t.jsx("p",{className:"text-red-800",children:"Something went wrong. Please try again later or contact us directly."})})]})]})})]}),(0,t.jsxs)("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[t.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:t.jsx(n.Z,{className:"h-6 w-6 text-green-600"})}),t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Email Support"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"Get a response within 24 hours"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[t.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:t.jsx(l.Z,{className:"h-6 w-6 text-green-600"})}),t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Phone Support"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"Available on WhatsApp"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[t.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:t.jsx(i.Z,{className:"h-6 w-6 text-green-600"})}),t.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Quick Response"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"We aim to respond within 24 hours"})]})]})]})})}},42887:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},69436:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},40304:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)},38932:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(19510),a=r(40304);let n=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Contact.tsx#default`);function l(){return t.jsx(a.Z,{children:t.jsx(n,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[9276,3757,434,9694,9536,5090],()=>r(70703));module.exports=t})();