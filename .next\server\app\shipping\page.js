(()=>{var e={};e.id=4553,e.ids=[4553],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17138:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>l}),r(9286),r(37254),r(35866);var s=r(23191),i=r(88716),n=r(37922),a=r.n(n),o=r(95231),p={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>o[e]);r.d(t,p);let l=["",{children:["shipping",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9286)),"C:\\Users\\<USER>\\Desktop\\project\\app\\shipping\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\shipping\\page.tsx"],c="/shipping/page",u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/shipping/page",pathname:"/shipping",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},35303:()=>{},9286:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(19510);r(71159);let i=()=>s.jsx("div",{className:"min-h-screen bg-white",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8",children:[s.jsx("h1",{className:"text-3xl font-extrabold text-gray-900",children:"Shipping and Returns"}),(0,s.jsxs)("div",{className:"mt-6 prose prose-indigo text-gray-500",children:[s.jsx("p",{children:"Last updated: July 31, 2024"}),s.jsx("h2",{children:"Shipping"}),s.jsx("p",{children:"We ship to all locations within the country. Orders are typically processed within 1-2 business days. Shipping times may vary based on your location."}),s.jsx("h2",{children:"Returns"}),s.jsx("p",{children:"We have a 30-day return policy, which means you have 30 days after receiving your item to request a return."}),s.jsx("p",{children:"To be eligible for a return, your item must be in the same condition that you received it, unworn or unused, with tags, and in its original packaging. You’ll also need the receipt or proof of purchase."})]})]})})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,3757,9536],()=>r(17138));module.exports=s})();