"use strict";(()=>{var e={};e.id=3615,e.ids=[3615],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},66137:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>x,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{GET:()=>f});var o=r(49303),s=r(88716),n=r(60670),a=r(87070),u=r(75571),l=r(95306),p=r(89456),c=r(81515),d=r(54211);async function f(e){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user?.id||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Admin access required"},{status:401});await (0,p.Z)();let t=await c.Vv.find({}).select("_id name title message emailSubject emailTemplate type isActive createdAt").sort({name:1}).lean();return d.kg.info("Admin fetched notification templates",{adminId:e.user.id,templateCount:t.length}),a.NextResponse.json({success:!0,templates:t})}catch(e){return d.kg.error("Failed to fetch notification templates",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/notifications/templates/route",pathname:"/api/admin/notifications/templates",filename:"route",bundlePath:"app/api/admin/notifications/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\templates\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:y}=m,v="/api/admin/notifications/templates/route";function x(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>s}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:o,context:s,error:n,userId:a,requestId:u}=e,l=i[r],p=`[${t}] ${l}: ${o}`;return a&&(p+=` | User: ${a}`),u&&(p+=` | Request: ${u}`),s&&Object.keys(s).length>0&&(p+=` | Context: ${JSON.stringify(s)}`),n&&(p+=` | Error: ${n.message}`,this.isDevelopment&&n.stack&&(p+=`
Stack: ${n.stack}`)),p}log(e,t,r,i){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},s=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(s);break;case 1:console.warn(s);break;case 2:console.info(s);break;case 3:console.debug(s)}else console.log(JSON.stringify(o))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,o){this.info(`API ${e} ${t} - ${r}`,{...o,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,o){this.error(`API ${e} ${t} failed`,r,{...o,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let s=new o},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var a=o?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(i,s,a):i[s]=e[s]}return i.default=e,r&&r.set(e,i),i}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972,8691,2830,5306],()=>r(66137));module.exports=i})();