"use strict";(()=>{var e={};e.id=999,e.ids=[999],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},80665:e=>{e.exports=require("dns")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},77949:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>k,patchFetch:()=>O,requestAsyncStorage:()=>_,routeModule:()=>v,serverHooks:()=>P,staticGenerationAsyncStorage:()=>q});var a={};t.r(a),t.d(a,{POST:()=>I});var n=t(49303),i=t(88716),o=t(60670),s=t(87070),d=t(65630),u=t(75571),p=t(95306),c=t(62822),m=t(84875),y=t(54211),l=t(8149),f=t(95921),g=t(89585),h=t(89456),x=t(81515);let w=d.Ry({razorpay_order_id:d.Z_(),razorpay_payment_id:d.Z_(),razorpay_signature:d.Z_(),order_id:d.Z_()}),I=(0,m.lm)(async e=>{y.kg.apiRequest("POST","/api/payments/verify"),await (0,h.Z)(),await (0,l.er)(e,l.Xw,20);let r=await (0,u.getServerSession)(p.L);if(!r?.user?.id)throw new m._7("Authentication required");let t=await e.json(),{razorpay_order_id:a,razorpay_payment_id:n,razorpay_signature:i,order_id:o}=w.parse(t);y.kg.info("Verifying payment",{userId:r.user.id,orderId:o,razorpayOrderId:a,razorpayPaymentId:n});try{let e=await x.Order.findById(o).populate([{path:"items.product"}]).lean();if(!e)throw new m.p8("Order not found");if(e&&e.userId.toString()!==r.user.id)throw new m._7("Unauthorized access to order");if(e&&e.paymentId!==a)throw new m.p8("Order ID mismatch");if(!(0,c.FT)({razorpay_order_id:a,razorpay_payment_id:n,razorpay_signature:i}))throw y.kg.warn("Invalid payment signature",{orderId:o,razorpayOrderId:a,razorpayPaymentId:n,userId:r.user.id}),await x.Order.updateOne({_id:o},{$set:{paymentStatus:"FAILED",status:"CANCELLED",notes:`Payment verification failed. Payment ID: ${n}`}}),new m.p8("Payment verification failed");let t=await (0,c.sS)(n);await x.Order.updateOne({_id:o},{$set:{paymentStatus:"PAID",status:"CONFIRMED",paymentMethod:t.method||"razorpay",notes:`Payment successful. Payment ID: ${n}, Method: ${t.method||"unknown"}`}});let d=await x.Order.findById(o).lean();y.kg.info("Payment verified successfully",{orderId:o,orderNumber:e?.orderNumber,razorpayPaymentId:n,amount:e?.total,userId:r.user.id,paymentMethod:t.method});try{if(e&&e.address){let r=(e.items||[]).map(e=>({name:e.product?.name??"",quantity:e.quantity,price:e.price})),t=`${e.address.firstName} ${e.address.lastName}
${e.address.address1}
${e.address.address2?e.address.address2+"\n":""}${e.address.city}, ${e.address.state} ${e.address.postalCode}
${e.address.country}`;e.userEmail&&await (0,f.bn)(e.userEmail,{orderId:e?.orderNumber,items:r,total:e?.total,shippingAddress:t}),y.kg.info("Order confirmation email sent",{orderId:o,email:e?.user?.email})}}catch(e){y.kg.error("Failed to send order confirmation email",e)}try{await g.aZ.orderConfirmed(e?.userId?.toString()||r.user.id,{orderId:String(e?._id),orderNumber:e?.orderNumber}),y.kg.info("Order confirmed notification sent",{orderId:o,userId:e?.userId||r.user.id})}catch(e){y.kg.error("Failed to send order confirmed notification",e)}return s.NextResponse.json({success:!0,message:"Payment verified successfully",order:{id:String(d?._id??o),orderNumber:d?.orderNumber??e?.orderNumber,status:d?.status??"CONFIRMED",paymentStatus:d?.paymentStatus??"PAID",total:d?.total??e?.total,paymentId:n}})}catch(e){if(y.kg.error("Payment verification failed",e),!(e instanceof m.p8)&&!(e instanceof m._7))try{await x.Order.updateOne({_id:o},{$set:{paymentStatus:"FAILED",status:"CANCELLED",notes:`Payment verification error: ${e.message}`}})}catch(e){y.kg.error("Failed to update order status",e)}throw e}}),v=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/payments/verify/route",pathname:"/api/payments/verify",filename:"route",bundlePath:"app/api/payments/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\verify\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:_,staticGenerationAsyncStorage:q,serverHooks:P}=v,k="/api/payments/verify/route";function O(){return(0,o.patchFetch)({serverHooks:P,staticGenerationAsyncStorage:q})}},62822:(e,r,t)=>{t.d(r,{FT:()=>c,My:()=>l,iP:()=>p,mT:()=>y,ml:()=>f,sS:()=>m});var a=t(41212),n=t.n(a),i=t(54211),o=t(84875),s=t(84770),d=t.n(s);let u=new(n())({key_id:process.env.RAZORPAY_KEY_ID,key_secret:process.env.RAZORPAY_KEY_SECRET});async function p(e){try{i.kg.info("Creating Razorpay order",{amount:e.amount,currency:e.currency,receipt:e.receipt});let r=await u.orders.create({amount:e.amount,currency:e.currency,receipt:e.receipt,notes:e.notes||{}});return i.kg.info("Razorpay order created successfully",{orderId:r.id,amount:r.amount,receipt:r.receipt}),r}catch(e){throw i.kg.error("Failed to create Razorpay order",e),new o.gz("Failed to create payment order",500)}}function c(e){try{let r=d().createHmac("sha256",process.env.RAZORPAY_KEY_SECRET).update(`${e.razorpay_order_id}|${e.razorpay_payment_id}`).digest("hex")===e.razorpay_signature;return i.kg.info("Payment signature verification",{orderId:e.razorpay_order_id,paymentId:e.razorpay_payment_id,isValid:r}),r}catch(e){return i.kg.error("Payment signature verification failed",e),!1}}async function m(e){try{i.kg.info("Fetching payment details",{paymentId:e});let r=await u.payments.fetch(e);return i.kg.info("Payment details fetched successfully",{paymentId:r.id,status:r.status,amount:r.amount}),r}catch(e){throw i.kg.error("Failed to fetch payment details",e),new o.gz("Failed to fetch payment details",500)}}function y(e){return Math.round(100*e)}function l(e="ORDER"){let r=Date.now(),t=Math.random().toString(36).substring(2,8).toUpperCase();return`${e}_${r}_${t}`}function f(e){return e>=100&&e<=15e8&&Number.isInteger(e)}},8149:(e,r,t)=>{t.d(r,{Ri:()=>i,Xw:()=>o,er:()=>d,jO:()=>s});var a=t(919);function n(e){let r=new a.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,t)=>new Promise((a,n)=>{let i=r.get(t)||[0];0===i[0]&&r.set(t,i),i[0]+=1,i[0]>=e?n(Error("Rate limit exceeded")):a()})}}let i=n({interval:9e5,uniqueTokenPerInterval:500}),o=n({interval:6e4,uniqueTokenPerInterval:500}),s=n({interval:36e5,uniqueTokenPerInterval:500});async function d(e,r,t){let a=function(e){let r=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip");return r?r.split(",")[0].trim():t||"unknown"}(e);try{await r.check(t,a)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[9276,5972,8691,2830,9489,5630,138,5245,656,5306,9585,83],()=>t(77949));module.exports=a})();