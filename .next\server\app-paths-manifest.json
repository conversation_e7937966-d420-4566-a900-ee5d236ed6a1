{"/_not-found/page": "app/_not-found/page.js", "/about/page": "app/about/page.js", "/addresses/page": "app/addresses/page.js", "/api/admin/notifications/broadcast/route": "app/api/admin/notifications/broadcast/route.js", "/api/admin/cleanup/route": "app/api/admin/cleanup/route.js", "/api/admin/notifications/history/route": "app/api/admin/notifications/history/route.js", "/api/admin/notifications/stats/route": "app/api/admin/notifications/stats/route.js", "/api/admin/notifications/templates/route": "app/api/admin/notifications/templates/route.js", "/api/admin/notifications/send/route": "app/api/admin/notifications/send/route.js", "/api/admin/products/export/route": "app/api/admin/products/export/route.js", "/api/admin/reviews/route": "app/api/admin/reviews/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/reset-password/route": "app/api/auth/reset-password/route.js", "/api/auth/session-debug/route": "app/api/auth/session-debug/route.js", "/api/categories/[id]/route": "app/api/categories/[id]/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/coupons/[id]/route": "app/api/coupons/[id]/route.js", "/api/coupons/validate/route": "app/api/coupons/validate/route.js", "/api/coupons/route": "app/api/coupons/route.js", "/api/enquiries/[id]/route": "app/api/enquiries/[id]/route.js", "/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/api/homepage-settings/route": "app/api/homepage-settings/route.js", "/api/enquiries/route": "app/api/enquiries/route.js", "/api/media/delete/route": "app/api/media/delete/route.js", "/api/media/list/route": "app/api/media/list/route.js", "/api/media/upload/route": "app/api/media/upload/route.js", "/api/media/config/route": "app/api/media/config/route.js", "/api/newsletter/export/route": "app/api/newsletter/export/route.js", "/api/newsletter/route": "app/api/newsletter/route.js", "/api/notifications/mark-all-read/route": "app/api/notifications/mark-all-read/route.js", "/api/notifications/[id]/read/route": "app/api/notifications/[id]/read/route.js", "/api/notifications/price-drop-check/route": "app/api/notifications/price-drop-check/route.js", "/api/notifications/route": "app/api/notifications/route.js", "/api/notifications/review-requests/route": "app/api/notifications/review-requests/route.js", "/api/notifications/unread-count/route": "app/api/notifications/unread-count/route.js", "/api/products/[id]/faqs/route": "app/api/products/[id]/faqs/route.js", "/api/products/[id]/route": "app/api/products/[id]/route.js", "/api/products/[id]/variations/[variationId]/route": "app/api/products/[id]/variations/[variationId]/route.js", "/api/test-auth/route": "app/api/test-auth/route.js", "/api/products/route": "app/api/products/route.js", "/api/testimonials/[id]/route": "app/api/testimonials/[id]/route.js", "/api/users/[id]/preferences/route": "app/api/users/[id]/preferences/route.js", "/api/testimonials/route": "app/api/testimonials/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/api/users/[id]/stats/route": "app/api/users/[id]/stats/route.js", "/api/users/route": "app/api/users/route.js", "/api/wishlist/route": "app/api/wishlist/route.js", "/cart/page": "app/cart/page.js", "/contact/page": "app/contact/page.js", "/checkout/page": "app/checkout/page.js", "/categories/page": "app/categories/page.js", "/edit-profile/page": "app/edit-profile/page.js", "/faq/page": "app/faq/page.js", "/login/page": "app/login/page.js", "/order-history/page": "app/order-history/page.js", "/page": "app/page.js", "/privacy/page": "app/privacy/page.js", "/product/[id]/page": "app/product/[id]/page.js", "/notifications/page": "app/notifications/page.js", "/shipping/page": "app/shipping/page.js", "/wishlist/page": "app/wishlist/page.js", "/signup/page": "app/signup/page.js", "/terms/page": "app/terms/page.js", "/sw.js/route": "app/sw.js/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/auth/forgot-password/route": "app/api/auth/forgot-password/route.js", "/api/payments/config/route": "app/api/payments/config/route.js", "/api/payments/test/route": "app/api/payments/test/route.js", "/api/orders/route": "app/api/orders/route.js", "/api/orders/bulk-update/route": "app/api/orders/bulk-update/route.js", "/api/payments/verify/route": "app/api/payments/verify/route.js", "/api/payments/create-order/route": "app/api/payments/create-order/route.js", "/api/orders/[orderId]/route": "app/api/orders/[orderId]/route.js", "/api/products/[id]/faqs/[faqId]/route": "app/api/products/[id]/faqs/[faqId]/route.js", "/api/products/[id]/reviews/route": "app/api/products/[id]/reviews/route.js", "/api/products/[id]/variations/route": "app/api/products/[id]/variations/route.js", "/api/products/filters/route": "app/api/products/filters/route.js", "/api/products/bulk/route": "app/api/products/bulk/route.js", "/api/products/import/route": "app/api/products/import/route.js", "/api/products/optimized/route": "app/api/products/optimized/route.js", "/api/test-email/route": "app/api/test-email/route.js", "/order-confirmation/page": "app/order-confirmation/page.js", "/shop/page": "app/shop/page.js", "/admin/enquiry/page": "app/admin/enquiry/page.js", "/admin/customers/page": "app/admin/customers/page.js", "/admin/categories/page": "app/admin/categories/page.js", "/admin/homepage/page": "app/admin/homepage/page.js", "/admin/coupons/page": "app/admin/coupons/page.js", "/admin/media/page": "app/admin/media/page.js", "/admin/newsletter/page": "app/admin/newsletter/page.js", "/admin/notifications/broadcast/page": "app/admin/notifications/broadcast/page.js", "/admin/notifications/page": "app/admin/notifications/page.js", "/admin/notifications/history/page": "app/admin/notifications/history/page.js", "/admin/orders/[id]/page": "app/admin/orders/[id]/page.js", "/admin/notifications/templates/page": "app/admin/notifications/templates/page.js", "/admin/notifications/send/page": "app/admin/notifications/send/page.js", "/admin/orders/page": "app/admin/orders/page.js", "/admin/page": "app/admin/page.js", "/admin/products/page": "app/admin/products/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/reviews/page": "app/admin/reviews/page.js", "/profile/page": "app/profile/page.js"}