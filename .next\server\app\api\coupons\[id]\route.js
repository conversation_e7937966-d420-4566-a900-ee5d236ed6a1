"use strict";(()=>{var e={};e.id=5502,e.ids=[5502],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},26891:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>b,requestAsyncStorage:()=>y,routeModule:()=>h,serverHooks:()=>w,staticGenerationAsyncStorage:()=>x});var o={};r.r(o),r.d(o,{DELETE:()=>f,GET:()=>m,PUT:()=>g});var i=r(49303),n=r(88716),s=r(60670),a=r(87070),u=r(75571),d=r(95306),l=r(89456),p=r(81515),c=r(54211);async function m(e,{params:t}){try{let e=await (0,u.getServerSession)(d.L);if(!e?.user||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});await (0,l.Z)();let r=await p.wV.findById(t.id).lean();if(!r)return a.NextResponse.json({error:"Coupon not found"},{status:404});let o=await p.Order.find({couponCodes:{$in:[String(r.code).toUpperCase()]}}).select("_id userId orderNumber total createdAt").lean(),i=Array.from(new Set(o.map(e=>String(e.userId)))),n=Array.from(new Set(o.map(e=>String(e._id)))),[s,c]=await Promise.all([p.n5.find({_id:{$in:i}}).select("_id name email").lean(),p.Order.find({_id:{$in:n}}).select("_id orderNumber total").lean()]),m=new Map(s.map(e=>[String(e._id),e])),g=new Map(c.map(e=>[String(e._id),e])),f=o.map(e=>({id:String(e._id),usedAt:e.createdAt,user:m.get(String(e.userId))?{id:m.get(String(e.userId))._id,name:m.get(String(e.userId)).name,email:m.get(String(e.userId)).email}:null,order:g.get(String(e._id))?{id:g.get(String(e._id))._id,orderNumber:g.get(String(e._id)).orderNumber,total:g.get(String(e._id)).total}:null}));return a.NextResponse.json({...r,usages:f})}catch(e){return c.kg.error("Error fetching coupon",e),a.NextResponse.json({error:"Failed to fetch coupon"},{status:500})}}async function g(e,{params:t}){try{let r=await (0,u.getServerSession)(d.L);if(!r?.user||"ADMIN"!==r.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let o=await e.json();if(!o.name||!o.type||!o.discountType||void 0===o.discountValue)return a.NextResponse.json({error:"Missing required fields"},{status:400});await (0,l.Z)();let i=await p.wV.findById(t.id).lean();if(!i)return a.NextResponse.json({error:"Coupon not found"},{status:404});let n=i.code;if(o.code&&o.code.toUpperCase()!==i.code){let e=o.code.toUpperCase();if(await p.wV.findOne({code:e,_id:{$ne:t.id}}).lean())return a.NextResponse.json({error:"Coupon code already exists"},{status:400});n=e}let s={code:n,name:o.name,description:o.description??i.description??null,type:o.type,value:o.discountValue,minimumAmount:o.minimumAmount??i.minimumAmount??null,maximumDiscount:o.maximumDiscount??i.maximumDiscount??null,usageLimit:o.usageLimit??i.usageLimit??null,userUsageLimit:o.userUsageLimit??i.userUsageLimit??null,isActive:o.isActive??i.isActive,isStackable:o.isStackable??i.isStackable,showInModule:o.showInModule??i.showInModule,validFrom:o.validFrom?new Date(o.validFrom):i.validFrom,validUntil:o.validUntil?new Date(o.validUntil):i.validUntil??null,applicableProducts:o.applicableProducts??i.applicableProducts??[],applicableCategories:o.applicableCategories??i.applicableCategories??[],excludedProducts:o.excludedProducts??i.excludedProducts??[],excludedCategories:o.excludedCategories??i.excludedCategories??[],customerSegments:o.customerSegments??i.customerSegments??[],updatedAt:new Date};await p.wV.updateOne({_id:t.id},{$set:s});let c=await p.wV.findById(t.id).lean();return a.NextResponse.json(c)}catch(e){if(e?.code===11e3)return a.NextResponse.json({error:"Coupon code already exists"},{status:400});return c.kg.error("Error updating coupon",e),a.NextResponse.json({error:"Failed to update coupon"},{status:500})}}async function f(e,{params:t}){try{let e=await (0,u.getServerSession)(d.L);if(!e?.user||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});await (0,l.Z)();let r=await p.wV.findById(t.id).lean();if(!r)return a.NextResponse.json({error:"Coupon not found"},{status:404});if(await p.Order.countDocuments({couponCodes:{$in:[String(r.code).toUpperCase()]}})>0)return a.NextResponse.json({error:"Cannot delete coupon that has been used. Consider deactivating it instead."},{status:400});return await p.wV.deleteOne({_id:t.id}),a.NextResponse.json({message:"Coupon deleted successfully"})}catch(e){return c.kg.error("Error deleting coupon",e),a.NextResponse.json({error:"Failed to delete coupon"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/coupons/[id]/route",pathname:"/api/coupons/[id]",filename:"route",bundlePath:"app/api/coupons/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:y,staticGenerationAsyncStorage:x,serverHooks:w}=h,v="/api/coupons/[id]/route";function b(){return(0,s.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:x})}},54211:(e,t,r)=>{var o;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(o||(o={}));class i{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:i,context:n,error:s,userId:a,requestId:u}=e,d=o[r],l=`[${t}] ${d}: ${i}`;return a&&(l+=` | User: ${a}`),u&&(l+=` | Request: ${u}`),n&&Object.keys(n).length>0&&(l+=` | Context: ${JSON.stringify(n)}`),s&&(l+=` | Error: ${s.message}`,this.isDevelopment&&s.stack&&(l+=`
Stack: ${s.stack}`)),l}log(e,t,r,o){if(!this.shouldLog(e))return;let i={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:o},n=this.formatMessage(i);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(i))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,o){this.info(`API ${e} ${t}`,{...o,userId:r,type:"api_request"})}apiResponse(e,t,r,o,i){this.info(`API ${e} ${t} - ${r}`,{...i,statusCode:r,duration:o,type:"api_response"})}apiError(e,t,r,o,i){this.error(`API ${e} ${t} failed`,r,{...i,userId:o,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,o){this.warn("Authentication failed",{...o,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,o){this.debug(`DB ${e} on ${t}`,{...o,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,o){this.error(`DB ${e} on ${t} failed`,r,{...o,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,o){this.warn("Rate limit exceeded",{...o,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,o){this.info("Email sent",{...o,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,o){this.error("Email failed to send",r,{...o,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new i},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=i?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(o,n,a):o[n]=e[n]}return o.default=e,r&&r.set(e,o),o}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,5972,8691,2830,5306],()=>r(26891));module.exports=o})();