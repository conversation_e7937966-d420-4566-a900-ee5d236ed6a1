"use strict";(()=>{var e={};e.id=2628,e.ids=[2628],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},44829:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>f});var i=r(49303),o=r(88716),n=r(60670),a=r(87070),u=r(75571),l=r(95306),p=r(89456),c=r(81515),d=r(54211);async function f(e){try{let t=await (0,u.getServerSession)(l.L);if(!t?.user?.id||"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Admin access required"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),i=parseInt(r.get("limit")||"50"),o=r.get("search"),n=r.get("role"),f=(s-1)*i;await (0,p.Z)();let m={};o&&(m.$or=[{name:{$regex:o,$options:"i"}},{email:{$regex:o,$options:"i"}}]),n&&(m.role=n);let[h,g]=await Promise.all([c.n5.find(m).select("_id name email role createdAt preferences").sort({createdAt:-1}).skip(f).limit(i).lean(),c.n5.countDocuments(m)]),y=await Promise.all(h.map(async e=>{let[t,r]=await Promise.all([c.Order.countDocuments({userId:e._id}),c.P_.countDocuments({userId:e._id})]);return{...e,id:e._id,_count:{orders:t,notifications:r}}})),x=Math.ceil(g/i);return d.kg.info("Admin fetched users list",{adminId:t.user.id,page:s,limit:i,totalCount:g,search:o,role:n}),a.NextResponse.json({success:!0,data:{users:y,pagination:{page:s,limit:i,totalCount:g,totalPages:x,hasNext:s<x,hasPrev:s>1}}})}catch(e){return d.kg.error("Failed to fetch users for admin",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:y}=m,x="/api/admin/users/route";function v(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>o}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class i{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:i,context:o,error:n,userId:a,requestId:u}=e,l=s[r],p=`[${t}] ${l}: ${i}`;return a&&(p+=` | User: ${a}`),u&&(p+=` | Request: ${u}`),o&&Object.keys(o).length>0&&(p+=` | Context: ${JSON.stringify(o)}`),n&&(p+=` | Error: ${n.message}`,this.isDevelopment&&n.stack&&(p+=`
Stack: ${n.stack}`)),p}log(e,t,r,s){if(!this.shouldLog(e))return;let i={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},o=this.formatMessage(i);if(this.isDevelopment)switch(e){case 0:console.error(o);break;case 1:console.warn(o);break;case 2:console.info(o);break;case 3:console.debug(o)}else console.log(JSON.stringify(i))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,i){this.info(`API ${e} ${t} - ${r}`,{...i,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,i){this.error(`API ${e} ${t} failed`,r,{...i,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let o=new i},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(s,o,a):s[o]=e[o]}return s.default=e,r&&r.set(e,s),s}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,5306],()=>r(44829));module.exports=s})();