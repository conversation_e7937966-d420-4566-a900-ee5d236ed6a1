"use strict";exports.id=389,exports.ids=[389],exports.modules={19922:(e,a,s)=>{s.d(a,{Z:()=>j});var t=s(10326);s(17577);var l=s(90434),c=s(53080),r=s(1572),d=s(76557);let h=(0,d.Z)("Droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]);var n=s(67427),y=s(48705);let i=(0,d.Z)("ShowerHead",[["path",{d:"m4 4 2.5 2.5",key:"uv2vmf"}],["path",{d:"M13.5 6.5a4.95 4.95 0 0 0-7 7",key:"frdkwv"}],["path",{d:"M15 5 5 15",key:"1ag8rq"}],["path",{d:"M14 17v.01",key:"eokfpp"}],["path",{d:"M10 16v.01",key:"14uyyl"}],["path",{d:"M13 13v.01",key:"1v1k97"}],["path",{d:"M16 10v.01",key:"5169yg"}],["path",{d:"M11 20v.01",key:"cj92p8"}],["path",{d:"M17 14v.01",key:"11cswd"}],["path",{d:"M20 11v.01",key:"19e0od"}]]);var p=s(33734),m=s(12714),o=s(89392);let k=(0,d.Z)("FlaskConical",[["path",{d:"M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2",key:"pzvekw"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M7 16h10",key:"wp8him"}]]);var x=s(3634);let u=(0,d.Z)("Flower2",[["path",{d:"M12 5a3 3 0 1 1 3 3m-3-3a3 3 0 1 0-3 3m3-3v1M9 8a3 3 0 1 0 3 3M9 8h1m5 0a3 3 0 1 1-3 3m3-3h-1m-2 3v-1",key:"3pnvol"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}],["path",{d:"M12 10v12",key:"6ubwww"}],["path",{d:"M12 22c4.2 0 7-1.667 7-5-4.2 0-7 1.667-7 5Z",key:"9hd38g"}],["path",{d:"M12 22c-4.2 0-7-1.667-7-5 4.2 0 7 1.667 7 5Z",key:"ufn41s"}]]),g=(0,d.Z)("Hand",[["path",{d:"M18 11V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0",key:"aigmz7"}],["path",{d:"M14 10V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v2",key:"1n6bmn"}],["path",{d:"M10 10.5V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v8",key:"a9iiix"}],["path",{d:"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"1s1gnw"}]]),Z=(0,d.Z)("Droplet",[["path",{d:"M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-4-6.5c-.5 2.5-2 4.9-4 6.5C6 11.1 5 13 5 15a7 7 0 0 0 7 7z",key:"c7niix"}]]),M=(0,d.Z)("Waves",[["path",{d:"M2 6c.6.5 1.2 1 2.5 1C7 7 7 5 9.5 5c2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"knzxuh"}],["path",{d:"M2 12c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"2jd2cc"}],["path",{d:"M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"rd2r6e"}]]),f=(0,d.Z)("Baby",[["path",{d:"M9 12h.01",key:"157uk2"}],["path",{d:"M15 12h.01",key:"1k8ypt"}],["path",{d:"M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5",key:"1u7htd"}],["path",{d:"M19 6.3a9 9 0 0 1 1.8 3.9 2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1",key:"5yv0yz"}]]),j=({product:e,showAsLinks:a=!1,className:s="",maxCategories:d,size:j="xs"})=>{let v=function(e){let a=[];return e.productCategories&&e.productCategories.length>0&&e.productCategories.forEach(e=>{e.category&&a.push({id:e.category.id,name:e.category.name,slug:e.category.slug})}),e.category&&!a.some(a=>a.id===e.category.id)&&a.push({id:e.category.id,name:e.category.name,slug:e.category.slug}),a}(e),N=d?v.slice(0,d):v;if(0===N.length)return null;let w={xs:"text-xs",sm:"text-sm",base:"text-base",lg:"text-lg"},b={xs:"w-3 h-3",sm:"w-4 h-4",base:"w-5 h-5",lg:"w-6 h-6"},C=e=>{let a=b[j],s=e.toLowerCase(),l={skincare:t.jsx(c.Z,{className:a}),"hair care":t.jsx(r.Z,{className:a}),"hair oils":t.jsx(h,{className:a}),"body care":t.jsx(n.Z,{className:a}),"all products":t.jsx(y.Z,{className:a}),"cleansers & face wash":t.jsx(i,{className:a}),"combo & complete care":t.jsx(p.Z,{className:a}),"creams & moisturizers":t.jsx(h,{className:a}),"eye & lip care":t.jsx(m.Z,{className:a}),"facial kits":t.jsx(o.Z,{className:a}),"facial oils & elixirs":t.jsx(k,{className:a}),"gels & serums":t.jsx(x.Z,{className:a}),"hair masks":t.jsx(u,{className:a}),"massage & body oils":t.jsx(g,{className:a}),"scrubs & exfoliants":t.jsx(r.Z,{className:a}),toners:t.jsx(Z,{className:a}),"ubtan & masks":t.jsx(M,{className:a}),cleanser:t.jsx(i,{className:a}),serum:t.jsx(x.Z,{className:a}),moisturizer:t.jsx(h,{className:a}),mask:t.jsx(u,{className:a}),exfoliator:t.jsx(r.Z,{className:a}),"eye-care":t.jsx(m.Z,{className:a}),oil:t.jsx(h,{className:a}),toner:t.jsx(Z,{className:a}),kit:t.jsx(o.Z,{className:a}),ubtan:t.jsx(M,{className:a}),baby:t.jsx(f,{className:a})};if(l[s])return l[s];for(let[e,a]of Object.entries(l))if(s.includes(e)||e.includes(s))return a;return t.jsx(c.Z,{className:a})};return t.jsx("div",{className:`flex flex-wrap gap-3 ${s}`,children:N.map(e=>{let s=C(e.name);return a?(0,t.jsxs)(l.default,{href:`/shop?category=${e.slug}`,className:`inline-flex items-center gap-1.5 ${w[j]} font-medium text-green-600 hover:text-green-700 transition-colors`,children:[s,t.jsx("span",{children:e.name})]},e.id):(0,t.jsxs)("span",{className:`inline-flex items-center gap-1.5 ${w[j]} font-medium text-green-600`,children:[s,t.jsx("span",{children:e.name})]},e.id)})})}},39108:(e,a,s)=>{function t(e){return!!e&&!!e.showFlashSale&&(!e.flashSaleEndDate||new Date<new Date(e.flashSaleEndDate))}function l(e,a){let s=t(a),l=a?.flashSalePercentage||0;return{isOnSale:s,salePrice:s?Math.round(e-e*l/100):e,originalPrice:e,discountPercentage:s?l:0}}s.d(a,{FO:()=>l,LR:()=>t})},48998:(e,a,s)=>{s.d(a,{Z:()=>t});let t=(0,s(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,a,s)=>{s.d(a,{Z:()=>t});let t=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},53080:(e,a,s)=>{s.d(a,{Z:()=>t});let t=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},48705:(e,a,s)=>{s.d(a,{Z:()=>t});let t=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},89392:(e,a,s)=>{s.d(a,{Z:()=>t});let t=(0,s(76557).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},1572:(e,a,s)=>{s.d(a,{Z:()=>t});let t=(0,s(76557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},3634:(e,a,s)=>{s.d(a,{Z:()=>t});let t=(0,s(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])}};