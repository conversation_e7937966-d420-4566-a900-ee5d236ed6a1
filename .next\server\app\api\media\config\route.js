"use strict";(()=>{var e={};e.id=7237,e.ids=[7237],e.modules={21841:e=>{e.exports=require("@aws-sdk/client-s3")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},32694:e=>{e.exports=require("http2")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},76162:e=>{e.exports=require("stream")},84492:e=>{e.exports=require("node:stream")},21602:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>u,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(49303),i=t(88716),n=t(60670),a=t(87070),c=t(8379);async function p(e){try{let e=(0,c.rP)();return a.NextResponse.json({success:!0,config:e,message:"R2 configuration check completed"})}catch(e){return console.error("Config check error:",e),a.NextResponse.json({success:!1,error:"Failed to check configuration"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/media/config/route",pathname:"/api/media/config",filename:"route",bundlePath:"app/api/media/config/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\config\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:m}=u,f="/api/media/config/route";function g(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}},8379:(e,r,t)=>{t.d(r,{D0:()=>m,NA:()=>l,Z7:()=>u,fo:()=>p,rP:()=>i});var s=t(21841);t(38376);let o=new s.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0});function i(){return{hasAccessKey:!!process.env.R2_ACCESS_KEY_ID,hasSecretKey:!!process.env.R2_SECRET_ACCESS_KEY,hasBucketName:!!process.env.R2_BUCKET_NAME,hasAccountId:!!process.env.R2_ACCOUNT_ID,hasPublicUrl:!!process.env.R2_PUBLIC_URL,bucketName:n,publicUrl:a,endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`}}let n=process.env.R2_BUCKET_NAME||"herbalicious-images",a=process.env.R2_PUBLIC_URL||`https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;function c(e){return a.includes("pub-")&&a.includes(".r2.dev")||a.includes(n)?`${a}/${e}`:`${a}/${n}/${e}`}async function p(e,r="uploads"){try{let t=m(e);if(!t.valid)return{success:!1,error:t.error};let i=Date.now(),a=e.name.toLowerCase().replace(/[^a-z0-9.-]/g,"_").replace(/_{2,}/g,"_").replace(/^_|_$/g,"");if(!a.split(".").pop())return{success:!1,error:"File must have an extension"};let p=`${r}/${i}_${a}`,u=await e.arrayBuffer(),l=new s.PutObjectCommand({Bucket:n,Key:p,Body:new Uint8Array(u),ContentType:e.type,ContentLength:e.size,ContentDisposition:"attachment",CacheControl:"max-age=********"});await o.send(l);let f={key:p,name:e.name,size:e.size,type:d(e.name,e.type),url:c(p),lastModified:new Date,folder:r};return{success:!0,file:f}}catch(e){return console.error("Error uploading to R2:",e),{success:!1,error:e instanceof Error?e.message:"Upload failed"}}}async function u(e){try{let r=new s.DeleteObjectCommand({Bucket:n,Key:e});return await o.send(r),!0}catch(e){return console.error("Error deleting from R2:",e),!1}}async function l(e,r=100){try{let t=new s.ListObjectsV2Command({Bucket:n,Prefix:e?`${e}/`:void 0,MaxKeys:r}),i=await o.send(t);if(!i.Contents)return[];return i.Contents.map(e=>{let r=e.Key,t=r.split("/").pop()||r;return{key:r,name:t,size:e.Size||0,type:d(t),url:c(r),lastModified:e.LastModified||new Date,folder:r.includes("/")?r.split("/")[0]:void 0}})}catch(e){return console.error("Error listing R2 files:",e),[]}}function d(e,r){let t=e.split(".").pop()?.toLowerCase();if(r){if(r.startsWith("image/"))return"image";if(r.startsWith("video/"))return"video";if("application/pdf"===r||r.startsWith("application/msword")||r.startsWith("application/vnd.openxmlformats-officedocument"))return"document"}return["jpg","jpeg","png","gif","webp","svg","bmp","ico"].includes(t||"")?"image":["mp4","avi","mov","wmv","flv","webm","mkv","m4v"].includes(t||"")?"video":["pdf","doc","docx","txt","rtf","xls","xlsx","ppt","pptx"].includes(t||"")?"document":"other"}function m(e){if(e.size>10485760)return{valid:!1,error:"File size must be less than 10MB"};if(!["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","video/mp4","video/webm","application/pdf"].includes(e.type))return{valid:!1,error:"File type not supported"};let r=e.name.toLowerCase();if(![".jpg",".jpeg",".png",".gif",".webp",".svg",".mp4",".webm",".pdf"].some(e=>r.endsWith(e)))return{valid:!1,error:"File extension not allowed"};let t=r.split(".");if(t.length>2){let e=["php","exe","sh","bat","cmd","com","scr","vbs","js","jar"];for(let r=0;r<t.length-1;r++)if(e.includes(t[r]))return{valid:!1,error:"Suspicious file name detected"}}return r.includes("\0")?{valid:!1,error:"Invalid file name"}:"image/svg+xml"===e.type||r.endsWith(".svg")?{valid:!0,error:void 0}:{valid:!0}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8376],()=>t(21602));module.exports=s})();