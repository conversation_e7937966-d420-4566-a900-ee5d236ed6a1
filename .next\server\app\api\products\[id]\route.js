"use strict";(()=>{var e={};e.id=3898,e.ids=[3898],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8592:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>q,requestAsyncStorage:()=>S,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>I});var i={};r.r(i),r.d(i,{DELETE:()=>y,GET:()=>p,PATCH:()=>l,PUT:()=>m});var d=r(49303),n=r(88716),a=r(60670),o=r(87070),s=r(89456),u=r(11185),c=r(81515);async function p(e,{params:t}){try{await (0,s.Z)();let e=(u.Types.ObjectId.isValid(t.id)?await c.xs.findById(new u.Types.ObjectId(t.id)).lean():null)||await c.xs.findOne({slug:t.id}).lean();if(!e)return o.NextResponse.json({success:!1,error:"Product not found"},{status:404});let r=e._id,[i,d,n,a]=await Promise.all([e.categoryId?c.WD.findById(e.categoryId).lean():null,c.qN.find({productId:r}).sort({position:1}).lean(),c.Th.find({productId:r}).lean(),c.Cq.find({productId:r}).sort({createdAt:-1}).lean()]),p=await Promise.all(a.map(async e=>{let t=await c.n5.findById(e.userId).select("_id name email").lean();return{...e,user:t?{_id:t._id,name:t.name,email:t.email}:null}})),l={...e,_id:r,category:i,images:d,variants:n,reviews:p};return o.NextResponse.json({success:!0,data:l})}catch(e){return console.error("Error fetching product:",e),o.NextResponse.json({success:!1,error:"Failed to fetch product"},{status:500})}}async function l(e,{params:t}){try{await (0,s.Z)();let{name:r,slug:i,description:d,shortDescription:n,price:a,comparePrice:p,sku:l,quantity:m,isFeatured:y,isActive:g,categoryId:S,images:I,variations:f=[]}=await e.json(),h={...r&&{name:r},...i&&{slug:i},...d&&{description:d},...n&&{shortDescription:n},...void 0!==a&&{price:a},...void 0!==p&&{comparePrice:p},...l&&{sku:l},...void 0!==m&&{quantity:m},...void 0!==y&&{isFeatured:y},...void 0!==g&&{isActive:g},...S&&{categoryId:S}},q=await c.xs.findByIdAndUpdate(u.Types.ObjectId.isValid(t.id)?new u.Types.ObjectId(t.id):t.id,h,{new:!0}).lean();if(!q)return o.NextResponse.json({success:!1,error:"Product not found"},{status:404});if(void 0!==I){let e=u.Types.ObjectId.isValid(t.id)?new u.Types.ObjectId(t.id):t.id;if(await c.qN.deleteMany({productId:e}),Array.isArray(I)&&I.length>0){let t=I.map((t,i)=>({productId:e,url:t.url,alt:t.alt||r||"Product image",position:i}));await c.qN.insertMany(t)}}if(void 0!==f){let e=u.Types.ObjectId.isValid(t.id)?new u.Types.ObjectId(t.id):t.id;if(await c.Th.deleteMany({productId:e}),Array.isArray(f)&&f.length>0){let t=f.map(t=>({productId:e,name:t.name,value:t.value,price:t.price??null,pricingMode:t.pricingMode||"INCREMENT"}));await c.Th.insertMany(t)}}let b=u.Types.ObjectId.isValid(t.id)?new u.Types.ObjectId(t.id):t.id,[N,T,w]=await Promise.all([q.categoryId?c.WD.findById(q.categoryId).lean():null,c.qN.find({productId:b}).sort({position:1}).lean(),c.Th.find({productId:b}).lean()]),O={...q,_id:q._id,category:N,images:T,variants:w};return o.NextResponse.json({success:!0,data:O,message:"Product updated successfully"})}catch(e){return console.error("Error updating product:",e),o.NextResponse.json({success:!1,error:"Failed to update product"},{status:500})}}async function m(e,{params:t}){return l(e,{params:t})}async function y(e,{params:t}){try{await (0,s.Z)();let e=await c.xs.findById(u.Types.ObjectId.isValid(t.id)?new u.Types.ObjectId(t.id):t.id);if(!e)return o.NextResponse.json({success:!1,error:"Product not found"},{status:404});let r=await c.xs.findByIdAndUpdate(u.Types.ObjectId.isValid(t.id)?new u.Types.ObjectId(t.id):t.id,{isActive:!1,name:`[DELETED] ${new Date().toISOString().split("T")[0]} - ${e.name}`},{new:!0});return o.NextResponse.json({success:!0,message:"Product has been deactivated (soft deleted). It will no longer appear in the store.",type:"soft_delete",data:r})}catch(e){return console.error("Error deleting product:",e),o.NextResponse.json({success:!1,error:"Failed to delete product"},{status:500})}}let g=new d.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:S,staticGenerationAsyncStorage:I,serverHooks:f}=g,h="/api/products/[id]/route";function q(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:I})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>D,Dd:()=>x,Order:()=>O,P_:()=>A,Pp:()=>U,Th:()=>v,Vv:()=>V,WD:()=>w,gc:()=>j,hQ:()=>B,kL:()=>C,mA:()=>M,n5:()=>N,nW:()=>P,p1:()=>L,qN:()=>E,wV:()=>R,xs:()=>T});var i=r(11185),d=r.n(i);let n=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),a=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),o=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),s=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),c=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),I=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),h=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),q=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=d().models.User||d().model("User",n),T=d().models.Product||d().model("Product",a),w=d().models.Category||d().model("Category",o),O=d().models.Order||d().model("Order",s),P=d().models.HomepageSetting||d().model("HomepageSetting",u),j=d().models.Testimonial||d().model("Testimonial",c),E=d().models.ProductImage||d().model("ProductImage",p),v=d().models.ProductVariant||d().model("ProductVariant",l),D=d().models.Review||d().model("Review",m),C=d().models.Address||d().model("Address",y),x=d().models.OrderItem||d().model("OrderItem",g),A=d().models.Notification||d().model("Notification",S),R=d().models.Coupon||d().model("Coupon",I);d().models.Wishlist||d().model("Wishlist",f);let M=d().models.Newsletter||d().model("Newsletter",h),U=d().models.ProductCategory||d().model("ProductCategory",q),B=d().models.WishlistItem||d().model("WishlistItem",b),F=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),V=d().models.NotificationTemplate||d().model("NotificationTemplate",F),_=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),L=d().models.Enquiry||d().model("Enquiry",_)},89456:(e,t,r)=>{r.d(t,{Z:()=>o});var i=r(11185),d=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=d().connect(n,{bufferCommands:!1}));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(8592));module.exports=i})();