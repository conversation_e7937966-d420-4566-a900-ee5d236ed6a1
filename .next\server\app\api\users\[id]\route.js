"use strict";(()=>{var e={};e.id=1380,e.ids=[1380],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},6810:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>c,serverHooks:()=>j,staticGenerationAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>l,PATCH:()=>x});var n=t(49303),o=t(88716),i=t(60670),a=t(87070),u=t(45609),p=t(95306),d=t(3474);async function l(e,{params:r}){try{let e=await (0,u.getServerSession)(p.L);if(!e?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=r.id;if(e.user.id!==t&&"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Forbidden"},{status:403});await (0,d.uD)();let s=await d.n5.findById(t).lean();if(!s)return a.NextResponse.json({error:"User not found"},{status:404});return a.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Error fetching user:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let t=await (0,u.getServerSession)(p.L);if(!t?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=r.id;if(t.user.id!==s&&"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Forbidden"},{status:403});let{name:n,email:o,phone:i}=await e.json();if(!n?.trim())return a.NextResponse.json({error:"Name is required"},{status:400});if(!o?.trim())return a.NextResponse.json({error:"Email is required"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return a.NextResponse.json({error:"Invalid email format"},{status:400});if(await (0,d.uD)(),await d.n5.findOne({email:o,_id:{$ne:s}}).lean())return a.NextResponse.json({error:"Email is already taken"},{status:400});if(i&&i.trim()&&!/^\+?[\d\s\-\(\)]+$/.test(i))return a.NextResponse.json({error:"Invalid phone number format"},{status:400});let l=await d.n5.findByIdAndUpdate(s,{name:n.trim(),email:o.trim(),phone:i?.trim()||null},{new:!0,projection:{name:1,email:1,phone:1,role:1,createdAt:1}}).lean();return a.NextResponse.json({success:!0,data:l,message:"Profile updated successfully"})}catch(e){return console.error("Error updating user:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:"app/api/users/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:j}=c,q="/api/users/[id]/route";function g(){return(0,i.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:f})}},3474:(e,r,t)=>{t.d(r,{KM:()=>n.Order,WD:()=>n.WD,n5:()=>n.n5,uD:()=>s.Z,xs:()=>n.xs});var s=t(89456),n=t(81515)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,2830,5306],()=>t(6810));module.exports=s})();