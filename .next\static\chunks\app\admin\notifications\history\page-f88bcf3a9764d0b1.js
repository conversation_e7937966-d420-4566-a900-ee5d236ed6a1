(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3614],{95319:function(e,s,t){Promise.resolve().then(t.bind(t,23361))},23361:function(e,s,t){"use strict";t.r(s);var r=t(57437),a=t(2265),n=t(99376),i=t(32660),c=t(73247),l=t(22252),d=t(94766),o=t(92369),u=t(91723),x=t(42208),h=t(65302),m=t(45131);s.default=()=>{let e=(0,n.useRouter)(),[s,t]=(0,a.useState)([]),[p,g]=(0,a.useState)(!0),[y,f]=(0,a.useState)(null),[j,v]=(0,a.useState)(""),[N,b]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),[E,Z]=(0,a.useState)(1),[S,R]=(0,a.useState)(1);(0,a.useEffect)(()=>{P()},[E,N,w]);let P=async()=>{try{g(!0);let e=new URLSearchParams({page:E.toString(),limit:"20",...j&&{search:j},...N&&{type:N},...w&&{status:w}}),s=await fetch("/api/admin/notifications/history?".concat(e)),r=await s.json();r.success?(t(r.data.notifications),R(r.data.pagination.totalPages)):f("Failed to fetch notification history")}catch(e){console.error("Error fetching notifications:",e),f("Failed to fetch notification history")}finally{g(!1)}},C=()=>{Z(1),P()},A=e=>{switch(e){case"ORDER_PLACED":return"bg-blue-100 text-blue-800";case"ORDER_SHIPPED":return"bg-green-100 text-green-800";case"ORDER_DELIVERED":return"bg-emerald-100 text-emerald-800";case"ADMIN_MESSAGE":return"bg-purple-100 text-purple-800";case"BROADCAST":return"bg-orange-100 text-orange-800";case"PROMOTIONAL":return"bg-pink-100 text-pink-800";default:return"bg-gray-100 text-gray-800"}},D=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(i.Z,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Notification History"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"View all sent notifications"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search notifications...",value:j,onChange:e=>v(e.target.value),onKeyPress:e=>"Enter"===e.key&&C(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"})]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("select",{value:N,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",children:[(0,r.jsx)("option",{value:"",children:"All Types"}),(0,r.jsx)("option",{value:"ORDER_PLACED",children:"Order Placed"}),(0,r.jsx)("option",{value:"ORDER_SHIPPED",children:"Order Shipped"}),(0,r.jsx)("option",{value:"ORDER_DELIVERED",children:"Order Delivered"}),(0,r.jsx)("option",{value:"ADMIN_MESSAGE",children:"Admin Message"}),(0,r.jsx)("option",{value:"BROADCAST",children:"Broadcast"}),(0,r.jsx)("option",{value:"PROMOTIONAL",children:"Promotional"})]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("select",{value:w,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",children:[(0,r.jsx)("option",{value:"",children:"All Status"}),(0,r.jsx)("option",{value:"read",children:"Read"}),(0,r.jsx)("option",{value:"unread",children:"Unread"}),(0,r.jsx)("option",{value:"email_sent",children:"Email Sent"}),(0,r.jsx)("option",{value:"email_failed",children:"Email Failed"})]})})]}),(0,r.jsx)("div",{className:"mt-4 flex justify-end",children:(0,r.jsxs)("button",{onClick:C,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[(0,r.jsx)(c.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Search"})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[p?(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},s))})}):y?(0,r.jsxs)("div",{className:"p-6 text-center",children:[(0,r.jsx)(l.Z,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-red-600",children:y}),(0,r.jsx)("button",{onClick:P,className:"mt-2 text-red-600 hover:text-red-800 underline",children:"Try again"})]}):0===s.length?(0,r.jsxs)("div",{className:"p-6 text-center",children:[(0,r.jsx)(d.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No notifications found"})]}):(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:s.map(e=>(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"w-5 h-5 text-green-600"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(A(e.type)),children:e.type.replace("_"," ")})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:e.user.name||e.user.email})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(u.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:D(e.createdAt)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isRead?(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-green-600",children:[(0,r.jsx)(x.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"Read"})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-gray-400",children:[(0,r.jsx)(x.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"Unread"})]}),e.emailSent?(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-green-600",children:[(0,r.jsx)(h.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"Email Sent"})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-red-500",children:[(0,r.jsx)(m.Z,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"Email Failed"})]})]})]})]})]})},e.id))}),S>1&&(0,r.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Page ",E," of ",S]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>Z(E-1),disabled:1===E,className:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsx)("button",{onClick:()=>Z(E+1),disabled:E===S,className:"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})]})}},39763:function(e,s,t){"use strict";t.d(s,{Z:function(){return i}});var r=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,s)=>{let t=(0,r.forwardRef)((t,i)=>{let{color:c="currentColor",size:l=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:u="",children:x,...h}=t;return(0,r.createElement)("svg",{ref:i,...a,width:l,height:l,stroke:c,strokeWidth:o?24*Number(d)/Number(l):d,className:["lucide","lucide-".concat(n(e)),u].join(" "),...h},[...s.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(x)?x:[x]])});return t.displayName="".concat(e),t}},22252:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},32660:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},94766:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},65302:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},91723:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},42208:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},73247:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},92369:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},45131:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},99376:function(e,s,t){"use strict";var r=t(35475);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=95319)}),_N_E=e.O()}]);