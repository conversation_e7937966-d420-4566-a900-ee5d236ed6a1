"use strict";(()=>{var e={};e.id=9314,e.ids=[9314],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},41604:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>h,requestAsyncStorage:()=>j,routeModule:()=>x,serverHooks:()=>g,staticGenerationAsyncStorage:()=>q});var n={};t.r(n),t.d(n,{DELETE:()=>y,GET:()=>d,PATCH:()=>f});var s=t(49303),o=t(88716),i=t(60670),u=t(87070),a=t(89456),p=t(81515),c=t(75571),l=t(95306);async function d(e,{params:r}){try{let e=await (0,c.getServerSession)(l.L);if(!e?.user||"ADMIN"!==e.user.role)return u.NextResponse.json({error:"Unauthorized"},{status:401});await (0,a.Z)();let t=await p.p1.findById(r.id).lean();if(!t)return u.NextResponse.json({error:"Enquiry not found"},{status:404});return u.NextResponse.json(t)}catch(e){return console.error("Error fetching enquiry:",e),u.NextResponse.json({error:"Failed to fetch enquiry"},{status:500})}}async function f(e,{params:r}){try{let t=await (0,c.getServerSession)(l.L);if(!t?.user||"ADMIN"!==t.user.role)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{status:n,notes:s}=await e.json();if(n&&!["NEW","IN_PROGRESS","RESOLVED","CLOSED"].includes(n))return u.NextResponse.json({error:"Invalid status"},{status:400});let o={};n&&(o.status=n),void 0!==s&&(o.notes=s),await (0,a.Z)();let i=await p.p1.findByIdAndUpdate(r.id,{$set:o},{new:!0}).lean();return u.NextResponse.json({success:!0,enquiry:i})}catch(e){return console.error("Error updating enquiry:",e),u.NextResponse.json({error:"Failed to update enquiry"},{status:500})}}async function y(e,{params:r}){try{let e=await (0,c.getServerSession)(l.L);if(!e?.user||"ADMIN"!==e.user.role)return u.NextResponse.json({error:"Unauthorized"},{status:401});return await (0,a.Z)(),await p.p1.findByIdAndDelete(r.id),u.NextResponse.json({success:!0,message:"Enquiry deleted successfully"})}catch(e){return console.error("Error deleting enquiry:",e),u.NextResponse.json({error:"Failed to delete enquiry"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/enquiries/[id]/route",pathname:"/api/enquiries/[id]",filename:"route",bundlePath:"app/api/enquiries/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\enquiries\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:j,staticGenerationAsyncStorage:q,serverHooks:g}=x,v="/api/enquiries/[id]/route";function h(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:q})}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var n={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var u=s?Object.getOwnPropertyDescriptor(e,o):null;u&&(u.get||u.set)?Object.defineProperty(n,o,u):n[o]=e[o]}return n.default=e,t&&t.set(e,n),n}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[9276,5972,8691,2830,5306],()=>t(41604));module.exports=n})();