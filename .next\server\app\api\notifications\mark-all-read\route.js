"use strict";(()=>{var e={};e.id=8663,e.ids=[8663],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},74669:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>y,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{POST:()=>c});var i=r(49303),s=r(88716),n=r(60670),a=r(87070),u=r(75571),l=r(95306),p=r(54211);async function c(e){try{let e=await (0,u.getServerSession)(l.L);if(!e?.user?.id)return a.NextResponse.json({error:"Authentication required"},{status:401});return p.kg.info("Mark all as read API temporarily disabled during migration"),a.NextResponse.json({success:!0,message:"Mark all as read temporarily disabled during migration",count:0})}catch(e){return p.kg.error("Failed to mark all notifications as read",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/notifications/mark-all-read/route",pathname:"/api/notifications/mark-all-read",filename:"route",bundlePath:"app/api/notifications/mark-all-read/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\mark-all-read\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:g}=d,m="/api/notifications/mark-all-read/route";function y(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},54211:(e,t,r)=>{var o;r.d(t,{kg:()=>s}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(o||(o={}));class i{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:i,context:s,error:n,userId:a,requestId:u}=e,l=o[r],p=`[${t}] ${l}: ${i}`;return a&&(p+=` | User: ${a}`),u&&(p+=` | Request: ${u}`),s&&Object.keys(s).length>0&&(p+=` | Context: ${JSON.stringify(s)}`),n&&(p+=` | Error: ${n.message}`,this.isDevelopment&&n.stack&&(p+=`
Stack: ${n.stack}`)),p}log(e,t,r,o){if(!this.shouldLog(e))return;let i={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:o},s=this.formatMessage(i);if(this.isDevelopment)switch(e){case 0:console.error(s);break;case 1:console.warn(s);break;case 2:console.info(s);break;case 3:console.debug(s)}else console.log(JSON.stringify(i))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,o){this.info(`API ${e} ${t}`,{...o,userId:r,type:"api_request"})}apiResponse(e,t,r,o,i){this.info(`API ${e} ${t} - ${r}`,{...i,statusCode:r,duration:o,type:"api_response"})}apiError(e,t,r,o,i){this.error(`API ${e} ${t} failed`,r,{...i,userId:o,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,o){this.warn("Authentication failed",{...o,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,o){this.debug(`DB ${e} on ${t}`,{...o,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,o){this.error(`DB ${e} on ${t} failed`,r,{...o,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,o){this.warn("Rate limit exceeded",{...o,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,o){this.info("Email sent",{...o,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,o){this.error("Email failed to send",r,{...o,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let s=new i},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var a=i?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(o,s,a):o[s]=e[s]}return o.default=e,r&&r.set(e,o),o}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,5972,8691,2830,5306],()=>r(74669));module.exports=o})();