import Razorpay from "razorpay";
import { logger } from "./logger";
import { AppError } from "./errors";
import crypto from "crypto";

// Initialize Razorpay instance
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

export interface PaymentOrderData {
  amount: number; // Amount in paise (INR)
  currency: string;
  receipt: string;
  notes?: Record<string, string>;
}

// Use Record type for Razorpay order to avoid type conflicts
export type RazorpayOrder = Record<string, any>;

export interface PaymentVerificationData {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
}

// Create Razorpay order
export async function createPaymentOrder(
  orderData: PaymentOrderData,
): Promise<RazorpayOrder> {
  try {
    logger.info("Creating Razorpay order", {
      amount: orderData.amount,
      currency: orderData.currency,
      receipt: orderData.receipt,
    });

    const order = await razorpay.orders.create({
      amount: orderData.amount,
      currency: orderData.currency,
      receipt: orderData.receipt,
      notes: orderData.notes || {},
    });

    logger.info("Razorpay order created successfully", {
      orderId: order.id,
      amount: order.amount,
      receipt: order.receipt,
    });

    return order;
  } catch (error) {
    logger.error("Failed to create Razorpay order", error as Error);
    throw new AppError("Failed to create payment order", 500);
  }
}

// Verify payment signature
export function verifyPaymentSignature(data: PaymentVerificationData): boolean {
  try {
    const expectedSignature = crypto
      .createHmac("sha256", process.env.RAZORPAY_KEY_SECRET!)
      .update(`${data.razorpay_order_id}|${data.razorpay_payment_id}`)
      .digest("hex");

    const isValid = expectedSignature === data.razorpay_signature;

    logger.info("Payment signature verification", {
      orderId: data.razorpay_order_id,
      paymentId: data.razorpay_payment_id,
      isValid,
    });

    return isValid;
  } catch (error) {
    logger.error("Payment signature verification failed", error as Error);
    return false;
  }
}

// Fetch payment details
export async function getPaymentDetails(paymentId: string) {
  try {
    logger.info("Fetching payment details", { paymentId });

    const payment = await razorpay.payments.fetch(paymentId);

    logger.info("Payment details fetched successfully", {
      paymentId: payment.id,
      status: payment.status,
      amount: payment.amount,
    });

    return payment;
  } catch (error) {
    logger.error("Failed to fetch payment details", error as Error);
    throw new AppError("Failed to fetch payment details", 500);
  }
}

// Refund payment
export async function refundPayment(paymentId: string, amount?: number) {
  try {
    logger.info("Initiating payment refund", { paymentId, amount });

    const refundData: Record<string, any> = { payment_id: paymentId };
    if (amount) {
      refundData.amount = amount;
    }

    const refund = await razorpay.payments.refund(paymentId, refundData);

    logger.info("Payment refund initiated successfully", {
      refundId: refund.id,
      paymentId,
      amount: refund.amount,
      status: refund.status,
    });

    return refund;
  } catch (error) {
    logger.error("Failed to initiate payment refund", error as Error);
    throw new AppError("Failed to initiate refund", 500);
  }
}

// Convert amount to paise (Razorpay uses paise)
export function convertToPaise(amount: number): number {
  return Math.round(amount * 100);
}

// Convert amount from paise to rupees
export function convertFromPaise(amount: number): number {
  return amount / 100;
}

// Generate unique receipt ID
export function generateReceiptId(prefix: string = "ORDER"): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}_${timestamp}_${random}`;
}

// Validate payment amount
export function validatePaymentAmount(amount: number): boolean {
  // Minimum amount is 1 INR (100 paise)
  // Maximum amount is 15,00,000 INR (15 crores paise)
  const minAmount = 100; // 1 INR in paise
  const maxAmount = 1500000000; // 15,00,000 INR in paise

  return amount >= minAmount && amount <= maxAmount && Number.isInteger(amount);
}

// Get payment status message
export function getPaymentStatusMessage(status: string): string {
  const statusMessages: Record<string, string> = {
    created: "Payment order created",
    authorized: "Payment authorized",
    captured: "Payment captured successfully",
    refunded: "Payment refunded",
    failed: "Payment failed",
    cancelled: "Payment cancelled",
  };

  return statusMessages[status] || "Unknown payment status";
}

export default razorpay;
