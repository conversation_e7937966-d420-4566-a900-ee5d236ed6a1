{"/_not-found/page": "/_not-found", "/about/page": "/about", "/addresses/page": "/addresses", "/api/admin/notifications/broadcast/route": "/api/admin/notifications/broadcast", "/api/admin/cleanup/route": "/api/admin/cleanup", "/api/admin/notifications/history/route": "/api/admin/notifications/history", "/api/admin/notifications/stats/route": "/api/admin/notifications/stats", "/api/admin/notifications/templates/route": "/api/admin/notifications/templates", "/api/admin/notifications/send/route": "/api/admin/notifications/send", "/api/admin/products/export/route": "/api/admin/products/export", "/api/admin/reviews/route": "/api/admin/reviews", "/api/admin/users/route": "/api/admin/users", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/reset-password/route": "/api/auth/reset-password", "/api/auth/session-debug/route": "/api/auth/session-debug", "/api/categories/[id]/route": "/api/categories/[id]", "/api/categories/route": "/api/categories", "/api/coupons/[id]/route": "/api/coupons/[id]", "/api/coupons/validate/route": "/api/coupons/validate", "/api/coupons/route": "/api/coupons", "/api/enquiries/[id]/route": "/api/enquiries/[id]", "/api/dashboard/stats/route": "/api/dashboard/stats", "/api/homepage-settings/route": "/api/homepage-settings", "/api/enquiries/route": "/api/enquiries", "/api/media/delete/route": "/api/media/delete", "/api/media/list/route": "/api/media/list", "/api/media/upload/route": "/api/media/upload", "/api/media/config/route": "/api/media/config", "/api/newsletter/export/route": "/api/newsletter/export", "/api/newsletter/route": "/api/newsletter", "/api/notifications/mark-all-read/route": "/api/notifications/mark-all-read", "/api/notifications/[id]/read/route": "/api/notifications/[id]/read", "/api/notifications/price-drop-check/route": "/api/notifications/price-drop-check", "/api/notifications/route": "/api/notifications", "/api/notifications/review-requests/route": "/api/notifications/review-requests", "/api/notifications/unread-count/route": "/api/notifications/unread-count", "/api/products/[id]/faqs/route": "/api/products/[id]/faqs", "/api/products/[id]/route": "/api/products/[id]", "/api/products/[id]/variations/[variationId]/route": "/api/products/[id]/variations/[variationId]", "/api/test-auth/route": "/api/test-auth", "/api/products/route": "/api/products", "/api/testimonials/[id]/route": "/api/testimonials/[id]", "/api/users/[id]/preferences/route": "/api/users/[id]/preferences", "/api/testimonials/route": "/api/testimonials", "/api/users/[id]/route": "/api/users/[id]", "/api/users/[id]/stats/route": "/api/users/[id]/stats", "/api/users/route": "/api/users", "/api/wishlist/route": "/api/wishlist", "/cart/page": "/cart", "/contact/page": "/contact", "/checkout/page": "/checkout", "/categories/page": "/categories", "/edit-profile/page": "/edit-profile", "/faq/page": "/faq", "/login/page": "/login", "/order-history/page": "/order-history", "/page": "/", "/privacy/page": "/privacy", "/product/[id]/page": "/product/[id]", "/notifications/page": "/notifications", "/shipping/page": "/shipping", "/wishlist/page": "/wishlist", "/signup/page": "/signup", "/terms/page": "/terms", "/sw.js/route": "/sw.js", "/api/auth/register/route": "/api/auth/register", "/api/auth/forgot-password/route": "/api/auth/forgot-password", "/api/payments/config/route": "/api/payments/config", "/api/payments/test/route": "/api/payments/test", "/api/orders/route": "/api/orders", "/api/orders/bulk-update/route": "/api/orders/bulk-update", "/api/payments/verify/route": "/api/payments/verify", "/api/payments/create-order/route": "/api/payments/create-order", "/api/orders/[orderId]/route": "/api/orders/[orderId]", "/api/products/[id]/faqs/[faqId]/route": "/api/products/[id]/faqs/[faqId]", "/api/products/[id]/reviews/route": "/api/products/[id]/reviews", "/api/products/[id]/variations/route": "/api/products/[id]/variations", "/api/products/filters/route": "/api/products/filters", "/api/products/bulk/route": "/api/products/bulk", "/api/products/import/route": "/api/products/import", "/api/products/optimized/route": "/api/products/optimized", "/api/test-email/route": "/api/test-email", "/order-confirmation/page": "/order-confirmation", "/shop/page": "/shop", "/admin/enquiry/page": "/admin/enquiry", "/admin/customers/page": "/admin/customers", "/admin/categories/page": "/admin/categories", "/admin/homepage/page": "/admin/homepage", "/admin/coupons/page": "/admin/coupons", "/admin/media/page": "/admin/media", "/admin/newsletter/page": "/admin/newsletter", "/admin/notifications/broadcast/page": "/admin/notifications/broadcast", "/admin/notifications/page": "/admin/notifications", "/admin/notifications/history/page": "/admin/notifications/history", "/admin/orders/[id]/page": "/admin/orders/[id]", "/admin/notifications/templates/page": "/admin/notifications/templates", "/admin/notifications/send/page": "/admin/notifications/send", "/admin/orders/page": "/admin/orders", "/admin/page": "/admin", "/admin/products/page": "/admin/products", "/admin/settings/page": "/admin/settings", "/admin/reviews/page": "/admin/reviews", "/profile/page": "/profile"}