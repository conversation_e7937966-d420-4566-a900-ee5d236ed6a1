"use strict";(()=>{var e={};e.id=5382,e.ids=[5382],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},18509:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>j,requestAsyncStorage:()=>q,routeModule:()=>m,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>x});var o=r(49303),i=r(88716),n=r(60670),a=r(87070),u=r(45609),c=r(95306),p=r(89456),l=r(81515);async function d(e){try{await (0,p.Z)();let{searchParams:t}=new URL(e.url),r="true"===t.get("active"),s=await l.gc.find(r?{isActive:!0}:{}).sort({order:1,createdAt:-1}).lean();return a.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Error fetching testimonials:",e),a.NextResponse.json({success:!1,error:"Failed to fetch testimonials"},{status:500})}}async function x(e){try{await (0,p.Z)();let t=await (0,u.getServerSession)(c.L);if(!t?.user?.email)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let r=await l.n5.findOne({email:t.user.email});if(!r||"ADMIN"!==r.role)return a.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{name:s,content:o,rating:i,image:n,position:d,company:x,isActive:m,order:q}=await e.json();if(!s||!o)return a.NextResponse.json({success:!1,error:"Name and content are required"},{status:400});let g=await l.gc.create({name:s,content:o,rating:i||5,image:n,position:d,company:x,isActive:void 0===m||m,order:q||0});return a.NextResponse.json({success:!0,data:g,message:"Testimonial created successfully"})}catch(e){return console.error("Error creating testimonial:",e),a.NextResponse.json({success:!1,error:"Failed to create testimonial"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/testimonials/route",pathname:"/api/testimonials",filename:"route",bundlePath:"app/api/testimonials/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:q,staticGenerationAsyncStorage:g,serverHooks:f}=m,h="/api/testimonials/route";function j(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,5306],()=>r(18509));module.exports=s})();