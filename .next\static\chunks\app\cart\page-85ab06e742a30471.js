(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5311],{62558:function(e,s,t){Promise.resolve().then(t.bind(t,28250)),Promise.resolve().then(t.bind(t,47609))},47609:function(e,s,t){"use strict";t.d(s,{default:function(){return F}});var a=t(57437),r=t(29),l=t.n(r),n=t(2265),i=t(27648),c=t(33145),d=t(42449),o=t(76858),x=t(18930),m=t(39763);let h=(0,m.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var u=t(99397),p=t(53827),g=t(80605);let f=(0,m.Z)("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var j=t(95252);let y=(0,m.Z)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var b=t(62720),N=t(82023),v=t(65302),w=t(32489),S=t(22252),k=t(78867),C=t(91723),E=t(1396),P=e=>{let{cartItems:s,subtotal:t,userId:r,onCouponApply:l,onCouponRemove:i,appliedCoupons:c}=e,{flashSaleSettings:d,isHydrated:o}=(0,E.u)(),[x,m]=(0,n.useState)([]),[h,u]=(0,n.useState)([]),[p,g]=(0,n.useState)(""),[P,F]=(0,n.useState)(!1),[Z,A]=(0,n.useState)(""),[D,M]=(0,n.useState)(!1),[T,I]=(0,n.useState)(!0),O=n.useMemo(()=>{if(!d||!d.showFlashSale)return!1;if(d.flashSaleEndDate){let e=new Date;return new Date(d.flashSaleEndDate)>=e}return!0},[d]);(0,n.useEffect)(()=>{U(),R()},[s,r,O]);let U=async()=>{try{I(!0);let e=new URLSearchParams({active:"true",limit:"20"});r&&e.append("userId",r);let s=await fetch("/api/coupons?".concat(e));if(s.ok){let e=await s.json(),t=V(e.coupons,!0);m(t)}}catch(e){console.error("Error fetching coupons:",e)}finally{I(!1)}},R=async()=>{try{let e=new URLSearchParams({active:"true",showInModule:"true",limit:"10"});r&&e.append("userId",r);let s=await fetch("/api/coupons?".concat(e));if(s.ok){let e=await s.json(),t=V(e.coupons,!1);u(t)}}catch(e){console.error("Error fetching featured coupons:",e)}},V=function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e.filter(e=>{if(c.some(s=>s.coupon.id===e.id)||a&&e.showInModule||e.minimumAmount&&t<e.minimumAmount)return!1;let r=s.map(e=>e.product.id),l=s.flatMap(e=>{var s;return(null===(s=e.product.categories)||void 0===s?void 0:s.map(e=>e.id))||[]});switch(e.type){case"PRODUCT_SPECIFIC":return e.applicableProducts.some(e=>r.includes(e));case"CATEGORY_SPECIFIC":return e.applicableCategories.some(e=>l.includes(e));case"MINIMUM_PURCHASE":return t>=(e.minimumAmount||0);default:return!0}})},_=async e=>{if(e.trim()){F(!0),A("");try{let a=await fetch("/api/coupons/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({couponCode:e,cartItems:s,userId:r,subtotal:t})}),n=await a.json();if(n.isValid&&n.coupon){let e={coupon:n.coupon,discountAmount:n.discountAmount,isValid:!0};l(e),g(""),A("Coupon applied successfully!"),U(),R()}else A(n.errorMessage||"Invalid coupon code")}catch(e){A("Error validating coupon")}finally{F(!1)}}},z=async e=>{await _(e.code)},q=e=>{navigator.clipboard.writeText(e)},H=e=>{switch(e){case"PERCENTAGE":return(0,a.jsx)(f,{className:"w-4 h-4"});case"FIXED_AMOUNT":return(0,a.jsx)(j.Z,{className:"w-4 h-4"});case"FREE_SHIPPING":return(0,a.jsx)(y,{className:"w-4 h-4"});default:return(0,a.jsx)(b.Z,{className:"w-4 h-4"})}},K=e=>{switch(e.discountType){case"PERCENTAGE":return"".concat(e.discountValue,"% OFF");case"FIXED_AMOUNT":return"₹".concat(e.discountValue," OFF");case"FREE_SHIPPING":return"FREE SHIPPING";default:return"DISCOUNT"}},L=e=>{if(!e.validUntil)return!1;let s=new Date(e.validUntil),t=new Date,a=Math.ceil((s.getTime()-t.getTime())/864e5);return a<=3&&a>0};if(T||!o)return(0,a.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-16 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-16 bg-gray-200 rounded"})]})]})});if(O)return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(N.Z,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Coupons"})]}),(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(y,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),(0,a.jsx)("p",{className:"text-gray-600 font-medium mb-2",children:"Flash Sale Active!"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Coupons cannot be used during flash sale periods. Enjoy the flash sale discounts instead!"})]})]});let G=(()=>{let e=x.filter(e=>("PERCENTAGE"===e.discountType?e.discountValue>=10:e.discountValue>=50)||L(e));return e.length>=3?e.slice(0,5):x.slice(0,Math.min(5,x.length))})(),Y=D?x:G;return(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)(N.Z,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Available Coupons"})]}),c.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-600 mb-3",children:"Applied Coupons"}),(0,a.jsx)("div",{className:"space-y-2",children:c.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(v.Z,{className:"w-4 h-4 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-green-800",children:e.coupon.code}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:["-₹",e.discountAmount.toFixed(2)]})]})]}),(0,a.jsx)("button",{onClick:()=>i(e.coupon.id),className:"p-1 text-green-600 hover:bg-green-100 rounded-full transition-colors",children:(0,a.jsx)(w.Z,{className:"w-4 h-4"})})]},e.coupon.id))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:p,onChange:e=>g(e.target.value.toUpperCase()),placeholder:"Enter coupon code",className:"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&_(p)}),(0,a.jsx)("button",{onClick:()=>_(p),disabled:P||!p.trim(),className:"px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:P?"Applying...":"Apply"})]}),Z&&(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-2 text-sm ".concat(Z.includes("successfully")?"text-green-600":"text-red-600"),children:[Z.includes("successfully")?(0,a.jsx)(v.Z,{className:"w-4 h-4"}):(0,a.jsx)(S.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:Z})]})]}),h.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-600 mb-4 flex items-center space-x-2",children:[(0,a.jsx)(N.Z,{className:"w-4 h-4 text-yellow-500"}),(0,a.jsx)("span",{children:"Featured Offers"})]}),(0,a.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,a.jsxs)("div",{className:"relative bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow border-2 border-dashed border-green-400",children:[(0,a.jsx)("div",{className:"absolute top-3 right-3 bg-yellow-400 text-yellow-900 px-3 py-1 text-xs font-bold rounded-full z-10",children:"FEATURED"}),(0,a.jsx)("div",{className:"p-5 sm:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"bg-green-100 text-green-700 p-2 rounded-lg",children:H(e.discountType)}),(0,a.jsxs)("div",{className:"flex items-center flex-wrap gap-2",children:[(0,a.jsx)("span",{className:"text-2xl sm:text-3xl font-medium text-gray-900",children:K(e)}),L(e)&&(0,a.jsx)("span",{className:"px-2 py-0.5 bg-red-500 text-white text-xs rounded-full font-bold",children:"EXPIRES SOON"})]})]}),(0,a.jsx)("h5",{className:"font-medium text-gray-900 text-base sm:text-lg mb-1",children:e.name}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2 hidden sm:block",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 text-xs sm:text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-gray-100 px-2 py-1 rounded font-mono",children:[(0,a.jsx)(b.Z,{className:"w-3 h-3 text-gray-600"}),(0,a.jsx)("span",{className:"font-bold text-gray-900",children:e.code}),(0,a.jsx)("button",{onClick:()=>q(e.code),className:"p-0.5 hover:bg-gray-200 rounded",title:"Copy",children:(0,a.jsx)(k.Z,{className:"w-3 h-3 text-gray-600"})})]}),e.validUntil&&(0,a.jsxs)("span",{className:"text-gray-500 flex items-center gap-1",children:[(0,a.jsx)(C.Z,{className:"w-3 h-3"}),new Date(e.validUntil).toLocaleDateString()]}),e.minimumAmount&&(0,a.jsxs)("span",{className:"text-gray-500",children:["Min: ₹",e.minimumAmount]})]})]}),(0,a.jsx)("button",{onClick:()=>z(e),className:"w-full sm:w-auto px-6 py-2.5 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition-colors shadow hover:shadow-md",children:"APPLY"})]})})]},e.id))})]}),Y.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-600",children:D?"All Available Coupons":"Recommended for You"}),x.length>Y.length&&(0,a.jsx)("button",{onClick:()=>M(!D),className:"text-sm text-green-600 hover:text-green-700 font-medium",children:D?"Show Less":"View All (".concat(x.length,")")})]}),(0,a.jsx)("div",{className:"space-y-2",children:Y.map(e=>(0,a.jsxs)("div",{className:"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow border border-gray-200",children:[(0,a.jsx)("div",{className:"absolute inset-0 border border-dashed ".concat(L(e)?"border-orange-300":"border-gray-300"," rounded-lg m-1")}),(0,a.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 sm:w-1.5 ".concat(L(e)?"bg-orange-500":"bg-green-500")}),(0,a.jsx)("div",{className:"p-3 pl-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("div",{className:"p-1.5 rounded ".concat(L(e)?"bg-orange-100 text-orange-700":"bg-green-100 text-green-700"),children:H(e.discountType)}),(0,a.jsx)("span",{className:"text-lg sm:text-xl font-medium text-gray-900",children:K(e)}),L(e)&&(0,a.jsx)("span",{className:"px-1.5 py-0.5 bg-orange-500 text-white text-xs rounded font-bold",children:"EXPIRES"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 text-sm",children:[(0,a.jsx)("h5",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-gray-100 px-2 py-0.5 rounded text-xs font-mono",children:[(0,a.jsx)("span",{className:"font-bold text-gray-900",children:e.code}),(0,a.jsx)("button",{onClick:()=>q(e.code),className:"p-0.5 hover:bg-gray-200 rounded",title:"Copy",children:(0,a.jsx)(k.Z,{className:"w-3 h-3 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-500 mt-1",children:[e.validUntil&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(C.Z,{className:"w-3 h-3"}),new Date(e.validUntil).toLocaleDateString()]}),e.minimumAmount&&(0,a.jsxs)("span",{children:["Min: ₹",e.minimumAmount]})]})]}),(0,a.jsx)("button",{onClick:()=>z(e),className:"w-full sm:w-auto px-4 py-2 bg-green-600 text-white text-sm rounded-lg font-semibold hover:bg-green-700 transition-colors",children:"APPLY"})]})})]},e.id))})]}),0===x.length&&!T&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(y,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No coupons available for your current cart"})]})]})},F=()=>{var e,s;let{state:t,dispatch:r}=(0,p.j)(),{data:n}=(0,g.useSession)(),m=(e,s)=>{s<=0?r({type:"REMOVE_ITEM",payload:e}):r({type:"UPDATE_QUANTITY",payload:{id:e,quantity:s}})},f=e=>{r({type:"REMOVE_ITEM",payload:e})},j=e=>{r({type:"APPLY_COUPON",payload:e})},y=e=>{r({type:"REMOVE_COUPON",payload:e})};return t.isHydrated?0===t.items.length?(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-8 text-center",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(d.Z,{className:"w-12 h-12 text-gray-400"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Add some products to get started"}),(0,a.jsxs)(i.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:["Start Shopping",(0,a.jsx)(o.Z,{className:"ml-2 w-4 h-4"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-16 text-center",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,a.jsx)(d.Z,{className:"w-16 h-16 text-gray-400"})}),(0,a.jsx)("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"Your cart is empty"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Discover our amazing products and start shopping"}),(0,a.jsxs)(i.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-green-700 transition-colors text-lg",children:["Start Shopping",(0,a.jsx)(o.Z,{className:"ml-3 w-5 h-5"})]})]})})]}):(0,a.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsxs)("div",{className:"px-4 py-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Shopping Cart"}),(0,a.jsx)("div",{className:"space-y-4 mb-6",children:t.items.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("div",{className:"w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0",children:(0,a.jsx)(c.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"80px"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-1 line-clamp-1",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-1",children:e.product.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["₹",e.product.price]}),(0,a.jsx)("button",{onClick:()=>f(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:(0,a.jsx)(x.Z,{className:"w-4 h-4"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4 pt-4 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>m(e.variantKey||e.product.id,e.quantity-1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(h,{className:"w-4 h-4"})}),(0,a.jsx)("span",{className:"font-medium text-gray-800 w-8 text-center",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>m(e.variantKey||e.product.id,e.quantity+1),className:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(u.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("span",{className:"font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]})]},e.variantKey||e.product.id))}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(P,{cartItems:t.items,subtotal:t.subtotal,userId:null==n?void 0:null===(e=n.user)||void 0===e?void 0:e.id,onCouponApply:j,onCouponRemove:y,appliedCoupons:t.coupons.appliedCoupons})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",t.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",t.subtotal.toFixed(2)]})]}),t.flashSaleDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,a.jsxs)("span",{children:["Flash Sale Discount (",t.flashSalePercentage,"% OFF)"]}),(0,a.jsxs)("span",{children:["-₹",t.flashSaleDiscount.toFixed(2)]})]}),t.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,a.jsx)("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",t.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{children:"Free"})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-2",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-lg",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",t.finalTotal.toFixed(2)]})]})})]})]}),(0,a.jsxs)(i.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[(0,a.jsx)("span",{children:"Proceed to Checkout"}),(0,a.jsx)(o.Z,{className:"w-5 h-5"})]})]})}),(0,a.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Shopping Cart"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-2",children:[(0,a.jsx)("div",{className:"space-y-6",children:t.items.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)("div",{className:"w-32 h-32 relative rounded-xl overflow-hidden flex-shrink-0",children:(0,a.jsx)(c.default,{src:e.product.image||"/placeholder-product.jpg",alt:e.product.name,fill:!0,className:"object-cover",sizes:"128px"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:e.product.name}),e.selectedVariants&&e.selectedVariants.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:e.selectedVariants.map((e,s)=>(0,a.jsxs)("span",{className:"text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full",children:[e.name,": ",e.value]},s))}),(0,a.jsx)("p",{className:"text-gray-600",children:e.product.shortDescription})]}),(0,a.jsx)("button",{onClick:()=>f(e.variantKey||e.product.id),className:"p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors",children:(0,a.jsx)(x.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>m(e.variantKey||e.product.id,e.quantity-1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(h,{className:"w-5 h-5"})}),(0,a.jsx)("span",{className:"font-medium text-gray-800 w-12 text-center text-lg",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>m(e.variantKey||e.product.id,e.quantity+1),className:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors",children:(0,a.jsx)(u.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["₹",e.product.price," each"]})]})]})]})]})},e.variantKey||e.product.id))}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(P,{cartItems:t.items,subtotal:t.subtotal,userId:null==n?void 0:null===(s=n.user)||void 0===s?void 0:s.id,onCouponApply:j,onCouponRemove:y,appliedCoupons:t.coupons.appliedCoupons})})]}),(0,a.jsx)("div",{className:"col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Order Summary"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsxs)("span",{children:["Subtotal (",t.itemCount," items)"]}),(0,a.jsxs)("span",{children:["₹",t.subtotal.toFixed(2)]})]}),t.flashSaleDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,a.jsxs)("span",{children:["Flash Sale Discount (",t.flashSalePercentage,"% OFF)"]}),(0,a.jsxs)("span",{children:["-₹",t.flashSaleDiscount.toFixed(2)]})]}),t.coupons.totalDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,a.jsx)("span",{children:"Coupon Discount"}),(0,a.jsxs)("span",{children:["-₹",t.coupons.totalDiscount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-gray-600",children:[(0,a.jsx)("span",{children:"Shipping"}),(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"Free"})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-xl",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:["₹",t.finalTotal.toFixed(2)]})]})})]}),(0,a.jsxs)(i.default,{href:"/checkout",className:"w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 text-lg",children:[(0,a.jsx)("span",{children:"Proceed to Checkout"}),(0,a.jsx)(o.Z,{className:"w-5 h-5"})]}),(0,a.jsx)(i.default,{href:"/shop",className:"w-full mt-3 border border-gray-300 text-gray-700 py-4 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center",children:"Continue Shopping"})]})})]})]})})]}):(0,a.jsxs)("div",{className:"jsx-a6d7afc75062cb7a flex justify-center items-center h-96",children:[(0,a.jsx)("div",{className:"jsx-a6d7afc75062cb7a loader"}),(0,a.jsx)(l(),{id:"a6d7afc75062cb7a",children:".loader.jsx-a6d7afc75062cb7a{border:8px solid#f3f3f3;border-top:8px solid#3498db;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;width:60px;height:60px;-webkit-animation:spin 2s linear infinite;-moz-animation:spin 2s linear infinite;-o-animation:spin 2s linear infinite;animation:spin 2s linear infinite}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]})}},1396:function(e,s,t){"use strict";t.d(s,{FlashSaleProvider:function(){return n},u:function(){return i}});var a=t(57437),r=t(2265);let l=(0,r.createContext)(void 0);function n(e){let{children:s}=e,[t,n]=(0,r.useState)(null),[i,c]=(0,r.useState)(!0),[d,o]=(0,r.useState)(!1),x=async()=>{try{let e=await fetch("/api/homepage-settings"),s=await e.json();if(s.success&&s.data.settings){let e=s.data.settings,t={showFlashSale:e.showFlashSale,flashSaleEndDate:e.flashSaleEndDate,flashSalePercentage:e.flashSalePercentage,flashSaleTitle:e.flashSaleTitle,flashSaleSubtitle:e.flashSaleSubtitle,flashSaleBackgroundColor:e.flashSaleBackgroundColor};n(t),localStorage.setItem("flashSaleSettings",JSON.stringify(t)),window.dispatchEvent(new Event("flashSaleSettingsUpdated"))}}catch(e){console.error("Error fetching flash sale settings:",e)}finally{c(!1)}};(0,r.useEffect)(()=>{x().then(()=>{o(!0)})},[]);let m=async()=>{c(!0),await x()};return(0,a.jsx)(l.Provider,{value:{flashSaleSettings:t,loading:i,isHydrated:d,refreshSettings:m},children:s})}function i(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useFlashSale must be used within a FlashSaleProvider");return e}},65302:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},78867:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},95252:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},99397:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},62720:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},18930:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});let a=(0,t(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}},function(e){e.O(0,[7349,7648,5644,3999,1682,8250,2971,2117,1744],function(){return e(e.s=62558)}),_N_E=e.O()}]);