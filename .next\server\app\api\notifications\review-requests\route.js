"use strict";(()=>{var e={};e.id=3193,e.ids=[3193],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},95439:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>b,requestAsyncStorage:()=>q,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>x});var o={};r.r(o),r.d(o,{GET:()=>v,POST:()=>g});var s=r(49303),n=r(88716),i=r(60670),a=r(87070),u=r(75571),d=r(95306),c=r(89585),p=r(54211),l=r(89456),f=r(81515);async function g(e){try{let e=await (0,u.getServerSession)(d.L);if(!e?.user?.id||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Admin access required"},{status:401});p.kg.info("Starting review request notifications for delivered orders"),await (0,l.Z)();let t=new Date;t.setDate(t.getDate()-7);let r=await f.Order.find({status:"DELIVERED",updatedAt:{$gte:t}}).populate([{path:"user",populate:{path:"preferences"}},{path:"items.product"}]).lean(),o=0;for(let e of r)try{let t=e.user.preferences;if(!t?.reviewNotifications||await f.P_.findOne({userId:e.userId,type:"REVIEW_REQUEST","data.orderId":String(e._id)}).select("_id").lean())continue;let r=(e.items||[]).map(e=>e.productId??e.product?._id??e.product?.id),s=await f.Cq.find({userId:e.userId,productId:{$in:r}}).select("_id").lean();if(0===s.length){let t=(e.items||[]).map(e=>e.product?.name??"");await c.kg.reviewRequest(e.userId,{orderId:String(e._id),orderNumber:e.orderNumber,productIds:r,productNames:t}),o++,p.kg.info("Review request notification sent",{orderId:String(e._id),orderNumber:e.orderNumber,userId:e.userId,productCount:e.items.length})}}catch(t){p.kg.error("Error processing order for review request",t,{orderId:String(e._id),orderNumber:e.orderNumber})}return p.kg.info("Review request notifications completed",{totalOrders:r.length,notificationsSent:o}),a.NextResponse.json({success:!0,message:"Review request notifications completed",stats:{totalOrders:r.length,notificationsSent:o}})}catch(e){return p.kg.error("Failed to send review request notifications",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function v(e){try{let e=await (0,u.getServerSession)(d.L);if(!e?.user?.id||"ADMIN"!==e.user.role)return a.NextResponse.json({error:"Admin access required"},{status:401});let t=new Date;t.setDate(t.getDate()-30),await (0,l.Z)();let[r,o,s]=await Promise.all([f.Order.countDocuments({status:"DELIVERED",updatedAt:{$gte:t}}),f.P_.countDocuments({type:"REVIEW_REQUEST",createdAt:{$gte:t}}),f.Cq.countDocuments({createdAt:{$gte:t}})]),n=o>0?Math.round(s/o*100):0;return a.NextResponse.json({success:!0,stats:{deliveredOrders:r,reviewRequestsSent:o,reviewsSubmitted:s,conversionRate:`${n}%`,period:"Last 30 days"}})}catch(e){return p.kg.error("Failed to get review request statistics",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/notifications/review-requests/route",pathname:"/api/notifications/review-requests",filename:"route",bundlePath:"app/api/notifications/review-requests/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\review-requests\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:q,staticGenerationAsyncStorage:x,serverHooks:w}=m,y="/api/notifications/review-requests/route";function b(){return(0,i.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:x})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=s?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(o,n,a):o[n]=e[n]}return o.default=e,r&&r.set(e,o),o}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,5972,8691,2830,5306,9585],()=>r(95439));module.exports=o})();