(()=>{var e={};e.id=6781,e.ids=[6781],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15835:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),s(63722),s(90596),s(37254),s(35866);var r=s(23191),a=s(88716),l=s(37922),i=s.n(l),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["admin",{children:["customers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,63722)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\customers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\customers\\page.tsx"],x="/admin/customers/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/customers/page",pathname:"/admin/customers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57376:(e,t,s)=>{Promise.resolve().then(s.bind(s,41052))},75671:(e,t,s)=>{Promise.resolve().then(s.bind(s,2802))},41052:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(10326),a=s(17577);let l=(0,s(76557).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var i=s(75290),n=s(24061),c=s(17069),d=s(34565),o=s(71821),x=s(88307),m=s(41137),h=s(31540),u=s(12714),p=s(5932),y=s(98091),g=s(9891);let f=()=>{let[e,t]=(0,a.useState)(""),[s,f]=(0,a.useState)(null),[j,b]=(0,a.useState)(!1),[v,N]=(0,a.useState)(!1),[w,k]=(0,a.useState)([]),[Z,M]=(0,a.useState)(!0),[C,P]=(0,a.useState)(null);(0,a.useEffect)(()=>{S()},[]);let S=async()=>{try{M(!0);let e=await fetch("/api/users"),t=await e.json();t.success?k(t.data):P("Failed to fetch customers")}catch(e){console.error("Error fetching customers:",e),P("Failed to fetch customers")}finally{M(!1)}},q=w.filter(t=>t.name?.toLowerCase().includes(e.toLowerCase())||t.email.toLowerCase().includes(e.toLowerCase())),U=e=>"ADMIN"===e?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800",_=e=>new Date(e).toLocaleDateString();return(0,r.jsxs)("div",{children:[r.jsx("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Customers"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your customer relationships"})]}),r.jsx("div",{className:"mt-4 sm:mt-0",children:(0,r.jsxs)("button",{onClick:()=>N(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[r.jsx(l,{className:"w-5 h-5 mr-2"}),"Add Customer"]})})]})}),Z&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[r.jsx(i.Z,{className:"w-8 h-8 animate-spin text-green-600"}),r.jsx("span",{className:"ml-2 text-gray-600",children:"Loading customers..."})]}),C&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[r.jsx("p",{className:"text-red-600",children:C}),r.jsx("button",{onClick:S,className:"mt-2 text-red-600 hover:text-red-700 underline",children:"Try again"})]}),!Z&&!C&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:w.length})]}),r.jsx("div",{className:"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center",children:r.jsx(n.Z,{className:"w-6 h-6 text-white"})})]})}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Customers"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:w.filter(e=>"CUSTOMER"===e.role).length})]}),r.jsx("div",{className:"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center",children:r.jsx(c.Z,{className:"w-6 h-6 text-white"})})]})}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:w.reduce((e,t)=>e+t._count.orders,0)})]}),r.jsx("div",{className:"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center",children:r.jsx(d.Z,{className:"w-6 h-6 text-white"})})]})}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),r.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:(0,g.T4)(0)})]}),r.jsx("div",{className:"w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center",children:r.jsx(o.Z,{className:"w-6 h-6 text-white"})})]})})]}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Search customers...",value:e,onChange:e=>t(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),r.jsx("button",{className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:r.jsx(m.Z,{className:"w-5 h-5 text-gray-600"})})]}),r.jsx("div",{className:"flex items-center space-x-3",children:r.jsx("button",{className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:r.jsx(h.Z,{className:"w-5 h-5 text-gray-600"})})})]})}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[r.jsx("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Orders"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Spent"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:q.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4",children:r.jsx("span",{className:"text-green-600 font-medium",children:e.name?e.name.split(" ").map(e=>e[0]).join(""):e.email[0].toUpperCase()})}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name||"No name"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined ",_(e.createdAt)]}),r.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:r.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${U(e.role)}`,children:e.role})})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm text-gray-900",children:e.email}),r.jsx("div",{className:"text-sm text-gray-500",children:e.phone||"No phone"})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm text-gray-900",children:e._count.orders}),r.jsx("div",{className:"text-sm text-gray-500",children:"Total orders"})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:[r.jsx("div",{className:"text-sm text-gray-900",children:"-"}),r.jsx("div",{className:"text-sm text-gray-500",children:"No data"})]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800",children:"Active"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{className:"p-1 text-gray-400 hover:text-green-600 transition-colors",children:r.jsx(u.Z,{className:"w-4 h-4"})}),r.jsx("button",{className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",children:r.jsx(p.Z,{className:"w-4 h-4"})}),r.jsx("button",{className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:r.jsx(y.Z,{className:"w-4 h-4"})})]})})]},e.id))})]})})}),(0,r.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:["Showing ",q.length," of ",w.length," customers"]})]})]})}},2802:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var r=s(10326),a=s(17577),l=s.n(a),i=s(90434),n=s(35047),c=s(77109),d=s(24319),o=s(48705),x=s(34565),m=s(57671),h=s(24061),u=s(40765),p=s(40617),y=s(35351),g=s(6507),f=s(5932),j=s(71709),b=s(95920),v=s(88378),N=s(94019),w=s(90748),k=s(53080),Z=s(71810);let M=({children:e})=>{let t=(0,n.usePathname)(),[s,a]=l().useState(!1),[M,C]=l().useState(!1),P=async()=>{if(!M)try{C(!0),a(!1),await (0,c.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},S=[{href:"/admin",label:"Dashboard",icon:d.Z},{href:"/admin/products",label:"Products",icon:o.Z},{href:"/admin/categories",label:"Categories",icon:x.Z},{href:"/admin/orders",label:"Orders",icon:m.Z},{href:"/admin/customers",label:"Customers",icon:h.Z},{href:"/admin/coupons",label:"Coupons",icon:u.Z},{href:"/admin/reviews",label:"Reviews",icon:p.Z},{href:"/admin/enquiry",label:"Enquiries",icon:y.Z},{href:"/admin/notifications",label:"Notifications",icon:g.Z},{href:"/admin/newsletter",label:"Newsletter",icon:f.Z},{href:"/admin/media",label:"Media",icon:j.Z},{href:"/admin/homepage",label:"Homepage",icon:b.Z},{href:"/admin/settings",label:"Settings",icon:v.Z}],q=e=>"/admin"===e?"/admin"===t:t.startsWith(e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:r.jsx("button",{onClick:()=>a(!s),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:s?r.jsx(N.Z,{className:"w-6 h-6 text-gray-600"}):r.jsx(w.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,r.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${s?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[r.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:r.jsx(k.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),r.jsx("nav",{className:"mt-6 px-3",children:S.map(e=>{let t=e.icon,s=q(e.href);return(0,r.jsxs)(i.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${s?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>a(!1),children:[r.jsx(t,{className:`w-5 h-5 mr-3 ${s?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,r.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[r.jsx(i.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,r.jsxs)("button",{onClick:P,disabled:M,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx(Z.Z,{className:"w-4 h-4 mr-2"}),M?"Logging out...":"Logout"]})]})]}),r.jsx("main",{className:"lg:ml-64",children:r.jsx("div",{className:"p-4 lg:p-8",children:e})}),s&&r.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>a(!1)})]})}},9891:(e,t,s)=>{"use strict";function r(e){return function(e,t=!0){if(isNaN(e))return"₹0";let s=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return`₹${s}`}(e,!0)}function a(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function l(e,t){return(e?a(e):a(t))||"product"}s.d(t,{GD:()=>a,T4:()=>r,w:()=>l})},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},71821:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},31540:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},41137:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},35351:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},75290:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},88307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},17069:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35047:(e,t,s)=>{"use strict";var r=s(77389);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},63722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\customers\page.tsx#default`)},90596:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,3757,434,9536],()=>s(15835));module.exports=r})();