(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5193],{33974:function(e,t,s){Promise.resolve().then(s.bind(s,80456))},80456:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return k}});var a=s(57437),r=s(2265),n=s(99376),i=s(80605),c=s(42449),l=s(65302),o=s(44794),d=s(40340),u=s(45131),x=s(88997),h=s(3085),m=s(86595),g=s(53417),y=s(82023),f=s(94766),p=s(15863),b=s(32660),N=s(39763);let j=(0,N.Z)("<PERSON><PERSON>he<PERSON>",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]);var E=s(22252);let w=(0,N.Z)("BellOff",[["path",{d:"M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5",key:"o7mx20"}],["path",{d:"M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7",key:"16f1lm"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var v=s(28250),R=s(19124),k=()=>{var e;let t=(0,n.useRouter)(),{data:s,status:N}=(0,i.useSession)(),{unreadCount:k,markAsRead:D,markAllAsRead:Z}=(0,R.z)(),[S,C]=(0,r.useState)([]),[_,O]=(0,r.useState)(!0),[M,I]=(0,r.useState)(null),[A,L]=(0,r.useState)(1),[P,T]=(0,r.useState)(1),[B,V]=(0,r.useState)(0);(0,r.useEffect)(()=>{"unauthenticated"===N&&t.push("/login")},[N,t]);let H=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;O(!0),I(null);try{let t=new URLSearchParams({page:e.toString(),limit:"20"}),s=await fetch("/api/notifications?".concat(t)),a=await s.json();a.success?(C(a.data.notifications),T(a.data.pagination.totalPages),V(a.data.pagination.totalCount),L(e)):I(a.message||"Failed to fetch notifications.")}catch(e){console.error("Error fetching notifications:",e),I("An unexpected error occurred.")}finally{O(!1)}};(0,r.useEffect)(()=>{var e;(null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.id)&&H(1)},[null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.id]);let W=e=>{switch(e){case"ORDER_PLACED":return(0,a.jsx)(c.Z,{className:"w-5 h-5"});case"ORDER_CONFIRMED":case"ORDER_DELIVERED":return(0,a.jsx)(l.Z,{className:"w-5 h-5"});case"ORDER_PROCESSING":return(0,a.jsx)(o.Z,{className:"w-5 h-5"});case"ORDER_SHIPPED":return(0,a.jsx)(d.Z,{className:"w-5 h-5"});case"ORDER_CANCELLED":return(0,a.jsx)(u.Z,{className:"w-5 h-5"});case"WISHLIST_ADDED":case"WISHLIST_REMOVED":return(0,a.jsx)(x.Z,{className:"w-5 h-5"});case"PRICE_DROP_ALERT":return(0,a.jsx)(h.Z,{className:"w-5 h-5"});case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return(0,a.jsx)(m.Z,{className:"w-5 h-5"});case"ADMIN_MESSAGE":case"BROADCAST":return(0,a.jsx)(g.Z,{className:"w-5 h-5"});case"PROMOTIONAL":return(0,a.jsx)(y.Z,{className:"w-5 h-5"});default:return(0,a.jsx)(f.Z,{className:"w-5 h-5"})}},z=e=>{switch(e){case"ORDER_PLACED":case"ORDER_CONFIRMED":case"ORDER_PROCESSING":case"PRICE_DROP_ALERT":case"REVIEW_REQUEST":case"REVIEW_SUBMITTED":return{iconBg:"bg-green-600",iconColor:"text-white",badge:"bg-green-100 text-green-700 border-green-200"};case"ORDER_SHIPPED":case"ORDER_DELIVERED":case"ADMIN_MESSAGE":case"BROADCAST":case"PROMOTIONAL":return{iconBg:"bg-green-700",iconColor:"text-white",badge:"bg-green-100 text-green-800 border-green-200"};case"ORDER_CANCELLED":return{iconBg:"bg-gray-600",iconColor:"text-white",badge:"bg-gray-100 text-gray-700 border-gray-200"};case"WISHLIST_ADDED":case"WISHLIST_REMOVED":return{iconBg:"bg-green-500",iconColor:"text-white",badge:"bg-green-50 text-green-700 border-green-200"};default:return{iconBg:"bg-gray-500",iconColor:"text-white",badge:"bg-gray-100 text-gray-700 border-gray-200"}}},q=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/1e3);return s<60?"Just now":s<3600?"".concat(Math.floor(s/60),"m ago"):s<86400?"".concat(Math.floor(s/3600),"h ago"):s<604800?"".concat(Math.floor(s/86400),"d ago"):t.toLocaleDateString("en-US",{month:"short",day:"numeric"})},U=async e=>{var s,a;e.isRead||await D(e.id),e.type.startsWith("ORDER_")?(null===(a=e.data)||void 0===a?void 0:a.orderId)&&t.push("/order-history"):"PRICE_DROP_ALERT"===e.type&&(null===(s=e.data)||void 0===s?void 0:s.productId)&&t.push("/product/".concat(e.data.productId))};return"loading"===N?(0,a.jsx)(v.default,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(p.Z,{className:"w-8 h-8 animate-spin text-green-600"})})}):(null==s?void 0:s.user)?(0,a.jsx)(v.default,{children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("button",{onClick:()=>t.back(),className:"inline-flex items-center space-x-2 text-gray-500 hover:text-gray-800 mb-4 group",children:[(0,a.jsx)(b.Z,{className:"w-4 h-4 group-hover:-translate-x-1 transition-transform"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Back"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),k>0&&(0,a.jsxs)("span",{className:"px-3 py-1 text-xs font-bold text-white bg-green-600 rounded-full",children:[k," New"]})]}),S.length>0&&(0,a.jsxs)("button",{onClick:Z,className:"flex items-center space-x-2 px-3 py-1.5 bg-white text-gray-600 hover:text-gray-800 hover:bg-gray-50 border border-gray-200 rounded-lg transition-all duration-200 text-sm font-medium",children:[(0,a.jsx)(j,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Mark all as read"})]})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:_?(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-12 text-center",children:[(0,a.jsx)(p.Z,{className:"w-12 h-12 animate-spin text-green-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Loading your notifications..."})]}):M?(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-12 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(E.Z,{className:"w-8 h-8 text-red-500"})}),(0,a.jsx)("p",{className:"text-gray-900 font-semibold text-lg mb-2",children:"Oops! Something went wrong"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:M}),(0,a.jsx)("button",{onClick:()=>H(A),className:"px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors font-medium",children:"Try again"})]}):0===S.length?(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-16 text-center",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(w,{className:"w-12 h-12 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"No notifications yet"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg mb-8",children:"We'll notify you when something important happens"}),(0,a.jsx)("button",{onClick:()=>t.push("/shop"),className:"px-8 py-3 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors font-medium",children:"Start Shopping"})]}):(0,a.jsx)(a.Fragment,{children:S.map(e=>{let t=z(e.type);return(0,a.jsx)("div",{onClick:()=>U(e),className:"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border ".concat(e.isRead?"border-gray-200":"border-green-500"),children:(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg ".concat(t.iconBg," flex items-center justify-center flex-shrink-0"),children:(0,a.jsx)("div",{className:t.iconColor,children:W(e.type)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-gray-800 group-hover:text-green-600 transition-colors",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isRead&&(0,a.jsx)("div",{className:"w-2.5 h-2.5 bg-green-500 rounded-full flex-shrink-0"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:q(e.createdAt)})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-snug",children:e.message})]})]})})},e.id)})})}),P>1&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Page ",A," of ",P]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>H(A-1),disabled:1===A,className:"px-3 py-1.5 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200 text-sm font-medium",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>H(A+1),disabled:A===P,className:"px-3 py-1.5 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200 text-sm font-medium",children:"Next"})]})]})})]})})}):null}},22252:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},32660:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},65302:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},88997:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},15863:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53417:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},44794:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},82023:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},86595:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},3085:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},45131:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=33974)}),_N_E=e.O()}]);