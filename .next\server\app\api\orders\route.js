"use strict";(()=>{var e={};e.id=9146,e.ids=[9146],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},48404:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>A,requestAsyncStorage:()=>_,routeModule:()=>N,serverHooks:()=>P,staticGenerationAsyncStorage:()=>x});var n={};r.r(n),r.d(n,{GET:()=>O,POST:()=>w});var o=r(49303),a=r(88716),s=r(60670),i=r(87070),u=r(65630),d=r(75571),c=r(95306),l=r(89456),p=r(81515),m=r(84875),f=r(54211),R=r(8149),y=r(89585),g=r(84770),I=r.n(g);let h=u.Ry({page:u.Z_().optional().default("1"),limit:u.Z_().optional().default("10"),status:u.Km(["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"]).optional(),paymentStatus:u.Km(["PENDING","PAID","FAILED","REFUNDED"]).optional()}),E=u.Ry({cartItems:u.IX(u.Ry({productId:u.Z_(),quantity:u.Rx().min(1),price:u.Rx().min(0)})),shippingAddress:u.Ry({firstName:u.Z_().min(1),lastName:u.Z_().min(1),address1:u.Z_().min(1),address2:u.Z_().optional(),city:u.Z_().min(1),state:u.Z_().min(1),postalCode:u.Z_().min(1),country:u.Z_().min(1),phone:u.Z_().min(1)}),totalAmount:u.Rx().min(1),paymentMethod:u.Km(["ONLINE","COD"]),appliedCoupons:u.IX(u.Ry({coupon:u.Ry({id:u.Z_(),code:u.Z_(),name:u.Z_()}),discountAmount:u.Rx().min(0)})).optional().default([]),flashSaleDiscount:u.Rx().optional().default(0)}),O=(0,m.lm)(async e=>{f.kg.apiRequest("GET","/api/orders"),await (0,R.er)(e,R.Xw,30);let t=await (0,d.getServerSession)(c.L);if(!t?.user?.id)throw new m._7("Authentication required");let{searchParams:r}=new URL(e.url),n=Object.fromEntries(r.entries()),o=h.parse(n),a=parseInt(o.page),s=parseInt(o.limit),u=(a-1)*s,y="ADMIN"===t.user.role;try{await (0,l.Z)();let e={};y||(e.userId=t.user.id),o.status&&(e.status=o.status),o.paymentStatus&&(e.paymentStatus=o.paymentStatus);let[r,n]=await Promise.all([p.Order.find(e).sort({createdAt:-1}).skip(u).limit(s).lean(),p.Order.countDocuments(e)]),d=await Promise.all(r.map(async e=>{let[t,r,n]=await Promise.all([Promise.all((await p.Dd.find({orderId:String(e._id)}).lean()).map(async e=>{let t=await p.xs.findById(e.productId).select("_id name slug price").lean();return{...e,product:t?{id:String(t._id),name:t.name,slug:t.slug,price:t.price}:null}})),e.addressId?p.kL.findById(e.addressId).lean():null,y?p.n5.findById(e.userId).select("_id name email phone").lean():null]);return{...e,id:String(e._id),items:t,address:r,user:n?{id:String(n._id),name:n.name,email:n.email,phone:n.phone}:void 0}})),c=Math.ceil(n/s);return f.kg.info("Orders retrieved successfully",{userId:t.user.id,isAdmin:y,count:r.length,totalCount:n,page:a,filters:{status:o.status,paymentStatus:o.paymentStatus}}),i.NextResponse.json({success:!0,orders:d,pagination:{page:a,limit:s,totalCount:n,totalPages:c,hasNext:a<c,hasPrev:a>1}})}catch(e){throw f.kg.error("Failed to retrieve orders",e),e}}),w=(0,m.lm)(async e=>{f.kg.apiRequest("POST","/api/orders"),await (0,R.er)(e,R.Xw,10);let t=await (0,d.getServerSession)(c.L);if(!t?.user?.id)throw new m._7("Authentication required");await (0,l.Z)();let r=await e.json(),{cartItems:n,shippingAddress:o,totalAmount:a,paymentMethod:s,appliedCoupons:u,flashSaleDiscount:g}=E.parse(r);f.kg.info("Creating COD order",{userId:t.user.id,totalAmount:a,itemCount:n.length,paymentMethod:s});let h=0,O=[];for(let e of n){let t=await p.xs.findById(e.productId);if(!t)throw new m.p8(`Product ${e.productId} not found`);let r=t.price||0;if(Math.abs(r-e.price)>.01)throw new m.p8(`Price mismatch for product ${e.productId}`);let n=r*e.quantity;h+=n,O.push({productId:e.productId,quantity:e.quantity,price:r,total:n})}let w=u.reduce((e,t)=>e+t.discountAmount,0),N=h,_=N-w-g;if("COD"===s&&(_+=50),Math.abs(_-a)>.01)throw new m.p8(`Total amount mismatch. Expected: ${_}, Received: ${a}`);let x=I().randomBytes(4).toString("hex").toUpperCase();try{let e=await p.kL.findOne({userId:t.user.id,firstName:o.firstName,lastName:o.lastName,address1:o.address1,city:o.city,state:o.state,postalCode:o.postalCode,country:o.country}).lean();e||(e=await p.kL.create({userId:t.user.id,firstName:o.firstName,lastName:o.lastName,address1:o.address1,address2:o.address2,city:o.city,state:o.state,postalCode:o.postalCode,country:o.country,phone:o.phone}));let r=await p.Order.create({orderNumber:x,userId:t.user.id,status:"CONFIRMED",paymentStatus:"PENDING",paymentMethod:s,subtotal:N,couponDiscount:w,flashSaleDiscount:g,total:_,currency:"INR",notes:`Order created via ${s}${"COD"===s?" with ₹50 COD charges":""}${u.length>0?` | Coupons: ${u.map(e=>e.coupon.code).join(", ")}`:""}`,addressId:e?String(e._id):null});await p.Dd.insertMany(O.map(e=>({orderId:String(r._id),productId:e.productId,quantity:e.quantity,price:e.price,total:e.total}))),u.length,f.kg.info("COD order created successfully",{orderId:String(r._id),orderNumber:r.orderNumber,amount:_,userId:t.user.id,paymentMethod:s});try{await y.aZ.orderPlaced(t.user.id,{orderId:String(r._id),orderNumber:r.orderNumber,total:_,currency:"INR",itemCount:n.length}),f.kg.info("Order placed notification sent",{orderId:String(r._id),userId:t.user.id})}catch(e){f.kg.error("Failed to send order placed notification",e)}return i.NextResponse.json({success:!0,order:{id:String(r._id),orderNumber:r.orderNumber,total:_,currency:"INR"}})}catch(e){throw f.kg.error("Failed to create COD order",e),new m.gz("Failed to create order",500)}}),N=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:_,staticGenerationAsyncStorage:x,serverHooks:P}=N,v="/api/orders/route";function A(){return(0,s.patchFetch)({serverHooks:P,staticGenerationAsyncStorage:x})}},84875:(e,t,r)=>{r.d(t,{AY:()=>c,M_:()=>u,_7:()=>i,dR:()=>d,gz:()=>a,lm:()=>m,p8:()=>s});var n=r(87070),o=r(29489);class a extends Error{constructor(e,t=500,r="INTERNAL_ERROR",n){super(e),this.statusCode=t,this.code=r,this.details=n,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,a)}}class s extends a{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class i extends a{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class u extends a{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class d extends a{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class c extends a{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class l extends a{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function p(e){let t={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return t[e]||t.INTERNAL_ERROR}function m(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){let t=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,error:e}),e instanceof a){let r={success:!1,error:{code:e.code,message:p(e.code),requestId:t,...!1}};return n.NextResponse.json(r,{status:e.statusCode})}if(e instanceof o.j){let r=new s("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),o={success:!1,error:{code:r.code,message:"Validation failed",requestId:t,...!1}};return n.NextResponse.json(o,{status:r.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let r=function(e){if(11e3===e.code||"MongoServerError"===e.name){let t=Object.keys(e.keyPattern||{})[0]||"field";return new c(`${t} already exists`)}return"ValidationError"===e.name?new s("Validation failed"):"CastError"===e.name?new s("Invalid data format"):"DocumentNotFoundError"===e.name?new d:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new l("Database connection failed"):new l("Database operation failed",{name:e.name,message:e.message})}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return n.NextResponse.json(o,{status:r.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let r=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new c(`${t} already exists`);case"P2003":let r=e.meta?.constraint;if(r?.includes("userId"))return new i("Invalid user session");return new s("Invalid reference to related record");case"P2025":case"P2001":return new d;case"P2014":return new s("Missing required relationship");case"P2000":return new s("Input value is too long");case"P2004":return new s("Data constraint violation");default:return new l("Database operation failed",{code:e.code,message:e.message})}}(e),o={success:!1,error:{code:r.code,message:p(r.code),requestId:t,...!1}};return n.NextResponse.json(o,{status:r.statusCode})}}let r={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:t,...!1}};return n.NextResponse.json(r,{status:500})}(e)}}}},8149:(e,t,r)=>{r.d(t,{Ri:()=>a,Xw:()=>s,er:()=>u,jO:()=>i});var n=r(919);function o(e){let t=new n.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,r)=>new Promise((n,o)=>{let a=t.get(r)||[0];0===a[0]&&t.set(r,a),a[0]+=1,a[0]>=e?o(Error("Rate limit exceeded")):n()})}}let a=o({interval:9e5,uniqueTokenPerInterval:500}),s=o({interval:6e4,uniqueTokenPerInterval:500}),i=o({interval:36e5,uniqueTokenPerInterval:500});async function u(e,t,r){let n=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);try{await t.check(r,n)}catch(e){throw Error("Too many requests. Please try again later.")}}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,5972,8691,2830,9489,5630,138,5306,9585],()=>r(48404));module.exports=n})();