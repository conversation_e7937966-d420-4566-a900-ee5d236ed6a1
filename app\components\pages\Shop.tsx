"use client";

import React, { useState, useEffect, useRef } from "react";
import { useSearchParams } from "next/navigation";
import {
  Filter,
  Loader2,
  ArrowUpDown,
  Search,
  X,
  Grid,
  List,
  SlidersHorizontal,
} from "lucide-react";
import ProductCard from "../ProductCard";
import InfiniteScroll from "../InfiniteScroll";
import { productBelongsToCategory } from "../../lib/productUtils";
import { ShopSkeleton } from "../loaders/SkeletonLoaders";
import FlashSaleBanner from "../FlashSaleBanner";

// Database product interface
interface DBProduct {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  comparePrice?: number;
  isFeatured: boolean;
  isActive: boolean;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  productCategories?: Array<{
    category: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
  images: Array<{
    id: string;
    url: string;
    alt: string;
    position?: number;
  }>;
  variants?: Array<{
    id: string;
    name: string;
    value: string;
    price?: number;
  }>;
}

// Frontend product interface (compatible with existing ProductCard)
interface Product {
  id: string;
  slug: string;
  name: string;
  description: string;
  shortDescription: string;
  price: number;
  image?: string;
  images?: Array<{
    id: string;
    url: string;
    alt: string;
    position: number;
  }>;
  category: string;
  featured: boolean;
  ingredients: string[];
  benefits: string[];
  rating: number;
  reviews: number;
}

interface Category {
  id: string;
  name: string;
}

// Extended Product interface to include raw data
interface ExtendedProduct extends Product {
  _raw: DBProduct;
}

// Convert database product to frontend format
const convertDBProductToFrontend = (dbProduct: DBProduct): Product => {
  // Get primary category for the product
  const primaryCategory = dbProduct.category?.slug || "skincare";

  // Calculate price based on variations
  let displayPrice = dbProduct.price || 0;
  if (dbProduct.variants && dbProduct.variants.length > 0) {
    const prices = [
      dbProduct.price || 0,
      ...dbProduct.variants.map((v) => v.price ?? 0),
    ].filter((p) => p > 0);
    if (prices.length > 0) {
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      displayPrice = minPrice === maxPrice ? minPrice : minPrice; // Will show range in ProductCard
    }
  }

  // Store variations for ProductCard to use
  const productWithVariants = {
    id: dbProduct.id,
    slug: dbProduct.slug,
    name: dbProduct.name,
    description: dbProduct.description,
    shortDescription: dbProduct.shortDescription,
    price: dbProduct.price || 0,
    image: dbProduct.images[0]?.url || "/placeholde.jpg",
    images: dbProduct.images.map((img) => ({
      id: img.id,
      url: img.url,
      alt: img.alt,
      position: img.position || 0,
    })),
    category: primaryCategory,
    featured: dbProduct.isFeatured,
    ingredients: [],
    benefits: [],
    rating: 0,
    reviews: 0,
    variants: dbProduct.variants, // Pass variants to ProductCard
  };

  return {
    ...productWithVariants,
    _raw: dbProduct, // Include raw database product data
  } as ExtendedProduct;
};

// Store original database products for better filtering
const originalDBProducts: DBProduct[] = [];

const Shop: React.FC = () => {
  const searchParams = useSearchParams();
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [sortBy, setSortBy] = useState("random");
  const [showFilters, setShowFilters] = useState(false);
  const [allProducts, setAllProducts] = useState<ExtendedProduct[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ExtendedProduct[]>(
    [],
  );
  const [displayedProducts, setDisplayedProducts] = useState<ExtendedProduct[]>(
    [],
  );
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [showSearch, setShowSearch] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const PRODUCTS_PER_PAGE = 12;

  // Handle URL parameters
  useEffect(() => {
    const categoryParam = searchParams.get("category");
    if (categoryParam) {
      setSelectedCategory(categoryParam);
    }
  }, [searchParams]);

  // Fetch all data on initial load and when sort changes
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Build query parameters
        const params = new URLSearchParams({
          limit: "1000",
          sort: sortBy,
        });

        // Fetch all products and categories in parallel
        const [productsResponse, categoriesResponse] = await Promise.all([
          fetch(`/api/products?${params.toString()}`),
          fetch("/api/categories"),
        ]);

        const [productsData, categoriesData] = await Promise.all([
          productsResponse.json(),
          categoriesResponse.json(),
        ]);

        if (productsData.success && categoriesData.success) {
          // Store the raw DB products for filtering
          const activeProducts = productsData.data.filter(
            (product: DBProduct) => product.isActive,
          );

          // Convert products for display but keep original data for filtering
          const displayProducts = activeProducts.map((product: DBProduct) => ({
            ...convertDBProductToFrontend(product),
            _raw: product,
          }));

          setAllProducts(displayProducts);
          setFilteredProducts(displayProducts);

          // Process categories (only on initial load)
          if (categories.length === 0) {
            const processedCategories: Category[] = [
              { id: "all", name: "All Products" },
              ...categoriesData.data.map((cat: any) => ({
                id: cat.slug,
                name: cat.name,
              })),
            ];
            setCategories(processedCategories);
          }
        } else {
          setError("Failed to fetch data");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load products");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [sortBy]);

  // Filter products when category, search, or price range changes
  useEffect(() => {
    let filtered = allProducts;

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter((product) => {
        const rawProduct = product._raw;

        // Check primary category
        if (rawProduct.category?.slug === selectedCategory) {
          return true;
        }

        // Check additional categories through productCategories
        return (
          rawProduct.productCategories?.some(
            (pc) => pc.category.slug === selectedCategory,
          ) ?? false
        );
      });
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          product.shortDescription.toLowerCase().includes(query),
      );
    }

    // Filter by price range
    filtered = filtered.filter((product) => {
      const price = product.price || 0;
      return price >= priceRange[0] && price <= priceRange[1];
    });

    setFilteredProducts(filtered);
    setCurrentPage(1);
    setHasMore(filtered.length > PRODUCTS_PER_PAGE);
  }, [selectedCategory, allProducts, searchQuery, priceRange]);

  // Update displayed products when filtered products or current page changes
  useEffect(() => {
    const startIndex = 0;
    const endIndex = currentPage * PRODUCTS_PER_PAGE;
    const newDisplayedProducts = filteredProducts.slice(startIndex, endIndex);

    setDisplayedProducts(newDisplayedProducts);
    setHasMore(endIndex < filteredProducts.length);
  }, [filteredProducts, currentPage]);

  // Load more products function
  const loadMoreProducts = () => {
    if (!loadingMore && hasMore) {
      setLoadingMore(true);

      // Simulate loading delay for better UX
      setTimeout(() => {
        setCurrentPage((prev) => prev + 1);
        setLoadingMore(false);
      }, 500);
    }
  };

  // Focus search input when search is shown
  useEffect(() => {
    if (showSearch && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [showSearch]);

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  if (loading) {
    return <ShopSkeleton />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Sticky Header */}
        <div className="sticky top-0 z-40 bg-white shadow-sm">
          {/* Main Header */}
          <div className="px-4 py-4">
            <div className="flex items-center justify-between mb-3">
              <h1 className="text-2xl font-bold text-gray-900">Shop</h1>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowSearch(!showSearch)}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <Search className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <SlidersHorizontal className="w-4 h-4" />
                  <span className="text-sm font-medium">Filters</span>
                </button>
              </div>
            </div>

            {/* Search Bar */}
            <div
              className={`transition-all duration-300 ${showSearch ? "max-h-20 opacity-100 mb-3" : "max-h-0 opacity-0 overflow-hidden"}`}
            >
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                {filteredProducts.length} products found
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode("grid")}
                  className={`p-1.5 rounded ${viewMode === "grid" ? "bg-green-100 text-green-600" : "text-gray-400"}`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode("list")}
                  className={`p-1.5 rounded ${viewMode === "list" ? "bg-green-100 text-green-600" : "text-gray-400"}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Filters Drawer */}
          <div
            className={`border-t bg-white transition-all duration-300 ${
              showFilters
                ? "max-h-screen opacity-100"
                : "max-h-0 opacity-0 overflow-hidden"
            }`}
          >
            <div className="px-4 py-4 space-y-4">
              {/* Sort Dropdown */}
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Sort by
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-sm"
                >
                  <option value="random">Random (Default)</option>
                  <option value="name_asc">Name (A-Z)</option>
                  <option value="name_desc">Name (Z-A)</option>
                  <option value="price_asc">Price (Low to High)</option>
                  <option value="price_desc">Price (High to Low)</option>
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                </select>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Price Range
                </label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={priceRange[0]}
                      onChange={(e) =>
                        setPriceRange([
                          parseInt(e.target.value) || 0,
                          priceRange[1],
                        ])
                      }
                      className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                    />
                    <span className="text-gray-400">-</span>
                    <input
                      type="number"
                      placeholder="Max"
                      value={priceRange[1]}
                      onChange={(e) =>
                        setPriceRange([
                          priceRange[0],
                          parseInt(e.target.value) || 10000,
                        ])
                      }
                      className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                    />
                  </div>
                  <div className="text-xs text-gray-500">
                    ₹{priceRange[0]} - ₹{priceRange[1]}
                  </div>
                </div>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Categories
                </label>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => handleCategorySelect(category.id)}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                        selectedCategory === category.id
                          ? "bg-green-600 text-white shadow-md scale-105"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200 active:scale-95"
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Clear Filters */}
              <button
                onClick={() => {
                  setSelectedCategory("all");
                  setSearchQuery("");
                  setPriceRange([0, 10000]);
                  setSortBy("random");
                }}
                className="w-full py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          </div>
        </div>

        {/* Flash Sale Banner */}
        <div className="px-4 pt-4">
          <FlashSaleBanner />
        </div>

        {/* Products Grid */}
        <div className="px-4 py-6">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Search className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No products found
              </h3>
              <p className="text-gray-500 mb-4">
                Try adjusting your search or filters
              </p>
              <button
                onClick={() => {
                  setSelectedCategory("all");
                  setSearchQuery("");
                  setPriceRange([0, 10000]);
                }}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          ) : (
            <InfiniteScroll
              hasMore={hasMore}
              loading={loadingMore}
              onLoadMore={loadMoreProducts}
            >
              <div
                className={`${
                  viewMode === "grid" ? "grid grid-cols-2 gap-4" : "space-y-4"
                }`}
              >
                {displayedProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    viewMode={viewMode}
                  />
                ))}
              </div>
            </InfiniteScroll>
          )}
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-6 py-8">
            {/* Search, Price Range, and Sort Controls */}
            <div className="flex items-center gap-4 mb-8">
              {/* Search Bar - Made smaller */}
              <div className="relative w-80">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-5 h-5" />
                  </button>
                )}
              </div>

              {/* Price Range Filter - Now in the middle */}
              <div className="flex items-center gap-3 flex-1 justify-center">
                <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                  Price Range:
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={priceRange[0]}
                    onChange={(e) =>
                      setPriceRange([
                        parseInt(e.target.value) || 0,
                        priceRange[1],
                      ])
                    }
                    className="w-24 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                  />
                  <span className="text-gray-400">-</span>
                  <input
                    type="number"
                    placeholder="Max"
                    value={priceRange[1]}
                    onChange={(e) =>
                      setPriceRange([
                        priceRange[0],
                        parseInt(e.target.value) || 10000,
                      ])
                    }
                    className="w-24 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                  />
                </div>
              </div>

              {/* Sort and View Controls */}
              <div className="flex items-center gap-4">
                {/* Sort Dropdown */}
                <div className="flex items-center gap-3">
                  <ArrowUpDown className="w-5 h-5 text-gray-600" />
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white"
                  >
                    <option value="random">Random (Default)</option>
                    <option value="name_asc">Name (A-Z)</option>
                    <option value="name_desc">Name (Z-A)</option>
                    <option value="price_asc">Price (Low to High)</option>
                    <option value="price_desc">Price (High to Low)</option>
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                  </select>
                </div>

                {/* View Mode Toggle */}
                <div className="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded ${viewMode === "grid" ? "bg-white shadow-sm text-green-600" : "text-gray-500"}`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 rounded ${viewMode === "list" ? "bg-white shadow-sm text-green-600" : "text-gray-500"}`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex justify-center mb-8">
              <div className="flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => handleCategorySelect(category.id)}
                    className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                      selectedCategory === category.id
                        ? "bg-green-600 text-white shadow-md scale-105"
                        : "text-gray-700 hover:bg-gray-100 active:scale-95"
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Products Section */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Flash Sale Banner */}
          <FlashSaleBanner />

          <div className="flex items-center justify-between mb-6">
            <p className="text-gray-600">
              {filteredProducts.length} products found
            </p>
            <button
              onClick={() => {
                setSelectedCategory("all");
                setSearchQuery("");
                setPriceRange([0, 10000]);
                setSortBy("random");
              }}
              className="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              Clear all filters
            </button>
          </div>

          {filteredProducts.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                <Search className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-3">
                No products found
              </h3>
              <p className="text-gray-500 mb-6">
                Try adjusting your search or filters
              </p>
              <button
                onClick={() => {
                  setSelectedCategory("all");
                  setSearchQuery("");
                  setPriceRange([0, 10000]);
                }}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          ) : (
            <InfiniteScroll
              hasMore={hasMore}
              loading={loadingMore}
              onLoadMore={loadMoreProducts}
            >
              <div
                className={`${
                  viewMode === "grid"
                    ? "grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                    : "grid grid-cols-1 lg:grid-cols-2 gap-6"
                }`}
              >
                {displayedProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    viewMode={viewMode}
                  />
                ))}
              </div>
            </InfiniteScroll>
          )}
        </div>
      </div>
    </div>
  );
};

export default Shop;
