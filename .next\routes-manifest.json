{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/admin/orders/[id]", "regex": "^/admin/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/orders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/categories/[id]", "regex": "^/api/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/categories/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/coupons/[id]", "regex": "^/api/coupons/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/coupons/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/enquiries/[id]", "regex": "^/api/enquiries/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/enquiries/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/notifications/[id]/read", "regex": "^/api/notifications/([^/]+?)/read(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/notifications/(?<nxtPid>[^/]+?)/read(?:/)?$"}, {"page": "/api/orders/[orderId]", "regex": "^/api/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/api/orders/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/api/products/[id]", "regex": "^/api/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/products/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/products/[id]/faqs", "regex": "^/api/products/([^/]+?)/faqs(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/products/(?<nxtPid>[^/]+?)/faqs(?:/)?$"}, {"page": "/api/products/[id]/faqs/[faqId]", "regex": "^/api/products/([^/]+?)/faqs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPfaqId": "nxtPfaqId"}, "namedRegex": "^/api/products/(?<nxtPid>[^/]+?)/faqs/(?<nxtPfaqId>[^/]+?)(?:/)?$"}, {"page": "/api/products/[id]/reviews", "regex": "^/api/products/([^/]+?)/reviews(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/products/(?<nxtPid>[^/]+?)/reviews(?:/)?$"}, {"page": "/api/products/[id]/variations", "regex": "^/api/products/([^/]+?)/variations(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/products/(?<nxtPid>[^/]+?)/variations(?:/)?$"}, {"page": "/api/products/[id]/variations/[variationId]", "regex": "^/api/products/([^/]+?)/variations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPvariationId": "nxtPvariationId"}, "namedRegex": "^/api/products/(?<nxtPid>[^/]+?)/variations/(?<nxtPvariationId>[^/]+?)(?:/)?$"}, {"page": "/api/testimonials/[id]", "regex": "^/api/testimonials/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/testimonials/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/users/[id]", "regex": "^/api/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/users/[id]/preferences", "regex": "^/api/users/([^/]+?)/preferences(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)/preferences(?:/)?$"}, {"page": "/api/users/[id]/stats", "regex": "^/api/users/([^/]+?)/stats(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)/stats(?:/)?$"}, {"page": "/product/[id]", "regex": "^/product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/product/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/addresses", "regex": "^/addresses(?:/)?$", "routeKeys": {}, "namedRegex": "^/addresses(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/categories", "regex": "^/admin/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/categories(?:/)?$"}, {"page": "/admin/coupons", "regex": "^/admin/coupons(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/coupons(?:/)?$"}, {"page": "/admin/customers", "regex": "^/admin/customers(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/customers(?:/)?$"}, {"page": "/admin/enquiry", "regex": "^/admin/enquiry(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/enquiry(?:/)?$"}, {"page": "/admin/homepage", "regex": "^/admin/homepage(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/homepage(?:/)?$"}, {"page": "/admin/media", "regex": "^/admin/media(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/media(?:/)?$"}, {"page": "/admin/newsletter", "regex": "^/admin/newsletter(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/newsletter(?:/)?$"}, {"page": "/admin/notifications", "regex": "^/admin/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/notifications(?:/)?$"}, {"page": "/admin/notifications/broadcast", "regex": "^/admin/notifications/broadcast(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/notifications/broadcast(?:/)?$"}, {"page": "/admin/notifications/history", "regex": "^/admin/notifications/history(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/notifications/history(?:/)?$"}, {"page": "/admin/notifications/send", "regex": "^/admin/notifications/send(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/notifications/send(?:/)?$"}, {"page": "/admin/notifications/templates", "regex": "^/admin/notifications/templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/notifications/templates(?:/)?$"}, {"page": "/admin/orders", "regex": "^/admin/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/orders(?:/)?$"}, {"page": "/admin/products", "regex": "^/admin/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products(?:/)?$"}, {"page": "/admin/reviews", "regex": "^/admin/reviews(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/reviews(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/cart", "regex": "^/cart(?:/)?$", "routeKeys": {}, "namedRegex": "^/cart(?:/)?$"}, {"page": "/categories", "regex": "^/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/categories(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/edit-profile", "regex": "^/edit\\-profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/edit\\-profile(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/order-confirmation", "regex": "^/order\\-confirmation(?:/)?$", "routeKeys": {}, "namedRegex": "^/order\\-confirmation(?:/)?$"}, {"page": "/order-history", "regex": "^/order\\-history(?:/)?$", "routeKeys": {}, "namedRegex": "^/order\\-history(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/shipping", "regex": "^/shipping(?:/)?$", "routeKeys": {}, "namedRegex": "^/shipping(?:/)?$"}, {"page": "/shop", "regex": "^/shop(?:/)?$", "routeKeys": {}, "namedRegex": "^/shop(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/sw.js", "regex": "^/sw\\.js(?:/)?$", "routeKeys": {}, "namedRegex": "^/sw\\.js(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/wishlist", "regex": "^/wishlist(?:/)?$", "routeKeys": {}, "namedRegex": "^/wishlist(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}