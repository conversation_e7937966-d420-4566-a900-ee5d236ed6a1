2:I[4707,[],""]
3:I[36423,[],""]
4:I[30578,["7349","static/chunks/7349-dd7980b8616a48d0.js","1682","static/chunks/1682-fef24c18b13d286d.js","3185","static/chunks/app/layout-c173c5a1c3436326.js"],"default"]
5:I[19124,["7349","static/chunks/7349-dd7980b8616a48d0.js","1682","static/chunks/1682-fef24c18b13d286d.js","3185","static/chunks/app/layout-c173c5a1c3436326.js"],"NotificationProvider"]
6:I[1396,["7349","static/chunks/7349-dd7980b8616a48d0.js","1682","static/chunks/1682-fef24c18b13d286d.js","3185","static/chunks/app/layout-c173c5a1c3436326.js"],"FlashSaleProvider"]
7:I[53827,["7349","static/chunks/7349-dd7980b8616a48d0.js","1682","static/chunks/1682-fef24c18b13d286d.js","3185","static/chunks/app/layout-c173c5a1c3436326.js"],"CartProvider"]
8:I[92840,["7349","static/chunks/7349-dd7980b8616a48d0.js","1682","static/chunks/1682-fef24c18b13d286d.js","3185","static/chunks/app/layout-c173c5a1c3436326.js"],"ToastProvider"]
0:["J2PvewOxlGqDv7jVkI3PI",[[["",{"children":["faq",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["faq",{"children":["__PAGE__",{},[["$L1",["$","div",null,{"className":"min-h-screen bg-white","children":["$","div",null,{"className":"max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8","children":[["$","h1",null,{"className":"text-3xl font-extrabold text-gray-900","children":"Frequently Asked Questions"}],["$","div",null,{"className":"mt-6 prose prose-indigo text-gray-500","children":[["$","div",null,{"className":"py-6","children":[["$","h2",null,{"className":"text-xl font-semibold","children":"What is your return policy?"}],["$","p",null,{"className":"mt-2","children":"We have a 30-day return policy, which means you have 30 days after receiving your item to request a return. To be eligible for a return, your item must be in the same condition that you received it, unworn or unused, with tags, and in its original packaging. You’ll also need the receipt or proof of purchase."}]]}],["$","div",null,{"className":"py-6","children":[["$","h2",null,{"className":"text-xl font-semibold","children":"How long does shipping take?"}],["$","p",null,{"className":"mt-2","children":"Orders are typically processed within 1-2 business days. Shipping times may vary based on your location, but most orders arrive within 5-7 business days."}]]}],["$","div",null,{"className":"py-6","children":[["$","h2",null,{"className":"text-xl font-semibold","children":"Do you ship internationally?"}],["$","p",null,{"className":"mt-2","children":"Currently, we only ship within the country. We are working on expanding our shipping options to include international destinations in the near future."}]]}]]}]]}]}],null],null],null]},[null,["$","$L2",null,{"parallelRouterKey":"children","segmentPath":["children","faq","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/20641def5e93222c.css","precedence":"next","crossOrigin":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L4",null,{"children":["$","$L5",null,{"children":["$","$L6",null,{"children":["$","$L7",null,{"children":["$","$L8",null,{"children":["$","$L2",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[]}]}]}]}]}]}]}]}]],null],null],["$L9",null]]]]
9:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"name":"theme-color","content":"#16a34a"}],["$","meta","2",{"charSet":"utf-8"}],["$","title","3",{"children":"Herbalicious - Natural Skincare"}],["$","meta","4",{"name":"description","content":"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients."}]]
1:null
