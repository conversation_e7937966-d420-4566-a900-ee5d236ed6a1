"use strict";(()=>{var e={};e.id=3568,e.ids=[3568],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48080:(e,t,r)=>{let i;r.r(t),r.d(t,{originalPathname:()=>q,patchFetch:()=>I,requestAsyncStorage:()=>g,routeModule:()=>S,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{POST:()=>p});var s=r(49303),n=r(88716),o=r(60670),d=r(87070),u=r(89456),c=r(81515);let p=(i=async e=>{await (0,u.Z)();let{action:t,productIds:r}=await e.json()||{};if(!t||!Array.isArray(r)||0===r.length)return d.NextResponse.json({success:!1,error:"Invalid request. Action and productIds array are required."},{status:400});switch(t){case"delete":return await l(r);case"activate":return await m(r,!0);case"deactivate":return await m(r,!1);case"feature":return await y(r,!0);case"unfeature":return await y(r,!1);default:return d.NextResponse.json({success:!1,error:"Invalid action. Supported actions: delete, activate, deactivate, feature, unfeature"},{status:400})}},async(...e)=>{try{return await i(...e)}catch(e){return console.error(e),d.NextResponse.json({success:!1,error:"Internal Server Error"},{status:500})}});async function l(e){let t={success:0,softDeleted:0,failed:0,errors:[]};for(let r of e)try{if(await c.Dd.countDocuments({productId:r})>0){let e=await c.xs.findById(r).select("name").lean();if(e){let i=new Date().toISOString().split("T")[0];await c.xs.updateOne({_id:r},{$set:{isActive:!1,name:`[DELETED] ${i} - ${e?.name??""}`}}),t.softDeleted++}}else await c.qN.deleteMany({productId:r}),await c.Th.deleteMany({productId:r}),await c.Pp.deleteMany({productId:r}),await c.Cq.deleteMany({productId:r}),await c.hQ.deleteMany({productId:r}),await c.xs.deleteOne({_id:r}),t.success++}catch(e){console.error(`Error deleting product ${r}:`,e),t.failed++,t.errors.push(`Failed to delete product ${r}: ${e instanceof Error?e.message:"Unknown error"}`)}let r="Bulk delete completed. ";return t.success>0&&(r+=`${t.success} products deleted permanently. `),t.softDeleted>0&&(r+=`${t.softDeleted} products deactivated (had order history). `),t.failed>0&&(r+=`${t.failed} products failed to delete.`),d.NextResponse.json({success:!0,message:r.trim(),data:t})}async function m(e,t){try{let r=await c.xs.updateMany({_id:{$in:e}},{$set:{isActive:t}});return d.NextResponse.json({success:!0,message:`${r.modifiedCount??0} products ${t?"activated":"deactivated"} successfully`,data:{updated:r.modifiedCount??0}})}catch(e){return console.error("Error in bulk activate/deactivate:",e),d.NextResponse.json({success:!1,error:`Failed to ${t?"activate":"deactivate"} products`},{status:500})}}async function y(e,t){try{let r=await c.xs.updateMany({_id:{$in:e}},{$set:{isFeatured:t}});return d.NextResponse.json({success:!0,message:`${r.modifiedCount??0} products ${t?"featured":"unfeatured"} successfully`,data:{updated:r.modifiedCount??0}})}catch(e){return console.error("Error in bulk feature/unfeature:",e),d.NextResponse.json({success:!1,error:`Failed to ${t?"feature":"unfeature"} products`},{status:500})}}let S=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/products/bulk/route",pathname:"/api/products/bulk",filename:"route",bundlePath:"app/api/products/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\bulk\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:h}=S,q="/api/products/bulk/route";function I(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>C,Dd:()=>x,Order:()=>T,P_:()=>R,Pp:()=>U,Th:()=>P,Vv:()=>B,WD:()=>v,gc:()=>O,hQ:()=>k,kL:()=>j,mA:()=>M,n5:()=>N,nW:()=>E,p1:()=>L,qN:()=>D,wV:()=>A,xs:()=>w});var i=r(11185),a=r.n(i);let s=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),n=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),o=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),c=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),S=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),g=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),f=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),h=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),q=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),I=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=a().models.User||a().model("User",s),w=a().models.Product||a().model("Product",n),v=a().models.Category||a().model("Category",o),T=a().models.Order||a().model("Order",d),E=a().models.HomepageSetting||a().model("HomepageSetting",u),O=a().models.Testimonial||a().model("Testimonial",c),D=a().models.ProductImage||a().model("ProductImage",p),P=a().models.ProductVariant||a().model("ProductVariant",l),C=a().models.Review||a().model("Review",m),j=a().models.Address||a().model("Address",y),x=a().models.OrderItem||a().model("OrderItem",S),R=a().models.Notification||a().model("Notification",g),A=a().models.Coupon||a().model("Coupon",f);a().models.Wishlist||a().model("Wishlist",h);let M=a().models.Newsletter||a().model("Newsletter",q),U=a().models.ProductCategory||a().model("ProductCategory",I),k=a().models.WishlistItem||a().model("WishlistItem",b),$=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),B=a().models.NotificationTemplate||a().model("NotificationTemplate",$),F=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),L=a().models.Enquiry||a().model("Enquiry",F)},89456:(e,t,r)=>{r.d(t,{Z:()=>o});var i=r(11185),a=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=a().connect(s,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(48080));module.exports=i})();