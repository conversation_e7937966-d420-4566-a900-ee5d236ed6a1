import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { Types } from "mongoose";
import {
  Product,
  Category,
  ProductImage,
  ProductVariant,
  Review,
} from "@/app/lib/models";

// GET /api/products - List all products
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const category = searchParams.get("category");
    const search = searchParams.get("search");
    const sort = searchParams.get("sort") || "random"; // Default to random ordering

    // Check if this is an admin request
    const isAdminRequest = request.headers.get("x-admin-request") === "true";

    const skip = (page - 1) * limit;

    const where: any = {};

    // Only filter by isActive for non-admin requests
    if (!isAdminRequest) {
      where.isActive = true;
    }

    // Build AND conditions array
    const andConditions: any[] = [];

    if (category) {
      // Validate category param: support both ObjectId and slug
      const isObjectId = Types.ObjectId.isValid(category);
      let categoryIdForFilter: Types.ObjectId | null = null;

      if (isObjectId) {
        categoryIdForFilter = new Types.ObjectId(category);
      } else {
        // Otherwise treat it as slug and resolve to _id
        const categoryDoc = await Category.findOne(
          { slug: category },
          { _id: 1 }
        ).lean<{ _id: Types.ObjectId }>().exec();
        if (categoryDoc?._id) {
          categoryIdForFilter = categoryDoc._id;
        } else {
          // No matching category by slug; force-empty result quickly
          return NextResponse.json({
            success: true,
            data: [],
            pagination: {
              page,
              limit,
              total: 0,
              pages: 0,
            },
          });
        }
      }

      andConditions.push({
        categoryId: categoryIdForFilter,
      });
    }

    if (search) {
      andConditions.push({
        $or: [
          { name: { $regex: search, $options: "i" } },
          { description: { $regex: search, $options: "i" } },
        ],
      });
    }

    if (andConditions.length > 0) {
      where.$and = andConditions;
    }

    // Define ordering based on sort parameter
    let sortQuery: any;
    switch (sort) {
      case "name_asc":
        sortQuery = { name: 1 };
        break;
      case "name_desc":
        sortQuery = { name: -1 };
        break;
      case "price_asc":
        sortQuery = { price: 1 };
        break;
      case "price_desc":
        sortQuery = { price: -1 };
        break;
      case "newest":
        sortQuery = { createdAt: -1 };
        break;
      case "oldest":
        sortQuery = { createdAt: 1 };
        break;
      case "random":
      default:
        // For random ordering, we'll use aggregate with $sample
        sortQuery = undefined;
        break;
    }

    let products;
    const total = await Product.countDocuments(where);

    if (sort === "random") {
      // For random ordering, use aggregation pipeline
      const pipeline = [
        { $match: where },
        { $sample: { size: Math.min(limit, total) } },
      ];

      products = await Product.aggregate(pipeline);

      // Apply skip if needed for pagination (though random + pagination is tricky)
      if (skip > 0) {
        products = products.slice(skip, skip + limit);
      }
    } else {
      // For non-random sorting, use standard Mongoose query
      products = await Product.find(where)
        .sort(sortQuery || {})
        .skip(skip)
        .limit(limit)
        .lean();
    }

    // Populate related data for each product
    const productsWithRelations = await Promise.all(
      products.map(async (product) => {
        // With ObjectId migration, resolve category directly by _id
        const categoryDoc = (product as any).categoryId
          ? await Category.findById((product as any).categoryId).lean()
          : null;

        const productId = new Types.ObjectId(product._id);

        const [images, variants, reviewCount] = await Promise.all([
          ProductImage.find({ productId }).sort({
            position: 1,
          }),
          ProductVariant.find({ productId }),
          Review.countDocuments({ productId }),
        ]);

        return {
          ...product,
          category: categoryDoc,
          productCategories: [], // Would need a join table for many-to-many
          images,
          variants,
          _count: {
            reviews: reviewCount,
          },
        };
      }),
    );

    return NextResponse.json({
      success: true,
      data: productsWithRelations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch products" },
      { status: 500 },
    );
  }
}

// POST /api/products - Create a new product
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      name,
      slug,
      description,
      shortDescription,
      price,
      comparePrice,
      categoryId,
      categoryIds = [],
      images,
      isFeatured,
      variations = [],
    } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { success: false, error: "Product name is required" },
        { status: 400 },
      );
    }

    // Allow zero or null base price - variations can provide pricing
    let basePrice = price !== undefined ? parseFloat(price.toString()) : null;

    // Validate that we have at least one pricing source
    const hasValidBasePrice = basePrice !== null && basePrice >= 0;
    const hasValidVariations = variations && variations.length > 0;
    const warnings: string[] = [];

    if (!hasValidBasePrice && !hasValidVariations) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Product must have either a base price (can be 0) or variations with pricing",
        },
        { status: 400 },
      );
    }

    // Add warnings for edge cases
    if (basePrice === 0 && (!variations || variations.length === 0)) {
      warnings.push(
        "Product has zero base price and no variations. Consider adding variations for pricing.",
      );
    }

    if (basePrice === 0 && variations && variations.length > 0) {
      const hasZeroPricedVariations = variations.some(
        (v: any) => !v.price || v.price === 0,
      );
      if (hasZeroPricedVariations) {
        warnings.push(
          "Some variations have zero price. Ensure all variations have valid pricing.",
        );
      }
    }

    // Default to 0 if no base price provided
    if (basePrice === null) {
      basePrice = 0;
    }

    // Create the product
    const product = await Product.create({
      name,
      slug,
      description,
      shortDescription,
      price: basePrice,
      comparePrice: comparePrice
        ? parseFloat(comparePrice.toString())
        : undefined,
      categoryId, // Keep for backward compatibility
      isFeatured: Boolean(isFeatured),
    });

    // Create related records
    if (images && images.length > 0) {
      const imageData = images.map((img: any, index: number) => ({
        productId: product._id,
        url: img.url,
        alt: img.alt || name,
        position: index,
      }));
      await ProductImage.insertMany(imageData);
    }

    if (variations.length > 0) {
      const variationData = variations.map((variation: any) => ({
        productId: product._id,
        name: variation.name,
        value: variation.value,
        price: variation.price || undefined,
        pricingMode: variation.pricingMode || "INCREMENT",
      }));
      await ProductVariant.insertMany(variationData);
    }

    // Get the product with relations for response
    const [category, createdImages, createdVariants] = await Promise.all([
      product.categoryId ? Category.findById(product.categoryId) : null,
      ProductImage.find({ productId: product._id }).sort({
        position: 1,
      }),
      ProductVariant.find({ productId: product._id }),
    ]);

    const productWithRelations = {
      ...product.toObject(),
      category,
      productCategories: [], // Would need a join table for many-to-many
      images: createdImages,
      variants: createdVariants,
    };

    return NextResponse.json({
      success: true,
      data: productWithRelations,
      message: "Product created successfully",
      warnings: warnings.length > 0 ? warnings : undefined,
    });
  } catch (error) {
    console.error("Error creating product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create product" },
      { status: 500 },
    );
  }
}
