"use strict";(()=>{var e={};e.id=2636,e.ids=[2636],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},31701:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>y,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{POST:()=>c});var o=r(49303),n=r(88716),i=r(60670),a=r(87070),u=r(75571),p=r(95306),l=r(54211);async function c(e){try{let e=await (0,u.getServerSession)(p.L);if(!e||"ADMIN"!==e.user.role)return a.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});return l.kg.info("Admin cleanup API temporarily disabled during migration"),a.NextResponse.json({success:!0,message:"Database cleanup temporarily disabled during migration",deletedCounts:{notifications:0,reviews:0,orders:0,orderItems:0,orderAddresses:0,couponUsages:0}})}catch(e){return l.kg.error("Error during database cleanup:",e),a.NextResponse.json({success:!1,error:"Failed to cleanup database"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/cleanup/route",pathname:"/api/admin/cleanup",filename:"route",bundlePath:"app/api/admin/cleanup/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\cleanup\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:h}=d,m="/api/admin/cleanup/route";function y(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}},54211:(e,t,r)=>{var s;r.d(t,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:o,context:n,error:i,userId:a,requestId:u}=e,p=s[r],l=`[${t}] ${p}: ${o}`;return a&&(l+=` | User: ${a}`),u&&(l+=` | Request: ${u}`),n&&Object.keys(n).length>0&&(l+=` | Context: ${JSON.stringify(n)}`),i&&(l+=` | Error: ${i.message}`,this.isDevelopment&&i.stack&&(l+=`
Stack: ${i.stack}`)),l}log(e,t,r,s){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:s},n=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(o))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,s){this.info(`API ${e} ${t}`,{...s,userId:r,type:"api_request"})}apiResponse(e,t,r,s,o){this.info(`API ${e} ${t} - ${r}`,{...o,statusCode:r,duration:s,type:"api_response"})}apiError(e,t,r,s,o){this.error(`API ${e} ${t} failed`,r,{...o,userId:s,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,s){this.warn("Authentication failed",{...s,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,s){this.debug(`DB ${e} on ${t}`,{...s,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,s){this.error(`DB ${e} on ${t} failed`,r,{...s,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,s){this.info("Email sent",{...s,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,s){this.error("Email failed to send",r,{...s,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let n=new o},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=o?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,5306],()=>r(31701));module.exports=s})();