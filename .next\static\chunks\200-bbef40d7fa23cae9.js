"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[200],{13905:function(s,e,a){a.d(e,{Gw:function(){return r},QW:function(){return d},sW:function(){return i}});var l=a(57437);a(2265);let c=s=>{let{className:e=""}=s;return(0,l.jsx)("div",{className:"animate-pulse bg-gray-200 rounded ".concat(e)})},d=()=>(0,l.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,l.jsxs)("div",{className:"lg:hidden bg-white",children:[(0,l.jsxs)("div",{className:"sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b",children:[(0,l.jsx)(c,{className:"w-8 h-8 rounded-full"}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(c,{className:"w-8 h-8 rounded-full"}),(0,l.jsx)(c,{className:"w-8 h-8 rounded-full"})]})]}),(0,l.jsx)("div",{className:"p-4",children:(0,l.jsx)(c,{className:"w-full h-80 rounded-xl"})}),(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsx)(c,{className:"h-8 w-3/4 mb-2"}),(0,l.jsx)(c,{className:"h-4 w-full mb-3"}),(0,l.jsx)(c,{className:"h-6 w-1/3 mb-4"}),(0,l.jsx)("div",{className:"flex border-b border-gray-200 mb-4",children:Array.from({length:4}).map((s,e)=>(0,l.jsx)(c,{className:"h-8 w-20 mr-4"},e))}),(0,l.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,l.jsx)(c,{className:"h-4 w-full"}),(0,l.jsx)(c,{className:"h-4 w-5/6"}),(0,l.jsx)(c,{className:"h-4 w-4/5"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(c,{className:"w-10 h-10 rounded-lg"}),(0,l.jsx)(c,{className:"w-10 h-6"}),(0,l.jsx)(c,{className:"w-10 h-10 rounded-lg"})]}),(0,l.jsx)(c,{className:"flex-1 h-12 rounded-lg"})]})]})]}),(0,l.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,l.jsxs)("div",{className:"py-8",children:[(0,l.jsx)(c,{className:"h-6 w-32 mb-8"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-12",children:[(0,l.jsx)(c,{className:"w-full h-96 rounded-xl"}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c,{className:"h-10 w-3/4 mb-4"}),(0,l.jsx)(c,{className:"h-6 w-full mb-6"}),(0,l.jsx)(c,{className:"h-8 w-1/3 mb-6"}),(0,l.jsxs)("div",{className:"flex items-center space-x-3 mb-8",children:[Array.from({length:5}).map((s,e)=>(0,l.jsx)(c,{className:"w-5 h-5"},e)),(0,l.jsx)(c,{className:"h-4 w-24"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(c,{className:"w-12 h-12 rounded-lg"}),(0,l.jsx)(c,{className:"w-12 h-6"}),(0,l.jsx)(c,{className:"w-12 h-12 rounded-lg"})]}),(0,l.jsx)(c,{className:"flex-1 h-14 rounded-lg"})]}),(0,l.jsx)("div",{className:"flex border-b border-gray-200 mb-6",children:Array.from({length:4}).map((s,e)=>(0,l.jsx)(c,{className:"h-10 w-24 mr-6"},e))}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(c,{className:"h-4 w-full"}),(0,l.jsx)(c,{className:"h-4 w-5/6"}),(0,l.jsx)(c,{className:"h-4 w-4/5"}),(0,l.jsx)(c,{className:"h-4 w-3/4"})]})]})]})]})})]}),r=()=>(0,l.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,l.jsxs)("div",{className:"lg:hidden",children:[(0,l.jsx)("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)(c,{className:"w-8 h-8 rounded-full"}),(0,l.jsx)(c,{className:"h-6 w-24"}),(0,l.jsx)("div",{className:"ml-auto",children:(0,l.jsx)(c,{className:"h-4 w-16"})})]})}),(0,l.jsxs)("div",{className:"px-4 py-6",children:[(0,l.jsx)(c,{className:"w-full h-12 rounded-2xl mb-6"}),(0,l.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((s,e)=>(0,l.jsx)("div",{className:"bg-white rounded-2xl p-4 shadow-sm border border-gray-100",children:(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)(c,{className:"w-20 h-20 rounded-xl flex-shrink-0"}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,l.jsx)(c,{className:"h-5 w-3/4"}),(0,l.jsx)(c,{className:"w-4 h-4 rounded-full"})]}),(0,l.jsx)(c,{className:"h-4 w-full mb-2"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,l.jsx)(c,{className:"w-4 h-4"}),(0,l.jsx)(c,{className:"h-4 w-8"}),(0,l.jsx)(c,{className:"h-4 w-16"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(c,{className:"h-5 w-16"}),(0,l.jsx)(c,{className:"h-8 w-24 rounded-full"})]})]})]})},e))})]})]}),(0,l.jsx)("div",{className:"hidden lg:block lg:col-span-12",children:(0,l.jsxs)("div",{className:"py-8",children:[(0,l.jsx)(c,{className:"h-6 w-16 mb-8"}),(0,l.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,l.jsx)(c,{className:"h-10 w-48"}),(0,l.jsx)(c,{className:"h-6 w-24"})]}),(0,l.jsx)(c,{className:"w-48 h-12 rounded-2xl mb-8"}),(0,l.jsx)("div",{className:"grid grid-cols-3 gap-6",children:Array.from({length:6}).map((s,e)=>(0,l.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,l.jsx)(c,{className:"w-full h-48 rounded-xl mb-4"}),(0,l.jsx)(c,{className:"h-6 w-3/4 mb-2"}),(0,l.jsx)(c,{className:"h-4 w-full mb-3"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(c,{className:"w-4 h-4"}),(0,l.jsx)(c,{className:"h-4 w-8"}),(0,l.jsx)(c,{className:"h-4 w-16"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(c,{className:"h-6 w-20"}),(0,l.jsx)(c,{className:"h-8 w-24 rounded-xl"})]})]},e))})]})})]}),i=()=>(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsxs)("div",{className:"lg:hidden",children:[(0,l.jsx)("div",{className:"sticky top-0 z-40 bg-white shadow-sm",children:(0,l.jsxs)("div",{className:"px-4 py-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,l.jsx)(c,{className:"h-8 w-16"}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(c,{className:"w-10 h-10 rounded-full"}),(0,l.jsx)(c,{className:"w-20 h-10 rounded-lg"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,l.jsx)(c,{className:"h-4 w-32"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(c,{className:"w-8 h-8 rounded"}),(0,l.jsx)(c,{className:"w-8 h-8 rounded"})]})]})]})}),(0,l.jsx)("div",{className:"px-4 py-6",children:(0,l.jsx)("div",{className:"grid grid-cols-2 gap-4",children:Array.from({length:6}).map((s,e)=>(0,l.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden",children:[(0,l.jsx)(c,{className:"w-full h-48"}),(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsx)(c,{className:"h-4 w-3/4 mb-2"}),(0,l.jsx)(c,{className:"h-3 w-full mb-3"}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)(c,{className:"h-5 w-16 mb-1"}),(0,l.jsx)(c,{className:"h-3 w-20"})]}),(0,l.jsx)(c,{className:"w-10 h-10 rounded-full"})]})]})]},e))})})]}),(0,l.jsxs)("div",{className:"hidden lg:block",children:[(0,l.jsx)("div",{className:"bg-white shadow-sm",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,l.jsx)(c,{className:"h-12 w-80 rounded-xl"}),(0,l.jsxs)("div",{className:"flex items-center gap-3 flex-1 justify-center",children:[(0,l.jsx)(c,{className:"h-4 w-20"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(c,{className:"w-24 h-10 rounded-lg"}),(0,l.jsx)(c,{className:"w-4 h-4"}),(0,l.jsx)(c,{className:"w-24 h-10 rounded-lg"})]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(c,{className:"h-12 w-40 rounded-xl"}),(0,l.jsx)(c,{className:"h-10 w-20 rounded-lg"})]})]}),(0,l.jsx)("div",{className:"flex justify-center mb-8",children:(0,l.jsx)("div",{className:"flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100",children:Array.from({length:6}).map((s,e)=>(0,l.jsx)(c,{className:"h-12 w-24 rounded-xl"},e))})})]})}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsx)(c,{className:"h-4 w-32"}),(0,l.jsx)(c,{className:"h-4 w-24"})]}),(0,l.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:12}).map((s,e)=>(0,l.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden",children:[(0,l.jsx)(c,{className:"w-full h-56"}),(0,l.jsxs)("div",{className:"p-5",children:[(0,l.jsx)(c,{className:"h-5 w-3/4 mb-2"}),(0,l.jsx)(c,{className:"h-4 w-full mb-4"}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)(c,{className:"h-5 w-20 mb-1"}),(0,l.jsx)(c,{className:"h-3 w-16"})]}),(0,l.jsx)(c,{className:"w-12 h-12 rounded-full"})]})]})]},e))})]})]})]})},92840:function(s,e,a){a.d(e,{ToastProvider:function(){return o},V:function(){return w}});var l=a(57437),c=a(2265);let d=()=>{let[s,e]=(0,c.useState)([]);return{toasts:s,showToast:(0,c.useCallback)(function(s){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",l=Date.now();e(e=>[...e,{id:l,message:s,type:a}])},[]),hideToast:(0,c.useCallback)(s=>{e(e=>e.filter(e=>e.id!==s))},[])}};var r=a(65302),i=a(45131),m=a(33245),x=a(32489);let n={success:(0,l.jsx)(r.Z,{className:"w-5 h-5 text-white"}),error:(0,l.jsx)(i.Z,{className:"w-5 h-5 text-white"}),info:(0,l.jsx)(m.Z,{className:"w-5 h-5 text-white"})},h={success:"bg-green-500",error:"bg-red-500",info:"bg-blue-500"};var t=s=>{let{message:e,type:a,onClose:d}=s;return(0,c.useEffect)(()=>{let s=setTimeout(()=>{d()},5e3);return()=>{clearTimeout(s)}},[d]),(0,l.jsxs)("div",{className:"fixed bottom-5 right-5 flex items-center p-4 rounded-lg shadow-lg text-white ".concat(h[a]," z-50 transition-transform transform animate-slide-in"),children:[(0,l.jsx)("div",{className:"mr-3",children:n[a]}),(0,l.jsx)("div",{className:"flex-1",children:e}),(0,l.jsx)("button",{onClick:d,className:"ml-4 p-1 rounded-full hover:bg-white/20",children:(0,l.jsx)(x.Z,{className:"w-4 h-4"})})]})},j=s=>{let{toasts:e,hideToast:a}=s;return(0,l.jsx)("div",{className:"fixed bottom-5 right-5 z-50",children:e.map(s=>(0,l.jsx)(t,{message:s.message,type:s.type,onClose:()=>a(s.id)},s.id))})};let N=(0,c.createContext)(void 0),o=s=>{let{children:e}=s,{toasts:a,showToast:c,hideToast:r}=d();return(0,l.jsxs)(N.Provider,{value:{showToast:c},children:[e,(0,l.jsx)(j,{toasts:a,hideToast:r})]})},w=()=>{let s=(0,c.useContext)(N);if(void 0===s)throw Error("useToastContext must be used within a ToastProvider");return s}}}]);