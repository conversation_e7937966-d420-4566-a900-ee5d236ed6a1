(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1327],{675:function(e,s,r){Promise.resolve().then(r.bind(r,28250)),Promise.resolve().then(r.bind(r,46214))},46214:function(e,s,r){"use strict";r.d(s,{default:function(){return c}});var a=r(57437),n=r(2265),t=r(89345),l=r(13041),i=r(44743);function c(){let[e,s]=(0,n.useState)({name:"",email:"",subject:"",message:""}),[r,c]=(0,n.useState)(!1),[d,o]=(0,n.useState)("idle"),m=e=>{let{name:r,value:a}=e.target;s(e=>({...e,[r]:a}))},h=async r=>{r.preventDefault(),c(!0),o("idle");try{let r=await fetch("/api/enquiries",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await r.json();if(!r.ok)throw Error(a.error||"Failed to submit enquiry");o("success"),s({name:"",email:"",subject:"",message:""})}catch(e){console.error("Error submitting form:",e),o("error")}finally{c(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Contact Us"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Have questions or need assistance? We're here to help. Reach out to us through any of the channels below."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Get in Touch"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(t.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"}),(0,a.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(l.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Phone"}),(0,a.jsx)("p",{className:"text-gray-600",children:"+91 99878 10707"}),(0,a.jsx)("p",{className:"text-gray-600",children:"WhatsApp Available"})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 bg-green-50 rounded-2xl p-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Need Quick Answers?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Check out our FAQ section for answers to common questions."}),(0,a.jsx)("a",{href:"/faq",className:"text-green-600 font-medium hover:text-green-700",children:"Visit FAQ →"})]})]}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Send us a Message"}),(0,a.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"John Doe"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,a.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:m,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select a subject"}),(0,a.jsx)("option",{value:"general",children:"General Inquiry"}),(0,a.jsx)("option",{value:"order",children:"Order Support"}),(0,a.jsx)("option",{value:"product",children:"Product Information"}),(0,a.jsx)("option",{value:"technical",children:"Technical Support"}),(0,a.jsx)("option",{value:"partnership",children:"Partnership Opportunities"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),(0,a.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:m,required:!0,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none",placeholder:"Tell us how we can help you..."})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:r,className:"w-full md:w-auto px-8 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,a.jsx)("span",{children:"Sending..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.Z,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Send Message"})]})})}),"success"===d&&(0,a.jsx)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-green-800",children:"Thank you for your message! We'll get back to you within 24 hours."})}),"error"===d&&(0,a.jsx)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-800",children:"Something went wrong. Please try again later or contact us directly."})})]})]})})]}),(0,a.jsxs)("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:(0,a.jsx)(t.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Email Support"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Get a response within 24 hours"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:(0,a.jsx)(l.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Phone Support"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Available on WhatsApp"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 text-center",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4",children:(0,a.jsx)(i.Z,{className:"h-6 w-6 text-green-600"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Quick Response"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"We aim to respond within 24 hours"})]})]})]})})}},13041:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});let a=(0,r(39763).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},44743:function(e,s,r){"use strict";r.d(s,{Z:function(){return a}});let a=(0,r(39763).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=675)}),_N_E=e.O()}]);