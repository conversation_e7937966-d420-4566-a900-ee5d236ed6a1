"use strict";(()=>{var e={};e.id=78,e.ids=[78],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},74066:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>q,requestAsyncStorage:()=>x,routeModule:()=>g,serverHooks:()=>j,staticGenerationAsyncStorage:()=>O});var n={};r.r(n),r.d(n,{POST:()=>m});var s=r(49303),i=r(88716),o=r(60670),a=r(87070),u=r(75571),p=r(95306),d=r(89585),c=r(54211),l=r(65630),f=r(29489);let y=l.Ry({userIds:l.IX(l.Z_()).min(1,"At least one user must be selected"),title:l.Z_().min(1,"Title is required"),content:l.Z_().min(1,"Content is required"),type:l.Km(["ADMIN_MESSAGE","BROADCAST","PROMOTIONAL","SYSTEM"]).default("ADMIN_MESSAGE"),priority:l.Km(["LOW","NORMAL","HIGH","URGENT"]).default("NORMAL"),sendEmail:l.O7().default(!0),sendInApp:l.O7().default(!0)});async function m(e){try{let t=await (0,u.getServerSession)(p.L);if(!t?.user?.id||"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Admin access required"},{status:401});let r=await e.json(),n=y.parse(r);c.kg.info("Admin sending notifications",{adminId:t.user.id,userCount:n.userIds.length,type:n.type,priority:n.priority});let s=[],i=0,o=0;for(let e of n.userIds)try{await d.$T.adminMessage(e,{title:n.title,content:n.content,type:n.type,sendEmail:n.sendEmail,sendInApp:n.sendInApp}),c.kg.info(`Notification sent with priority: ${n.priority}`,{userId:e,priority:n.priority}),s.push({userId:e,success:!0}),i++}catch(r){c.kg.error("Failed to send notification to user",r,{userId:e,adminId:t.user.id}),s.push({userId:e,success:!1,error:r instanceof Error?r.message:"Unknown error"}),o++}return c.kg.info("Notification sending completed",{adminId:t.user.id,totalUsers:n.userIds.length,successCount:i,errorCount:o}),a.NextResponse.json({success:!0,message:`Notifications sent successfully to ${i} user(s)`,stats:{total:n.userIds.length,success:i,errors:o},results:s})}catch(e){if(e instanceof f.j)return a.NextResponse.json({error:"Validation error",details:e.issues},{status:400});return c.kg.error("Failed to send admin notifications",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/notifications/send/route",pathname:"/api/admin/notifications/send",filename:"route",bundlePath:"app/api/admin/notifications/send/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\send\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:x,staticGenerationAsyncStorage:O,serverHooks:j}=g,h="/api/admin/notifications/send/route";function q(){return(0,o.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:O})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=s?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(45609));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,5972,8691,2830,9489,5630,5306,9585],()=>r(74066));module.exports=n})();