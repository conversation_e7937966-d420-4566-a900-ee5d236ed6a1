(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9699],{49315:function(e,t,s){Promise.resolve().then(s.bind(s,25743))},25743:function(e,t,s){"use strict";s.r(t);var a=s(57437),r=s(2265),i=s(80605),n=s(99376),c=s(76450),l=s(95805),d=s(42208),o=s(87769),x=s(92735);t.default=()=>{let{data:e,status:t}=(0,i.useSession)(),s=(0,n.useRouter)(),[m,u]=(0,r.useState)(!0),[h,g]=(0,r.useState)([]),[p,b]=(0,r.useState)({page:1,limit:50,total:0,pages:0}),[f,y]=(0,r.useState)({total:0,active:0,inactive:0}),[j,v]=(0,r.useState)({active:"all",source:"",search:""}),[w,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if("loading"!==t){if(!e||"ADMIN"!==e.user.role){s.push("/admin/login");return}k()}},[e,t,s,p.page,j]);let k=async()=>{try{u(!0);let e=new URLSearchParams({page:p.page.toString(),limit:p.limit.toString()});"all"!==j.active&&e.append("active",j.active),j.source&&e.append("source",j.source);let t=await fetch("/api/newsletter?".concat(e)),s=await t.json();s.success&&(g(s.data.subscribers),b(s.data.pagination),y(s.data.stats))}catch(e){console.error("Error fetching subscribers:",e)}finally{u(!1)}},S=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"csv";try{N(!0);let t=new URLSearchParams({format:e});"all"!==j.active&&t.append("active",j.active),j.source&&t.append("source",j.source);let s=await fetch("/api/newsletter/export?".concat(t));if("csv"===e){let e=await s.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="newsletter-subscribers-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}else{let e=await s.json(),t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=window.URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download="newsletter-subscribers-".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}}catch(e){console.error("Error exporting subscribers:",e)}finally{N(!1)}},C=e=>{b(t=>({...t,page:e}))};return"loading"===t||m?(0,a.jsx)(c.fq,{}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Newsletter Subscribers"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Manage your newsletter subscribers and export data"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.Z,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Subscribers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:f.total})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.Z,{className:"w-8 h-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Subscribers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:f.active})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.Z,{className:"w-8 h-8 text-red-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unsubscribed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:f.inactive})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:j.active,onChange:e=>v(t=>({...t,active:e.target.value})),className:"rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Subscribers"}),(0,a.jsx)("option",{value:"true",children:"Active Only"}),(0,a.jsx)("option",{value:"false",children:"Unsubscribed Only"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Source"}),(0,a.jsxs)("select",{value:j.source,onChange:e=>v(t=>({...t,source:e.target.value})),className:"rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Sources"}),(0,a.jsx)("option",{value:"homepage",children:"Homepage"}),(0,a.jsx)("option",{value:"checkout",children:"Checkout"}),(0,a.jsx)("option",{value:"product",children:"Product Page"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>S("csv"),disabled:w,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4 mr-2"}),w?"Exporting...":"Export CSV"]}),(0,a.jsxs)("button",{onClick:()=>S("json"),disabled:w,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4 mr-2"}),w?"Exporting...":"Export JSON"]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subscriber"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Source"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subscribed"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Unsubscribed"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.email}),e.name&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.name})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Unsubscribed"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.source||"Unknown"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.subscribedAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.unsubscribedAt?new Date(e.unsubscribedAt).toLocaleDateString():"-"})]},e.id))})]})}),p.pages>1&&(0,a.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>C(p.page-1),disabled:p.page<=1,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>C(p.page+1),disabled:p.page>=p.pages,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(p.page-1)*p.limit+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(p.page*p.limit,p.total)})," ","of ",(0,a.jsx)("span",{className:"font-medium",children:p.total})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[(0,a.jsx)("button",{onClick:()=>C(p.page-1),disabled:p.page<=1,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"Previous"}),Array.from({length:Math.min(5,p.pages)},(e,t)=>{let s=t+1;return(0,a.jsx)("button",{onClick:()=>C(s),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(s===p.page?"z-10 bg-indigo-50 border-indigo-500 text-indigo-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:s},s)}),(0,a.jsx)("button",{onClick:()=>C(p.page+1),disabled:p.page>=p.pages,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"Next"})]})})]})]})]})]})})}},76450:function(e,t,s){"use strict";s.d(t,{fq:function(){return n}});var a=s(57437);s(2265);var r=s(15863);let i=e=>{let{size:t="md",className:s=""}=e;return(0,a.jsx)(r.Z,{className:"animate-spin text-green-600 ".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," ").concat(s)})},n=e=>{let{message:t="Loading..."}=e;return(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(i,{size:"lg",className:"mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:t})]})})}},92735:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},87769:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},42208:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15863:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},95805:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});let a=(0,s(39763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},99376:function(e,t,s){"use strict";var a=s(35475);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})}},function(e){e.O(0,[7349,2971,2117,1744],function(){return e(e.s=49315)}),_N_E=e.O()}]);