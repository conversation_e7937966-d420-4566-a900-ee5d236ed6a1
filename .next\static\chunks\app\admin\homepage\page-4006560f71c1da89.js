(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7416],{79054:function(e,s,t){Promise.resolve().then(t.bind(t,20710))},20710:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return C}});var r=t(57437),a=t(2265),l=t(80605),n=t(99376),o=t(76450),i=t(30401),c=t(22252),d=t(76858),u=t(53417),m=t(32489),h=t(40875),x=t(73247),g=e=>{var s;let{products:t,selectedProductId:l,onProductSelect:n,placeholder:o="Search and select a product...",className:c=""}=e,[d,u]=(0,a.useState)(!1),[g,f]=(0,a.useState)(""),b=(0,a.useRef)(null),p=(0,a.useRef)(null),y=t.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.slug&&e.slug.toLowerCase().includes(g.toLowerCase())),j=t.find(e=>e.id===l);(0,a.useEffect)(()=>{let e=e=>{b.current&&!b.current.contains(e.target)&&(u(!1),f(""))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.useEffect)(()=>{d&&p.current&&p.current.focus()},[d]);let v=e=>{n(e),u(!1),f("")};return(0,r.jsxs)("div",{className:"relative ".concat(c),ref:b,children:[(0,r.jsx)("div",{onClick:()=>{u(!d),d||f("")},className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus-within:border-indigo-500 focus-within:ring-1 focus-within:ring-indigo-500 cursor-pointer",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex-1 min-w-0",children:j?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(null===(s=j.images)||void 0===s?void 0:s[0])&&(0,r.jsx)("img",{src:j.images[0].url,alt:j.images[0].alt||j.name,className:"w-6 h-6 rounded object-cover flex-shrink-0"}),(0,r.jsx)("span",{className:"text-sm text-gray-900 truncate",children:j.name})]}):(0,r.jsx)("span",{className:"text-sm text-gray-500",children:o})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[j&&(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),n(null)},className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"Clear selection",children:(0,r.jsx)(m.Z,{className:"w-4 h-4 text-gray-400"})}),(0,r.jsx)(h.Z,{className:"w-4 h-4 text-gray-400 transition-transform ".concat(d?"rotate-180":"")})]})]})}),d&&(0,r.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden",children:[(0,r.jsx)("div",{className:"p-3 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,r.jsx)("input",{ref:p,type:"text",placeholder:"Search products...",value:g,onChange:e=>f(e.target.value),className:"w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"})]})}),(0,r.jsxs)("div",{className:"max-h-60 overflow-y-auto",children:[(0,r.jsxs)("div",{onClick:()=>v(null),className:"px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ".concat(l?"text-gray-900":"bg-indigo-50 text-indigo-700"),children:[(0,r.jsx)("span",{className:"text-sm",children:"None - Use automatic selection"}),!l&&(0,r.jsx)(i.Z,{className:"w-4 h-4"})]}),y.length>0?y.map(e=>{var s;return(0,r.jsxs)("div",{onClick:()=>v(e.id),className:"px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ".concat(l===e.id?"bg-indigo-50 text-indigo-700":"text-gray-900"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 min-w-0 flex-1",children:[(null===(s=e.images)||void 0===s?void 0:s[0])&&(0,r.jsx)("img",{src:e.images[0].url,alt:e.images[0].alt||e.name,className:"w-8 h-8 rounded object-cover flex-shrink-0"}),(0,r.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium truncate",children:e.name}),e.price&&(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["₹",e.price]})]})]}),l===e.id&&(0,r.jsx)(i.Z,{className:"w-4 h-4 flex-shrink-0"})]},e.id)}):(0,r.jsxs)("div",{className:"px-3 py-4 text-sm text-gray-500 text-center",children:['No products found matching "',g,'"']})]})]})]})},f=t(12805),b=e=>{let{value:s,onChange:t,label:l,presetColors:n=["#22c55e","#16a34a","#15803d","#f0fdf4","#dcfce7","#bbf7d0","#86efac","#4ade80","#3b82f6","#6366f1","#8b5cf6","#ec4899","#f59e0b","#ef4444","#6b7280","#1f2937"],className:o=""}=e,[c,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(s),h=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=e=>{h.current&&!h.current.contains(e.target)&&d(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let x=e=>{t(e),m(e),d(!1)};return(0,r.jsxs)("div",{className:"relative ".concat(o),ref:h,children:[l&&(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:l}),(0,r.jsxs)("div",{onClick:()=>d(!c),className:"w-full h-10 border border-gray-300 rounded-md cursor-pointer flex items-center px-3 bg-white hover:border-gray-400 transition-colors",children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded border border-gray-300 mr-3 flex-shrink-0",style:{backgroundColor:s}}),(0,r.jsx)("span",{className:"text-sm text-gray-700 flex-1",children:s}),(0,r.jsx)(f.Z,{className:"w-4 h-4 text-gray-400"})]}),c&&(0,r.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg p-4",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Preset Colors"}),(0,r.jsx)("div",{className:"grid grid-cols-8 gap-2",children:n.map(e=>(0,r.jsx)("button",{onClick:()=>x(e),className:"w-8 h-8 rounded border border-gray-300 hover:scale-110 transition-transform relative",style:{backgroundColor:e},title:e,children:s===e&&(0,r.jsx)(i.Z,{className:"w-4 h-4 text-white absolute inset-0 m-auto drop-shadow-sm"})},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Custom Color"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"color",value:u,onChange:e=>{let s=e.target.value;m(s),t(s)},className:"w-10 h-8 border border-gray-300 rounded cursor-pointer"}),(0,r.jsx)("input",{type:"text",value:u,onChange:e=>{m(e.target.value),e.target.value.match(/^#[0-9A-Fa-f]{6}$/)&&t(e.target.value)},placeholder:"#22c55e",className:"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"})]})]}),(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Brand Colors"}),(0,r.jsx)("div",{className:"grid grid-cols-4 gap-2",children:[{name:"Primary Green",color:"#22c55e"},{name:"Dark Green",color:"#16a34a"},{name:"Light Green",color:"#f0fdf4"},{name:"Green Accent",color:"#dcfce7"}].map(e=>{let{name:s,color:t}=e;return(0,r.jsxs)("button",{onClick:()=>x(t),className:"flex flex-col items-center p-2 rounded hover:bg-gray-50 transition-colors",title:s,children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded border border-gray-300 mb-1",style:{backgroundColor:t}}),(0,r.jsx)("span",{className:"text-xs text-gray-600 text-center",children:s})]},t)})})]})]})]})},p=t(95805),y=t(99397),j=t(86595);let v=(0,t(39763).Z)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);var N=t(18930),w=t(83229),k=e=>{let{isOpen:s,onClose:t,onSave:l}=e,[n,o]=(0,a.useState)([]),[i,c]=(0,a.useState)(!1),[d,u]=(0,a.useState)(null),[h,x]=(0,a.useState)(!1),[g,f]=(0,a.useState)(null),[b,k]=(0,a.useState)({name:"",content:"",rating:5,image:"",position:"",company:"",isActive:!0,order:0});(0,a.useEffect)(()=>{s&&C()},[s]);let C=async()=>{try{c(!0);let e=await fetch("/api/testimonials"),s=await e.json();s.success&&o(s.data)}catch(e){console.error("Error fetching testimonials:",e),S("error","Failed to load testimonials")}finally{c(!1)}},S=(e,s)=>{f({type:e,message:s}),setTimeout(()=>f(null),3e3)},T=async e=>{e.preventDefault();try{c(!0);let e=d?"/api/testimonials/".concat(d.id):"/api/testimonials",s=await fetch(e,{method:d?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)}),t=await s.json();t.success?(S("success",t.message),await C(),L(),l()):S("error",t.error)}catch(e){console.error("Error saving testimonial:",e),S("error","Failed to save testimonial")}finally{c(!1)}},I=async e=>{if(confirm("Are you sure you want to delete this testimonial?"))try{c(!0);let s=await fetch("/api/testimonials/".concat(e),{method:"DELETE"}),t=await s.json();t.success?(S("success","Testimonial deleted successfully"),await C(),l()):S("error",t.error)}catch(e){console.error("Error deleting testimonial:",e),S("error","Failed to delete testimonial")}finally{c(!1)}},B=e=>{u(e),k({name:e.name,content:e.content,rating:e.rating,image:e.image||"",position:e.position||"",company:e.company||"",isActive:e.isActive,order:e.order}),x(!0)},L=()=>{u(null),x(!1),k({name:"",content:"",rating:5,image:"",position:"",company:"",isActive:!0,order:0})};return s?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.Z,{className:"h-6 w-6 text-green-600 mr-2"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Manage Testimonials"})]}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(m.Z,{className:"h-6 w-6"})})]}),g&&(0,r.jsx)("div",{className:"p-4 ".concat("success"===g.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:g.message}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:h?(0,r.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:d?"Edit Testimonial":"Add New Testimonial"}),(0,r.jsx)("button",{type:"button",onClick:L,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(m.Z,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:b.name,onChange:e=>k({...b,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Customer name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating"}),(0,r.jsx)("select",{value:b.rating,onChange:e=>k({...b,rating:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("option",{value:e,children:[e," Star",1!==e?"s":""]},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Testimonial Content *"}),(0,r.jsx)("textarea",{required:!0,rows:3,value:b.content,onChange:e=>k({...b,content:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"What did the customer say about your products?"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Position"}),(0,r.jsx)("input",{type:"text",value:b.position,onChange:e=>k({...b,position:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"e.g., Marketing Manager"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company"}),(0,r.jsx)("input",{type:"text",value:b.company,onChange:e=>k({...b,company:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"e.g., ABC Corp"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Image URL"}),(0,r.jsx)("input",{type:"url",value:b.image,onChange:e=>k({...b,image:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"https://example.com/image.jpg"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Order"}),(0,r.jsx)("input",{type:"number",min:"0",value:b.order,onChange:e=>k({...b,order:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"0"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"isActive",checked:b.isActive,onChange:e=>k({...b,isActive:e.target.checked}),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("label",{htmlFor:"isActive",className:"ml-2 text-sm text-gray-700",children:"Active (show on website)"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:L,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",children:"Cancel"}),(0,r.jsxs)("button",{type:"submit",disabled:i,className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50",children:[(0,r.jsx)(w.Z,{className:"h-4 w-4 mr-2"}),i?"Saving...":d?"Update":"Create"]})]})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("button",{onClick:()=>{u(null),k({name:"",content:"",rating:5,image:"",position:"",company:"",isActive:!0,order:n.length}),x(!0)},className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:[(0,r.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Add New Testimonial"]})}),i?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"}),(0,r.jsx)("p",{className:"text-gray-500 mt-2",children:"Loading testimonials..."})]}):0===n.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(p.Z,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No testimonials found. Add your first testimonial!"})]}):(0,r.jsx)("div",{className:"grid gap-4",children:n.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),e.position&&e.company&&(0,r.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:[e.position," at ",e.company]}),(0,r.jsx)("div",{className:"ml-auto flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((s,t)=>(0,r.jsx)(j.Z,{className:"h-4 w-4 ".concat(t<e.rating?"text-yellow-400 fill-current":"text-gray-300")},t))})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,r.jsx)("span",{className:"px-2 py-1 rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.isActive?"Active":"Inactive"}),(0,r.jsxs)("span",{className:"ml-2",children:["Order: ",e.order]})]})]}),(0,r.jsxs)("div",{className:"flex items-center ml-4 space-x-2",children:[(0,r.jsx)("button",{onClick:()=>B(e),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(v,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>I(e.id),className:"p-2 text-gray-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(N.Z,{className:"h-4 w-4"})})]})]})},e.id))})]})})]})}):null};function C(){let{data:e,status:s}=(0,l.useSession)(),t=(0,n.useRouter)(),[m,h]=(0,a.useState)(!0),[x,f]=(0,a.useState)(!1),[p,y]=(0,a.useState)(null),[j,v]=(0,a.useState)([]),[N,w]=(0,a.useState)(!1),[C,S]=(0,a.useState)({id:"",heroTitle:"Natural Skincare Essentials",heroSubtitle:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",heroCtaText:"Shop Collection",heroCtaLink:"/shop",heroSecondaryCtaText:"View Categories",heroSecondaryCtaLink:"/categories",heroBadgeText:"New Collection",heroBackgroundColor:"#f0fdf4",showHero:!0,trustIndicator1Value:"100%",trustIndicator1Label:"Natural",trustIndicator2Value:"500+",trustIndicator2Label:"Happy Customers",trustIndicator3Value:"50+",trustIndicator3Label:"Products",trustIndicator4Value:"4.8★",trustIndicator4Label:"Rating",productOfTheMonthId:null,showProductOfMonth:!0,bannerText:null,bannerCtaText:null,bannerCtaLink:null,bannerBackgroundColor:"#22c55e",showBanner:!0,showCategories:!0,productSectionBgColor:"#f0fdf4",bestsellerIds:[],showBestsellers:!0,newsletterTitle:"Stay Updated",newsletterSubtitle:"Get the latest updates on new products and exclusive offers",showNewsletter:!0,showTrustBadges:!0,flashSaleTitle:"Weekend Flash Sale",flashSaleSubtitle:"Get 25% off all natural skincare products",flashSaleEndDate:null,flashSaleBackgroundColor:"#16a34a",flashSalePercentage:25,showFlashSale:!0,testimonialsTitle:"What Our Customers Say",testimonialsSubtitle:"Real reviews from real customers who love our natural skincare",testimonialsBackgroundColor:"#f0fdf4",showTestimonials:!0,isActive:!0,createdAt:"",updatedAt:""});(0,a.useEffect)(()=>{if("loading"!==s){if(!e||"ADMIN"!==e.user.role){t.push("/login");return}I()}},[e,s,t]);let T=(e,s)=>{y({type:e,message:s}),setTimeout(()=>y(null),3e3)},I=async()=>{try{let e=await fetch("/api/products?limit=1000"),s=await e.json();v(s.data||[]);let t=await fetch("/api/homepage-settings"),r=await t.json();r.success&&r.data.settings?S(r.data.settings):S({id:"",heroTitle:"Natural Skincare Essentials",heroSubtitle:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",heroCtaText:"Shop Collection",heroCtaLink:"/shop",heroSecondaryCtaText:"View Categories",heroSecondaryCtaLink:"/categories",heroBadgeText:"New Collection",heroBackgroundColor:"#f0fdf4",showHero:!0,trustIndicator1Value:"100%",trustIndicator1Label:"Natural",trustIndicator2Value:"500+",trustIndicator2Label:"Happy Customers",trustIndicator3Value:"50+",trustIndicator3Label:"Products",trustIndicator4Value:"4.8★",trustIndicator4Label:"Rating",productOfTheMonthId:null,showProductOfMonth:!0,bannerText:null,bannerCtaText:null,bannerCtaLink:null,bannerBackgroundColor:"#22c55e",showBanner:!0,showCategories:!0,productSectionBgColor:"#f0fdf4",bestsellerIds:[],showBestsellers:!0,newsletterTitle:"Stay Updated",newsletterSubtitle:"Get the latest updates on new products and exclusive offers",showNewsletter:!0,showTrustBadges:!0,flashSaleTitle:"Weekend Flash Sale",flashSaleSubtitle:"Get 25% off all natural skincare products",flashSaleEndDate:null,flashSaleBackgroundColor:"#16a34a",flashSalePercentage:25,showFlashSale:!0,testimonialsTitle:"What Our Customers Say",testimonialsSubtitle:"Real reviews from real customers who love our natural skincare",testimonialsBackgroundColor:"#f0fdf4",showTestimonials:!0,isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()})}catch(e){console.error("Error fetching data:",e),T("error","Failed to load data")}finally{h(!1)}},B=async()=>{f(!0);try{let e=await fetch("/api/homepage-settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(C)}),s=await e.json();s.success?(T("success","Homepage settings saved successfully"),s.data&&S(s.data)):T("error",s.error||"Failed to save settings")}catch(e){console.error("Error saving settings:",e),T("error","Failed to save settings")}finally{f(!1)}},L=(e,s)=>{S(t=>({...t,[e]:"flashSalePercentage"===e?parseFloat(s):s}))};return"loading"===s||m?(0,r.jsx)(o.fq,{}):(0,r.jsxs)("div",{className:"p-6",children:[p&&(0,r.jsxs)("div",{className:"fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center ".concat("success"===p.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:["success"===p.type?(0,r.jsx)(i.Z,{className:"w-5 h-5 mr-2"}):(0,r.jsx)(c.Z,{className:"w-5 h-5 mr-2"}),p.message]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Homepage Settings"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Customize your homepage content, featured products, and promotional banners"}),(0,r.jsx)("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100",children:(0,r.jsxs)("p",{className:"text-blue-800 text-sm",children:[(0,r.jsx)("span",{className:"font-medium",children:"Tip:"})," Changes made here will be reflected on your store's homepage immediately after saving. For best results, use high-quality images and compelling copy for your featured products."]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Hero Section"}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showHero,onChange:e=>L("showHero",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Show Hero Section"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Configure the main hero section that appears at the top of your homepage."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Hero Title"}),(0,r.jsx)("input",{type:"text",value:C.heroTitle||"",onChange:e=>L("heroTitle",e.target.value),placeholder:"Natural Skincare Essentials",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Button Text"}),(0,r.jsx)("input",{type:"text",value:C.heroCtaText||"",onChange:e=>L("heroCtaText",e.target.value),placeholder:"Shop Collection",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Hero Subtitle"}),(0,r.jsx)("textarea",{value:C.heroSubtitle||"",onChange:e=>L("heroSubtitle",e.target.value),placeholder:"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Button Link"}),(0,r.jsx)("input",{type:"text",value:C.heroCtaLink||"",onChange:e=>L("heroCtaLink",e.target.value),placeholder:"/shop",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Secondary CTA Text"}),(0,r.jsx)("input",{type:"text",value:C.heroSecondaryCtaText||"",onChange:e=>L("heroSecondaryCtaText",e.target.value),placeholder:"View Categories",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Secondary CTA Link"}),(0,r.jsx)("input",{type:"text",value:C.heroSecondaryCtaLink||"",onChange:e=>L("heroSecondaryCtaLink",e.target.value),placeholder:"/categories",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Badge Text"}),(0,r.jsx)("input",{type:"text",value:C.heroBadgeText||"",onChange:e=>L("heroBadgeText",e.target.value),placeholder:"New Collection",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)(b,{value:C.heroBackgroundColor||"#f0fdf4",onChange:e=>L("heroBackgroundColor",e),label:"Hero Background Color"}),(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-1",children:"Background color for the hero section"})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Trust Indicators"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 1"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator1Value||"",onChange:e=>L("trustIndicator1Value",e.target.value),placeholder:"100%",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 1"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator1Label||"",onChange:e=>L("trustIndicator1Label",e.target.value),placeholder:"Natural",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 2"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator2Value||"",onChange:e=>L("trustIndicator2Value",e.target.value),placeholder:"500+",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 2"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator2Label||"",onChange:e=>L("trustIndicator2Label",e.target.value),placeholder:"Happy Customers",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 3"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator3Value||"",onChange:e=>L("trustIndicator3Value",e.target.value),placeholder:"50+",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 3"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator3Label||"",onChange:e=>L("trustIndicator3Label",e.target.value),placeholder:"Products",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Value 4"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator4Value||"",onChange:e=>L("trustIndicator4Value",e.target.value),placeholder:"4.8★",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Label 4"}),(0,r.jsx)("input",{type:"text",value:C.trustIndicator4Label||"",onChange:e=>L("trustIndicator4Label",e.target.value),placeholder:"Rating",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Product of the Month"}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showProductOfMonth,onChange:e=>L("showProductOfMonth",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Show Product of the Month"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Select a featured product to highlight on the homepage. This product will be displayed prominently on the homepage."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search and Select Featured Product"}),(0,r.jsx)(g,{products:j,selectedProductId:C.productOfTheMonthId,onProductSelect:e=>L("productOfTheMonthId",e),placeholder:"Search and select a product..."}),(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-2",children:'If no product is selected, the system will automatically feature the first product marked as "Featured" in the product settings.'})]}),C.productOfTheMonthId&&(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Selected Product Preview"}),(()=>{var e;let s=j.find(e=>e.id===C.productOfTheMonthId);return s?(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(null===(e=s.images)||void 0===e?void 0:e[0])&&(0,r.jsx)("img",{src:s.images[0].url,alt:s.name,className:"w-16 h-16 object-cover rounded-lg"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:s.name}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:["₹",s.price]})]})]}):null})()]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)(b,{value:C.productSectionBgColor||"#f0fdf4",onChange:e=>L("productSectionBgColor",e),label:"Product Section Background Color"}),(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-1",children:"Background color for the Product of the Month section"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Promotional Banner"}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showBanner,onChange:e=>L("showBanner",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Show Promotional Banner"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Create an eye-catching banner to promote special offers, sales, or important announcements."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Banner Text"}),(0,r.jsx)("input",{type:"text",value:C.bannerText||"",onChange:e=>L("bannerText",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Limited time offer! Get 20% off on all herbal products"}),(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-1",children:"Keep it short and compelling to grab attention"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Button Text"}),(0,r.jsx)("input",{type:"text",value:C.bannerCtaText||"",onChange:e=>L("bannerCtaText",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Shop Now"}),(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-1",children:'Action-oriented text like "Shop Now", "Learn More", or "Get Offer"'})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CTA Link"}),(0,r.jsx)("input",{type:"text",value:C.bannerCtaLink||"",onChange:e=>L("bannerCtaLink",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"/shop"}),(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-1",children:"URL where users should be redirected when clicking the CTA"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b,{value:C.bannerBackgroundColor||"#22c55e",onChange:e=>L("bannerBackgroundColor",e),label:"Banner Background Color"}),(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-1",children:"Choose a color that matches your brand"})]})]}),C.bannerText&&(0,r.jsxs)("div",{className:"mt-6 p-4 rounded-lg text-white",style:{backgroundColor:C.bannerBackgroundColor||"#22c55e"},children:[(0,r.jsx)("h3",{className:"font-bold text-sm mb-1",children:C.bannerText}),C.bannerCtaText&&C.bannerCtaLink&&(0,r.jsxs)("a",{href:C.bannerCtaLink,className:"inline-flex items-center text-xs font-medium underline",children:[C.bannerCtaText,(0,r.jsx)(d.Z,{className:"ml-1 w-3 h-3"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Bestseller Products"}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showBestsellers,onChange:e=>L("showBestsellers",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Show Bestsellers Section"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Manually select products to feature as bestsellers. If none are selected, the system will automatically show products with the most reviews."}),(0,r.jsxs)("div",{className:"space-y-4",children:[C.bestsellerIds&&C.bestsellerIds.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:["Selected Bestsellers (",C.bestsellerIds.length,"/4)"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:C.bestsellerIds.map((e,s)=>{var t;let a=j.find(s=>s.id===e);return a?(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(null===(t=a.images)||void 0===t?void 0:t[0])&&(0,r.jsx)("img",{src:a.images[0].url,alt:a.name,className:"w-12 h-12 object-cover rounded-lg"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 text-sm",children:a.name}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs",children:["₹",a.price]})]})]}),(0,r.jsx)("button",{onClick:()=>{L("bestsellerIds",C.bestsellerIds.filter(s=>s!==e))},className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},e):null})})]}),(!C.bestsellerIds||C.bestsellerIds.length<4)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Add Bestseller Product"}),(0,r.jsx)(g,{products:j.filter(e=>{var s;return!(null===(s=C.bestsellerIds)||void 0===s?void 0:s.includes(e.id))}),selectedProductId:"",onProductSelect:e=>{e&&L("bestsellerIds",[...C.bestsellerIds||[],e])},placeholder:"Search and select a bestseller product..."})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Settings"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Control the visibility and activation of homepage features"}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex items-center h-5",children:(0,r.jsx)("input",{id:"isActive",type:"checkbox",checked:C.isActive,onChange:e=>L("isActive",e.target.checked),className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"})}),(0,r.jsxs)("div",{className:"ml-3 text-sm",children:[(0,r.jsx)("label",{htmlFor:"isActive",className:"font-medium text-gray-700",children:"Enable Custom Homepage Settings"}),(0,r.jsx)("p",{className:"text-gray-500",children:"When enabled, your custom settings will be displayed on the homepage. When disabled, default content will be shown."})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Newsletter Signup"}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showNewsletter,onChange:e=>L("showNewsletter",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Show Newsletter Section"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Customize the newsletter signup section that appears on the homepage"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Newsletter Title"}),(0,r.jsx)("input",{type:"text",value:C.newsletterTitle||"",onChange:e=>L("newsletterTitle",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Stay Updated"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Newsletter Subtitle"}),(0,r.jsx)("input",{type:"text",value:C.newsletterSubtitle||"",onChange:e=>L("newsletterSubtitle",e.target.value),className:"w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",placeholder:"Get the latest updates on new products and exclusive offers"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Section Visibility"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Control which sections appear on your homepage"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showCategories,onChange:e=>L("showCategories",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-3 text-sm font-medium text-gray-700",children:"Show Categories Section"})]}),(0,r.jsxs)("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showTrustBadges,onChange:e=>L("showTrustBadges",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-3 text-sm font-medium text-gray-700",children:"Show Trust Badges Section"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Flash Sale Section"}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showFlashSale,onChange:e=>L("showFlashSale",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Show Flash Sale"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Flash Sale Title"}),(0,r.jsx)("input",{type:"text",value:C.flashSaleTitle||"",onChange:e=>L("flashSaleTitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Weekend Flash Sale"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Flash Sale Subtitle"}),(0,r.jsx)("input",{type:"text",value:C.flashSaleSubtitle||"",onChange:e=>L("flashSaleSubtitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Get 25% off all natural skincare products"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Percentage"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"number",min:"0",max:"100",value:C.flashSalePercentage||25,onChange:e=>L("flashSalePercentage",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 pr-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"25"}),(0,r.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500",children:"%"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Store-wide discount percentage during flash sale"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date & Time"}),(0,r.jsx)("input",{type:"datetime-local",value:C.flashSaleEndDate||"",onChange:e=>L("flashSaleEndDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Background Color"}),(0,r.jsx)(b,{value:C.flashSaleBackgroundColor||"#16a34a",onChange:e=>L("flashSaleBackgroundColor",e)})]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Testimonials Section"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>w(!0),className:"inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors",children:[(0,r.jsx)(u.Z,{className:"h-4 w-4 mr-1"}),"Manage Testimonials"]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.showTestimonials,onChange:e=>L("showTestimonials",e.target.checked),className:"rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"}),(0,r.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-700",children:"Show Testimonials"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Section Title"}),(0,r.jsx)("input",{type:"text",value:C.testimonialsTitle||"",onChange:e=>L("testimonialsTitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"What Our Customers Say"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Section Subtitle"}),(0,r.jsx)("input",{type:"text",value:C.testimonialsSubtitle||"",onChange:e=>L("testimonialsSubtitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Real reviews from real customers who love our natural skincare"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Background Color"}),(0,r.jsx)(b,{value:C.testimonialsBackgroundColor||"#f0fdf4",onChange:e=>L("testimonialsBackgroundColor",e)})]})]})]}),(0,r.jsx)("div",{className:"flex justify-end mt-8 pt-6 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[p&&(0,r.jsx)("div",{className:"mr-4 px-4 py-2 rounded-md text-sm ".concat("success"===p.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:p.message}),C.id&&(0,r.jsxs)("span",{className:"text-sm text-gray-500 mr-4",children:["Last saved: ",new Date(C.updatedAt).toLocaleString()]}),(0,r.jsx)("button",{type:"button",onClick:B,disabled:x,className:"inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:x?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving Changes..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.Z,{className:"h-5 w-5 mr-2"}),"Save Changes"]})})]})}),(0,r.jsx)(k,{isOpen:N,onClose:()=>w(!1),onSave:()=>{w(!1)}})]})}},76450:function(e,s,t){"use strict";t.d(s,{fq:function(){return n}});var r=t(57437);t(2265);var a=t(15863);let l=e=>{let{size:s="md",className:t=""}=e;return(0,r.jsx)(a.Z,{className:"animate-spin text-green-600 ".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," ").concat(t)})},n=e=>{let{message:s="Loading..."}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(l,{size:"lg",className:"mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg",children:s})]})})}},22252:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},76858:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},30401:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},40875:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},15863:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53417:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},12805:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},99397:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},83229:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},73247:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},86595:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},18930:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},95805:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},32489:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(39763).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},99376:function(e,s,t){"use strict";var r=t(35475);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})}},function(e){e.O(0,[7349,2971,2117,1744],function(){return e(e.s=79054)}),_N_E=e.O()}]);