(()=>{var e={};e.id=8606,e.ids=[8606],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},82363:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c}),s(40377),s(90596),s(37254),s(35866);var a=s(23191),r=s(88716),l=s(37922),i=s.n(l),d=s(95231),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);s.d(t,n);let c=["",{children:["admin",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,40377)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\orders\\page.tsx"],x="/admin/orders/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/orders/page",pathname:"/admin/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},75671:(e,t,s)=>{Promise.resolve().then(s.bind(s,2802))},30198:(e,t,s)=>{Promise.resolve().then(s.bind(s,9564))},2802:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Z});var a=s(10326),r=s(17577),l=s.n(r),i=s(90434),d=s(35047),n=s(77109),c=s(24319),o=s(48705),x=s(34565),m=s(57671),h=s(24061),u=s(40765),p=s(40617),y=s(35351),g=s(6507),j=s(5932),f=s(71709),N=s(95920),b=s(88378),v=s(94019),w=s(90748),k=s(53080),C=s(71810);let Z=({children:e})=>{let t=(0,d.usePathname)(),[s,r]=l().useState(!1),[Z,P]=l().useState(!1),D=async()=>{if(!Z)try{P(!0),r(!1),await (0,n.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},E=[{href:"/admin",label:"Dashboard",icon:c.Z},{href:"/admin/products",label:"Products",icon:o.Z},{href:"/admin/categories",label:"Categories",icon:x.Z},{href:"/admin/orders",label:"Orders",icon:m.Z},{href:"/admin/customers",label:"Customers",icon:h.Z},{href:"/admin/coupons",label:"Coupons",icon:u.Z},{href:"/admin/reviews",label:"Reviews",icon:p.Z},{href:"/admin/enquiry",label:"Enquiries",icon:y.Z},{href:"/admin/notifications",label:"Notifications",icon:g.Z},{href:"/admin/newsletter",label:"Newsletter",icon:j.Z},{href:"/admin/media",label:"Media",icon:f.Z},{href:"/admin/homepage",label:"Homepage",icon:N.Z},{href:"/admin/settings",label:"Settings",icon:b.Z}],S=e=>"/admin"===e?"/admin"===t:t.startsWith(e);return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:a.jsx("button",{onClick:()=>r(!s),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:s?a.jsx(v.Z,{className:"w-6 h-6 text-gray-600"}):a.jsx(w.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,a.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${s?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[a.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:a.jsx(k.Z,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),a.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),a.jsx("nav",{className:"mt-6 px-3",children:E.map(e=>{let t=e.icon,s=S(e.href);return(0,a.jsxs)(i.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${s?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>r(!1),children:[a.jsx(t,{className:`w-5 h-5 mr-3 ${s?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,a.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[a.jsx(i.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,a.jsxs)("button",{onClick:D,disabled:Z,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(C.Z,{className:"w-4 h-4 mr-2"}),Z?"Logging out...":"Logout"]})]})]}),a.jsx("main",{className:"lg:ml-64",children:a.jsx("div",{className:"p-4 lg:p-8",children:e})}),s&&a.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>r(!1)})]})}},9564:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var a=s(10326),r=s(17577),l=s(48998),i=s(54659),d=s(48705),n=s(14228),c=s(91470),o=s(21405),x=s(75290),m=s(88307),h=s(31540),u=s(87888),p=s(76557);let y=(0,p.Z)("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]]),g=(0,p.Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var j=s(12714),f=s(82685),N=s(11890),b=s(39183),v=s(9891),w=s(77109),k=s(35047);let C=()=>{let{data:e,status:t}=(0,w.useSession)(),s=(0,k.useRouter)(),[p,C]=(0,r.useState)([]),[Z,P]=(0,r.useState)(!0),[D,E]=(0,r.useState)(null),[S,M]=(0,r.useState)(""),[L,I]=(0,r.useState)("all"),[R,O]=(0,r.useState)("all"),[A,q]=(0,r.useState)(null),[H,F]=(0,r.useState)(!1),[U,T]=(0,r.useState)(1),[G,$]=(0,r.useState)(1),[z,_]=(0,r.useState)(0),[V,B]=(0,r.useState)([]),[J,Q]=(0,r.useState)(!1),[W,X]=(0,r.useState)(!1);(0,r.useEffect)(()=>{"loading"===t||e&&e.user?.role==="ADMIN"||s.push("/")},[e,t,s]);let K=async(e=1)=>{try{P(!0),E(null);let t=new URLSearchParams({page:e.toString(),limit:"20",..."all"!==L&&{status:L},..."all"!==R&&{paymentStatus:R}}),s=await fetch(`/api/orders?${t}`),a=await s.json();if(!s.ok)throw Error(a.message||"Failed to fetch orders");C(a.orders),$(a.pagination.totalPages),_(a.pagination.totalCount),T(e),B([])}catch(e){E(e instanceof Error?e.message:"Failed to fetch orders")}finally{P(!1)}};(0,r.useEffect)(()=>{e?.user?.role==="ADMIN"&&K(1)},[e,L,R]);let Y=p.filter(e=>{if(!S)return!0;let t=S.toLowerCase();return e.orderNumber.toLowerCase().includes(t)||e.user?.name?.toLowerCase().includes(t)||e.user?.email?.toLowerCase().includes(t)||e.address.firstName.toLowerCase().includes(t)||e.address.lastName.toLowerCase().includes(t)}),ee=e=>{switch(e){case"PENDING":default:return a.jsx(l.Z,{className:"w-4 h-4"});case"CONFIRMED":case"DELIVERED":return a.jsx(i.Z,{className:"w-4 h-4"});case"PROCESSING":return a.jsx(d.Z,{className:"w-4 h-4"});case"SHIPPED":return a.jsx(n.Z,{className:"w-4 h-4"});case"CANCELLED":return a.jsx(c.Z,{className:"w-4 h-4"});case"REFUNDED":return a.jsx(o.Z,{className:"w-4 h-4"})}},et=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"CONFIRMED":return"bg-blue-100 text-blue-800";case"PROCESSING":return"bg-indigo-100 text-indigo-800";case"SHIPPED":return"bg-purple-100 text-purple-800";case"DELIVERED":return"bg-green-100 text-green-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},es=e=>{switch(e){case"PAID":return"text-green-600";case"PENDING":return"text-yellow-600";case"FAILED":return"text-red-600";default:return"text-gray-600"}},ea=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),er=async(e,t,s)=>{try{if("SHIPPED"===t&&!s){let e=prompt("Please enter the tracking number:");if(!e){alert("Tracking number is required when marking order as shipped");return}s=e}let a=await fetch(`/api/orders/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t,...s&&{trackingNumber:s}})});if(!a.ok){let e=await a.json();throw Error(e.message||"Failed to update order status")}K(U),A&&A.id===e&&q({...A,status:t})}catch(e){console.error("Error updating order status:",e),alert("Failed to update order status")}},el=async e=>{if(0!==V.length){if("SHIPPED"===e){alert("Cannot bulk update orders to Shipped status. Please update each order individually with tracking information.");return}X(!0);try{let t=await fetch("/api/orders/bulk-update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderIds:V,updates:{status:e}})});if(!t.ok)throw Error("Failed to update orders");let s=await t.json();alert(`Successfully updated ${s.updatedCount} orders`),K(U),B([]),Q(!1)}catch(e){console.error("Error updating orders:",e),alert("Failed to update orders")}finally{X(!1)}}},ei=e=>{B(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return"loading"===t||Z?a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx(x.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Orders"}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Manage and track customer orders"})]}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(m.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),a.jsx("input",{type:"text",placeholder:"Search orders...",value:S,onChange:e=>M(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),(0,a.jsxs)("select",{value:L,onChange:e=>I(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"PENDING",children:"Pending"}),a.jsx("option",{value:"CONFIRMED",children:"Confirmed"}),a.jsx("option",{value:"PROCESSING",children:"Processing"}),a.jsx("option",{value:"SHIPPED",children:"Shipped"}),a.jsx("option",{value:"DELIVERED",children:"Delivered"}),a.jsx("option",{value:"CANCELLED",children:"Cancelled"}),a.jsx("option",{value:"REFUNDED",children:"Refunded"})]}),(0,a.jsxs)("select",{value:R,onChange:e=>O(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[a.jsx("option",{value:"all",children:"All Payment Status"}),a.jsx("option",{value:"PENDING",children:"Payment Pending"}),a.jsx("option",{value:"PAID",children:"Paid"}),a.jsx("option",{value:"FAILED",children:"Failed"}),a.jsx("option",{value:"REFUNDED",children:"Refunded"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>K(U),className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"Refresh orders",children:a.jsx(o.Z,{className:"w-5 h-5 text-gray-600"})}),a.jsx("button",{onClick:()=>{let e=new Blob([["Order Number,Customer Name,Email,Date,Status,Payment Status,Total",...Y.map(e=>[e.orderNumber,e.user?.name||`${e.address.firstName} ${e.address.lastName}`,e.user?.email||"",ea(e.createdAt),e.status,e.paymentStatus,e.total].join(","))].join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download=`orders-${new Date().toISOString().split("T")[0]}.csv`,s.click()},className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"Export orders",children:a.jsx(h.Z,{className:"w-5 h-5 text-gray-600"})})]})]})}),V.length>0&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-green-800 font-medium",children:[V.length," order",V.length>1?"s":""," selected"]}),a.jsx("button",{onClick:()=>Q(!J),className:"text-green-700 hover:text-green-900 font-medium",children:"Bulk Actions"})]}),a.jsx("button",{onClick:()=>B([]),className:"text-green-600 hover:text-green-800",children:"Clear Selection"})]}),J&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[a.jsx("button",{onClick:()=>el("CONFIRMED"),disabled:W,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Mark as Confirmed"}),a.jsx("button",{onClick:()=>el("PROCESSING"),disabled:W,className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50",children:"Mark as Processing"}),a.jsx("button",{onClick:()=>el("DELIVERED"),disabled:W,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50",children:"Mark as Delivered"}),a.jsx("button",{onClick:()=>el("CANCELLED"),disabled:W,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50",children:"Mark as Cancelled"})]})]}),D&&a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(u.Z,{className:"w-5 h-5 text-red-600"}),a.jsx("p",{className:"text-red-800",children:D})]})}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[a.jsx("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left",children:a.jsx("button",{onClick:()=>{V.length===Y.length?B([]):B(Y.map(e=>e.id))},className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900",children:V.length===Y.length&&Y.length>0?a.jsx(y,{className:"w-5 h-5"}):a.jsx(g,{className:"w-5 h-5"})})}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:0===Y.length?a.jsx("tr",{children:a.jsx("td",{colSpan:8,className:"px-6 py-12 text-center text-gray-500",children:"No orders found"})}):Y.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4",children:a.jsx("button",{onClick:()=>ei(e.id),className:"text-gray-600 hover:text-gray-900",children:V.includes(e.id)?a.jsx(y,{className:"w-5 h-5 text-green-600"}):a.jsx(g,{className:"w-5 h-5"})})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("button",{onClick:()=>s.push(`/admin/orders/${e.id}`),className:"text-left hover:text-green-600 transition-colors",children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.orderNumber}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.items.length," items"]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.user?.name||`${e.address.firstName} ${e.address.lastName}`}),a.jsx("div",{className:"text-xs text-gray-500",children:e.user?.email||"Guest"})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:ea(e.createdAt)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full items-center space-x-1 ${et(e.status)}`,children:[ee(e.status),a.jsx("span",{className:"capitalize",children:e.status.toLowerCase()})]}),a.jsx("div",{className:"absolute left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10",children:(0,a.jsxs)("div",{className:"py-1",children:[a.jsx("button",{onClick:()=>er(e.id,"CONFIRMED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Confirmed"}),a.jsx("button",{onClick:()=>er(e.id,"PROCESSING"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Processing"}),a.jsx("button",{onClick:()=>er(e.id,"SHIPPED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Shipped"}),a.jsx("button",{onClick:()=>er(e.id,"DELIVERED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Delivered"}),a.jsx("button",{onClick:()=>er(e.id,"CANCELLED"),className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Mark as Cancelled"})]})})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:`text-sm font-medium ${es(e.paymentStatus)}`,children:e.paymentStatus.toLowerCase()}),a.jsx("div",{className:"text-xs text-gray-500",children:"COD"===e.paymentMethod?"Cash on Delivery":e.paymentMethod||"Online"})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:(0,v.T4)(e.total)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("button",{onClick:()=>s.push(`/admin/orders/${e.id}`),className:"text-green-600 hover:text-green-800 transition-colors",title:"View Details",children:a.jsx(j.Z,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>er(e.id,"PROCESSING"),className:"text-blue-600 hover:text-blue-800 transition-colors",title:"Quick Update",children:a.jsx(f.Z,{className:"w-4 h-4"})})]})})]},e.id))})]})})}),G>1&&(0,a.jsxs)("div",{className:"mt-6 flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(U-1)*20+1," to"," ",Math.min(20*U,z)," of ",z," orders"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>K(U-1),disabled:1===U,className:"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:a.jsx(N.Z,{className:"w-5 h-5"})}),(0,a.jsxs)("span",{className:"px-3 py-2 text-gray-900",children:["Page ",U," of ",G]}),a.jsx("button",{onClick:()=>K(U+1),disabled:U===G,className:"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:a.jsx(b.Z,{className:"w-5 h-5"})})]})]}),H&&A&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Order Details"}),a.jsx("button",{onClick:()=>F(!1),className:"text-gray-500 hover:text-gray-700",children:a.jsx(c.Z,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Information"}),(0,a.jsxs)("dl",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Order Number"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:A.orderNumber})]}),(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Date"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:ea(A.createdAt)})]}),(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Status"}),a.jsx("dd",{children:(0,a.jsxs)("select",{value:A.status,onChange:e=>er(A.id,e.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-green-500",children:[a.jsx("option",{value:"PENDING",children:"Pending"}),a.jsx("option",{value:"CONFIRMED",children:"Confirmed"}),a.jsx("option",{value:"PROCESSING",children:"Processing"}),a.jsx("option",{value:"SHIPPED",children:"Shipped"}),a.jsx("option",{value:"DELIVERED",children:"Delivered"}),a.jsx("option",{value:"CANCELLED",children:"Cancelled"}),a.jsx("option",{value:"REFUNDED",children:"Refunded"})]})})]}),(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Payment Status"}),a.jsx("dd",{className:`text-sm font-medium ${es(A.paymentStatus)}`,children:A.paymentStatus})]}),(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Payment Method"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:"COD"===A.paymentMethod?"Cash on Delivery":"Online Payment"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Customer Information"}),(0,a.jsxs)("dl",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Name"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:A.user?.name||`${A.address.firstName} ${A.address.lastName}`})]}),(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Email"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:A.user?.email||"Guest"})]}),(0,a.jsxs)("div",{children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Phone"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:A.user?.phone||A.address.phone||"N/A"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Shipping Address"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[A.address.firstName," ",A.address.lastName,a.jsx("br",{}),A.address.address1,a.jsx("br",{}),A.address.address2&&(0,a.jsxs)(a.Fragment,{children:[A.address.address2,a.jsx("br",{})]}),A.address.city,", ",A.address.state," ",A.address.postalCode,a.jsx("br",{}),A.address.country]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Items"}),a.jsx("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:(0,a.jsxs)("table",{className:"w-full",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Product"}),a.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Quantity"}),a.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Price"}),a.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Total"})]})}),a.jsx("tbody",{className:"divide-y divide-gray-200",children:A.items.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.product.name}),a.jsx("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.quantity}),a.jsx("td",{className:"px-4 py-2 text-sm text-gray-900",children:(0,v.T4)(e.price)}),a.jsx("td",{className:"px-4 py-2 text-sm text-gray-900",children:(0,v.T4)(e.total)})]},e.id))})]})})]}),a.jsx("div",{className:"border-t border-gray-200 pt-4",children:(0,a.jsxs)("dl",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Subtotal"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:(0,v.T4)(A.subtotal)})]}),A.couponDiscount&&A.couponDiscount>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"Discount"}),(0,a.jsxs)("dd",{className:"text-sm font-medium text-green-600",children:["-",(0,v.T4)(A.couponDiscount)]})]}),"COD"===A.paymentMethod&&(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("dt",{className:"text-sm text-gray-500",children:"COD Charges"}),a.jsx("dd",{className:"text-sm font-medium text-gray-900",children:(0,v.T4)(50)})]}),(0,a.jsxs)("div",{className:"flex justify-between border-t border-gray-200 pt-2",children:[a.jsx("dt",{className:"text-base font-medium text-gray-900",children:"Total"}),a.jsx("dd",{className:"text-base font-medium text-gray-900",children:(0,v.T4)(A.total)})]})]})})]})]})})]})}},9891:(e,t,s)=>{"use strict";function a(e){return function(e,t=!0){if(isNaN(e))return"₹0";let s=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return`₹${s}`}(e,!0)}function r(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function l(e,t){return(e?r(e):r(t))||"product"}s.d(t,{GD:()=>r,T4:()=>a,w:()=>l})},87888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},11890:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},39183:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},48998:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},31540:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},35351:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},75290:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},82685:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]])},21405:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},88307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},14228:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},90596:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)},40377:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\orders\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,3757,434,9536],()=>s(82363));module.exports=a})();