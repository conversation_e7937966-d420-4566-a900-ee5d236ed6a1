"use strict";(()=>{var e={};e.id=1034,e.ids=[1034],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},57575:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>m,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{GET:()=>l});var n=r(49303),i=r(88716),s=r(60670),a=r(87070),u=r(75571),c=r(95306),p=r(54211);async function l(e){try{let e=await (0,u.getServerSession)(c.L);if(!e?.user?.id)return a.NextResponse.json({error:"Authentication required"},{status:401});return p.kg.info("Unread count API temporarily disabled during migration"),a.NextResponse.json({success:!0,unreadCount:0})}catch(e){return p.kg.error("Failed to fetch unread notification count",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/notifications/unread-count/route",pathname:"/api/notifications/unread-count",filename:"route",bundlePath:"app/api/notifications/unread-count/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\unread-count\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:g}=d,y="/api/notifications/unread-count/route";function m(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},54211:(e,t,r)=>{var o;r.d(t,{kg:()=>i}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(o||(o={}));class n{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:n,context:i,error:s,userId:a,requestId:u}=e,c=o[r],p=`[${t}] ${c}: ${n}`;return a&&(p+=` | User: ${a}`),u&&(p+=` | Request: ${u}`),i&&Object.keys(i).length>0&&(p+=` | Context: ${JSON.stringify(i)}`),s&&(p+=` | Error: ${s.message}`,this.isDevelopment&&s.stack&&(p+=`
Stack: ${s.stack}`)),p}log(e,t,r,o){if(!this.shouldLog(e))return;let n={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:o},i=this.formatMessage(n);if(this.isDevelopment)switch(e){case 0:console.error(i);break;case 1:console.warn(i);break;case 2:console.info(i);break;case 3:console.debug(i)}else console.log(JSON.stringify(n))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,o){this.info(`API ${e} ${t}`,{...o,userId:r,type:"api_request"})}apiResponse(e,t,r,o,n){this.info(`API ${e} ${t} - ${r}`,{...n,statusCode:r,duration:o,type:"api_response"})}apiError(e,t,r,o,n){this.error(`API ${e} ${t} failed`,r,{...n,userId:o,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,o){this.warn("Authentication failed",{...o,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,o){this.debug(`DB ${e} on ${t}`,{...o,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,o){this.error(`DB ${e} on ${t} failed`,r,{...o,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,o){this.warn("Rate limit exceeded",{...o,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,o){this.info("Email sent",{...o,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,o){this.error("Email failed to send",r,{...o,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let i=new n},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var n=r(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var o={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(o,i,a):o[i]=e[i]}return o.default=e,r&&r.set(e,o),o}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[9276,5972,8691,2830,5306],()=>r(57575));module.exports=o})();