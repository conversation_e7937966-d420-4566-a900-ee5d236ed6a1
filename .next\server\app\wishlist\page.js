(()=>{var e={};e.id=4456,e.ids=[4456],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},63689:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>c}),t(84699),t(37254),t(35866);var r=t(23191),i=t(88716),l=t(37922),a=t.n(l),n=t(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c=["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,84699)),"C:\\Users\\<USER>\\Desktop\\project\\app\\wishlist\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\wishlist\\page.tsx"],x="/wishlist/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16785:(e,s,t)=>{Promise.resolve().then(t.bind(t,65090)),Promise.resolve().then(t.bind(t,80084))},80084:(e,s,t)=>{"use strict";t.d(s,{default:()=>j});var r=t(10326),i=t(17577),l=t(35047),a=t(77109),n=t(90434),o=t(46226),c=t(75290),d=t(94019),x=t(33734),m=t(57671),h=t(67427),p=t(94494),u=t(11417),g=t(47537),f=t(52807);let j=()=>{let e=(0,l.useRouter)(),{data:s,status:t}=(0,a.useSession)(),{dispatch:j}=(0,p.j)(),{showToast:w}=(0,g.V)(),{refreshUnreadCount:v,refreshNotifications:y}=(0,f.z)(),[b,N]=(0,i.useState)([]),[k,E]=(0,i.useState)(!0),[P,_]=(0,i.useState)(null),[C,A]=(0,i.useState)([]);(0,i.useEffect)(()=>{"unauthenticated"!==t||s||e.push("/login")},[t,e]),(0,i.useEffect)(()=>{(async()=>{if("authenticated"!==t||!s?.user?.id){"loading"!==t&&E(!1);return}try{let e=await fetch("/api/wishlist");if(!e.ok)throw Error("Failed to fetch wishlist");let s=await e.json();N(s.items||[])}catch(e){console.error("Error fetching wishlist:",e),_("Failed to load wishlist")}finally{E(!1)}})()},[s,t]),(0,i.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/products?limit=4");if(e.ok){let s=(await e.json()).data.map(e=>({id:e.id,name:e.name,slug:e.slug,price:e.price,shortDescription:e.shortDescription,image:e.images?.[0]?.url||"/placeholder-product.jpg",rating:e.rating||0,reviews:e.reviews||0,wishlistItemId:""}));A(s||[])}}catch(e){console.error("Error fetching recommendations:",e)}})()},[]);let D=async e=>{try{let s=await fetch(`/api/wishlist?productId=${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to remove from wishlist")}N(s=>s.filter(s=>s.id!==e)),w("Item removed from wishlist","success"),v(),y()}catch(e){console.error("Error removing from wishlist:",e),w(e.message||"Could not remove item from wishlist.","error")}},U=e=>{let{wishlistItemId:s,...t}=e;j({type:"ADD_ITEM",payload:t}),w(`${t.name} added to cart`,"success")};return"loading"===t?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(c.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):"unauthenticated"===t?null:r.jsx("div",{className:"bg-gray-50 min-h-screen",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"My Wishlist"}),(0,r.jsxs)("span",{className:"text-gray-600",children:[b.length," items"]})]}),k?r.jsx(u.Gw,{}):P?(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx("div",{className:"w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-8",children:r.jsx(d.Z,{className:"w-16 h-16 text-red-500"})}),r.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Error loading wishlist"}),r.jsx("p",{className:"text-xl text-gray-600 mb-8",children:P}),r.jsx("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Try Again"})]}):b.length>0?(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx("div",{className:"space-y-4",children:b.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4",children:[r.jsx(n.default,{href:`/product/${e.slug||e.id}`,children:r.jsx("div",{className:"w-24 h-24 relative rounded-md overflow-hidden flex-shrink-0",children:r.jsx(o.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"96px"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx(n.default,{href:`/product/${e.slug||e.id}`,children:r.jsx("h3",{className:"font-semibold text-gray-800 hover:text-green-600",children:e.name})}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["₹",e.price]}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[r.jsx(x.Z,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600 ml-1",children:[e.rating," (",e.reviews," reviews)"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-end space-y-2",children:[r.jsx("button",{onClick:()=>D(e.id),className:"p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors",children:r.jsx(d.Z,{className:"w-5 h-5"})}),r.jsx("button",{onClick:()=>U(e),className:"bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-green-200 transition-colors",children:"Add to Cart"})]})]},e.id))})}),r.jsx("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 sticky top-24",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Wishlist Summary"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[r.jsx("span",{className:"text-gray-600",children:"Total Items:"}),r.jsx("span",{className:"font-semibold text-gray-900",children:b.length})]}),(0,r.jsxs)("button",{onClick:()=>{b.forEach(e=>{let{wishlistItemId:s,...t}=e;j({type:"ADD_ITEM",payload:t})}),w("All wishlist items added to cart","success")},className:"w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2",children:[r.jsx(m.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Add All to Cart"})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"You might also like"}),r.jsx("div",{className:"space-y-4",children:C.slice(0,2).map(e=>(0,r.jsxs)(n.default,{href:`/product/${e.slug||e.id}`,className:"flex items-center space-x-3 group",children:[r.jsx("div",{className:"w-16 h-16 relative rounded-md overflow-hidden flex-shrink-0",children:r.jsx(o.default,{src:e.image||"/placeholder-product.jpg",alt:e.name,fill:!0,className:"object-cover",sizes:"64px"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-800 group-hover:text-green-600",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price]})]})]},e.id))})]})]})})]}):(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx("div",{className:"w-32 h-32 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8",children:r.jsx(h.Z,{className:"w-16 h-16 text-green-500"})}),r.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Your wishlist is empty"}),r.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Looks like you haven't added anything yet. Let's change that!"}),r.jsx(n.default,{href:"/shop",className:"inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg",children:"Explore Products"})]})]})})}},84699:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(19510),i=t(40304);let l=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Wishlist.tsx#default`);function a(){return r.jsx(i.Z,{children:r.jsx(l,{})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,3757,434,9694,9536,5090,561],()=>t(63689));module.exports=r})();