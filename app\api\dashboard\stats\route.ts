import { NextResponse } from "next/server";
import { connectDB, Product, Category, User, Order } from "@/app/lib/db";
import { logger } from "@/app/lib/logger";

// GET /api/dashboard/stats - Get dashboard statistics
// NOTE: This endpoint is temporarily using mock data during Prisma to MongoDB migration
export async function GET() {
  try {
    await connectDB();

    // Get counts for all main entities using MongoDB
    const [
      totalProducts,
      totalCategories,
      totalUsers,
      totalCustomers,
      totalOrders,
      activeProducts,
      featuredProducts,
    ] = await Promise.all([
      Product.countDocuments(),
      Category.countDocuments(),
      User.countDocuments(),
      User.countDocuments({ role: "CUSTOMER" }),
      Order.countDocuments(),
      Product.countDocuments({ isActive: true }),
      Product.countDocuments({ isFeatured: true }),
    ]);

    // Get recent products with populated category
    const recentProducts = await Product.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('categoryId', 'name')
      .lean();

    // Get recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select("-password")
      .lean();

    // Calculate some basic stats
    const stats = {
      overview: {
        totalProducts,
        totalCategories,
        totalUsers,
        totalCustomers,
        totalOrders,
        activeProducts,
        featuredProducts,
      },
      recent: {
        products: recentProducts,
        users: recentUsers,
      },
      growth: {
        // These would be calculated based on time periods in a real app
        productsGrowth: "+12.5%",
        categoriesGrowth: "+5.2%",
        usersGrowth: "+8.1%",
        ordersGrowth: "+15.3%",
      },
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    logger.error("Error fetching dashboard stats:", error as Error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch dashboard statistics" },
      { status: 500 },
    );
  }
}
