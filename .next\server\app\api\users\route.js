"use strict";(()=>{var e={};e.id=5701,e.ids=[5701],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27273:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>S,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{GET:()=>c,POST:()=>m});var o=r(49303),n=r(88716),s=r(60670),a=r(87070),d=r(89456),u=r(81515);async function c(e){try{await (0,d.Z)();let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"10"),o=t.get("role"),n=t.get("search"),s=(r-1)*i,c={};o&&(c.role=o),n&&(c.$or=[{name:{$regex:n,$options:"i"}},{email:{$regex:n,$options:"i"}}]);let[m,p]=await Promise.all([u.n5.find(c).skip(s).limit(i).sort({createdAt:-1}).lean(),u.n5.countDocuments(c)]),l=await Promise.all(m.map(async e=>{let t=await u.Order.countDocuments({userId:String(e._id)});return{...e,_count:{orders:t}}}));return a.NextResponse.json({success:!0,data:l,pagination:{page:r,limit:i,total:p,pages:Math.ceil(p/i)}})}catch(e){return console.error("Error fetching users:",e),a.NextResponse.json({success:!1,error:"Failed to fetch users"},{status:500})}}async function m(e){try{await (0,d.Z)();let{name:t,email:r,phone:i,role:o="CUSTOMER"}=await e.json(),n=await u.n5.create({name:t,email:r,phone:i,role:o}),s=await u.Order.countDocuments({userId:n._id}),c={...n.toObject(),_count:{orders:s}};return a.NextResponse.json({success:!0,data:c,message:"User created successfully"})}catch(e){return console.error("Error creating user:",e),a.NextResponse.json({success:!1,error:"Failed to create user"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:l,staticGenerationAsyncStorage:y,serverHooks:g}=p,S="/api/users/route";function h(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:y})}},81515:(e,t,r)=>{r.d(t,{Cq:()=>j,Dd:()=>R,Order:()=>P,P_:()=>x,Pp:()=>M,Th:()=>C,Vv:()=>k,WD:()=>T,gc:()=>E,hQ:()=>B,kL:()=>v,mA:()=>U,n5:()=>N,nW:()=>w,p1:()=>L,qN:()=>D,wV:()=>A,xs:()=>O});var i=r(11185),o=r.n(i);let n=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),s=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),a=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),c=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),q=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),I=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),b=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=o().models.User||o().model("User",n),O=o().models.Product||o().model("Product",s),T=o().models.Category||o().model("Category",a),P=o().models.Order||o().model("Order",d),w=o().models.HomepageSetting||o().model("HomepageSetting",u),E=o().models.Testimonial||o().model("Testimonial",c),D=o().models.ProductImage||o().model("ProductImage",m),C=o().models.ProductVariant||o().model("ProductVariant",p),j=o().models.Review||o().model("Review",l),v=o().models.Address||o().model("Address",y),R=o().models.OrderItem||o().model("OrderItem",g),x=o().models.Notification||o().model("Notification",S),A=o().models.Coupon||o().model("Coupon",h);o().models.Wishlist||o().model("Wishlist",f);let U=o().models.Newsletter||o().model("Newsletter",q),M=o().models.ProductCategory||o().model("ProductCategory",I),B=o().models.WishlistItem||o().model("WishlistItem",b),F=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),k=o().models.NotificationTemplate||o().model("NotificationTemplate",F),G=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),L=o().models.Enquiry||o().model("Enquiry",G)},89456:(e,t,r)=>{r.d(t,{Z:()=>a});var i=r(11185),o=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let a=async function(){if(s.conn)return s.conn;s.promise||(s.promise=o().connect(n,{bufferCommands:!1}));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(27273));module.exports=i})();