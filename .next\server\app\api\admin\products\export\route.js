"use strict";(()=>{var e={};e.id=5493,e.ids=[5493],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},41526:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>d});var o=r(49303),n=r(88716),s=r(60670),i=r(87070),p=r(75571),c=r(95306),u=r(89456),l=r(81515);async function d(e){try{let t=await (0,p.getServerSession)(c.L);if(!t||"ADMIN"!==t.user.role)return i.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});await (0,u.Z)();let r=await l.xs.find({}).populate([{path:"images"},{path:"categoryId",model:l.WD},{path:"reviews",populate:{path:"userId",select:"name email"}},{path:"faqs"},{path:"variants"}]).sort({createdAt:-1}).lean(),a={exportDate:new Date().toISOString(),totalProducts:r.length,products:r.map(e=>{let t=e.categoryId&&"object"==typeof e.categoryId?e.categoryId:null;return{name:e.name,slug:e.slug,description:e.description||"",shortDescription:e.shortDescription||"",price:e.price||0,comparePrice:e.comparePrice||null,isFeatured:e.isFeatured||!1,isActive:!1!==e.isActive,categoryNames:t?[t.name]:[],images:e.images?e.images.map(t=>({url:t.url,alt:t.alt||e.name,position:t.position||0})):[],variations:e.variants?e.variants.map(e=>({name:e.name,value:e.value,price:e.price||0})):[],metaTitle:e.metaTitle||"",metaDescription:e.metaDescription||"",metaKeywords:e.metaKeywords||"",tags:e.tags||[]}})},o=e.nextUrl.searchParams.get("format")||"json";if("csv"===o){let e=r.map(e=>{let t=e.images?e.images.map(e=>e.url).join(";"):"",r=e.variants?JSON.stringify(e.variants.map(e=>({name:e.name,value:e.value,price:e.price||0}))):"",a=e.categoryId&&"object"==typeof e.categoryId?e.categoryId:null,o=a?a.name:"";return[`"${e.name.replace(/"/g,'""')}"`,`"${e.slug?.replace(/"/g,'""')||""}"`,`"${(e.description||"").replace(/"/g,'""')}"`,`"${(e.shortDescription||"").replace(/"/g,'""')}"`,e.price||0,e.comparePrice||"",o,o,e.isFeatured?"yes":"no",e.isActive?"yes":"no",`"${t}"`,`"${r.replace(/"/g,'""')}"`].join(",")}),t=["Name,Slug,Description,Short Description,Price,Compare Price,Category,Categories,Featured,Active,Images,Variations",...e].join("\n"),a=`products-backup-${new Date().toISOString().split("T")[0]}.csv`;return new i.NextResponse(t,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="${a}"`,"Cache-Control":"no-cache"}})}let n=`products-backup-${new Date().toISOString().split("T")[0]}.json`;return new i.NextResponse(JSON.stringify(a,null,2),{status:200,headers:{"Content-Type":"application/json","Content-Disposition":`attachment; filename="${n}"`,"Cache-Control":"no-cache"}})}catch(e){return console.error("Error exporting products:",e),i.NextResponse.json({success:!1,error:"Failed to export products"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/products/export/route",pathname:"/api/admin/products/export",filename:"route",bundlePath:"app/api/admin/products/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\products\\export\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:y}=m,x="/api/admin/products/export/route";function v(){return(0,s.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:f})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=o?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(a,n,i):a[n]=e[n]}return a.default=e,r&&r.set(e,a),a}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,5972,8691,2830,5306],()=>r(41526));module.exports=a})();