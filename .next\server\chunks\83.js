"use strict";exports.id=83,exports.ids=[83],exports.modules={95921:(e,t,o)=>{o.d(t,{LS:()=>l,Pi:()=>c,bn:()=>p,jC:()=>u});var r=o(55245),s=o(54211);async function n(e){let t=process.env.BREVO_API_KEY;if(!t)throw Error("BREVO_API_KEY is not configured");let o={sender:{name:process.env.FROM_NAME||"Herbalicious",email:process.env.FROM_EMAIL||"<EMAIL>"},to:[{email:e.to}],subject:e.subject,htmlContent:e.html},r=await fetch("https://api.brevo.com/v3/smtp/email",{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","api-key":t},body:JSON.stringify(o)});if(!r.ok){let t=await r.text();throw s.kg.emailError(e.to,e.subject,Error(`Brevo API error: ${r.status} - ${t}`)),Error(`Failed to send email via Brevo API: ${r.status}`)}s.kg.emailSent(e.to,e.subject,"brevo-api")}let a=()=>{if(process.env.SMTP_HOST)return r.createTransport({host:process.env.SMTP_HOST,port:parseInt(process.env.SMTP_PORT||"587"),secure:!1,auth:{user:process.env.SMTP_USER,pass:process.env.SMTP_PASS}});throw Error("No email configuration found")};async function i(e){let t=a(),o={from:e.from||process.env.FROM_EMAIL||"<EMAIL>",to:e.to,subject:e.subject,html:e.html};await t.sendMail(o),s.kg.emailSent(e.to,e.subject,"smtp")}async function d(e){try{if(process.env.BREVO_API_KEY){await n(e);return}await i(e)}catch(t){throw s.kg.emailError(e.to,e.subject,t),t}}async function l(e,t){let o=`${process.env.NEXTAUTH_URL}/reset-password?token=${t}`,r=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Password Reset Request</h2>
      <p>You have requested to reset your password for your Herbalicious account.</p>
      <p>Click the button below to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${o}" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Reset Password
        </a>
      </div>
      <p>If the button doesn't work, copy and paste this link into your browser:</p>
      <p style="word-break: break-all; color: #666;">${o}</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        This link will expire in 1 hour. If you didn't request this password reset, please ignore this email.
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await d({to:e,subject:"Password Reset Request - Herbalicious",html:r})}async function c(e,t){let o=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Welcome to Herbalicious, ${t}!</h2>
      <p>Thank you for joining our community of natural health enthusiasts.</p>
      <p>We're excited to have you on board and look forward to helping you discover the best herbal products for your wellness journey.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXTAUTH_URL}/shop" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Start Shopping
        </a>
      </div>
      <p>If you have any questions, feel free to contact our support team.</p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await d({to:e,subject:"Welcome to Herbalicious!",html:o})}async function p(e,t){let o=t.items.map(e=>`
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${e.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${e.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">₹${e.price.toFixed(2)}</td>
    </tr>
  `).join(""),r=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Order Confirmation</h2>
      <p>Thank you for your order! Here are the details:</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin: 0 0 10px 0;">Order #${t.orderId}</h3>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Item</th>
            <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Qty</th>
            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Price</th>
          </tr>
        </thead>
        <tbody>
          ${o}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2" style="padding: 12px; font-weight: bold; border-top: 2px solid #ddd;">Total:</td>
            <td style="padding: 12px; font-weight: bold; text-align: right; border-top: 2px solid #ddd;">₹${t.total.toFixed(2)}</td>
          </tr>
        </tfoot>
      </table>
      
      <div style="margin: 20px 0;">
        <h4>Shipping Address:</h4>
        <p style="background-color: #f9f9f9; padding: 10px; border-radius: 5px;">${t.shippingAddress}</p>
      </div>
      
      <p>We'll send you another email when your order ships.</p>
      
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;await d({to:e,subject:`Order Confirmation - ${t.orderId}`,html:r})}async function u(e){let t=`
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Email Configuration Test</h2>
      <p>This is a test email to verify that your email configuration is working correctly.</p>
      <p>If you received this email, your Brevo integration is working properly!</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        Sent at: ${new Date().toISOString()}
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious for testing purposes.
      </p>
    </div>
  `;await d({to:e,subject:"Email Configuration Test - Herbalicious",html:t})}},84875:(e,t,o)=>{o.d(t,{AY:()=>c,M_:()=>d,_7:()=>i,dR:()=>l,gz:()=>n,lm:()=>m,p8:()=>a});var r=o(87070),s=o(29489);class n extends Error{constructor(e,t=500,o="INTERNAL_ERROR",r){super(e),this.statusCode=t,this.code=o,this.details=r,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class a extends n{constructor(e,t){super(e,400,"VALIDATION_ERROR",t),this.name="ValidationError"}}class i extends n{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class d extends n{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class l extends n{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class c extends n{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class p extends n{constructor(e,t){super(e,500,"DATABASE_ERROR",t),this.name="DatabaseError"}}function u(e){let t={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return t[e]||t.INTERNAL_ERROR}function m(e){return async(...t)=>{try{return await e(...t)}catch(e){return function(e){let t=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:t,error:e}),e instanceof n){let o={success:!1,error:{code:e.code,message:u(e.code),requestId:t,...!1}};return r.NextResponse.json(o,{status:e.statusCode})}if(e instanceof s.j){let o=new a("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),s={success:!1,error:{code:o.code,message:"Validation failed",requestId:t,...!1}};return r.NextResponse.json(s,{status:o.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let o=function(e){if(11e3===e.code||"MongoServerError"===e.name){let t=Object.keys(e.keyPattern||{})[0]||"field";return new c(`${t} already exists`)}return"ValidationError"===e.name?new a("Validation failed"):"CastError"===e.name?new a("Invalid data format"):"DocumentNotFoundError"===e.name?new l:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new p("Database connection failed"):new p("Database operation failed",{name:e.name,message:e.message})}(e),s={success:!1,error:{code:o.code,message:u(o.code),requestId:t,...!1}};return r.NextResponse.json(s,{status:o.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let o=function(e){switch(e.code){case"P2002":let t=e.meta?.target?.[0]||"field";return new c(`${t} already exists`);case"P2003":let o=e.meta?.constraint;if(o?.includes("userId"))return new i("Invalid user session");return new a("Invalid reference to related record");case"P2025":case"P2001":return new l;case"P2014":return new a("Missing required relationship");case"P2000":return new a("Input value is too long");case"P2004":return new a("Data constraint violation");default:return new p("Database operation failed",{code:e.code,message:e.message})}}(e),s={success:!1,error:{code:o.code,message:u(o.code),requestId:t,...!1}};return r.NextResponse.json(s,{status:o.statusCode})}}let o={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:t,...!1}};return r.NextResponse.json(o,{status:500})}(e)}}}}};