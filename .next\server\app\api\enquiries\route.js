"use strict";(()=>{var e={};e.id=7726,e.ids=[7726],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},39646:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>j,patchFetch:()=>m,requestAsyncStorage:()=>g,routeModule:()=>x,serverHooks:()=>q,staticGenerationAsyncStorage:()=>y});var n={};r.r(n),r.d(n,{GET:()=>f,POST:()=>d});var s=r(49303),o=r(88716),i=r(60670),a=r(87070),u=r(89456),p=r(81515),c=r(75571),l=r(95306);async function d(e){try{let{name:t,email:r,subject:n,message:s}=await e.json();if(!t||!r||!n||!s)return a.NextResponse.json({error:"All fields are required"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))return a.NextResponse.json({error:"Invalid email format"},{status:400});await (0,u.Z)();let o=await p.p1.create({name:t,email:r,subject:n,message:s,status:"NEW"});return a.NextResponse.json({success:!0,message:"Your enquiry has been submitted successfully. We will get back to you within 24 hours.",enquiry:{id:o.id,createdAt:o.createdAt}})}catch(e){return console.error("Error creating enquiry:",e),a.NextResponse.json({error:"Failed to submit enquiry. Please try again later."},{status:500})}}async function f(e){try{let t=await (0,c.getServerSession)(l.L);if(!t?.user||"ADMIN"!==t.user.role)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),n=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),o=r.get("status"),i=r.get("search")||"",d=(n-1)*s;await (0,u.Z)();let f={};o&&"ALL"!==o&&(f.status=o),i&&(f.$or=[{name:{$regex:i,$options:"i"}},{email:{$regex:i,$options:"i"}},{subject:{$regex:i,$options:"i"}},{message:{$regex:i,$options:"i"}}]);let x=await p.p1.countDocuments(f),g=await p.p1.find(f).skip(d).limit(s).sort({createdAt:-1}).lean();return a.NextResponse.json({enquiries:g,pagination:{page:n,limit:s,total:x,totalPages:Math.ceil(x/s)}})}catch(e){return console.error("Error fetching enquiries:",e),a.NextResponse.json({error:"Failed to fetch enquiries"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/enquiries/route",pathname:"/api/enquiries",filename:"route",bundlePath:"app/api/enquiries/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\enquiries\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:q}=x,j="/api/enquiries/route";function m(){return(0,i.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:y})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var s=r(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(45609));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9276,5972,8691,2830,5306],()=>r(39646));module.exports=n})();