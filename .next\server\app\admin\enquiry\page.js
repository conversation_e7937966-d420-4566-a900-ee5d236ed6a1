(()=>{var e={};e.id=328,e.ids=[328],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},87667:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(63145),t(90596),t(37254),t(35866);var r=t(23191),a=t(88716),l=t(37922),i=t.n(l),n=t(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d=["",{children:["admin",{children:["enquiry",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,63145)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\enquiry\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\enquiry\\page.tsx"],u="/admin/enquiry/page",x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/enquiry/page",pathname:"/admin/enquiry",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29042:(e,s,t)=>{Promise.resolve().then(t.bind(t,32112))},32112:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(10326),a=t(17577),l=t(77109),i=t(35047),n=t(87888),o=t(48998),d=t(54659),c=t(91470),u=t(88307),x=t(41137),m=t(11890),p=t(39183),h=t(79635),g=t(5932),y=t(37358),j=t(40617);let b={NEW:{label:"New",color:"bg-blue-100 text-blue-800",icon:n.Z},IN_PROGRESS:{label:"In Progress",color:"bg-yellow-100 text-yellow-800",icon:o.Z},RESOLVED:{label:"Resolved",color:"bg-green-100 text-green-800",icon:d.Z},CLOSED:{label:"Closed",color:"bg-gray-100 text-gray-800",icon:c.Z}};function v(){let{data:e,status:s}=(0,l.useSession)();(0,i.useRouter)();let[t,n]=(0,a.useState)([]),[o,d]=(0,a.useState)(!0),[c,v]=(0,a.useState)(null),[f,N]=(0,a.useState)(""),[w,q]=(0,a.useState)("ALL"),[k,C]=(0,a.useState)(1),[Z,S]=(0,a.useState)(1),[E,P]=(0,a.useState)(!1),_=async(e,s)=>{try{P(!0);let t=await fetch(`/api/enquiries/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error("Failed to update enquiry");let r=await t.json();n(s=>s.map(s=>s.id===e?r.enquiry:s)),c?.id===e&&v(r.enquiry)}catch(e){console.error("Error updating enquiry:",e)}finally{P(!1)}},A=async e=>{if(confirm("Are you sure you want to delete this enquiry?"))try{if(!(await fetch(`/api/enquiries/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete enquiry");n(s=>s.filter(s=>s.id!==e)),c?.id===e&&v(null)}catch(e){console.error("Error deleting enquiry:",e)}},D=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return"loading"===s||o?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):(0,r.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Contact Enquiries"}),r.jsx("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[r.jsx("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(u.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),r.jsx("input",{type:"text",placeholder:"Search by name, email, subject, or message...",value:f,onChange:e=>N(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(x.Z,{className:"text-gray-400 h-5 w-5"}),(0,r.jsxs)("select",{value:w,onChange:e=>q(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[r.jsx("option",{value:"ALL",children:"All Status"}),r.jsx("option",{value:"NEW",children:"New"}),r.jsx("option",{value:"IN_PROGRESS",children:"In Progress"}),r.jsx("option",{value:"RESOLVED",children:"Resolved"}),r.jsx("option",{value:"CLOSED",children:"Closed"})]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[r.jsx("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[r.jsx("div",{className:"p-4 border-b",children:r.jsx("h2",{className:"font-semibold text-gray-900",children:"Enquiries"})}),r.jsx("div",{className:"divide-y",children:t.map(e=>{let s=b[e.status].icon;return(0,r.jsxs)("div",{onClick:()=>v(e),className:`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${c?.id===e.id?"bg-green-50":""}`,children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[r.jsx("h3",{className:"font-medium text-gray-900 truncate",children:e.name}),(0,r.jsxs)("span",{className:`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${b[e.status].color}`,children:[r.jsx(s,{className:"h-3 w-3"}),b[e.status].label]})]}),r.jsx("p",{className:"text-sm text-gray-600 truncate",children:e.subject}),r.jsx("p",{className:"text-xs text-gray-500 mt-1",children:D(e.createdAt)})]},e.id)})}),Z>1&&(0,r.jsxs)("div",{className:"p-4 border-t flex items-center justify-between",children:[r.jsx("button",{onClick:()=>C(e=>Math.max(1,e-1)),disabled:1===k,className:"p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:r.jsx(m.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",k," of ",Z]}),r.jsx("button",{onClick:()=>C(e=>Math.min(Z,e+1)),disabled:k===Z,className:"p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:r.jsx(p.Z,{className:"h-5 w-5"})})]})]})}),r.jsx("div",{className:"lg:col-span-2",children:c?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[r.jsx("div",{className:"p-6 border-b",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:c.subject}),(0,r.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[r.jsx(h.Z,{className:"h-4 w-4"}),r.jsx("span",{children:c.name})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[r.jsx(g.Z,{className:"h-4 w-4"}),r.jsx("a",{href:`mailto:${c.email}`,className:"text-green-600 hover:underline",children:c.email})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[r.jsx(y.Z,{className:"h-4 w-4"}),r.jsx("span",{children:D(c.createdAt)})]})]})]}),r.jsx("select",{value:c.status,onChange:e=>_(c.id,{status:e.target.value}),disabled:E,className:`px-3 py-1 rounded-full text-sm font-medium ${b[c.status].color} border-0 focus:ring-2 focus:ring-green-500`,children:Object.entries(b).map(([e,s])=>r.jsx("option",{value:e,children:s.label},e))})]})}),(0,r.jsxs)("div",{className:"p-6 border-b",children:[(0,r.jsxs)("h3",{className:"font-medium text-gray-900 mb-3 flex items-center gap-2",children:[r.jsx(j.Z,{className:"h-4 w-4"}),"Message"]}),r.jsx("p",{className:"text-gray-700 whitespace-pre-wrap",children:c.message})]}),(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Admin Notes"}),r.jsx("textarea",{value:c.notes||"",onChange:e=>v({...c,notes:e.target.value}),placeholder:"Add internal notes about this enquiry...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none",rows:4}),(0,r.jsxs)("div",{className:"mt-4 flex gap-3",children:[r.jsx("button",{onClick:()=>_(c.id,{notes:c.notes||""}),disabled:E,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Save Notes"}),r.jsx("button",{onClick:()=>A(c.id),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Delete Enquiry"})]})]})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[r.jsx(j.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500",children:"Select an enquiry to view details"})]})})]})]})}},87888:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},11890:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},39183:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},41137:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},88307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},79635:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63145:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\enquiry\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,3757,434,9536,4855],()=>t(87667));module.exports=r})();