(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4966],{71029:function(e,r,s){Promise.resolve().then(s.bind(s,28250)),Promise.resolve().then(s.bind(s,71795))},71795:function(e,r,s){"use strict";var t=s(57437),a=s(2265),n=s(27648),l=s(99376),o=s(80605),i=s(89547),c=s(92369),d=s(89345),m=s(66337),u=s(87769),x=s(42208);r.default=()=>{let e=(0,l.useRouter)(),[r,s]=(0,a.useState)({name:"",email:"",password:"",confirmPassword:""}),[h,f]=(0,a.useState)(!1),[g,p]=(0,a.useState)(!1),[y,b]=(0,a.useState)({}),[j,w]=(0,a.useState)(!1),N=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t})),y[r]&&b(e=>({...e,[r]:""}))},v=()=>{let e={};return r.name||(e.name="Name is required"),r.email?/\S+@\S+\.\S+/.test(r.email)||(e.email="Please enter a valid email"):e.email="Email is required",r.password?r.password.length<8&&(e.password="Password must be at least 8 characters"):e.password="Password is required",r.confirmPassword?r.password!==r.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your password",b(e),0===Object.keys(e).length},k=async s=>{if(s.preventDefault(),v()){w(!0);try{let s=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r.name,email:r.email,password:r.password})}),t=await s.json();if(s.ok){let s=await (0,o.signIn)("credentials",{email:r.email,password:r.password,redirect:!1});(null==s?void 0:s.error)?b({general:"An error occurred during login after signup"}):e.push("/profile")}else b({general:t.error||"An error occurred during signup"})}catch(e){b({general:"An error occurred during signup"})}finally{w(!1)}}},P=e=>{(0,o.signIn)(e,{callbackUrl:"/profile"})};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center py-12 px-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(i.Z,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-3",children:"Create Account"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Join Herbalicious for natural skincare"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-3xl p-8 shadow-lg border border-gray-100",children:[(0,t.jsxs)("form",{onSubmit:k,className:"space-y-6 mb-6",children:[y.general&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:y.general}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:"text",name:"name",value:r.name,onChange:N,placeholder:"Enter your full name",className:"w-full pl-12 pr-4 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ".concat(y.name?"border-red-500":"border-gray-300")})]}),y.name&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:y.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:"email",name:"email",value:r.email,onChange:N,placeholder:"Enter your email address",className:"w-full pl-12 pr-4 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ".concat(y.email?"border-red-500":"border-gray-300")})]}),y.email&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:y.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:h?"text":"password",name:"password",value:r.password,onChange:N,placeholder:"Create a strong password",className:"w-full pl-12 pr-14 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ".concat(y.password?"border-red-500":"border-gray-300")}),(0,t.jsx)("button",{type:"button",onClick:()=>f(!h),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:h?(0,t.jsx)(u.Z,{className:"w-5 h-5"}):(0,t.jsx)(x.Z,{className:"w-5 h-5"})})]}),y.password&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:y.password})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:g?"text":"password",name:"confirmPassword",value:r.confirmPassword,onChange:N,placeholder:"Confirm your password",className:"w-full pl-12 pr-14 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 text-lg ".concat(y.confirmPassword?"border-red-500":"border-gray-300")}),(0,t.jsx)("button",{type:"button",onClick:()=>p(!g),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:g?(0,t.jsx)(u.Z,{className:"w-5 h-5"}):(0,t.jsx)(x.Z,{className:"w-5 h-5"})})]}),y.confirmPassword&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:y.confirmPassword})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("input",{type:"checkbox",className:"mt-1 rounded text-green-600 focus:ring-green-500",required:!0}),(0,t.jsxs)("span",{className:"ml-3 text-gray-600",children:["I agree to the"," ",(0,t.jsx)(n.default,{href:"/terms",className:"text-green-600 hover:text-green-700 font-medium",children:"Terms of Service"})," ","and"," ",(0,t.jsx)(n.default,{href:"/privacy",className:"text-green-600 hover:text-green-700 font-medium",children:"Privacy Policy"})]})]}),(0,t.jsx)("button",{type:"submit",disabled:j,className:"w-full bg-green-600 text-white py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center text-lg",children:j?(0,t.jsx)("div",{className:"w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"}):"Create Account"})]}),(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-4 bg-white text-gray-500",children:"Or sign up with"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsx)("button",{onClick:()=>P("google"),className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors font-medium",children:"Google"}),(0,t.jsx)("button",{onClick:()=>P("facebook"),className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors font-medium",children:"Facebook"})]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",(0,t.jsx)(n.default,{href:"/login",className:"text-green-600 font-semibold hover:text-green-700",children:"Sign in"})]})})]})]})})}},87769:function(e,r,s){"use strict";s.d(r,{Z:function(){return t}});let t=(0,s(39763).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},42208:function(e,r,s){"use strict";s.d(r,{Z:function(){return t}});let t=(0,s(39763).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89547:function(e,r,s){"use strict";s.d(r,{Z:function(){return t}});let t=(0,s(39763).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},66337:function(e,r,s){"use strict";s.d(r,{Z:function(){return t}});let t=(0,s(39763).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])}},function(e){e.O(0,[7349,7648,5644,1682,8250,2971,2117,1744],function(){return e(e.s=71029)}),_N_E=e.O()}]);