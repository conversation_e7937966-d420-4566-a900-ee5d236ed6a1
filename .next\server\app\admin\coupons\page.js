(()=>{var e={};e.id=7460,e.ids=[7460],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},86462:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(45482),s(90596),s(37254),s(35866);var a=s(23191),r=s(88716),i=s(37922),n=s.n(i),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["admin",{children:["coupons",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,45482)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\coupons\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\coupons\\page.tsx"],u="/admin/coupons/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/coupons/page",pathname:"/admin/coupons",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93840:(e,t,s)=>{Promise.resolve().then(s.bind(s,69036))},75671:(e,t,s)=>{Promise.resolve().then(s.bind(s,2802))},69036:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(10326),r=s(17577),i=s(77109),n=s(35047),l=s(75290),o=s(87888),c=s(83855),d=s(40765),u=s(91216),m=s(12714),x=s(69508),g=s(98091),p=s(94019);function h(){let{data:e,status:t}=(0,i.useSession)();(0,n.useRouter)();let[s,h]=(0,r.useState)([]),[y,b]=(0,r.useState)(!0),[f,j]=(0,r.useState)(!1),[v,N]=(0,r.useState)(!1),[k,w]=(0,r.useState)(null),[C,E]=(0,r.useState)([]),[P,A]=(0,r.useState)([]),[S,Z]=(0,r.useState)({code:"",name:"",description:"",type:"STORE_WIDE",discountType:"PERCENTAGE",discountValue:0,minimumAmount:void 0,maximumDiscount:void 0,usageLimit:void 0,userUsageLimit:void 0,isActive:!0,isStackable:!1,showInModule:!1,validFrom:new Date().toISOString().split("T")[0],validUntil:void 0,applicableProducts:[],applicableCategories:[],excludedProducts:[],excludedCategories:[],customerSegments:[],buyQuantity:1,getQuantity:1,buyProducts:[],buyCategories:[],getProducts:[],getCategories:[],maxApplications:1,discountApplication:"FREE",getDiscountValue:0}),T=(0,r.useCallback)(async()=>{try{b(!0);let e=await fetch("/api/coupons?limit=100");if(e.ok){let t=await e.json();h(t.coupons)}}catch(e){console.error("Error fetching coupons:",e)}finally{b(!1)}},[]);(0,r.useCallback)(async()=>{try{let e=await fetch("/api/products?limit=100");if(e.ok){let t=await e.json();console.log("Products API response:",t);let s=t.data||[];console.log("Products list:",s),E(s)}else console.error("Failed to fetch products:",e.status)}catch(e){console.error("Error fetching products:",e)}},[]),(0,r.useCallback)(async()=>{try{let e=await fetch("/api/categories");if(e.ok){let t=await e.json();console.log("Categories API response:",t);let s=t.data||t.categories||[];console.log("Categories list:",s),A(s)}else console.error("Failed to fetch categories:",e.status)}catch(e){console.error("Error fetching categories:",e)}},[]);let _=async e=>{e.preventDefault();try{let e=k?`/api/coupons/${k.id}`:"/api/coupons",t=await fetch(e,{method:k?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(S)});if(t.ok)await T(),D(),N(!1),w(null);else{let e=await t.json();alert(e.error||"Failed to save coupon")}}catch(e){console.error("Error saving coupon:",e),alert("Failed to save coupon")}},U=async e=>{if(confirm("Are you sure you want to delete this coupon?"))try{(await fetch(`/api/coupons/${e}`,{method:"DELETE"})).ok?await T():alert("Failed to delete coupon")}catch(e){console.error("Error deleting coupon:",e),alert("Failed to delete coupon")}},M=async e=>{try{(await fetch(`/api/coupons/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,isActive:!e.isActive})})).ok&&await T()}catch(e){console.error("Error updating coupon status:",e)}},D=()=>{Z({code:"",name:"",description:"",type:"STORE_WIDE",discountType:"PERCENTAGE",discountValue:0,minimumAmount:void 0,maximumDiscount:void 0,usageLimit:void 0,userUsageLimit:void 0,isActive:!0,isStackable:!1,showInModule:!1,validFrom:new Date().toISOString().split("T")[0],validUntil:void 0,applicableProducts:[],applicableCategories:[],excludedProducts:[],excludedCategories:[],customerSegments:[],buyQuantity:1,getQuantity:1,buyProducts:[],buyCategories:[],getProducts:[],getCategories:[],maxApplications:1,discountApplication:"FREE",getDiscountValue:0})},F=e=>{w(e),Z({code:e.code,name:e.name,description:e.description||"",type:e.type,discountType:e.discountType,discountValue:e.discountValue,minimumAmount:e.minimumAmount||void 0,maximumDiscount:e.maximumDiscount||void 0,usageLimit:e.usageLimit||void 0,userUsageLimit:e.userUsageLimit||void 0,isActive:e.isActive,isStackable:e.isStackable,showInModule:e.showInModule,validFrom:e.validFrom.split("T")[0],validUntil:e.validUntil?e.validUntil.split("T")[0]:void 0,applicableProducts:e.applicableProducts,applicableCategories:e.applicableCategories,excludedProducts:e.excludedProducts,excludedCategories:e.excludedCategories,customerSegments:e.customerSegments,buyQuantity:e.buyQuantity||1,getQuantity:e.getQuantity||1,buyProducts:e.buyProducts||[],buyCategories:e.buyCategories||[],getProducts:e.getProducts||[],getCategories:e.getCategories||[],maxApplications:e.maxApplications||1,discountApplication:e.discountApplication||"FREE",getDiscountValue:e.getDiscountValue||0}),N(!0)},I=e=>{if("BUY_X_GET_Y"===e.discountType){let t="FREE"===e.discountApplication?"FREE":"PERCENTAGE"===e.discountApplication?`${e.getDiscountValue}% OFF`:`₹${e.getDiscountValue} OFF`;return`Buy ${e.buyQuantity} Get ${e.getQuantity} ${t}`}switch(e.discountType){case"PERCENTAGE":return`${e.discountValue}% OFF`;case"FIXED_AMOUNT":return`₹${e.discountValue} OFF`;case"FREE_SHIPPING":return"FREE SHIPPING";default:return"DISCOUNT"}},R=e=>{if(!e.isActive)return"bg-gray-100 text-gray-700 border-gray-200";let t=new Date,s=e.validUntil?new Date(e.validUntil):null;return s&&s<t?"bg-red-100 text-red-700 border-red-200":s&&s.getTime()-t.getTime()<2592e5?"bg-orange-100 text-orange-700 border-orange-200":"bg-green-100 text-green-700 border-green-200"},G=(e,t)=>{let s=S[t]||[],a=s.includes(e)?s.filter(t=>t!==e):[...s,e];Z({...S,[t]:a})},L=(e,t)=>{let s=S[t]||[],a=s.includes(e)?s.filter(t=>t!==e):[...s,e];Z({...S,[t]:a})};return"loading"===t?a.jsx("div",{className:"min-h-screen bg-gray-50",children:a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx(l.Z,{className:"w-8 h-8 animate-spin text-green-600"})})})}):e&&e.user?.role==="ADMIN"?a.jsx("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Coupon Management"}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Create and manage discount coupons"})]}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[s.length," ",1===s.length?"coupon":"coupons"," ","total"]}),(0,a.jsxs)("button",{onClick:()=>{D(),N(!0)},className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[a.jsx(c.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Create Coupon"})]})]})}),y?a.jsx("div",{className:"flex items-center justify-center py-12",children:a.jsx(l.Z,{className:"w-8 h-8 animate-spin text-green-600"})}):0===s.length?a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(d.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No coupons found"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Get started by creating your first coupon"}),a.jsx("button",{onClick:()=>{D(),N(!0)},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:"Create Your First Coupon"})]})}):a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[a.jsx("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Coupon"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Discount"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Usage"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Validity"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[a.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200",children:e.code}),a.jsx("span",{className:"text-xs text-gray-500",children:e.type.replace("_"," ")}),e.isStackable&&a.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200",children:"Stackable"})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm font-semibold text-green-600",children:I(e)}),e.minimumAmount&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Min: ₹",e.minimumAmount]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[e.usageCount,"/",e.usageLimit||"∞"]}),a.jsx("div",{className:"text-xs text-gray-500",children:e.usageLimit?`${Math.round(e.usageCount/e.usageLimit*100)}% used`:"Unlimited"})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:new Date(e.validFrom).toLocaleDateString()}),e.validUntil&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Until"," ",new Date(e.validUntil).toLocaleDateString()]})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${R(e)}`,children:e.isActive?"Active":"Inactive"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[a.jsx("button",{onClick:()=>M(e),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:e.isActive?"Deactivate":"Activate",children:e.isActive?a.jsx(u.Z,{className:"w-5 h-5"}):a.jsx(m.Z,{className:"w-5 h-5"})}),a.jsx("button",{onClick:()=>F(e),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:"Edit",children:a.jsx(x.Z,{className:"w-5 h-5"})}),a.jsx("button",{onClick:()=>U(e.id),className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",title:"Delete",children:a.jsx(g.Z,{className:"w-5 h-5"})})]})})]},e.id))})]})})}),v&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:k?"Edit Coupon":"Create New Coupon"}),a.jsx("button",{onClick:()=>{N(!1),w(null),D()},className:"text-gray-500 hover:text-gray-700",children:a.jsx(p.Z,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("form",{onSubmit:_,className:"p-6",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Basic Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Coupon Code *"}),a.jsx("input",{type:"text",value:S.code,onChange:e=>Z({...S,code:e.target.value.toUpperCase()}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Coupon Name *"}),a.jsx("input",{type:"text",value:S.name,onChange:e=>Z({...S,name:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),a.jsx("textarea",{value:S.description,onChange:e=>Z({...S,description:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:3})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Discount Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Coupon Type *"}),(0,a.jsxs)("select",{value:S.type,onChange:e=>{let t=e.target.value,s={type:t};"BUY_X_GET_Y"===t?s.discountType="BUY_X_GET_Y":"BUY_X_GET_Y"===S.type&&"BUY_X_GET_Y"===S.discountType&&(s.discountType="PERCENTAGE"),Z({...S,...s})},className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,children:[a.jsx("option",{value:"STORE_WIDE",children:"Store Wide"}),a.jsx("option",{value:"PRODUCT_SPECIFIC",children:"Product Specific"}),a.jsx("option",{value:"CATEGORY_SPECIFIC",children:"Category Specific"}),a.jsx("option",{value:"MINIMUM_PURCHASE",children:"Minimum Purchase"}),a.jsx("option",{value:"BUNDLE_DEAL",children:"Bundle Deal"}),a.jsx("option",{value:"FIRST_TIME_CUSTOMER",children:"First Time Customer"}),a.jsx("option",{value:"LOYALTY_REWARD",children:"Loyalty Reward"}),a.jsx("option",{value:"SEASONAL",children:"Seasonal"}),a.jsx("option",{value:"BUY_X_GET_Y",children:"Buy X Get Y"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Type *"}),(0,a.jsxs)("select",{value:S.discountType,onChange:e=>Z({...S,discountType:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0,children:[a.jsx("option",{value:"PERCENTAGE",children:"Percentage"}),a.jsx("option",{value:"FIXED_AMOUNT",children:"Fixed Amount"}),a.jsx("option",{value:"FREE_SHIPPING",children:"Free Shipping"}),"BUY_X_GET_Y"===S.type&&a.jsx("option",{value:"BUY_X_GET_Y",children:"Buy X Get Y"})]})]})]}),"BUY_X_GET_Y"===S.discountType&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[a.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-3",children:"Buy X Get Y Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Buy Quantity *"}),a.jsx("input",{type:"number",value:S.buyQuantity,onChange:e=>Z({...S,buyQuantity:parseInt(e.target.value)||1}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Get Quantity *"}),a.jsx("input",{type:"number",value:S.getQuantity,onChange:e=>Z({...S,getQuantity:parseInt(e.target.value)||1}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Applications"}),a.jsx("input",{type:"number",value:S.maxApplications,onChange:e=>Z({...S,maxApplications:parseInt(e.target.value)||1}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Application *"}),(0,a.jsxs)("select",{value:S.discountApplication,onChange:e=>Z({...S,discountApplication:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[a.jsx("option",{value:"FREE",children:"Free"}),a.jsx("option",{value:"PERCENTAGE",children:"Percentage Off"}),a.jsx("option",{value:"FIXED_AMOUNT",children:"Fixed Amount Off"})]})]}),"FREE"!==S.discountApplication&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Value *"}),a.jsx("input",{type:"number",value:S.getDiscountValue,onChange:e=>Z({...S,getDiscountValue:parseFloat(e.target.value)||0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01",required:!0})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Buy Products/Categories"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Buy Products"}),a.jsx("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:C.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[a.jsx("input",{type:"checkbox",checked:S.buyProducts?.includes(e.id)||!1,onChange:()=>G(e.id,"buyProducts"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Buy Categories"}),a.jsx("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:P.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[a.jsx("input",{type:"checkbox",checked:S.buyCategories?.includes(e.id)||!1,onChange:()=>L(e.id,"buyCategories"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h5",{className:"text-sm font-medium text-gray-700 mb-2",children:"Get Products/Categories"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Get Products"}),a.jsx("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:C.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[a.jsx("input",{type:"checkbox",checked:S.getProducts?.includes(e.id)||!1,onChange:()=>G(e.id,"getProducts"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-xs text-gray-600 mb-1",children:"Select Get Categories"}),a.jsx("div",{className:"border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto",children:P.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[a.jsx("input",{type:"checkbox",checked:S.getCategories?.includes(e.id)||!1,onChange:()=>L(e.id,"getCategories"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]})]})]})]}),"BUY_X_GET_Y"!==S.discountType&&"FREE_SHIPPING"!==S.discountType&&(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Value *"}),a.jsx("input",{type:"number",value:S.discountValue,onChange:e=>Z({...S,discountValue:parseFloat(e.target.value)}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Amount"}),a.jsx("input",{type:"number",value:S.minimumAmount||"",onChange:e=>Z({...S,minimumAmount:e.target.value?parseFloat(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Maximum Discount"}),a.jsx("input",{type:"number",value:S.maximumDiscount||"",onChange:e=>Z({...S,maximumDiscount:e.target.value?parseFloat(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"0",step:"0.01"})]})]})]}),("PRODUCT_SPECIFIC"===S.type||"CATEGORY_SPECIFIC"===S.type)&&"BUY_X_GET_Y"!==S.discountType&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Applicable Items"}),"PRODUCT_SPECIFIC"===S.type&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Products"}),a.jsx("div",{className:"border border-gray-300 rounded-lg p-3 max-h-48 overflow-y-auto",children:0===C.length?a.jsx("p",{className:"text-sm text-gray-500 text-center py-4",children:"No products available"}):C.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[a.jsx("input",{type:"checkbox",checked:S.applicableProducts?.includes(e.id)||!1,onChange:()=>G(e.id,"applicableProducts"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:[e.name," - ₹",e.price]})]},e.id))})]}),"CATEGORY_SPECIFIC"===S.type&&(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Categories"}),a.jsx("div",{className:"border border-gray-300 rounded-lg p-3 max-h-48 overflow-y-auto",children:P.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 py-1",children:[a.jsx("input",{type:"checkbox",checked:S.applicableCategories?.includes(e.id)||!1,onChange:()=>L(e.id,"applicableCategories"),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Usage Limits"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Total Usage Limit"}),a.jsx("input",{type:"number",value:S.usageLimit||"",onChange:e=>Z({...S,usageLimit:e.target.value?parseInt(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Per User Usage Limit"}),a.jsx("input",{type:"number",value:S.userUsageLimit||"",onChange:e=>Z({...S,userUsageLimit:e.target.value?parseInt(e.target.value):void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",min:"1"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Validity Period"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Valid From *"}),a.jsx("input",{type:"date",value:S.validFrom,onChange:e=>Z({...S,validFrom:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Valid Until"}),a.jsx("input",{type:"date",value:S.validUntil||"",onChange:e=>Z({...S,validUntil:e.target.value||void 0}),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Settings"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:S.isActive,onChange:e=>Z({...S,isActive:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Active"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:S.isStackable,onChange:e=>Z({...S,isStackable:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Stackable"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:S.showInModule,onChange:e=>Z({...S,showInModule:e.target.checked}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),a.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Show in Module"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200",children:[a.jsx("button",{type:"button",onClick:()=>{N(!1),w(null),D()},className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[k?"Update":"Create"," Coupon"]})]})]})]})})]})}):a.jsx("div",{className:"min-h-screen bg-gray-50",children:a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(o.Z,{className:"w-5 h-5 text-red-600"}),a.jsx("p",{className:"text-red-800",children:"You don't have permission to access this page."})]})})})})}},2802:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var a=s(10326),r=s(17577),i=s.n(r),n=s(90434),l=s(35047),o=s(77109),c=s(24319),d=s(48705),u=s(34565),m=s(57671),x=s(24061),g=s(40765),p=s(40617),h=s(35351),y=s(6507),b=s(5932),f=s(71709),j=s(95920),v=s(88378),N=s(94019),k=s(90748),w=s(53080),C=s(71810);let E=({children:e})=>{let t=(0,l.usePathname)(),[s,r]=i().useState(!1),[E,P]=i().useState(!1),A=async()=>{if(!E)try{P(!0),r(!1),await (0,o.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},S=[{href:"/admin",label:"Dashboard",icon:c.Z},{href:"/admin/products",label:"Products",icon:d.Z},{href:"/admin/categories",label:"Categories",icon:u.Z},{href:"/admin/orders",label:"Orders",icon:m.Z},{href:"/admin/customers",label:"Customers",icon:x.Z},{href:"/admin/coupons",label:"Coupons",icon:g.Z},{href:"/admin/reviews",label:"Reviews",icon:p.Z},{href:"/admin/enquiry",label:"Enquiries",icon:h.Z},{href:"/admin/notifications",label:"Notifications",icon:y.Z},{href:"/admin/newsletter",label:"Newsletter",icon:b.Z},{href:"/admin/media",label:"Media",icon:f.Z},{href:"/admin/homepage",label:"Homepage",icon:j.Z},{href:"/admin/settings",label:"Settings",icon:v.Z}],Z=e=>"/admin"===e?"/admin"===t:t.startsWith(e);return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:a.jsx("button",{onClick:()=>r(!s),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:s?a.jsx(N.Z,{className:"w-6 h-6 text-gray-600"}):a.jsx(k.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,a.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${s?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[a.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:a.jsx(w.Z,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),a.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),a.jsx("nav",{className:"mt-6 px-3",children:S.map(e=>{let t=e.icon,s=Z(e.href);return(0,a.jsxs)(n.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${s?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>r(!1),children:[a.jsx(t,{className:`w-5 h-5 mr-3 ${s?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,a.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[a.jsx(n.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,a.jsxs)("button",{onClick:A,disabled:E,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(C.Z,{className:"w-4 h-4 mr-2"}),E?"Logging out...":"Logout"]})]})]}),a.jsx("main",{className:"lg:ml-64",children:a.jsx("div",{className:"p-4 lg:p-8",children:e})}),s&&a.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>r(!1)})]})}},87888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},91216:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},35351:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},75290:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},83855:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},69508:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},45482:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\coupons\page.tsx#default`)},90596:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,3757,434,9536],()=>s(86462));module.exports=a})();