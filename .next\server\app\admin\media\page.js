(()=>{var e={};e.id=1565,e.ids=[1565],e.modules={21841:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},32694:e=>{"use strict";e.exports=require("http2")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},84492:e=>{"use strict";e.exports=require("node:stream")},45659:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>c}),t(99185),t(90596),t(37254),t(35866);var r=t(23191),a=t(88716),l=t(37922),i=t.n(l),n=t(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c=["",{children:["admin",{children:["media",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,99185)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\media\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\media\\page.tsx"],x="/admin/media/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/media/page",pathname:"/admin/media",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},75671:(e,s,t)=>{Promise.resolve().then(t.bind(t,2802))},43136:(e,s,t)=>{Promise.resolve().then(t.bind(t,80162))},2802:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var r=t(10326),a=t(17577),l=t.n(a),i=t(90434),n=t(35047),o=t(77109),c=t(24319),d=t(48705),x=t(34565),m=t(57671),h=t(24061),g=t(40765),u=t(40617),p=t(35351),f=t(6507),y=t(5932),j=t(71709),b=t(95920),N=t(88378),v=t(94019),w=t(90748),k=t(53080),C=t(71810);let Z=({children:e})=>{let s=(0,n.usePathname)(),[t,a]=l().useState(!1),[Z,S]=l().useState(!1),D=async()=>{if(!Z)try{S(!0),a(!1),await (0,o.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},P=[{href:"/admin",label:"Dashboard",icon:c.Z},{href:"/admin/products",label:"Products",icon:d.Z},{href:"/admin/categories",label:"Categories",icon:x.Z},{href:"/admin/orders",label:"Orders",icon:m.Z},{href:"/admin/customers",label:"Customers",icon:h.Z},{href:"/admin/coupons",label:"Coupons",icon:g.Z},{href:"/admin/reviews",label:"Reviews",icon:u.Z},{href:"/admin/enquiry",label:"Enquiries",icon:p.Z},{href:"/admin/notifications",label:"Notifications",icon:f.Z},{href:"/admin/newsletter",label:"Newsletter",icon:y.Z},{href:"/admin/media",label:"Media",icon:j.Z},{href:"/admin/homepage",label:"Homepage",icon:b.Z},{href:"/admin/settings",label:"Settings",icon:N.Z}],_=e=>"/admin"===e?"/admin"===s:s.startsWith(e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:r.jsx("button",{onClick:()=>a(!t),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:t?r.jsx(v.Z,{className:"w-6 h-6 text-gray-600"}):r.jsx(w.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,r.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${t?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[r.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:r.jsx(k.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),r.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),r.jsx("nav",{className:"mt-6 px-3",children:P.map(e=>{let s=e.icon,t=_(e.href);return(0,r.jsxs)(i.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${t?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>a(!1),children:[r.jsx(s,{className:`w-5 h-5 mr-3 ${t?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,r.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[r.jsx(i.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,r.jsxs)("button",{onClick:D,disabled:Z,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx(C.Z,{className:"w-4 h-4 mr-2"}),Z?"Logging out...":"Logout"]})]})]}),r.jsx("main",{className:"lg:ml-64",children:r.jsx("div",{className:"p-4 lg:p-8",children:e})}),t&&r.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>a(!1)})]})}},80162:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(10326),a=t(17577),l=t(71709),i=t(49758),n=t(39572),o=t(94019),c=t(63685),d=t(43810),x=t(32933),m=t(87888),h=t(21405),g=t(88307),u=t(924),p=t(29389),f=t(98091),y=t(12714),j=t(31540),b=t(47483);let N=()=>{let[e,s]=(0,a.useState)([]),[t,N]=(0,a.useState)(!0),[v,w]=(0,a.useState)(!1),[k,C]=(0,a.useState)(""),[Z,S]=(0,a.useState)([]),[D,P]=(0,a.useState)("grid"),[_,U]=(0,a.useState)("all"),[E,$]=(0,a.useState)("all"),[q,M]=(0,a.useState)(!1),[T,L]=(0,a.useState)(null),[A,R]=(0,a.useState)(null),F=(0,a.useCallback)(async()=>{try{N(!0);let e="all"===E?void 0:E,t=`/api/media/list${e?`?folder=${e}`:""}`;console.log("Loading files from:",t);let r=await fetch(t);if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);let a=await r.json();if(a.success)s(a.files||[]),console.log(`Loaded ${a.files?.length||0} files`);else throw Error(a.error||"Failed to load files")}catch(e){console.error("Error loading files:",e),z("error",e instanceof Error?e.message:"Failed to load files"),s([])}finally{N(!1)}},[E]);(0,a.useEffect)(()=>{F()},[F]);let z=(e,s)=>{R({type:e,message:s}),setTimeout(()=>R(null),3e3)},B=async e=>{w(!0),console.log(`Starting upload of ${e.length} files`);let s=Array.from(e).map(async e=>{let s=new FormData;s.append("file",e),s.append("folder","all"===E?"uploads":E);try{console.log(`Uploading file: ${e.name} (${e.size} bytes)`);let t=await fetch("/api/media/upload",{method:"POST",body:s});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let r=await t.json();return console.log(`Upload result for ${e.name}:`,r),r}catch(s){return console.error(`Upload failed for ${e.name}:`,s),{success:!1,error:s instanceof Error?s.message:"Upload failed"}}}),t=await Promise.all(s),r=t.filter(e=>e.success).length,a=t.filter(e=>!e.success).length;r>0&&(z("success",`${r} file(s) uploaded successfully`),F()),a>0&&z("error",`${a} file(s) failed to upload`),w(!1),M(!1)},O=async e=>{if(confirm(`Are you sure you want to delete ${e.length} file(s)?`))try{console.log("Deleting files:",e);let s=e.map(async e=>{let s=await fetch("/api/media/delete",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({key:e})});if(!s.ok)throw Error(`Failed to delete ${e}: ${s.status}`);let t=await s.json();if(!t.success)throw Error(`Failed to delete ${e}: ${t.error}`);return t});await Promise.all(s),z("success",`${e.length} file(s) deleted successfully`),S([]),F()}catch(e){console.error("Delete error:",e),z("error",e instanceof Error?e.message:"Failed to delete files")}},I=e=>{navigator.clipboard.writeText(e),z("success","URL copied to clipboard")},K=e=>{let s=document.createElement("a");s.href=e.url,s.download=e.name,s.target="_blank",document.body.appendChild(s),s.click(),document.body.removeChild(s),z("success","Download started")},G=e.filter(e=>{let s=e.name.toLowerCase().includes(k.toLowerCase()),t=!0;return"all"!==_&&(t="document"===_?"document"===e.type||"other"===e.type:e.type===_),s&&t}),H=["all",...Array.from(new Set(e.map(e=>e.folder).filter(Boolean)))],V=e=>{switch(e){case"image":return r.jsx(l.Z,{className:"w-5 h-5"});case"video":return r.jsx(i.Z,{className:"w-5 h-5"});default:return r.jsx(n.Z,{className:"w-5 h-5"})}};return(0,r.jsxs)("div",{children:[A&&(0,r.jsxs)("div",{className:`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center ${"success"===A.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:["success"===A.type?r.jsx(x.Z,{className:"w-5 h-5 mr-2"}):r.jsx(m.Z,{className:"w-5 h-5 mr-2"}),A.message]}),r.jsx("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Media Library"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your images, videos, and documents"})]}),(0,r.jsxs)("div",{className:"mt-4 sm:mt-0 flex items-center space-x-3",children:[r.jsx("button",{onClick:()=>F(),disabled:t,className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:r.jsx(h.Z,{className:`w-5 h-5 text-gray-600 ${t?"animate-spin":""}`})}),(0,r.jsxs)("button",{onClick:()=>M(!0),className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[r.jsx(c.Z,{className:"w-5 h-5 mr-2"}),"Upload Files"]})]})]})}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(g.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Search files...",value:k,onChange:e=>C(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]}),r.jsx("select",{value:E,onChange:e=>$(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:H.map(e=>r.jsx("option",{value:e,children:"all"===e?"All Folders":e},e))}),(0,r.jsxs)("select",{value:_,onChange:e=>U(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",children:[r.jsx("option",{value:"all",children:"All Types"}),r.jsx("option",{value:"image",children:"Images"}),r.jsx("option",{value:"video",children:"Videos"}),r.jsx("option",{value:"document",children:"Documents"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex border border-gray-300 rounded-lg",children:[r.jsx("button",{onClick:()=>P("grid"),className:`p-2 ${"grid"===D?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"}`,children:r.jsx(u.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>P("list"),className:`p-2 ${"list"===D?"bg-green-600 text-white":"text-gray-600 hover:bg-gray-50"}`,children:r.jsx(p.Z,{className:"w-4 h-4"})})]}),Z.length>0&&r.jsx("button",{onClick:()=>O(Z),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:r.jsx(f.Z,{className:"w-5 h-5"})})]})]})}),t?r.jsx("div",{className:"flex items-center justify-center h-64",children:r.jsx(h.Z,{className:"w-8 h-8 text-gray-400 animate-spin"})}):0===G.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(l.Z,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-500 text-lg",children:"No files found"}),r.jsx("p",{className:"text-gray-400",children:"Upload some files to get started"})]}):"grid"===D?r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:G.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx("input",{type:"checkbox",checked:Z.includes(e.key),onChange:s=>{s.target.checked?S([...Z,e.key]):S(Z.filter(s=>s!==e.key))},className:"absolute top-2 left-2 rounded border-gray-300 text-green-600 focus:ring-green-500 z-10"}),r.jsx("div",{className:"aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center overflow-hidden",children:"image"===e.type?r.jsx("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover"}):r.jsx("div",{className:"text-gray-400",children:V(e.type)})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",title:e.name,children:e.name}),r.jsx("p",{className:"text-xs text-gray-500",children:(0,b.sS)(e.size)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-3",children:[r.jsx("button",{onClick:()=>L(e),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Preview",children:r.jsx(y.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>K(e),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Download",children:r.jsx(j.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>I(e.url),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Copy URL",children:r.jsx(d.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>O([e.key]),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:r.jsx(f.Z,{className:"w-4 h-4"})})]})]},e.key))}):r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[r.jsx("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left",children:r.jsx("input",{type:"checkbox",checked:Z.length===G.length&&G.length>0,onChange:e=>{e.target.checked?S(G.map(e=>e.key)):S([])},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Modified"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:G.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4",children:r.jsx("input",{type:"checkbox",checked:Z.includes(e.key),onChange:s=>{s.target.checked?S([...Z,e.key]):S(Z.filter(s=>s!==e.key))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"})}),r.jsx("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3",children:"image"===e.type?r.jsx("img",{src:e.url,alt:e.name,className:"w-full h-full object-cover rounded-lg"}):r.jsx("div",{className:"text-gray-400",children:V(e.type)})}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.folder||"Root"})]})]})}),r.jsx("td",{className:"px-6 py-4 text-sm text-gray-900 capitalize",children:e.type}),r.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:(0,b.sS)(e.size)}),r.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:new Date(e.lastModified).toLocaleDateString()}),r.jsx("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>L(e),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Preview",children:r.jsx(y.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>K(e),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Download",children:r.jsx(j.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>I(e.url),className:"p-1 text-gray-400 hover:text-green-600 transition-colors",title:"Copy URL",children:r.jsx(d.Z,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>O([e.key]),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:r.jsx(f.Z,{className:"w-4 h-4"})})]})})]},e.key))})]})})}),(0,r.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:["Showing ",G.length," of ",e.length," files",Z.length>0&&` • ${Z.length} selected`]}),r.jsx(()=>{let[e,s]=(0,a.useState)(!1);return q?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-md mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Upload Files"}),r.jsx("button",{onClick:()=>M(!1),className:"text-gray-400 hover:text-gray-600",children:r.jsx(o.Z,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${e?"border-green-500 bg-green-50":"border-gray-300"}`,onDragOver:e=>{e.preventDefault(),s(!0)},onDragLeave:()=>s(!1),onDrop:e=>{e.preventDefault(),s(!1),e.dataTransfer.files&&B(e.dataTransfer.files)},children:[r.jsx(c.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Drag and drop files here, or click to select"}),r.jsx("input",{type:"file",multiple:!0,onChange:e=>{e.target.files&&B(e.target.files)},className:"hidden",id:"file-upload",accept:"image/*,video/*,.pdf"}),r.jsx("label",{htmlFor:"file-upload",className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors cursor-pointer",children:"Select Files"})]}),r.jsx("p",{className:"text-sm text-gray-500 mt-4",children:"Supported formats: Images, Videos, PDFs (Max 10MB each)"})]})}):null},{}),r.jsx(()=>T?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:T.name}),r.jsx("button",{onClick:()=>L(null),className:"text-gray-400 hover:text-gray-600",children:r.jsx(o.Z,{className:"w-5 h-5"})})]}),r.jsx("div",{className:"mb-4",children:"image"===T.type?r.jsx("img",{src:T.url,alt:T.name,className:"max-w-full h-auto rounded-lg"}):"video"===T.type?r.jsx("video",{src:T.url,controls:!0,className:"max-w-full h-auto rounded-lg"}):r.jsx("div",{className:"flex items-center justify-center h-64 bg-gray-100 rounded-lg",children:r.jsx(n.Z,{className:"w-16 h-16 text-gray-400"})})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Size:"}),r.jsx("span",{className:"ml-2 text-gray-600",children:(0,b.sS)(T.size)})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Type:"}),r.jsx("span",{className:"ml-2 text-gray-600",children:T.type})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Modified:"}),r.jsx("span",{className:"ml-2 text-gray-600",children:new Date(T.lastModified).toLocaleDateString()})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Folder:"}),r.jsx("span",{className:"ml-2 text-gray-600",children:T.folder||"Root"})]})]}),(0,r.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[r.jsx("span",{className:"font-medium text-gray-700",children:"URL:"}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[r.jsx("input",{type:"text",value:T.url,readOnly:!0,className:"flex-1 px-3 py-1 text-sm border border-gray-300 rounded mr-2"}),r.jsx("button",{onClick:()=>I(T.url),className:"p-1 text-green-600 hover:text-green-700",children:r.jsx(d.Z,{className:"w-4 h-4"})})]})]})]})}):null,{})]})}},47483:(e,s,t)=>{"use strict";t.d(s,{sS:()=>a});var r=t(21841);function a(e){if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]}t(86947),new r.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0}),process.env.R2_BUCKET_NAME,process.env.R2_PUBLIC_URL||process.env.R2_ACCOUNT_ID},87888:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},43810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},90596:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)},99185:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\media\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,3757,434,6428,9536],()=>t(45659));module.exports=r})();