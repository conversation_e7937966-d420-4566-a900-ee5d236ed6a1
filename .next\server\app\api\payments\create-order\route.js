"use strict";(()=>{var e={};e.id=3381,e.ids=[3381],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},37117:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>A,patchFetch:()=>q,requestAsyncStorage:()=>w,routeModule:()=>x,serverHooks:()=>O,staticGenerationAsyncStorage:()=>N});var o={};t.r(o),t.d(o,{POST:()=>_});var n=t(49303),a=t(88716),s=t(60670),i=t(87070),c=t(65630),u=t(75571),d=t(95306),p=t(62822),l=t(84875),m=t(54211),R=t(8149),y=t(89585),f=t(84770),g=t.n(f),h=t(89456),I=t(81515);let E=c.Ry({cartItems:c.IX(c.Ry({productId:c.Z_(),quantity:c.Rx().min(1),price:c.Rx().min(0)})),shippingAddress:c.Ry({firstName:c.Z_().min(1),lastName:c.Z_().min(1),address1:c.Z_().min(1),address2:c.Z_().optional(),city:c.Z_().min(1),state:c.Z_().min(1),postalCode:c.Z_().min(1),country:c.Z_().min(1),phone:c.Z_().min(1)}),totalAmount:c.Rx().min(1),appliedCoupons:c.IX(c.Ry({coupon:c.Ry({id:c.Z_(),code:c.Z_(),name:c.Z_()}),discountAmount:c.Rx().min(0)})).optional().default([]),flashSaleDiscount:c.Rx().optional().default(0)}),_=(0,l.lm)(async e=>{m.kg.apiRequest("POST","/api/payments/create-order"),await (0,h.Z)(),await (0,R.er)(e,R.Xw,10);let r=await (0,u.getServerSession)(d.L);if(!r?.user?.id)throw new l._7("Authentication required");let t=await e.json(),{cartItems:o,shippingAddress:n,totalAmount:a,appliedCoupons:s,flashSaleDiscount:c}=E.parse(t);m.kg.info("Creating payment order",{userId:r.user.id,totalAmount:a,itemCount:o.length});let f=0,_=[];for(let e of o){let r=await I.xs.findById(e.productId).select("_id price name").lean();if(!r)throw new l.p8(`Product ${e.productId} not found`);let t=r.price||0;if(Math.abs(t-e.price)>.01)throw new l.p8(`Price mismatch for product ${e.productId}`);let o=t*e.quantity;f+=o,_.push({productId:e.productId,quantity:e.quantity,price:t,total:o})}let x=s.reduce((e,r)=>e+r.discountAmount,0),w=f,N=w-x-c;if(Math.abs(N-a)>.01)throw new l.p8(`Total amount mismatch. Expected: ${N}, Received: ${a}`);let O=(0,p.mT)(a);if(!(0,p.ml)(O))throw new l.p8("Invalid payment amount");let A=(0,p.My)("HERB"),q=g().randomBytes(4).toString("hex").toUpperCase().substring(0,8);try{let e=await (0,p.iP)({amount:O,currency:"INR",receipt:A,notes:{userId:r.user.id,userEmail:r.user.email||"",orderNumber:q}}),t=(await I.Order.create({orderNumber:q,userId:r.user.id,status:"PENDING",paymentStatus:"PENDING",paymentId:e.id,subtotal:w,couponDiscount:x,flashSaleDiscount:c,total:a,currency:"INR",notes:`Razorpay Order ID: ${e.id}${s.length>0?` | Coupons: ${s.map(e=>e.coupon.code).join(", ")}`:""}`,address:{firstName:n.firstName,lastName:n.lastName,address1:n.address1,address2:n.address2,city:n.city,state:n.state,postalCode:n.postalCode,country:n.country,phone:n.phone},items:_.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price,total:e.total})),couponCodes:s.length>0?s.map(e=>String(e.coupon.code).toUpperCase()):[]})).toObject();m.kg.info("Payment order created successfully",{orderId:String(t._id),orderNumber:t.orderNumber,razorpayOrderId:e.id,amount:a,userId:r.user.id});try{await y.aZ.orderPlaced(r.user.id,{orderId:String(t._id??t.id),orderNumber:t.orderNumber,total:a,currency:"INR",itemCount:o.length}),m.kg.info("Order placed notification sent",{orderId:t.id,userId:r.user.id})}catch(e){m.kg.error("Failed to send order placed notification",e)}return i.NextResponse.json({success:!0,order:{id:String(t._id),orderNumber:t.orderNumber,razorpayOrderId:e.id,amount:O,currency:"INR",receipt:A},razorpayKeyId:"rzp_test_H8VYcEtWS9hwc8"})}catch(e){throw m.kg.error("Failed to create payment order",e),new l.gz("Failed to create payment order",500)}}),x=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/payments/create-order/route",pathname:"/api/payments/create-order",filename:"route",bundlePath:"app/api/payments/create-order/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\create-order\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:w,staticGenerationAsyncStorage:N,serverHooks:O}=x,A="/api/payments/create-order/route";function q(){return(0,s.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:N})}},84875:(e,r,t)=>{t.d(r,{AY:()=>d,M_:()=>c,_7:()=>i,dR:()=>u,gz:()=>a,lm:()=>m,p8:()=>s});var o=t(87070),n=t(29489);class a extends Error{constructor(e,r=500,t="INTERNAL_ERROR",o){super(e),this.statusCode=r,this.code=t,this.details=o,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,a)}}class s extends a{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class i extends a{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class c extends a{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class u extends a{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class d extends a{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class p extends a{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function l(e){let r={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return r[e]||r.INTERNAL_ERROR}function m(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){let r=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,error:e}),e instanceof a){let t={success:!1,error:{code:e.code,message:l(e.code),requestId:r,...!1}};return o.NextResponse.json(t,{status:e.statusCode})}if(e instanceof n.j){let t=new s("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),n={success:!1,error:{code:t.code,message:"Validation failed",requestId:r,...!1}};return o.NextResponse.json(n,{status:t.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let t=function(e){if(11e3===e.code||"MongoServerError"===e.name){let r=Object.keys(e.keyPattern||{})[0]||"field";return new d(`${r} already exists`)}return"ValidationError"===e.name?new s("Validation failed"):"CastError"===e.name?new s("Invalid data format"):"DocumentNotFoundError"===e.name?new u:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new p("Database connection failed"):new p("Database operation failed",{name:e.name,message:e.message})}(e),n={success:!1,error:{code:t.code,message:l(t.code),requestId:r,...!1}};return o.NextResponse.json(n,{status:t.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let t=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new d(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new i("Invalid user session");return new s("Invalid reference to related record");case"P2025":case"P2001":return new u;case"P2014":return new s("Missing required relationship");case"P2000":return new s("Input value is too long");case"P2004":return new s("Data constraint violation");default:return new p("Database operation failed",{code:e.code,message:e.message})}}(e),n={success:!1,error:{code:t.code,message:l(t.code),requestId:r,...!1}};return o.NextResponse.json(n,{status:t.statusCode})}}let t={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:r,...!1}};return o.NextResponse.json(t,{status:500})}(e)}}}},62822:(e,r,t)=>{t.d(r,{FT:()=>p,My:()=>R,iP:()=>d,mT:()=>m,ml:()=>y,sS:()=>l});var o=t(41212),n=t.n(o),a=t(54211),s=t(84875),i=t(84770),c=t.n(i);let u=new(n())({key_id:process.env.RAZORPAY_KEY_ID,key_secret:process.env.RAZORPAY_KEY_SECRET});async function d(e){try{a.kg.info("Creating Razorpay order",{amount:e.amount,currency:e.currency,receipt:e.receipt});let r=await u.orders.create({amount:e.amount,currency:e.currency,receipt:e.receipt,notes:e.notes||{}});return a.kg.info("Razorpay order created successfully",{orderId:r.id,amount:r.amount,receipt:r.receipt}),r}catch(e){throw a.kg.error("Failed to create Razorpay order",e),new s.gz("Failed to create payment order",500)}}function p(e){try{let r=c().createHmac("sha256",process.env.RAZORPAY_KEY_SECRET).update(`${e.razorpay_order_id}|${e.razorpay_payment_id}`).digest("hex")===e.razorpay_signature;return a.kg.info("Payment signature verification",{orderId:e.razorpay_order_id,paymentId:e.razorpay_payment_id,isValid:r}),r}catch(e){return a.kg.error("Payment signature verification failed",e),!1}}async function l(e){try{a.kg.info("Fetching payment details",{paymentId:e});let r=await u.payments.fetch(e);return a.kg.info("Payment details fetched successfully",{paymentId:r.id,status:r.status,amount:r.amount}),r}catch(e){throw a.kg.error("Failed to fetch payment details",e),new s.gz("Failed to fetch payment details",500)}}function m(e){return Math.round(100*e)}function R(e="ORDER"){let r=Date.now(),t=Math.random().toString(36).substring(2,8).toUpperCase();return`${e}_${r}_${t}`}function y(e){return e>=100&&e<=15e8&&Number.isInteger(e)}},8149:(e,r,t)=>{t.d(r,{Ri:()=>a,Xw:()=>s,er:()=>c,jO:()=>i});var o=t(919);function n(e){let r=new o.z({max:e?.uniqueTokenPerInterval||500,ttl:e?.interval||6e4});return{check:(e,t)=>new Promise((o,n)=>{let a=r.get(t)||[0];0===a[0]&&r.set(t,a),a[0]+=1,a[0]>=e?n(Error("Rate limit exceeded")):o()})}}let a=n({interval:9e5,uniqueTokenPerInterval:500}),s=n({interval:6e4,uniqueTokenPerInterval:500}),i=n({interval:36e5,uniqueTokenPerInterval:500});async function c(e,r,t){let o=function(e){let r=e.headers.get("x-forwarded-for"),t=e.headers.get("x-real-ip");return r?r.split(",")[0].trim():t||"unknown"}(e);try{await r.check(t,o)}catch(e){throw Error("Too many requests. Please try again later.")}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[9276,5972,8691,2830,9489,5630,138,656,5306,9585],()=>t(37117));module.exports=o})();