(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3],{58819:function(e,t,r){Promise.resolve().then(r.bind(r,69725))},69725:function(e,t,r){"use strict";r.r(t);var s=r(57437),a=r(2265),n=r(15863),i=r(22252),l=r(44794),c=r(42449),d=r(95805),o=r(86595),u=r(70525),m=r(3085),h=r(16275),x=r(51380);t.default=()=>{let[e,t]=(0,a.useState)(null),[r,g]=(0,a.useState)(!0),[p,y]=(0,a.useState)(null);(0,a.useEffect)(()=>{f()},[]);let f=async()=>{try{g(!0);let e=await fetch("/api/dashboard/stats"),r=await e.json();r.success?t(r.data):y("Failed to fetch dashboard data")}catch(e){console.error("Error fetching dashboard data:",e),y("Failed to fetch dashboard data")}finally{g(!1)}};if(r)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsx)(n.Z,{className:"w-8 h-8 animate-spin text-green-600"})})})});if(p)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(i.Z,{className:"w-5 h-5 text-red-600"}),(0,s.jsx)("p",{className:"text-red-800",children:p})]}),(0,s.jsx)("button",{onClick:f,className:"mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium",children:"Try again"})]})})});let v=[{title:"Total Products",value:(null==e?void 0:e.overview.totalProducts)||0,change:(null==e?void 0:e.growth.productsGrowth)||"+0%",trend:"up",icon:l.Z},{title:"Total Categories",value:(null==e?void 0:e.overview.totalCategories)||0,change:(null==e?void 0:e.growth.categoriesGrowth)||"+0%",trend:"up",icon:c.Z},{title:"Total Users",value:(null==e?void 0:e.overview.totalUsers)||0,change:(null==e?void 0:e.growth.usersGrowth)||"+0%",trend:"up",icon:d.Z},{title:"Featured Products",value:(null==e?void 0:e.overview.featuredProducts)||0,change:"+0%",trend:"up",icon:o.Z}],j=(null==e?void 0:e.recent.products)||[];return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Welcome back! Here's what's happening with your store."})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:v.map((e,t)=>{let r=e.icon,a="up"===e.trend?u.Z:m.Z;return(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.title}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]}),(0,s.jsx)(r,{className:"w-8 h-8 text-green-600"})]}),(0,s.jsxs)("div",{className:"flex items-center mt-4",children:[(0,s.jsx)(a,{className:"w-4 h-4 mr-1 ".concat("up"===e.trend?"text-green-600":"text-red-600")}),(0,s.jsx)("span",{className:"text-sm font-medium ".concat("up"===e.trend?"text-green-600":"text-red-600"),children:e.change}),(0,s.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"vs last month"})]})]},t)})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Orders"}),(0,s.jsx)("button",{className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"View all"})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(h.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders yet"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Orders will appear here once customers start purchasing"})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Products"}),(0,s.jsx)("button",{className:"text-green-600 hover:text-green-700 text-sm font-medium",children:"View all"})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"space-y-4",children:j.length>0?j.map(e=>{var t,r;return(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center flex-1",children:[(0,s.jsx)("img",{src:e.images&&e.images.length>0&&(null===(t=e.images[0])||void 0===t?void 0:t.url)?e.images[0].url:"/images/default-product.jpg",alt:e.name,className:"w-12 h-12 rounded-lg object-cover mr-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center mt-1 space-x-3",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:(null===(r=e.category)||void 0===r?void 0:r.name)||"No category"}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["Stock: ",e.quantity||0]})]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:(0,x.T4)(e.price||0)}),e.isFeatured&&(0,s.jsx)("span",{className:"inline-flex px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-700 border border-green-200 mt-1",children:"Featured"})]})]},e._id||e.id)}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(l.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products yet"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Add products to see them here"})]})})})]})]})]})})}},51380:function(e,t,r){"use strict";function s(e){return function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(isNaN(e))return"₹0";let r=new Intl.NumberFormat("en-IN",{minimumFractionDigits:t?2:0,maximumFractionDigits:t?2:0}).format(e);return"₹".concat(r)}(e,!0)}function a(e){return e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function n(e,t){return(e?a(e):a(t))||"product"}r.d(t,{GD:function(){return a},T4:function(){return s},w:function(){return n}})},39763:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var s=r(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let r=(0,s.forwardRef)((r,i)=>{let{color:l="currentColor",size:c=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:u="",children:m,...h}=r;return(0,s.createElement)("svg",{ref:i,...a,width:c,height:c,stroke:l,strokeWidth:o?24*Number(d)/Number(c):d,className:["lucide","lucide-".concat(n(e)),u].join(" "),...h},[...t.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(m)?m:[m]])});return r.displayName="".concat(e),r}},22252:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},15863:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44794:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},42449:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},16275:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},86595:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},3085:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},70525:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},95805:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(39763).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}},function(e){e.O(0,[2971,2117,1744],function(){return e(e.s=58819)}),_N_E=e.O()}]);