(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[819],{41697:function(e,s,a){Promise.resolve().then(a.bind(a,1424))},1424:function(e,s,a){"use strict";a.r(s);var t=a(57437),r=a(2265),n=a(99376),l=a(80605),i=a(32660),d=a(92369),c=a(89345),o=a(13041),u=a(83229),m=a(83774),x=a(99397),h=a(15868),p=a(18930);s.default=()=>{var e;let s=(0,n.useRouter)(),{data:a,status:f,update:y}=(0,l.useSession)(),[g,v]=(0,r.useState)(!0),[j,b]=(0,r.useState)(!1),[N,k]=(0,r.useState)(null),[w,Z]=(0,r.useState)([]),[P,S]=(0,r.useState)({});(0,r.useEffect)(()=>{"loading"!==f&&"unauthenticated"===f&&s.push("/login")},[f,s]),(0,r.useEffect)(()=>{(async()=>{var e;if(null==a?void 0:null===(e=a.user)||void 0===e?void 0:e.id)try{let e=await fetch("/api/users/".concat(a.user.id));if(e.ok){let s=await e.json();k({id:s.data.id,name:s.data.name||"",email:s.data.email,phone:s.data.phone||""}),Z(s.data.addresses||[])}}catch(e){console.error("Error fetching user data:",e)}finally{v(!1)}})()},[null==a?void 0:null===(e=a.user)||void 0===e?void 0:e.id]);let E=(e,s)=>{N&&(k({...N,[e]:s}),P[e]&&S(s=>({...s,[e]:""})))},M=()=>{var e,s;let a={};return(null==N?void 0:null===(e=N.name)||void 0===e?void 0:e.trim())||(a.name="Name is required"),(null==N?void 0:null===(s=N.email)||void 0===s?void 0:s.trim())?/\S+@\S+\.\S+/.test(N.email)||(a.email="Please enter a valid email"):a.email="Email is required",(null==N?void 0:N.phone)&&!/^\+?[\d\s\-\(\)]+$/.test(N.phone)&&(a.phone="Please enter a valid phone number"),S(a),0===Object.keys(a).length},C=async()=>{if(M()&&N){b(!0);try{(await fetch("/api/users/".concat(N.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:N.name,email:N.email,phone:N.phone||null})})).ok?(await y({...a,user:{...null==a?void 0:a.user,name:N.name,email:N.email}}),s.push("/profile")):S({general:"Failed to update profile"})}catch(e){S({general:"An error occurred while saving"})}finally{b(!1)}}},A=async e=>{if(confirm("Are you sure you want to delete this address?"))try{Z(s=>s.filter(s=>s.id!==e))}catch(e){console.error("Error deleting address:",e)}},q=async e=>{try{Z(s=>s.map(s=>({...s,isDefault:s.id===e})))}catch(e){console.error("Error setting default address:",e)}};return"loading"===f||g?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-8"}),(0,t.jsx)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded"},e))})})]})})}):"unauthenticated"===f?null:(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,t.jsx)("div",{className:"flex items-center mb-8",children:(0,t.jsxs)("button",{onClick:()=>s.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,t.jsx)(i.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Back"})]})}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Edit Profile"}),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:(0,t.jsx)(d.Z,{className:"w-6 h-6 text-green-600"})}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-800",children:"Personal Information"})]}),P.general&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:P.general}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,t.jsx)("input",{type:"text",value:(null==N?void 0:N.name)||"",onChange:e=>E("name",e.target.value),className:"w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(P.name?"border-red-500":"border-gray-300"),placeholder:"Enter your full name"}),P.name&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:P.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:"email",value:(null==N?void 0:N.email)||"",onChange:e=>E("email",e.target.value),className:"w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(P.email?"border-red-500":"border-gray-300"),placeholder:"Enter your email address"})]}),P.email&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:P.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.Z,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:"tel",value:(null==N?void 0:N.phone)||"",onChange:e=>E("phone",e.target.value),className:"w-full pl-12 pr-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ".concat(P.phone?"border-red-500":"border-gray-300"),placeholder:"Enter your phone number (optional)"})]}),P.phone&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:P.phone})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)("button",{onClick:C,disabled:j,className:"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors disabled:opacity-50",children:[(0,t.jsx)(u.Z,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:j?"Saving...":"Save Changes"})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:(0,t.jsx)(m.Z,{className:"w-6 h-6 text-green-600"})}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-800",children:"Shipping Addresses"})]}),(0,t.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:[(0,t.jsx)(x.Z,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Add Address"})]})]}),0===w.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(m.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"No addresses added yet"}),(0,t.jsx)("button",{className:"mt-4 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Add Your First Address"})]}):(0,t.jsx)("div",{className:"space-y-4",children:w.map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&(0,t.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[e.company&&(0,t.jsx)("p",{className:"font-medium",children:e.company}),(0,t.jsx)("p",{children:e.address1}),e.address2&&(0,t.jsx)("p",{children:e.address2}),(0,t.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),(0,t.jsx)("p",{children:e.country}),e.phone&&(0,t.jsx)("p",{className:"font-medium",children:e.phone})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&(0,t.jsx)("button",{onClick:()=>q(e.id),className:"px-3 py-1 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors",children:"Set as Default"}),(0,t.jsx)("button",{className:"p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",children:(0,t.jsx)(h.Z,{className:"w-4 h-4"})}),(0,t.jsx)("button",{onClick:()=>A(e.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:(0,t.jsx)(p.Z,{className:"w-4 h-4"})})]})]})},e.id))})]})]})]})})}},32660:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},89345:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},83774:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13041:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},99397:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},83229:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},15868:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},18930:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},92369:function(e,s,a){"use strict";a.d(s,{Z:function(){return t}});let t=(0,a(39763).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},99376:function(e,s,a){"use strict";var t=a(35475);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})}},function(e){e.O(0,[7349,2971,2117,1744],function(){return e(e.s=41697)}),_N_E=e.O()}]);