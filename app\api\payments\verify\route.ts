import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import {
  verifyPaymentSignature,
  getPaymentDetails,
} from "../../../lib/payment";
import {
  handleApiError,
  ValidationError,
  AuthenticationError,
  AppError,
  asyncHandler,
} from "../../../lib/errors";
import { logger } from "../../../lib/logger";
import { withRateLimit, generalLimiter } from "../../../lib/rate-limit";
import { sendOrderConfirmationEmail } from "../../../lib/email";
import { orderNotifications } from "../../../lib/notification-helpers";
import connectDB from "../../../lib/mongoose";
import { Order, IOrder } from "../../../lib/models";

const verifyPaymentSchema = z.object({
  razorpay_order_id: z.string(),
  razorpay_payment_id: z.string(),
  razorpay_signature: z.string(),
  order_id: z.string(),
});

export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest("POST", "/api/payments/verify");
  await connectDB();

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 20);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError("Authentication required");
  }

  const body = await request.json();
  const validatedData = verifyPaymentSchema.parse(body);
  const {
    razorpay_order_id,
    razorpay_payment_id,
    razorpay_signature,
    order_id,
  } = validatedData;

  logger.info("Verifying payment", {
    userId: session.user.id,
    orderId: order_id,
    razorpayOrderId: razorpay_order_id,
    razorpayPaymentId: razorpay_payment_id,
  });

  try {
    // Find the order (populate items.product for email/notifications)
    const orderDoc = await Order.findById(order_id)
      .populate([{ path: "items.product" }])
      .lean();
    // Convert orderDoc to any first before casting to our desired type
    const order = orderDoc as unknown as IOrder & { items?: any[], address?: any, userEmail?: string, user?: { email: string } };

    if (!order) {
      throw new ValidationError("Order not found");
    }

    // Verify the order belongs to the authenticated user
    if (order && order.userId.toString() !== session.user.id) {
      throw new AuthenticationError("Unauthorized access to order");
    }

    // Verify the Razorpay order ID matches
    if (order && order.paymentId !== razorpay_order_id) {
      throw new ValidationError("Order ID mismatch");
    }

    // Verify payment signature
    const isSignatureValid = verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
    });

    if (!isSignatureValid) {
      logger.warn("Invalid payment signature", {
        orderId: order_id,
        razorpayOrderId: razorpay_order_id,
        razorpayPaymentId: razorpay_payment_id,
        userId: session.user.id,
      });

      // Update order status to failed
      await Order.updateOne(
        { _id: order_id },
        {
          $set: {
            paymentStatus: "FAILED",
            status: "CANCELLED",
            notes: `Payment verification failed. Payment ID: ${razorpay_payment_id}`,
          },
        },
      );

      throw new ValidationError("Payment verification failed");
    }

    // Get payment details from Razorpay
    const paymentDetails = await getPaymentDetails(razorpay_payment_id);

    // Update order status to paid
    await Order.updateOne(
      { _id: order_id },
      {
        $set: {
          paymentStatus: "PAID",
          status: "CONFIRMED",
          paymentMethod: paymentDetails.method || "razorpay",
          notes: `Payment successful. Payment ID: ${razorpay_payment_id}, Method: ${paymentDetails.method || "unknown"}`,
        },
      },
    );
    const updatedOrder = await Order.findById(order_id).lean() as unknown as IOrder;

    logger.info("Payment verified successfully", {
      orderId: order_id,
      orderNumber: order?.orderNumber,
      razorpayPaymentId: razorpay_payment_id,
      amount: order?.total,
      userId: session.user.id,
      paymentMethod: paymentDetails.method,
    });

    // Send order confirmation email
    try {
      // order.user was included via Prisma; with Mongoose we may only have userId.
      // Only send email if we have necessary fields on order object.
      if (order && (order as any).address) {
        const orderItems = (order.items || []).map((item: any) => ({
          name: item.product?.name ?? "",
          quantity: item.quantity,
          price: item.price,
        }));

        const shippingAddress = `${(order as any).address.firstName} ${(order as any).address.lastName}
${(order as any).address.address1}
${(order as any).address.address2 ? (order as any).address.address2 + "\n" : ""}${(order as any).address.city}, ${(order as any).address.state} ${(order as any).address.postalCode}
${(order as any).address.country}`;

        // We don't have user email populated here. If your Order schema stores userEmail, use it; otherwise skip email sending.
        if ((order as any).userEmail) {
          await sendOrderConfirmationEmail((order as any).userEmail, {
            orderId: order?.orderNumber,
            items: orderItems,
            total: order?.total,
            shippingAddress,
          });
        }

        logger.info("Order confirmation email sent", {
          orderId: order_id,
          email: order?.user?.email,
        });
      }
    } catch (emailError) {
      logger.error(
        "Failed to send order confirmation email",
        emailError as Error,
      );
      // Don't fail the payment verification if email fails
    }

    // Send order confirmed notification
    try {
      await orderNotifications.orderConfirmed(order?.userId?.toString() || session.user.id, {
        orderId: String(order?._id),
        orderNumber: order?.orderNumber,
      });

      logger.info("Order confirmed notification sent", {
        orderId: order_id,
        userId: order?.userId || session.user.id,
      });
    } catch (notificationError) {
      logger.error(
        "Failed to send order confirmed notification",
        notificationError as Error,
      );
      // Don't fail the payment verification if notification fails
    }

    return NextResponse.json({
      success: true,
      message: "Payment verified successfully",
      order: {
        id: String(updatedOrder?._id ?? order_id),
        orderNumber: updatedOrder?.orderNumber ?? order?.orderNumber,
        status: updatedOrder?.status ?? "CONFIRMED",
        paymentStatus: updatedOrder?.paymentStatus ?? "PAID",
        total: updatedOrder?.total ?? order?.total,
        paymentId: razorpay_payment_id,
      },
    });
  } catch (error) {
    logger.error("Payment verification failed", error as Error);

    // If it's not a validation error, mark as failed
    if (
      !(error instanceof ValidationError) &&
      !(error instanceof AuthenticationError)
    ) {
      try {
        await Order.updateOne(
          { _id: order_id },
          {
            $set: {
              paymentStatus: "FAILED",
              status: "CANCELLED",
              notes: `Payment verification error: ${(error as Error).message}`,
            },
          },
        );
      } catch (dbError) {
        logger.error("Failed to update order status", dbError as Error);
      }
    }

    throw error;
  }
});
