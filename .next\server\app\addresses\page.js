(()=>{var e={};e.id=1899,e.ids=[1899],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19980:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>i}),r(982),r(37254),r(35866);var t=r(23191),a=r(88716),d=r(37922),n=r.n(d),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let i=["",{children:["addresses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,982)),"C:\\Users\\<USER>\\Desktop\\project\\app\\addresses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\addresses\\page.tsx"],u="/addresses/page",x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/addresses/page",pathname:"/addresses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},27051:(e,s,r)=>{Promise.resolve().then(r.bind(r,39388))},39388:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(10326);r(17577);var a=r(35047),d=r(86333),n=r(83855),l=r(77636),o=r(69508),i=r(98091);let c=()=>{let e=(0,a.useRouter)();return t.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[t.jsx("div",{className:"flex items-center mb-8",children:(0,t.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[t.jsx(d.Z,{className:"w-5 h-5"}),t.jsx("span",{children:"Back"})]})}),t.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Shipping Addresses"}),(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,t.jsxs)("button",{className:"w-full flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-xl hover:border-green-400 hover:bg-green-50 transition-colors",children:[t.jsx(n.Z,{className:"w-6 h-6 text-gray-400"}),t.jsx("span",{className:"text-gray-600 font-medium",children:"Add New Address"})]})}),[{id:"1",firstName:"John",lastName:"Doe",company:"Tech Corp",address1:"123 Main Street",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"India",phone:"+91 99878 10707",isDefault:!0},{id:"2",firstName:"John",lastName:"Doe",company:"",address1:"456 Oak Avenue",address2:"",city:"Brooklyn",state:"NY",postalCode:"11201",country:"India",phone:"+91 99878 10707",isDefault:!1}].map(e=>t.jsx("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mt-1",children:t.jsx(l.Z,{className:"w-6 h-6 text-gray-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&t.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[e.company&&t.jsx("p",{className:"font-medium",children:e.company}),t.jsx("p",{children:e.address1}),e.address2&&t.jsx("p",{children:e.address2}),(0,t.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),t.jsx("p",{children:e.country}),e.phone&&t.jsx("p",{className:"font-medium",children:e.phone})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&t.jsx("button",{className:"px-4 py-2 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors",children:"Set as Default"}),t.jsx("button",{className:"p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",children:t.jsx(o.Z,{className:"w-4 h-4"})}),t.jsx("button",{className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:t.jsx(i.Z,{className:"w-4 h-4"})})]})]})},e.id)),(0,t.jsxs)("div",{className:"bg-blue-50 rounded-2xl p-6 border border-blue-100",children:[t.jsx("h3",{className:"font-semibold text-blue-900 mb-2",children:"Address Information"}),t.jsx("p",{className:"text-blue-800 text-sm",children:"Your default address will be automatically selected during checkout. You can always change it before placing an order."})]})]})]})})}},86333:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},77636:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},83855:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},69508:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},98091:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},35047:(e,s,r)=>{"use strict";var t=r(77389);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},982:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\addresses\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[9276,3757,9536],()=>r(19980));module.exports=t})();