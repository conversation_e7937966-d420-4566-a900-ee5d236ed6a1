"use strict";(()=>{var e={};e.id=976,e.ids=[976],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},77249:(e,t,i)=>{i.r(t),i.d(t,{originalPathname:()=>A,patchFetch:()=>x,requestAsyncStorage:()=>h,routeModule:()=>y,serverHooks:()=>v,staticGenerationAsyncStorage:()=>b});var r={};i.r(r),i.d(r,{POST:()=>m});var o=i(49303),a=i(88716),s=i(60670),n=i(87070),u=i(75571),c=i(95306),l=i(89456),p=i(81515),d=i(54211);async function m(e){try{await (0,l.Z)(),await (0,u.getServerSession)(c.L);let{couponCode:t,cartItems:i,userId:r,subtotal:o}=await e.json();if(!t||!i||void 0===o)return n.NextResponse.json({error:"Missing required fields"},{status:400});let a=await f(t,i,o,r);return n.NextResponse.json(a)}catch(e){return d.kg.error("Error validating coupon",e),n.NextResponse.json({error:"Failed to validate coupon"},{status:500})}}async function g(){try{let e=await p.nW.find({}).sort({createdAt:-1}).limit(1).lean();if(0===e.length)return!1;let t=e[0];if(!t.showFlashSale)return!1;if(t.flashSaleEndDate){let e=new Date;if(new Date(t.flashSaleEndDate)<e)return!1}return!0}catch(e){return d.kg.error("Error checking flash sale status",e),!1}}async function f(e,t,r,o){if(await g())return{isValid:!1,discountAmount:0,errorMessage:"Coupons cannot be used during flash sale periods"};let a=await p.wV.findOne({code:e.toUpperCase()}).lean();if(!a)return{isValid:!1,discountAmount:0,errorMessage:"Coupon code not found"};if(!a.isActive)return{isValid:!1,discountAmount:0,errorMessage:"This coupon is no longer active"};let s=new Date;if(a.validFrom>s)return{isValid:!1,discountAmount:0,errorMessage:"This coupon is not yet valid"};if(a.validUntil&&a.validUntil<s)return{isValid:!1,discountAmount:0,errorMessage:"This coupon has expired"};if(a.usageLimit&&a.usedCount>=a.usageLimit)return{isValid:!1,discountAmount:0,errorMessage:"This coupon has reached its usage limit"};if(o&&a.userUsageLimit){let{Order:e}=await Promise.resolve().then(i.bind(i,81515));if(await e.countDocuments({userId:o,couponCodes:{$in:[a.code]}})>=a.userUsageLimit)return{isValid:!1,discountAmount:0,errorMessage:"You have already used this coupon the maximum number of times"}}if(a.minimumAmount&&r<a.minimumAmount)return{isValid:!1,discountAmount:0,errorMessage:`Minimum order amount of ₹${a.minimumAmount} required`};let n=function(e,t){let i=t.map(e=>e.product.id),r=t.flatMap(e=>e.product.categories?.map(e=>e.id)||[]);if(e.excludedProducts.length>0&&i.some(t=>e.excludedProducts.includes(t)))return{isApplicable:!1,message:"This coupon cannot be applied to some items in your cart",applicableAmount:0};if(e.excludedCategories.length>0&&r.some(t=>e.excludedCategories.includes(t)))return{isApplicable:!1,message:"This coupon cannot be applied to some categories in your cart",applicableAmount:0};let o=0;switch(e.type){case"STORE_WIDE":o=t.reduce((e,t)=>e+t.product.price*t.quantity,0);break;case"PRODUCT_SPECIFIC":if(0===e.applicableProducts.length)return{isApplicable:!1,message:"No applicable products specified for this coupon",applicableAmount:0};let a=t.filter(t=>e.applicableProducts.includes(t.product.id));if(0===a.length)return{isApplicable:!1,message:"This coupon is not applicable to any items in your cart",applicableAmount:0};o=a.reduce((e,t)=>e+t.product.price*t.quantity,0);break;case"CATEGORY_SPECIFIC":if(0===e.applicableCategories.length)return{isApplicable:!1,message:"No applicable categories specified for this coupon",applicableAmount:0};let s=t.filter(t=>t.product.categories?.some(t=>e.applicableCategories.includes(t.id)));if(0===s.length)return{isApplicable:!1,message:"This coupon is not applicable to any categories in your cart",applicableAmount:0};o=s.reduce((e,t)=>e+t.product.price*t.quantity,0);break;case"BUY_X_GET_Y":let n=e.buyProducts||[],u=e.buyCategories||[];if(t.filter(e=>{let t=n.includes(e.product.id),i=e.product.categories?.some(e=>u.includes(e.id))||!1;return t||i}).reduce((e,t)=>e+t.quantity,0)<(e.buyQuantity||1))return{isApplicable:!1,message:`You need to buy at least ${e.buyQuantity} qualifying items to use this coupon`,applicableAmount:0};o=t.reduce((e,t)=>e+t.product.price*t.quantity,0);break;default:o=t.reduce((e,t)=>e+t.product.price*t.quantity,0)}return{isApplicable:!0,message:"",applicableAmount:o}}(a,t);return n.isApplicable?{isValid:!0,discountAmount:function(e,t,i,r){let o=0;if(e.discountType)switch(e.discountType){case"PERCENTAGE":o=r*(e.discountValue||0)/100;break;case"FIXED_AMOUNT":o=Math.min(e.discountValue||0,r);break;case"FREE_SHIPPING":default:o=0;break;case"BUY_X_GET_Y":let a=e.buyQuantity||1,s=e.getQuantity||1,n=e.buyProducts||[],u=e.buyCategories||[],c=e.getProducts||[],l=e.getCategories||[],p=e.maxApplications||1,d=t.filter(e=>{let t=n.includes(e.product.id),i=e.product.categories?.some(e=>u.includes(e.id))||!1;return t||i}),m=t.filter(e=>{let t=c.includes(e.product.id),i=e.product.categories?.some(e=>l.includes(e.id))||!1;return t||i}),g=Math.min(Math.floor(d.reduce((e,t)=>e+t.quantity,0)/a),p);if(g>0&&m.length>0){let t=[...m].sort((e,t)=>t.product.price-e.product.price),i=s*g;for(let r of t){if(i<=0)break;let t=Math.min(r.quantity,i);"FREE"===e.discountApplication?o+=r.product.price*t:"PERCENTAGE"===e.discountApplication?o+=r.product.price*t*(e.getDiscountValue||0)/100:"FIXED_AMOUNT"===e.discountApplication&&(o+=Math.min(e.getDiscountValue||0,r.product.price)*t),i-=t}}}else"PERCENTAGE"===e.type?o=r*(e.value||0)/100:"FIXED"===e.type&&(o=Math.min(e.value||0,r));return e.maximumDiscount&&o>e.maximumDiscount&&(o=e.maximumDiscount),Math.round(100*o)/100}(a,t,0,n.applicableAmount),coupon:{id:String(a._id),code:a.code,name:a.name,description:a.description||void 0,type:a.type,value:a.value,minimumAmount:a.minimumAmount??void 0,maximumDiscount:a.maximumDiscount??void 0,usageLimit:a.usageLimit??void 0,usageCount:a.usedCount??0,userUsageLimit:a.userUsageLimit??void 0,isActive:a.isActive,isStackable:a.isStackable??void 0,showInModule:a.showInModule??void 0,validFrom:new Date(a.validFrom).toISOString(),validUntil:a.validUntil?new Date(a.validUntil).toISOString():void 0,applicableProducts:a.applicableProducts??[],applicableCategories:a.applicableCategories??[],excludedProducts:a.excludedProducts??[],excludedCategories:a.excludedCategories??[],customerSegments:a.customerSegments??[],createdAt:new Date(a.createdAt).toISOString(),updatedAt:new Date(a.updatedAt).toISOString(),discountType:a.discountType,discountValue:a.discountValue,buyQuantity:a.buyQuantity,getQuantity:a.getQuantity,buyProducts:a.buyProducts,buyCategories:a.buyCategories,getProducts:a.getProducts,getCategories:a.getCategories,maxApplications:a.maxApplications,discountApplication:a.discountApplication,getDiscountValue:a.getDiscountValue}}:{isValid:!1,discountAmount:0,errorMessage:n.message}}let y=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/coupons/validate/route",pathname:"/api/coupons/validate",filename:"route",bundlePath:"app/api/coupons/validate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\validate\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:b,serverHooks:v}=y,A="/api/coupons/validate/route";function x(){return(0,s.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:b})}},54211:(e,t,i)=>{var r;i.d(t,{kg:()=>a}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(r||(r={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:i,message:o,context:a,error:s,userId:n,requestId:u}=e,c=r[i],l=`[${t}] ${c}: ${o}`;return n&&(l+=` | User: ${n}`),u&&(l+=` | Request: ${u}`),a&&Object.keys(a).length>0&&(l+=` | Context: ${JSON.stringify(a)}`),s&&(l+=` | Error: ${s.message}`,this.isDevelopment&&s.stack&&(l+=`
Stack: ${s.stack}`)),l}log(e,t,i,r){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:i,error:r},a=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(a);break;case 1:console.warn(a);break;case 2:console.info(a);break;case 3:console.debug(a)}else console.log(JSON.stringify(o))}error(e,t,i){this.log(0,e,i,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,i,r){this.info(`API ${e} ${t}`,{...r,userId:i,type:"api_request"})}apiResponse(e,t,i,r,o){this.info(`API ${e} ${t} - ${i}`,{...o,statusCode:i,duration:r,type:"api_response"})}apiError(e,t,i,r,o){this.error(`API ${e} ${t} failed`,i,{...o,userId:r,type:"api_error"})}authSuccess(e,t,i){this.info("Authentication successful",{...i,userId:e,method:t,type:"auth_success"})}authFailure(e,t,i,r){this.warn("Authentication failed",{...r,email:e,method:t,reason:i,type:"auth_failure"})}dbQuery(e,t,i,r){this.debug(`DB ${e} on ${t}`,{...r,operation:e,table:t,duration:i,type:"db_query"})}dbError(e,t,i,r){this.error(`DB ${e} on ${t} failed`,i,{...r,operation:e,table:t,type:"db_error"})}securityEvent(e,t,i){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...i,severity:t,type:"security_event"})}rateLimitHit(e,t,i,r){this.warn("Rate limit exceeded",{...r,identifier:e,limit:t,window:i,type:"rate_limit"})}emailSent(e,t,i,r){this.info("Email sent",{...r,to:e,subject:t,template:i,type:"email_sent"})}emailError(e,t,i,r){this.error("Email failed to send",i,{...r,to:e,subject:t,type:"email_error"})}performance(e,t,i){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...i,operation:e,duration:t,type:"performance"})}}let a=new o},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0});var r={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var o=i(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(r,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var i=s(void 0);if(i&&i.has(e))return i.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var n=o?Object.getOwnPropertyDescriptor(e,a):null;n&&(n.get||n.set)?Object.defineProperty(r,a,n):r[a]=e[a]}return r.default=e,i&&i.set(e,r),r}(i(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,i=new WeakMap;return(s=function(e){return e?i:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(r,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[9276,5972,8691,2830,5306],()=>i(77249));module.exports=r})();