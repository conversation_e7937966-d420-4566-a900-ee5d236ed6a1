(()=>{var e={};e.id=6888,e.ids=[6888],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15155:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),s(97513),s(90596),s(37254),s(35866);var a=s(23191),r=s(88716),n=s(37922),i=s.n(n),l=s(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["admin",{children:["notifications",{children:["send",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,97513)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\send\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,90596)),"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\send\\page.tsx"],x="/admin/notifications/send/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/notifications/send/page",pathname:"/admin/notifications/send",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},75671:(e,t,s)=>{Promise.resolve().then(s.bind(s,2802))},74770:(e,t,s)=>{Promise.resolve().then(s.bind(s,39950))},2802:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(10326),r=s(17577),n=s.n(r),i=s(90434),l=s(35047),c=s(77109),d=s(24319),o=s(48705),x=s(34565),m=s(57671),h=s(24061),u=s(40765),p=s(40617),g=s(35351),y=s(6507),f=s(5932),b=s(71709),j=s(95920),k=s(88378),v=s(94019),N=s(90748),w=s(53080),Z=s(71810);let S=({children:e})=>{let t=(0,l.usePathname)(),[s,r]=n().useState(!1),[S,C]=n().useState(!1),P=async()=>{if(!S)try{C(!0),r(!1),await (0,c.signOut)({redirect:!1,callbackUrl:"/"}),setTimeout(()=>{window.location.replace("/")},100)}catch(e){console.error("Error signing out:",e),window.location.replace("/")}},M=[{href:"/admin",label:"Dashboard",icon:d.Z},{href:"/admin/products",label:"Products",icon:o.Z},{href:"/admin/categories",label:"Categories",icon:x.Z},{href:"/admin/orders",label:"Orders",icon:m.Z},{href:"/admin/customers",label:"Customers",icon:h.Z},{href:"/admin/coupons",label:"Coupons",icon:u.Z},{href:"/admin/reviews",label:"Reviews",icon:p.Z},{href:"/admin/enquiry",label:"Enquiries",icon:g.Z},{href:"/admin/notifications",label:"Notifications",icon:y.Z},{href:"/admin/newsletter",label:"Newsletter",icon:f.Z},{href:"/admin/media",label:"Media",icon:b.Z},{href:"/admin/homepage",label:"Homepage",icon:j.Z},{href:"/admin/settings",label:"Settings",icon:k.Z}],q=e=>"/admin"===e?"/admin"===t:t.startsWith(e);return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:a.jsx("button",{onClick:()=>r(!s),className:"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors",children:s?a.jsx(v.Z,{className:"w-6 h-6 text-gray-600"}):a.jsx(N.Z,{className:"w-6 h-6 text-gray-600"})})}),(0,a.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${s?"translate-x-0":"-translate-x-full"} lg:translate-x-0`,children:[a.jsx("div",{className:"flex items-center justify-center h-16 px-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center",children:a.jsx(w.Z,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Herbalicious"}),a.jsx("p",{className:"text-xs text-gray-500",children:"Admin Panel"})]})]})}),a.jsx("nav",{className:"mt-6 px-3",children:M.map(e=>{let t=e.icon,s=q(e.href);return(0,a.jsxs)(i.default,{href:e.href,className:`flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${s?"bg-green-50 text-green-700 border-r-2 border-green-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,onClick:()=>r(!1),children:[a.jsx(t,{className:`w-5 h-5 mr-3 ${s?"text-green-600":"text-gray-400"}`}),e.label]},e.href)})}),(0,a.jsxs)("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:[a.jsx(i.default,{href:"/",className:"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:"Back to Store"}),(0,a.jsxs)("button",{onClick:P,disabled:S,className:"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(Z.Z,{className:"w-4 h-4 mr-2"}),S?"Logging out...":"Logout"]})]})]}),a.jsx("main",{className:"lg:ml-64",children:a.jsx("div",{className:"p-4 lg:p-8",children:e})}),s&&a.jsx("div",{className:"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden",onClick:()=>r(!1)})]})}},39950:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(10326),r=s(17577),n=s(35047),i=s(54659),l=s(86333),c=s(87888),d=s(24061),o=s(79635),x=s(69436);let m=()=>{let e=(0,n.useRouter)(),[t,s]=(0,r.useState)(!1),[m,h]=(0,r.useState)([]),[u,p]=(0,r.useState)([]),[g,y]=(0,r.useState)([]),[f,b]=(0,r.useState)(""),[j,k]=(0,r.useState)(!1),[v,N]=(0,r.useState)(!1),[w,Z]=(0,r.useState)(null),[S,C]=(0,r.useState)({title:"",content:"",type:"ADMIN_MESSAGE",sendEmail:!0,sendInApp:!0});(0,r.useEffect)(()=>{P(),M()},[]);let P=async()=>{try{let e=await fetch("/api/admin/users?limit=100"),t=await e.json();t.success&&h(t.data.users||[])}catch(e){console.error("Error fetching users:",e)}},M=async()=>{try{let e=await fetch("/api/admin/notifications/templates"),t=await e.json();t.success&&p(t.templates||[])}catch(e){console.error("Error fetching templates:",e)}},q=e=>{y(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},A=e=>{if(b(e),e){let t=u.find(t=>t.id===e);t&&(C(e=>({...e,title:t.subject,content:t.content})),k(!1))}else k(!0)},E=async t=>{if(t.preventDefault(),0===g.length){Z("Please select at least one user");return}if(!S.title.trim()||!S.content.trim()){Z("Please provide both title and content");return}s(!0),Z(null);try{let t=await fetch("/api/admin/notifications/send",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userIds:g,title:S.title,content:S.content,type:S.type,sendEmail:S.sendEmail,sendInApp:S.sendInApp})}),s=await t.json();s.success?(N(!0),setTimeout(()=>{e.push("/admin/notifications")},2e3)):Z(s.error||"Failed to send notification")}catch(e){console.error("Error sending notification:",e),Z("Failed to send notification")}finally{s(!1)}};return v?a.jsx("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 text-center",children:[a.jsx(i.Z,{className:"w-12 h-12 text-green-600 mx-auto mb-4"}),a.jsx("h2",{className:"text-xl font-semibold text-green-900 mb-2",children:"Notification Sent Successfully!"}),(0,a.jsxs)("p",{className:"text-green-700",children:["Your notification has been sent to ",g.length," user(s)."]}),a.jsx("p",{className:"text-sm text-green-600 mt-2",children:"Redirecting to notifications dashboard..."})]})}):(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[a.jsx("button",{onClick:()=>e.back(),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(l.Z,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Send Notification"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Send a notification to selected users"})]})]}),w&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center space-x-2",children:[a.jsx(c.Z,{className:"w-5 h-5 text-red-600"}),a.jsx("span",{className:"text-red-700",children:w})]}),(0,a.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 flex items-center space-x-2",children:[a.jsx(d.Z,{className:"w-5 h-5"}),(0,a.jsxs)("span",{children:["Select Users (",g.length," selected)"]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{type:"button",onClick:()=>{y(m.map(e=>e.id))},className:"text-sm text-green-600 hover:text-green-700",children:"Select All"}),a.jsx("button",{type:"button",onClick:()=>{y([])},className:"text-sm text-gray-600 hover:text-gray-700",children:"Clear"})]})]}),a.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:m.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:g.includes(e.id),onChange:()=>q(e.id),className:"w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"}),a.jsx(o.Z,{className:"w-4 h-4 text-gray-400"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.name||"No name"}),a.jsx("p",{className:"text-xs text-gray-500 truncate",children:e.email})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Message Content"}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Use Template (Optional)"}),(0,a.jsxs)("select",{value:f,onChange:e=>A(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",children:[a.jsx("option",{value:"",children:"Custom Message"}),u.map(e=>a.jsx("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),a.jsx("input",{type:"text",value:S.title,onChange:e=>C(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"Notification title",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Content *"}),a.jsx("textarea",{value:S.content,onChange:e=>C(t=>({...t,content:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"Notification content",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",id:"sendEmail",checked:S.sendEmail,onChange:e=>C(t=>({...t,sendEmail:e.target.checked})),className:"w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"}),a.jsx("label",{htmlFor:"sendEmail",className:"text-sm text-gray-700",children:"Send via Email"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",id:"sendInApp",checked:S.sendInApp,onChange:e=>C(t=>({...t,sendInApp:e.target.checked})),className:"w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"}),a.jsx("label",{htmlFor:"sendInApp",className:"text-sm text-gray-700",children:"Send In-App Notification"})]})]})]})]}),a.jsx("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{type:"submit",disabled:t||0===g.length,className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[a.jsx(x.Z,{className:"w-4 h-4"}),a.jsx("span",{children:t?"Sending...":`Send to ${g.length} user(s)`})]})})]})]})}},87888:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},35351:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},95920:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},71709:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},24319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53080:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},71810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},90748:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},40617:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},48705:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},69436:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},88378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},34565:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},57671:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},40765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},79635:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},90596:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`)},97513:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\notifications\send\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,3757,434,9536],()=>s(15155));module.exports=a})();