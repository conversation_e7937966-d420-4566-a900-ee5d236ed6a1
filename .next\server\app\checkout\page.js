(()=>{var e={};e.id=285,e.ids=[285],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},36666:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o}),t(28549),t(37254),t(35866);var a=t(23191),r=t(88716),n=t(37922),l=t.n(n),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let o=["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28549)),"C:\\Users\\<USER>\\Desktop\\project\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,37254)),"C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\project\\app\\checkout\\page.tsx"],m="/checkout/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},95247:(e,s,t)=>{Promise.resolve().then(t.bind(t,65090)),Promise.resolve().then(t.bind(t,62670))},62670:(e,s,t)=>{"use strict";t.d(s,{default:()=>C});var a,r=t(10326),n=t(17577),l=t(35047),i=t(77109),d=t(14228),o=t(28916),c=t(58038),m=t(86333),x=t(32933),h=t(79635),u=t(76557);let p=(0,u.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var g=t(82685);let y=(0,u.Z)("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]]),b=(0,u.Z)("Banknote",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M6 12h.01M18 12h.01",key:"113zkx"}]]);var j=t(24230),f=t(94494);!function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(a||(a={}));class N{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:s,level:t,message:r,context:n,error:l,userId:i,requestId:d}=e,o=a[t],c=`[${s}] ${o}: ${r}`;return i&&(c+=` | User: ${i}`),d&&(c+=` | Request: ${d}`),n&&Object.keys(n).length>0&&(c+=` | Context: ${JSON.stringify(n)}`),l&&(c+=` | Error: ${l.message}`,this.isDevelopment&&l.stack&&(c+=`
Stack: ${l.stack}`)),c}log(e,s,t,a){if(!this.shouldLog(e))return;let r={timestamp:new Date().toISOString(),level:e,message:s,context:t,error:a},n=this.formatMessage(r);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(r))}error(e,s,t){this.log(0,e,t,s)}warn(e,s){this.log(1,e,s)}info(e,s){this.log(2,e,s)}debug(e,s){this.log(3,e,s)}apiRequest(e,s,t,a){this.info(`API ${e} ${s}`,{...a,userId:t,type:"api_request"})}apiResponse(e,s,t,a,r){this.info(`API ${e} ${s} - ${t}`,{...r,statusCode:t,duration:a,type:"api_response"})}apiError(e,s,t,a,r){this.error(`API ${e} ${s} failed`,t,{...r,userId:a,type:"api_error"})}authSuccess(e,s,t){this.info("Authentication successful",{...t,userId:e,method:s,type:"auth_success"})}authFailure(e,s,t,a){this.warn("Authentication failed",{...a,email:e,method:s,reason:t,type:"auth_failure"})}dbQuery(e,s,t,a){this.debug(`DB ${e} on ${s}`,{...a,operation:e,table:s,duration:t,type:"db_query"})}dbError(e,s,t,a){this.error(`DB ${e} on ${s} failed`,t,{...a,operation:e,table:s,type:"db_error"})}securityEvent(e,s,t){this.log("high"===s?0:"medium"===s?1:2,`Security event: ${e}`,{...t,severity:s,type:"security_event"})}rateLimitHit(e,s,t,a){this.warn("Rate limit exceeded",{...a,identifier:e,limit:s,window:t,type:"rate_limit"})}emailSent(e,s,t,a){this.info("Email sent",{...a,to:e,subject:s,template:t,type:"email_sent"})}emailError(e,s,t,a){this.error("Email failed to send",t,{...a,to:e,subject:s,type:"email_error"})}performance(e,s,t){this.log(s>5e3?1:3,`Performance: ${e} took ${s}ms`,{...t,operation:e,duration:s,type:"performance"})}}let v=new N;function w({cartItems:e,shippingAddress:s,totalAmount:t,appliedCoupons:a=[],onSuccess:l,onError:d,className:o="",disabled:c=!1,flashSaleDiscount:m=0}){let{data:x}=(0,i.useSession)(),[h,u]=(0,n.useState)(!1),p=()=>new Promise(e=>{let s=document.createElement("script");s.src="https://checkout.razorpay.com/v1/checkout.js",s.onload=()=>e(!0),s.onerror=()=>e(!1),document.body.appendChild(s)}),g=async()=>{if(!x?.user){d?.("Please login to continue");return}if(0===e.length){d?.("Cart is empty");return}u(!0);try{if(!await p())throw Error("Failed to load payment gateway");let r=await fetch("/api/payments/create-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cartItems:e.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price})),shippingAddress:s,totalAmount:t,appliedCoupons:a,flashSaleDiscount:m})});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to create order")}let n=await r.json(),i={key:n.razorpayKeyId,amount:n.order.amount,currency:n.order.currency,name:"Herbalicious",description:`Order #${n.order.orderNumber||"New Order"}`,order_id:n.order.razorpayOrderId,handler:async e=>{try{let s=await fetch("/api/payments/verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({razorpay_order_id:e.razorpay_order_id,razorpay_payment_id:e.razorpay_payment_id,razorpay_signature:e.razorpay_signature,order_id:n.order.id})});if(!s.ok){let e=await s.json();throw Error(e.message||"Payment verification failed")}let t=await s.json();v.info("Payment completed successfully",{orderId:t.order.id,orderNumber:t.order.orderNumber,paymentId:e.razorpay_payment_id}),l?.(t.order.id)}catch(e){console.error("Payment verification error:",e),d?.(e.message||"Payment verification failed")}},prefill:{name:`${s.firstName} ${s.lastName}`,email:x.user.email||"",contact:s.phone},notes:{address:`${s.address1}, ${s.city}, ${s.state}`},theme:{color:"#2d5a27"},modal:{ondismiss:()=>{u(!1),d?.("Payment cancelled")}}};new window.Razorpay(i).open()}catch(e){console.error("Payment error:",e),u(!1),d?.(e.message||"Payment failed")}};return r.jsx("button",{onClick:g,disabled:c||h||!x?.user,className:`
        w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 
        text-white font-semibold py-3 px-6 rounded-lg transition-colors
        flex items-center justify-center space-x-2
        ${o}
      `,children:h?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),r.jsx("span",{children:"Processing..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{children:["Pay ₹",t.toFixed(2)]}),r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})]})})}let C=()=>{let e=(0,l.useRouter)(),{data:s,status:t}=(0,i.useSession)(),{state:a,dispatch:u}=(0,f.j)(),[N,v]=(0,n.useState)(1),[C,k]=(0,n.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",state:"",zipCode:"",country:"India",paymentMethod:"online",billingDifferent:!1,billingAddress:"",billingCity:"",billingState:"",billingZipCode:""}),[S,P]=(0,n.useState)({}),[$,D]=(0,n.useState)(!1),[M,F]=(0,n.useState)([]),[_,O]=(0,n.useState)(""),[A,q]=(0,n.useState)(!1),[Z,z]=(0,n.useState)(!1),[E,I]=(0,n.useState)({email:"",password:""}),[L,R]=(0,n.useState)(""),[U,T]=(0,n.useState)(!1),[B,G]=(0,n.useState)(!1);(0,n.useEffect)(()=>{(async()=>{if(s?.user?.id&&"authenticated"===t){G(!0);try{let e=await fetch(`/api/users/${s.user.id}`);if(e.ok){let s=(await e.json()).data;F(s.addresses||[]);let t=s.addresses?.find(e=>e.isDefault)||s.addresses?.[0];t?(O(t.id),k(e=>({...e,firstName:t.firstName,lastName:t.lastName,email:s.email,phone:t.phone||s.phone||"",address:t.address1,city:t.city,state:t.state,zipCode:t.postalCode,country:t.country}))):k(e=>({...e,email:s.email,phone:s.phone||"",firstName:s.name?.split(" ")[0]||"",lastName:s.name?.split(" ").slice(1).join(" ")||""}))}}catch(e){console.error("Error fetching user data:",e)}finally{G(!1)}}})()},[s?.user?.id,t]);let H=[{id:1,name:"Shipping",icon:d.Z},{id:2,name:"Payment",icon:o.Z},{id:3,name:"Review",icon:c.Z}],Y=e=>{let{name:s,value:t,type:a}=e.target;k(r=>({...r,[s]:"checkbox"===a?e.target.checked:t})),S[s]&&P(e=>({...e,[s]:""}))},W=e=>{let s=M.find(s=>s.id===e);s&&(O(e),k(e=>({...e,firstName:s.firstName,lastName:s.lastName,phone:s.phone||e.phone,address:s.address1,city:s.city,state:s.state,zipCode:s.postalCode,country:s.country})),q(!1))},J=async e=>{e.preventDefault(),T(!0),R("");try{let e=await (0,i.signIn)("credentials",{email:E.email,password:E.password,redirect:!1});e?.error?R("Invalid email or password"):(z(!1),I({email:"",password:""}))}catch(e){R("An error occurred during login")}finally{T(!1)}},Q=e=>{let{name:s,value:t}=e.target;I(e=>({...e,[s]:t})),L&&R("")},V=e=>{let s={};return 1!==e||(C.firstName||(s.firstName="First name is required"),C.lastName||(s.lastName="Last name is required"),C.email||(s.email="Email is required"),C.phone||(s.phone="Phone is required"),C.address||(s.address="Address is required"),C.city||(s.city="City is required"),C.state||(s.state="State is required"),C.zipCode||(s.zipCode="ZIP code is required")),2!==e||C.paymentMethod||(s.paymentMethod="Please select a payment method"),P(s),0===Object.keys(s).length},K=()=>{V(N)&&v(e=>Math.min(e+1,3))},X=()=>{v(e=>Math.max(e-1,1))},ee=s=>{D(!1),u({type:"CLEAR_CART"}),e.push(`/order-confirmation?orderId=${s}`)},es=e=>{D(!1),P({payment:e})},et=async()=>{if(V(2)){D(!0);try{let e=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cartItems:a.items.map(e=>({productId:e.product.id,quantity:e.quantity,price:e.product.price})),shippingAddress:{firstName:C.firstName,lastName:C.lastName,address1:C.address,address2:"",city:C.city,state:C.state,postalCode:C.zipCode,country:C.country,phone:C.phone},totalAmount:er,paymentMethod:"COD",appliedCoupons:[],flashSaleDiscount:a.flashSaleDiscount})});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to create order")}let s=await e.json();ee(s.order.id)}catch(e){es(e.message||"Failed to create order")}}};a.subtotal;let ea="cod"===C.paymentMethod?50:0,er=a.finalTotal+ea;return 0===a.items.length?r.jsx("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:(0,r.jsxs)("div",{className:"lg:col-span-12 text-center py-16",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Your cart is empty"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"Add some products before checkout"}),r.jsx("button",{onClick:()=>e.push("/shop"),className:"bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:"Continue Shopping"})]})}):(0,r.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-8",children:[(0,r.jsxs)("div",{className:"lg:hidden",children:[(0,r.jsxs)("div",{className:"sticky top-16 bg-white z-30 px-4 py-4 border-b",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("button",{onClick:()=>e.back(),className:"p-2 rounded-full hover:bg-gray-100 transition-colors",children:r.jsx(m.Z,{className:"w-5 h-5"})}),r.jsx("h1",{className:"text-xl font-bold text-gray-800",children:"Checkout"}),r.jsx("div",{})]}),r.jsx("div",{className:"flex items-center justify-between",children:H.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${N>=e.id?"bg-green-600 text-white":"bg-gray-200 text-gray-500"}`,children:N>e.id?r.jsx(x.Z,{className:"w-4 h-4"}):r.jsx(e.icon,{className:"w-4 h-4"})}),r.jsx("span",{className:`ml-2 text-sm font-medium ${N>=e.id?"text-green-600":"text-gray-500"}`,children:e.name}),s<H.length-1&&r.jsx("div",{className:`w-8 h-0.5 mx-2 ${N>e.id?"bg-green-600":"bg-gray-200"}`})]},e.id))})]}),(0,r.jsxs)("div",{className:"px-4 py-6",children:["authenticated"!==t&&!Z&&r.jsx("div",{className:"mb-6 p-4 bg-blue-50 rounded-xl border border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(h.Z,{className:"w-5 h-5 text-blue-600"}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium text-blue-900",children:"Have an account?"}),r.jsx("p",{className:"text-sm text-blue-700",children:"Login to use saved addresses and faster checkout"})]})]}),(0,r.jsxs)("button",{onClick:()=>z(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors",children:[r.jsx(p,{className:"w-4 h-4"}),r.jsx("span",{children:"Login"})]})]})}),Z&&"authenticated"!==t&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-white rounded-xl border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"font-semibold text-gray-800",children:"Login to Your Account"}),r.jsx("button",{onClick:()=>z(!1),className:"text-gray-500 hover:text-gray-700",children:"\xd7"})]}),(0,r.jsxs)("form",{onSubmit:J,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:E.email,onChange:Q,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),r.jsx("input",{type:"password",name:"password",value:E.password,onChange:Q,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),L&&r.jsx("p",{className:"text-red-500 text-sm",children:L}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[r.jsx("button",{type:"submit",disabled:U,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50",children:U?"Logging in...":"Login"}),r.jsx("button",{type:"button",onClick:()=>z(!1),className:"px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"})]})]})]}),1===N&&(0,r.jsxs)("div",{className:"space-y-4",children:["authenticated"===t&&M.length>0&&!A&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Select Shipping Address"}),(0,r.jsxs)("button",{onClick:()=>q(!0),className:"flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium",children:[r.jsx(g.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Edit Details"})]})]}),r.jsx("div",{className:"space-y-3",children:M.map(e=>(0,r.jsxs)("label",{className:`flex items-start p-4 border-2 rounded-xl cursor-pointer transition-all ${_===e.id?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:[r.jsx("input",{type:"radio",name:"selectedAddress",value:e.id,checked:_===e.id,onChange:()=>W(e.id),className:"sr-only"}),r.jsx("div",{className:`w-5 h-5 rounded-full border-2 mr-3 mt-0.5 flex items-center justify-center ${_===e.id?"border-green-600":"border-gray-300"}`,children:_===e.id&&r.jsx("div",{className:"w-3 h-3 rounded-full bg-green-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&r.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full",children:"Default"})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.address1,e.address2&&`, ${e.address2}`]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.city,", ",e.state," ",e.postalCode]}),e.phone&&r.jsx("p",{className:"text-sm text-gray-600",children:e.phone})]})]},e.id))})]}),("authenticated"!==t||0===M.length||A)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"authenticated"===t&&M.length>0?"Edit Shipping Details":"Shipping Information"}),"authenticated"===t&&M.length>0&&A&&r.jsx("button",{onClick:()=>q(!1),className:"text-gray-600 hover:text-gray-800 font-medium",children:"Use Saved Address"})]}),B&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[r.jsx("div",{className:"w-6 h-6 border-2 border-green-600 border-t-transparent rounded-full animate-spin"}),r.jsx("span",{className:"ml-2 text-gray-600",children:"Loading your details..."})]})]}),("authenticated"!==t||0===M.length||A)&&!B&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),r.jsx("input",{type:"text",name:"firstName",value:C.firstName,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.firstName?"border-red-500":"border-gray-300"}`}),S.firstName&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.firstName})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),r.jsx("input",{type:"text",name:"lastName",value:C.lastName,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.lastName?"border-red-500":"border-gray-300"}`}),S.lastName&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.lastName})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:C.email,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.email?"border-red-500":"border-gray-300"}`}),S.email&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.email})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),r.jsx("input",{type:"tel",name:"phone",value:C.phone,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.phone?"border-red-500":"border-gray-300"}`}),S.phone&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.phone})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address"}),r.jsx("input",{type:"text",name:"address",value:C.address,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.address?"border-red-500":"border-gray-300"}`}),S.address&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.address})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City"}),r.jsx("input",{type:"text",name:"city",value:C.city,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.city?"border-red-500":"border-gray-300"}`}),S.city&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.city})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State"}),r.jsx("input",{type:"text",name:"state",value:C.state,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.state?"border-red-500":"border-gray-300"}`}),S.state&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.state})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ZIP Code"}),r.jsx("input",{type:"text",name:"zipCode",value:C.zipCode,onChange:Y,className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${S.zipCode?"border-red-500":"border-gray-300"}`}),S.zipCode&&r.jsx("p",{className:"text-red-500 text-xs mt-1",children:S.zipCode})]})]})]}),2===N&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Payment Method"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all ${"online"===C.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:[r.jsx("input",{type:"radio",name:"paymentMethod",value:"online",checked:"online"===C.paymentMethod,onChange:Y,className:"sr-only"}),r.jsx("div",{className:`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${"online"===C.paymentMethod?"border-green-600":"border-gray-300"}`,children:"online"===C.paymentMethod&&r.jsx("div",{className:"w-3 h-3 rounded-full bg-green-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(y,{className:"w-5 h-5 text-gray-600"}),r.jsx("span",{className:"font-medium text-gray-800",children:"Online Payment"})]}),r.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Pay securely using Credit/Debit Card, UPI, Net Banking, or Wallets"})]})]}),(0,r.jsxs)("label",{className:`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all ${"cod"===C.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:[r.jsx("input",{type:"radio",name:"paymentMethod",value:"cod",checked:"cod"===C.paymentMethod,onChange:Y,className:"sr-only"}),r.jsx("div",{className:`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${"cod"===C.paymentMethod?"border-green-600":"border-gray-300"}`,children:"cod"===C.paymentMethod&&r.jsx("div",{className:"w-3 h-3 rounded-full bg-green-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(b,{className:"w-5 h-5 text-gray-600"}),r.jsx("span",{className:"font-medium text-gray-800",children:"Cash on Delivery"})]}),r.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Pay when your order is delivered to your doorstep"})]})]})]}),S.paymentMethod&&r.jsx("p",{className:"text-red-500 text-sm mt-2",children:S.paymentMethod}),"online"===C.paymentMethod&&r.jsx("div",{className:"mt-4 p-4 bg-blue-50 rounded-xl",children:(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:[r.jsx("strong",{children:"Note:"})," You will be redirected to a secure payment gateway after reviewing your order."]})}),"cod"===C.paymentMethod&&r.jsx("div",{className:"mt-4 p-4 bg-yellow-50 rounded-xl",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-800",children:[r.jsx("strong",{children:"Note:"})," Please keep exact change ready for delivery. Additional charges may apply for COD orders."]})})]}),3===N&&(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Review Your Order"}),r.jsx("div",{className:"space-y-4",children:a.items.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[r.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-medium text-gray-800",children:e.product.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Qty: ",e.quantity]})]}),(0,r.jsxs)("span",{className:"font-medium text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]},e.product.id))}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Subtotal"}),(0,r.jsxs)("span",{children:["₹",a.subtotal.toFixed(2)]})]}),a.flashSaleDiscount>0&&(0,r.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,r.jsxs)("span",{children:["Flash Sale Discount (",a.flashSalePercentage,"% OFF)"]}),(0,r.jsxs)("span",{children:["-₹",a.flashSaleDiscount.toFixed(2)]})]}),a.coupons.totalDiscount>0&&(0,r.jsxs)("div",{className:"flex justify-between text-green-600",children:[r.jsx("span",{children:"Coupon Discount"}),(0,r.jsxs)("span",{children:["-₹",a.coupons.totalDiscount.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Shipping"}),r.jsx("span",{children:"Free"})]}),"cod"===C.paymentMethod&&(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"COD Charges"}),(0,r.jsxs)("span",{children:["₹",ea.toFixed(2)]})]}),r.jsx("div",{className:"border-t border-gray-300 pt-2",children:(0,r.jsxs)("div",{className:"flex justify-between font-bold text-gray-900",children:[r.jsx("span",{children:"Total"}),(0,r.jsxs)("span",{children:["₹",er.toFixed(2)]})]})})]})]}),(0,r.jsxs)("div",{className:"flex justify-between mt-8 pt-6 border-t",children:[N>1&&(0,r.jsxs)("button",{onClick:X,className:"flex items-center space-x-2 px-6 py-3 border border-gray-300 rounded-full font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[r.jsx(m.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Back"})]}),r.jsx("div",{className:"ml-auto",children:N<3?(0,r.jsxs)("button",{onClick:K,className:"flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors",children:[r.jsx("span",{children:"Continue"}),r.jsx(j.Z,{className:"w-4 h-4"})]}):(0,r.jsxs)("div",{className:"space-y-3",children:["online"===C.paymentMethod?r.jsx(w,{cartItems:a.items.map(e=>({productId:e.product.id,quantity:e.quantity,price:e.product.price,product:{name:e.product.name,price:e.product.price}})),shippingAddress:{firstName:C.firstName,lastName:C.lastName,address1:C.address,address2:"",city:C.city,state:C.state,postalCode:C.zipCode,country:C.country,phone:C.phone},totalAmount:a.finalTotal,appliedCoupons:[],flashSaleDiscount:a.flashSaleDiscount,onSuccess:ee,onError:es,disabled:$,className:"rounded-full"}):r.jsx("button",{onClick:et,disabled:$,className:"flex items-center justify-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors disabled:opacity-50 w-full",children:$?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"Processing..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("span",{children:"Place COD Order"}),r.jsx(j.Z,{className:"w-4 h-4"})]})}),S.payment&&r.jsx("p",{className:"text-red-500 text-sm text-center",children:S.payment})]})})]})]})]}),r.jsx("div",{className:"hidden lg:block lg:col-span-12",children:(0,r.jsxs)("div",{className:"py-8",children:[r.jsx("div",{className:"flex items-center mb-8",children:(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors",children:[r.jsx(m.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Back to Cart"})]})}),r.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"Checkout"}),"authenticated"!==t&&!Z&&r.jsx("div",{className:"mb-8 p-6 bg-blue-50 rounded-xl border border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx(h.Z,{className:"w-6 h-6 text-blue-600"}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Have an account?"}),r.jsx("p",{className:"text-blue-700",children:"Login to use saved addresses and faster checkout"})]})]}),(0,r.jsxs)("button",{onClick:()=>z(!0),className:"flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors",children:[r.jsx(p,{className:"w-5 h-5"}),r.jsx("span",{children:"Login"})]})]})}),Z&&"authenticated"!==t&&(0,r.jsxs)("div",{className:"mb-8 p-6 bg-white rounded-xl border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-800",children:"Login to Your Account"}),r.jsx("button",{onClick:()=>z(!1),className:"text-gray-500 hover:text-gray-700 text-xl",children:"\xd7"})]}),(0,r.jsxs)("form",{onSubmit:J,className:"grid grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:E.email,onChange:Q,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),r.jsx("input",{type:"password",name:"password",value:E.password,onChange:Q,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,r.jsxs)("div",{className:"col-span-2",children:[L&&r.jsx("p",{className:"text-red-500 text-sm mb-4",children:L}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[r.jsx("button",{type:"submit",disabled:U,className:"bg-blue-600 text-white px-8 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors disabled:opacity-50",children:U?"Logging in...":"Login"}),r.jsx("button",{type:"button",onClick:()=>z(!1),className:"px-8 py-3 border border-gray-300 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"})]})]})]})]}),r.jsx("div",{className:"flex items-center justify-center mb-12",children:H.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${N>=e.id?"bg-green-600 text-white":"bg-gray-200 text-gray-500"}`,children:N>e.id?r.jsx(x.Z,{className:"w-6 h-6"}):r.jsx(e.icon,{className:"w-6 h-6"})}),r.jsx("span",{className:`ml-3 text-lg font-medium ${N>=e.id?"text-green-600":"text-gray-500"}`,children:e.name}),s<H.length-1&&r.jsx("div",{className:`w-24 h-0.5 mx-8 ${N>e.id?"bg-green-600":"bg-gray-200"}`})]},e.id))}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-12",children:[(0,r.jsxs)("div",{className:"col-span-2",children:[1===N&&(0,r.jsxs)("div",{className:"space-y-6",children:["authenticated"===t&&M.length>0&&!A&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:"Select Shipping Address"}),(0,r.jsxs)("button",{onClick:()=>q(!0),className:"flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium",children:[r.jsx(g.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Edit Details"})]})]}),r.jsx("div",{className:"space-y-4",children:M.map(e=>(0,r.jsxs)("label",{className:`flex items-start p-6 border-2 rounded-xl cursor-pointer transition-all ${_===e.id?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:[r.jsx("input",{type:"radio",name:"selectedAddress",value:e.id,checked:_===e.id,onChange:()=>W(e.id),className:"sr-only"}),r.jsx("div",{className:`w-6 h-6 rounded-full border-2 mr-4 mt-1 flex items-center justify-center ${_===e.id?"border-green-600":"border-gray-300"}`,children:_===e.id&&r.jsx("div",{className:"w-3.5 h-3.5 rounded-full bg-green-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsxs)("span",{className:"text-lg font-medium text-gray-800",children:[e.firstName," ",e.lastName]}),e.isDefault&&r.jsx("span",{className:"px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-full",children:"Default"})]}),(0,r.jsxs)("p",{className:"text-gray-600 mb-1",children:[e.address1,e.address2&&`, ${e.address2}`]}),(0,r.jsxs)("p",{className:"text-gray-600 mb-1",children:[e.city,", ",e.state," ",e.postalCode]}),e.phone&&r.jsx("p",{className:"text-gray-600",children:e.phone})]})]},e.id))})]}),("authenticated"!==t||0===M.length||A)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"authenticated"===t&&M.length>0?"Edit Shipping Details":"Shipping Information"}),"authenticated"===t&&M.length>0&&A&&r.jsx("button",{onClick:()=>q(!1),className:"text-gray-600 hover:text-gray-800 font-medium",children:"Use Saved Address"})]}),B&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[r.jsx("div",{className:"w-8 h-8 border-2 border-green-600 border-t-transparent rounded-full animate-spin"}),r.jsx("span",{className:"ml-3 text-gray-600",children:"Loading your details..."})]}),!B&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),r.jsx("input",{type:"text",name:"firstName",value:C.firstName,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.firstName?"border-red-500":"border-gray-300"}`}),S.firstName&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.firstName})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),r.jsx("input",{type:"text",name:"lastName",value:C.lastName,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.lastName?"border-red-500":"border-gray-300"}`}),S.lastName&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.lastName})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),r.jsx("input",{type:"email",name:"email",value:C.email,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.email?"border-red-500":"border-gray-300"}`}),S.email&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.email})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone"}),r.jsx("input",{type:"tel",name:"phone",value:C.phone,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.phone?"border-red-500":"border-gray-300"}`}),S.phone&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.phone})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Address"}),r.jsx("input",{type:"text",name:"address",value:C.address,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.address?"border-red-500":"border-gray-300"}`}),S.address&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.address})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),r.jsx("input",{type:"text",name:"city",value:C.city,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.city?"border-red-500":"border-gray-300"}`}),S.city&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.city})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"State"}),r.jsx("input",{type:"text",name:"state",value:C.state,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.state?"border-red-500":"border-gray-300"}`}),S.state&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.state})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ZIP Code"}),r.jsx("input",{type:"text",name:"zipCode",value:C.zipCode,onChange:Y,className:`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${S.zipCode?"border-red-500":"border-gray-300"}`}),S.zipCode&&r.jsx("p",{className:"text-red-500 text-sm mt-1",children:S.zipCode})]})]})]})]})]}),2===N&&(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:"Payment Method"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("label",{className:`flex items-center p-6 border-2 rounded-xl cursor-pointer transition-all ${"online"===C.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:[r.jsx("input",{type:"radio",name:"paymentMethod",value:"online",checked:"online"===C.paymentMethod,onChange:Y,className:"sr-only"}),r.jsx("div",{className:`w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center ${"online"===C.paymentMethod?"border-green-600":"border-gray-300"}`,children:"online"===C.paymentMethod&&r.jsx("div",{className:"w-3.5 h-3.5 rounded-full bg-green-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(y,{className:"w-6 h-6 text-gray-600"}),r.jsx("span",{className:"text-lg font-medium text-gray-800",children:"Online Payment"})]}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Pay securely using Credit Card, Debit Card, UPI, Net Banking, or Digital Wallets"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 mt-3",children:[r.jsx("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"Credit Card"}),r.jsx("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"Debit Card"}),r.jsx("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"UPI"}),r.jsx("span",{className:"px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm",children:"Net Banking"})]})]})]}),(0,r.jsxs)("label",{className:`flex items-center p-6 border-2 rounded-xl cursor-pointer transition-all ${"cod"===C.paymentMethod?"border-green-600 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:[r.jsx("input",{type:"radio",name:"paymentMethod",value:"cod",checked:"cod"===C.paymentMethod,onChange:Y,className:"sr-only"}),r.jsx("div",{className:`w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center ${"cod"===C.paymentMethod?"border-green-600":"border-gray-300"}`,children:"cod"===C.paymentMethod&&r.jsx("div",{className:"w-3.5 h-3.5 rounded-full bg-green-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx(b,{className:"w-6 h-6 text-gray-600"}),r.jsx("span",{className:"text-lg font-medium text-gray-800",children:"Cash on Delivery"})]}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Pay when your order is delivered to your doorstep"}),r.jsx("p",{className:"text-sm text-yellow-700 mt-2 bg-yellow-50 px-3 py-1 rounded-lg inline-block",children:"₹50 additional COD charges apply"})]})]})]}),S.paymentMethod&&r.jsx("p",{className:"text-red-500 text-sm mt-2",children:S.paymentMethod}),"online"===C.paymentMethod&&(0,r.jsxs)("div",{className:"mt-6 p-6 bg-blue-50 rounded-xl",children:[r.jsx("h3",{className:"font-medium text-blue-900 mb-2",children:"Secure Payment Gateway"}),r.jsx("p",{className:"text-blue-800",children:"You will be redirected to our secure payment partner after reviewing your order. All major payment methods are accepted."})]}),"cod"===C.paymentMethod&&(0,r.jsxs)("div",{className:"mt-6 p-6 bg-yellow-50 rounded-xl",children:[r.jsx("h3",{className:"font-medium text-yellow-900 mb-2",children:"Cash on Delivery Guidelines"}),(0,r.jsxs)("ul",{className:"text-yellow-800 space-y-1 list-disc list-inside",children:[r.jsx("li",{children:"Please keep exact change ready for smooth delivery"}),r.jsx("li",{children:"Additional ₹50 will be charged for COD orders"}),r.jsx("li",{children:"Order cannot be cancelled once out for delivery"})]})]})]}),3===N&&(0,r.jsxs)("div",{className:"space-y-8",children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:"Review Your Order"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6",children:[r.jsx("h3",{className:"font-semibold text-gray-800 mb-4",children:"Shipping Address"}),(0,r.jsxs)("p",{className:"text-gray-600",children:[C.firstName," ",C.lastName,r.jsx("br",{}),C.address,r.jsx("br",{}),C.city,", ",C.state," ",C.zipCode]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6",children:[r.jsx("h3",{className:"font-semibold text-gray-800 mb-4",children:"Payment Method"}),r.jsx("p",{className:"text-gray-600",children:"online"===C.paymentMethod?(0,r.jsxs)(r.Fragment,{children:[r.jsx(y,{className:"w-5 h-5 inline mr-2"}),"Online Payment - Secure Gateway"]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(b,{className:"w-5 h-5 inline mr-2"}),"Cash on Delivery (COD)"]})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h3",{className:"font-semibold text-gray-800",children:"Order Items"}),a.items.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-6 p-4 bg-gray-50 rounded-xl",children:[r.jsx("div",{className:"w-20 h-20 bg-gray-200 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h4",{className:"font-medium text-gray-800",children:e.product.name}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Quantity: ",e.quantity]})]}),(0,r.jsxs)("span",{className:"font-semibold text-gray-900",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]},e.product.id))]})]}),(0,r.jsxs)("div",{className:"flex justify-between mt-12 pt-8 border-t",children:[N>1&&(0,r.jsxs)("button",{onClick:X,className:"flex items-center space-x-2 px-8 py-4 border border-gray-300 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[r.jsx(m.Z,{className:"w-5 h-5"}),r.jsx("span",{children:"Back"})]}),r.jsx("div",{className:"ml-auto",children:N<3?(0,r.jsxs)("button",{onClick:K,className:"flex items-center space-x-2 bg-green-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors text-lg",children:[r.jsx("span",{children:"Continue"}),r.jsx(j.Z,{className:"w-5 h-5"})]}):(0,r.jsxs)("div",{className:"space-y-3",children:["online"===C.paymentMethod?r.jsx(w,{cartItems:a.items.map(e=>({productId:e.product.id,quantity:e.quantity,price:e.product.price,product:{name:e.product.name,price:e.product.price}})),shippingAddress:{firstName:C.firstName,lastName:C.lastName,address1:C.address,address2:"",city:C.city,state:C.state,postalCode:C.zipCode,country:C.country,phone:C.phone},totalAmount:a.finalTotal,appliedCoupons:[],flashSaleDiscount:a.flashSaleDiscount,onSuccess:ee,onError:es,disabled:$,className:"rounded-xl text-lg px-8 py-4"}):r.jsx("button",{onClick:et,disabled:$,className:"flex items-center justify-center space-x-2 bg-green-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 text-lg w-full",children:$?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"Processing..."})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("span",{children:"Place COD Order"}),r.jsx(j.Z,{className:"w-5 h-5"})]})}),S.payment&&r.jsx("p",{className:"text-red-500 text-sm text-center",children:S.payment})]})})]})]}),r.jsx("div",{className:"col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Order Summary"}),r.jsx("div",{className:"space-y-4 mb-6",children:a.items.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx("p",{className:"font-medium text-gray-800 text-sm line-clamp-1",children:e.product.name}),(0,r.jsxs)("p",{className:"text-gray-500 text-xs",children:["Qty: ",e.quantity]})]}),(0,r.jsxs)("span",{className:"font-medium text-gray-900 text-sm",children:["₹",(e.product.price*e.quantity).toFixed(2)]})]},e.product.id))}),(0,r.jsxs)("div",{className:"space-y-3 mb-6 pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Subtotal"}),(0,r.jsxs)("span",{children:["₹",a.subtotal.toFixed(2)]})]}),a.flashSaleDiscount>0&&(0,r.jsxs)("div",{className:"flex justify-between text-red-600",children:[(0,r.jsxs)("span",{children:["Flash Sale Discount (",a.flashSalePercentage,"% OFF)"]}),(0,r.jsxs)("span",{children:["-₹",a.flashSaleDiscount.toFixed(2)]})]}),a.coupons.totalDiscount>0&&(0,r.jsxs)("div",{className:"flex justify-between text-green-600",children:[r.jsx("span",{children:"Coupon Discount"}),(0,r.jsxs)("span",{children:["-₹",a.coupons.totalDiscount.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"Shipping"}),r.jsx("span",{className:"text-green-600 font-medium",children:"Free"})]}),"cod"===C.paymentMethod&&(0,r.jsxs)("div",{className:"flex justify-between text-gray-600",children:[r.jsx("span",{children:"COD Charges"}),(0,r.jsxs)("span",{children:["₹",ea.toFixed(2)]})]}),r.jsx("div",{className:"border-t border-gray-200 pt-3",children:(0,r.jsxs)("div",{className:"flex justify-between font-bold text-gray-900 text-lg",children:[r.jsx("span",{children:"Total"}),(0,r.jsxs)("span",{children:["₹",er.toFixed(2)]})]})})]})]})})]})]})})]})}},86333:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},24230:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},32933:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},28916:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},82685:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]])},28549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(19510),r=t(40304);let n=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Checkout.tsx#default`);function l(){return a.jsx(r.Z,{children:a.jsx(n,{})})}},40304:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,3757,434,9694,9536,5090],()=>t(36666));module.exports=a})();