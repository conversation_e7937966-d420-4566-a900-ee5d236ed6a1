"use strict";(()=>{var e={};e.id=6041,e.ids=[6041],e.modules={11185:e=>{e.exports=require("mongoose")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},30305:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>S,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>l});var i={};r.r(i),r.d(i,{GET:()=>c});var o=r(49303),s=r(88716),a=r(60670),n=r(87070),d=r(3474),u=r(54211);async function c(){try{await (0,d.uD)();let[e,t,r,i,o,s,a]=await Promise.all([d.xs.countDocuments(),d.WD.countDocuments(),d.n5.countDocuments(),d.n5.countDocuments({role:"CUSTOMER"}),d.KM.countDocuments(),d.xs.countDocuments({isActive:!0}),d.xs.countDocuments({isFeatured:!0})]),u=await d.xs.find().sort({createdAt:-1}).limit(5).populate("categoryId","name").lean(),c=await d.n5.find().sort({createdAt:-1}).limit(5).select("-password").lean();return n.NextResponse.json({success:!0,data:{overview:{totalProducts:e,totalCategories:t,totalUsers:r,totalCustomers:i,totalOrders:o,activeProducts:s,featuredProducts:a},recent:{products:u,users:c},growth:{productsGrowth:"+12.5%",categoriesGrowth:"+5.2%",usersGrowth:"+8.1%",ordersGrowth:"+15.3%"}}})}catch(e){return u.kg.error("Error fetching dashboard stats:",e),n.NextResponse.json({success:!1,error:"Failed to fetch dashboard statistics"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:p,staticGenerationAsyncStorage:l,serverHooks:y}=m,g="/api/dashboard/stats/route";function S(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:l})}},3474:(e,t,r)=>{r.d(t,{KM:()=>o.Order,WD:()=>o.WD,n5:()=>o.n5,uD:()=>i.Z,xs:()=>o.xs});var i=r(89456),o=r(81515)},54211:(e,t,r)=>{var i;r.d(t,{kg:()=>s}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(i||(i={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:t,level:r,message:o,context:s,error:a,userId:n,requestId:d}=e,u=i[r],c=`[${t}] ${u}: ${o}`;return n&&(c+=` | User: ${n}`),d&&(c+=` | Request: ${d}`),s&&Object.keys(s).length>0&&(c+=` | Context: ${JSON.stringify(s)}`),a&&(c+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(c+=`
Stack: ${a.stack}`)),c}log(e,t,r,i){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:t,context:r,error:i},s=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(s);break;case 1:console.warn(s);break;case 2:console.info(s);break;case 3:console.debug(s)}else console.log(JSON.stringify(o))}error(e,t,r){this.log(0,e,r,t)}warn(e,t){this.log(1,e,t)}info(e,t){this.log(2,e,t)}debug(e,t){this.log(3,e,t)}apiRequest(e,t,r,i){this.info(`API ${e} ${t}`,{...i,userId:r,type:"api_request"})}apiResponse(e,t,r,i,o){this.info(`API ${e} ${t} - ${r}`,{...o,statusCode:r,duration:i,type:"api_response"})}apiError(e,t,r,i,o){this.error(`API ${e} ${t} failed`,r,{...o,userId:i,type:"api_error"})}authSuccess(e,t,r){this.info("Authentication successful",{...r,userId:e,method:t,type:"auth_success"})}authFailure(e,t,r,i){this.warn("Authentication failed",{...i,email:e,method:t,reason:r,type:"auth_failure"})}dbQuery(e,t,r,i){this.debug(`DB ${e} on ${t}`,{...i,operation:e,table:t,duration:r,type:"db_query"})}dbError(e,t,r,i){this.error(`DB ${e} on ${t} failed`,r,{...i,operation:e,table:t,type:"db_error"})}securityEvent(e,t,r){this.log("high"===t?0:"medium"===t?1:2,`Security event: ${e}`,{...r,severity:t,type:"security_event"})}rateLimitHit(e,t,r,i){this.warn("Rate limit exceeded",{...i,identifier:e,limit:t,window:r,type:"rate_limit"})}emailSent(e,t,r,i){this.info("Email sent",{...i,to:e,subject:t,template:r,type:"email_sent"})}emailError(e,t,r,i){this.error("Email failed to send",r,{...i,to:e,subject:t,type:"email_error"})}performance(e,t,r){this.log(t>5e3?1:3,`Performance: ${e} took ${t}ms`,{...r,operation:e,duration:t,type:"performance"})}}let s=new o},81515:(e,t,r)=>{r.d(t,{Cq:()=>R,Dd:()=>A,Order:()=>E,P_:()=>j,Pp:()=>$,Th:()=>T,Vv:()=>B,WD:()=>w,gc:()=>v,hQ:()=>M,kL:()=>C,mA:()=>U,n5:()=>N,nW:()=>O,p1:()=>_,qN:()=>P,wV:()=>x,xs:()=>D});var i=r(11185),o=r.n(i);let s=new i.Schema({email:{type:String,required:!0,unique:!0},name:String,phone:String,avatar:String,password:String,role:{type:String,enum:["ADMIN","CUSTOMER"],default:"CUSTOMER"},emailVerified:Date,resetToken:String,resetTokenExpiry:Date},{timestamps:!0,collection:"user"}),a=new i.Schema({name:{type:String,required:!0},slug:{type:String,required:!0,unique:!0},description:String,shortDescription:String,comparePrice:Number,costPrice:Number,weight:Number,dimensions:String,isActive:{type:Boolean,default:!0},isFeatured:{type:Boolean,default:!1},metaTitle:String,metaDescription:String,categoryId:{type:i.Schema.Types.ObjectId,ref:"Category"},price:Number},{timestamps:!0,collection:"product"}),n=new i.Schema({name:{type:String,required:!0,unique:!0},slug:{type:String,required:!0,unique:!0},description:String,parentId:{type:i.Schema.Types.ObjectId,ref:"Category"},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"category"}),d=new i.Schema({orderNumber:{type:String,required:!0,unique:!0},status:{type:String,enum:["PENDING","CONFIRMED","PROCESSING","SHIPPED","DELIVERED","CANCELLED","REFUNDED"],default:"PENDING"},paymentStatus:{type:String,enum:["PENDING","PAID","FAILED","REFUNDED"],default:"PENDING"},paymentMethod:String,paymentId:String,subtotal:{type:Number,required:!0},tax:{type:Number,default:0},shipping:{type:Number,default:0},discount:{type:Number,default:0},total:{type:Number,required:!0},currency:{type:String,default:"INR"},notes:String,userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},couponCodes:[String],couponDiscount:{type:Number,default:0},flashSaleDiscount:{type:Number,default:0},estimatedDelivery:String,trackingNumber:String,addressId:{type:i.Schema.Types.ObjectId,ref:"Address"}},{timestamps:!0,collection:"order"}),u=new i.Schema({productOfTheMonthId:{type:i.Schema.Types.ObjectId,ref:"Product"},bannerText:String,bannerCtaText:String,bannerCtaLink:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"homepageSetting"}),c=new i.Schema({name:{type:String,required:!0},content:{type:String,required:!0},rating:{type:Number,default:5,min:1,max:5},image:String,position:String,company:String,isActive:{type:Boolean,default:!0},order:{type:Number,default:0}},{timestamps:!0,collection:"testimonials"}),m=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},url:{type:String,required:!0},alt:String,position:{type:Number,default:0}},{timestamps:!0,collection:"images"}),p=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},name:{type:String,required:!0},value:{type:String,required:!0},price:Number,pricingMode:{type:String,enum:["REPLACE","INCREMENT","FIXED"],default:"REPLACE"}},{timestamps:!0,collection:"variants"}),l=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},rating:{type:Number,required:!0,min:1,max:5},title:String,comment:String,isApproved:{type:Boolean,default:!1}},{timestamps:!0,collection:"reviews"}),y=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},firstName:{type:String,required:!0},lastName:{type:String,required:!0},address1:{type:String,required:!0},address2:String,city:{type:String,required:!0},state:{type:String,required:!0},postalCode:{type:String,required:!0},country:{type:String,required:!0},phone:{type:String,required:!0},isDefault:{type:Boolean,default:!1}},{timestamps:!0,collection:"address"}),g=new i.Schema({orderId:{type:i.Schema.Types.ObjectId,ref:"Order",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},quantity:{type:Number,required:!0},price:{type:Number,required:!0},total:{type:Number,required:!0}},{timestamps:!0,collection:"items"}),S=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},title:{type:String,required:!0},message:{type:String,required:!0},type:{type:String,enum:["ORDER","PRODUCT","SYSTEM","MARKETING"],required:!0},isRead:{type:Boolean,default:!1},data:i.Schema.Types.Mixed},{timestamps:!0,collection:"notifications"}),h=new i.Schema({code:{type:String,required:!0,unique:!0},name:{type:String,required:!0},description:String,type:{type:String,enum:["PERCENTAGE","FIXED"],required:!0},value:{type:Number,required:!0},minimumAmount:Number,maximumDiscount:Number,usageLimit:Number,usedCount:{type:Number,default:0},isActive:{type:Boolean,default:!0},validFrom:{type:Date,required:!0},validUntil:Date,userUsageLimit:Number,isStackable:Boolean,showInModule:Boolean,applicableProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],applicableCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],excludedProducts:[{type:i.Schema.Types.ObjectId,ref:"Product"}],excludedCategories:[{type:i.Schema.Types.ObjectId,ref:"Category"}],customerSegments:[String]},{timestamps:!0,collection:"coupons"}),f=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlist"}),b=new i.Schema({email:{type:String,required:!0,unique:!0},isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"newsletter"}),q=new i.Schema({productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0},categoryId:{type:i.Schema.Types.ObjectId,ref:"Category",required:!0}},{timestamps:!0,collection:"productCategories"}),I=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:!0},productId:{type:i.Schema.Types.ObjectId,ref:"Product",required:!0}},{timestamps:!0,collection:"wishlistItems"}),N=o().models.User||o().model("User",s),D=o().models.Product||o().model("Product",a),w=o().models.Category||o().model("Category",n),E=o().models.Order||o().model("Order",d),O=o().models.HomepageSetting||o().model("HomepageSetting",u),v=o().models.Testimonial||o().model("Testimonial",c),P=o().models.ProductImage||o().model("ProductImage",m),T=o().models.ProductVariant||o().model("ProductVariant",p),R=o().models.Review||o().model("Review",l),C=o().models.Address||o().model("Address",y),A=o().models.OrderItem||o().model("OrderItem",g),j=o().models.Notification||o().model("Notification",S),x=o().models.Coupon||o().model("Coupon",h);o().models.Wishlist||o().model("Wishlist",f);let U=o().models.Newsletter||o().model("Newsletter",b),$=o().models.ProductCategory||o().model("ProductCategory",q),M=o().models.WishlistItem||o().model("WishlistItem",I),k=new i.Schema({name:{type:String,required:!0,unique:!0},type:{type:String,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},emailSubject:String,emailTemplate:String,isActive:{type:Boolean,default:!0}},{timestamps:!0,collection:"notification_templates"}),B=o().models.NotificationTemplate||o().model("NotificationTemplate",k),G=new i.Schema({name:{type:String,required:!0},email:{type:String,required:!0},subject:{type:String,required:!0},message:{type:String,required:!0},status:{type:String,default:"NEW"},notes:String},{timestamps:!0,collection:"enquiry"}),_=o().models.Enquiry||o().model("Enquiry",G)},89456:(e,t,r)=>{r.d(t,{Z:()=>n});var i=r(11185),o=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let n=async function(){if(a.conn)return a.conn;a.promise||(a.promise=o().connect(s,{bufferCommands:!1}));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9276,5972],()=>r(30305));module.exports=i})();