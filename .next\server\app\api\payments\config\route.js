"use strict";(()=>{var e={};e.id=4946,e.ids=[4946],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},98115:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>R,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p});var s={};t.r(s),t.d(s,{GET:()=>u});var o=t(49303),n=t(88716),a=t(60670),i=t(87070),c=t(54211);let u=(0,t(84875).lm)(async e=>{c.kg.apiRequest("GET","/api/payments/config");try{let e=["RAZORPAY_KEY_ID","RAZORPAY_KEY_SECRET","NEXT_PUBLIC_RAZORPAY_KEY_ID"].reduce((e,r)=>(e[r]=!!process.env[r],e),{}),r=Object.values(e).every(Boolean);return i.NextResponse.json({success:!0,configured:r,environment_variables:e,public_key:"rzp_test_H8VYcEtWS9hwc8",environment:"production",message:r?"Razorpay payment integration is properly configured":"Some environment variables are missing"})}catch(e){return c.kg.error("Payment config check failed",e),i.NextResponse.json({success:!1,configured:!1,error:"Failed to check payment configuration",details:e.message},{status:500})}}),d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/payments/config/route",pathname:"/api/payments/config",filename:"route",bundlePath:"app/api/payments/config/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\config\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:m}=d,R="/api/payments/config/route";function g(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}},84875:(e,r,t)=>{t.d(r,{AY:()=>d,M_:()=>c,_7:()=>i,dR:()=>u,gz:()=>n,lm:()=>m,p8:()=>a});var s=t(87070),o=t(29489);class n extends Error{constructor(e,r=500,t="INTERNAL_ERROR",s){super(e),this.statusCode=r,this.code=t,this.details=s,this.name="AppError",Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class a extends n{constructor(e,r){super(e,400,"VALIDATION_ERROR",r),this.name="ValidationError"}}class i extends n{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class c extends n{constructor(e="Insufficient permissions"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class u extends n{constructor(e="Resource"){super(`${e} not found`,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class d extends n{constructor(e){super(e,409,"CONFLICT_ERROR"),this.name="ConflictError"}}class l extends n{constructor(e,r){super(e,500,"DATABASE_ERROR",r),this.name="DatabaseError"}}function p(e){let r={VALIDATION_ERROR:"Invalid input provided",AUTHENTICATION_ERROR:"Authentication required",AUTHORIZATION_ERROR:"Insufficient permissions",NOT_FOUND_ERROR:"Resource not found",CONFLICT_ERROR:"Request conflicts with current state",RATE_LIMIT_ERROR:"Too many requests",DATABASE_ERROR:"Database operation failed",EXTERNAL_SERVICE_ERROR:"External service unavailable",INTERNAL_ERROR:"An error occurred processing your request"};return r[e]||r.INTERNAL_ERROR}function m(e){return async(...r)=>{try{return await e(...r)}catch(e){return function(e){let r=`req_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;if(e instanceof Error?console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,name:e.name,message:e.message,stack:void 0,code:e.code}):console.error(`[${new Date().toISOString()}] API Error:`,{requestId:r,error:e}),e instanceof n){let t={success:!1,error:{code:e.code,message:p(e.code),requestId:r,...!1}};return s.NextResponse.json(t,{status:e.statusCode})}if(e instanceof o.j){let t=new a("Validation failed",{errors:e.issues.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}),o={success:!1,error:{code:t.code,message:"Validation failed",requestId:r,...!1}};return s.NextResponse.json(o,{status:t.statusCode})}if(e&&"object"==typeof e&&("code"in e||"name"in e)){if("MongoServerError"===e.name||"ValidationError"===e.name||"CastError"===e.name||"DocumentNotFoundError"===e.name||"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name||"number"==typeof e.code&&11e3===e.code){let t=function(e){if(11e3===e.code||"MongoServerError"===e.name){let r=Object.keys(e.keyPattern||{})[0]||"field";return new d(`${r} already exists`)}return"ValidationError"===e.name?new a("Validation failed"):"CastError"===e.name?new a("Invalid data format"):"DocumentNotFoundError"===e.name?new u:"MongoNetworkError"===e.name||"MongoTimeoutError"===e.name?new l("Database connection failed"):new l("Database operation failed",{name:e.name,message:e.message})}(e),o={success:!1,error:{code:t.code,message:p(t.code),requestId:r,...!1}};return s.NextResponse.json(o,{status:t.statusCode})}if("string"==typeof e.code&&e.code.startsWith("P")){let t=function(e){switch(e.code){case"P2002":let r=e.meta?.target?.[0]||"field";return new d(`${r} already exists`);case"P2003":let t=e.meta?.constraint;if(t?.includes("userId"))return new i("Invalid user session");return new a("Invalid reference to related record");case"P2025":case"P2001":return new u;case"P2014":return new a("Missing required relationship");case"P2000":return new a("Input value is too long");case"P2004":return new a("Data constraint violation");default:return new l("Database operation failed",{code:e.code,message:e.message})}}(e),o={success:!1,error:{code:t.code,message:p(t.code),requestId:r,...!1}};return s.NextResponse.json(o,{status:t.statusCode})}}let t={success:!1,error:{code:"INTERNAL_ERROR",message:"An error occurred processing your request",requestId:r,...!1}};return s.NextResponse.json(t,{status:500})}(e)}}}},54211:(e,r,t)=>{var s;t.d(r,{kg:()=>n}),function(e){e[e.ERROR=0]="ERROR",e[e.WARN=1]="WARN",e[e.INFO=2]="INFO",e[e.DEBUG=3]="DEBUG"}(s||(s={}));class o{constructor(){this.isDevelopment=!1,this.logLevel=this.isDevelopment?1:2}shouldLog(e){return e<=this.logLevel}formatMessage(e){let{timestamp:r,level:t,message:o,context:n,error:a,userId:i,requestId:c}=e,u=s[t],d=`[${r}] ${u}: ${o}`;return i&&(d+=` | User: ${i}`),c&&(d+=` | Request: ${c}`),n&&Object.keys(n).length>0&&(d+=` | Context: ${JSON.stringify(n)}`),a&&(d+=` | Error: ${a.message}`,this.isDevelopment&&a.stack&&(d+=`
Stack: ${a.stack}`)),d}log(e,r,t,s){if(!this.shouldLog(e))return;let o={timestamp:new Date().toISOString(),level:e,message:r,context:t,error:s},n=this.formatMessage(o);if(this.isDevelopment)switch(e){case 0:console.error(n);break;case 1:console.warn(n);break;case 2:console.info(n);break;case 3:console.debug(n)}else console.log(JSON.stringify(o))}error(e,r,t){this.log(0,e,t,r)}warn(e,r){this.log(1,e,r)}info(e,r){this.log(2,e,r)}debug(e,r){this.log(3,e,r)}apiRequest(e,r,t,s){this.info(`API ${e} ${r}`,{...s,userId:t,type:"api_request"})}apiResponse(e,r,t,s,o){this.info(`API ${e} ${r} - ${t}`,{...o,statusCode:t,duration:s,type:"api_response"})}apiError(e,r,t,s,o){this.error(`API ${e} ${r} failed`,t,{...o,userId:s,type:"api_error"})}authSuccess(e,r,t){this.info("Authentication successful",{...t,userId:e,method:r,type:"auth_success"})}authFailure(e,r,t,s){this.warn("Authentication failed",{...s,email:e,method:r,reason:t,type:"auth_failure"})}dbQuery(e,r,t,s){this.debug(`DB ${e} on ${r}`,{...s,operation:e,table:r,duration:t,type:"db_query"})}dbError(e,r,t,s){this.error(`DB ${e} on ${r} failed`,t,{...s,operation:e,table:r,type:"db_error"})}securityEvent(e,r,t){this.log("high"===r?0:"medium"===r?1:2,`Security event: ${e}`,{...t,severity:r,type:"security_event"})}rateLimitHit(e,r,t,s){this.warn("Rate limit exceeded",{...s,identifier:e,limit:r,window:t,type:"rate_limit"})}emailSent(e,r,t,s){this.info("Email sent",{...s,to:e,subject:r,template:t,type:"email_sent"})}emailError(e,r,t,s){this.error("Email failed to send",t,{...s,to:e,subject:r,type:"email_error"})}performance(e,r,t){this.log(r>5e3?1:3,`Performance: ${e} took ${r}ms`,{...t,operation:e,duration:r,type:"performance"})}}let n=new o}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,9489],()=>t(98115));module.exports=s})();