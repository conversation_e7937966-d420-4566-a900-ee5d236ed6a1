"use strict";(()=>{var e={};e.id=9424,e.ids=[9424],e.modules={11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},86624:e=>{e.exports=require("querystring")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},54737:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>j,requestAsyncStorage:()=>m,routeModule:()=>f,serverHooks:()=>y,staticGenerationAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>p});var o=r(49303),n=r(88716),a=r(60670),u=r(87070),i=r(75571),d=r(95306),c=r(89456),l=r(81515);async function p(e,{params:t}){try{let e=await (0,i.getServerSession)(d.L);if(!e?.user)return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let r=e.user?.role==="ADMIN",s=e.user.id===t.id;if(!r&&!s)return u.NextResponse.json({success:!1,error:"Forbidden"},{status:403});await (0,c.Z)();let o=await l.n5.findById(t.id,{_id:1,name:1,email:1,createdAt:1,role:1}).lean();if(!o)return u.NextResponse.json({success:!1,error:"User not found"},{status:404});let n=await l.Order.aggregate([{$match:{userId:t.id,paymentStatus:"PAID"}},{$group:{_id:null,totalCount:{$sum:1},totalSum:{$sum:"$total"}}}]),a={_count:{id:n[0]?.totalCount??0},_sum:{total:n[0]?.totalSum??0}},[p,f,m]=await Promise.all([l.Cq.countDocuments({userId:t.id}),l.hQ.countDocuments({userId:t.id}),l.kL.countDocuments({userId:t.id})]),x=await l.Order.find({userId:t.id},{_id:1,orderNumber:1,status:1,total:1,createdAt:1}).sort({createdAt:-1}).limit(5).lean(),y={id:String(o._id),name:o.name??void 0,email:o.email??void 0,joinDate:o.createdAt,role:o.role??void 0},g=x.map(e=>({id:String(e._id),orderNumber:e.orderNumber,status:e.status,total:e.total,createdAt:e.createdAt})),j={user:y,orders:{total:a._count.id||0,totalSpent:a._sum.total||0,recent:g},counts:{reviews:p,wishlist:f,addresses:m},accountStatus:"ACTIVE"};return u.NextResponse.json({success:!0,data:j})}catch(e){return console.error("Error fetching user stats:",e),u.NextResponse.json({success:!1,error:"Failed to fetch user statistics"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/users/[id]/stats/route",pathname:"/api/users/[id]/stats",filename:"route",bundlePath:"app/api/users/[id]/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\stats\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:x,serverHooks:y}=f,g="/api/users/[id]/stats/route";function j(){return(0,a.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:x})}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var u=o?Object.getOwnPropertyDescriptor(e,n):null;u&&(u.get||u.set)?Object.defineProperty(s,n,u):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(45609));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,8691,2830,5306],()=>r(54737));module.exports=s})();