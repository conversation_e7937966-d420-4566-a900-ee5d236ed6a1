"use strict";(()=>{var e={};e.id=5511,e.ids=[5511],e.modules={21841:e=>{e.exports=require("@aws-sdk/client-s3")},11185:e=>{e.exports=require("mongoose")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},32694:e=>{e.exports=require("http2")},35240:e=>{e.exports=require("https")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},84492:e=>{e.exports=require("node:stream")},89062:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>x,requestAsyncStorage:()=>f,routeModule:()=>d,serverHooks:()=>v,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>c});var o=t(49303),n=t(88716),i=t(60670),a=t(87070),u=t(75571),p=t(95306),l=t(8379);async function c(e){try{let r=await (0,u.getServerSession)(p.L);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.formData(),s=t.get("file"),o=t.get("folder")||"uploads";if(!s)return a.NextResponse.json({error:"No file provided"},{status:400});let n=(0,l.D0)(s);if(!n.valid)return a.NextResponse.json({error:n.error},{status:400});let i=await (0,l.fo)(s,o);if(i.success&&i.file)return a.NextResponse.json({success:!0,file:i.file,message:"File uploaded successfully to R2"});return a.NextResponse.json({success:!1,error:i.error||"Upload failed"},{status:500})}catch(e){return console.error("Error uploading file to R2:",e),a.NextResponse.json({success:!1,error:"Failed to upload file to R2"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/media/upload/route",pathname:"/api/media/upload",filename:"route",bundlePath:"app/api/media/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\upload\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:v}=d,g="/api/media/upload/route";function x(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:m})}},8379:(e,r,t)=>{t.d(r,{D0:()=>f,NA:()=>c,Z7:()=>l,fo:()=>p,rP:()=>n});var s=t(21841);t(38376);let o=new s.S3Client({region:"auto",endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,credentials:{accessKeyId:process.env.R2_ACCESS_KEY_ID,secretAccessKey:process.env.R2_SECRET_ACCESS_KEY},forcePathStyle:!0});function n(){return{hasAccessKey:!!process.env.R2_ACCESS_KEY_ID,hasSecretKey:!!process.env.R2_SECRET_ACCESS_KEY,hasBucketName:!!process.env.R2_BUCKET_NAME,hasAccountId:!!process.env.R2_ACCOUNT_ID,hasPublicUrl:!!process.env.R2_PUBLIC_URL,bucketName:i,publicUrl:a,endpoint:`https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`}}let i=process.env.R2_BUCKET_NAME||"herbalicious-images",a=process.env.R2_PUBLIC_URL||`https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;function u(e){return a.includes("pub-")&&a.includes(".r2.dev")||a.includes(i)?`${a}/${e}`:`${a}/${i}/${e}`}async function p(e,r="uploads"){try{let t=f(e);if(!t.valid)return{success:!1,error:t.error};let n=Date.now(),a=e.name.toLowerCase().replace(/[^a-z0-9.-]/g,"_").replace(/_{2,}/g,"_").replace(/^_|_$/g,"");if(!a.split(".").pop())return{success:!1,error:"File must have an extension"};let p=`${r}/${n}_${a}`,l=await e.arrayBuffer(),c=new s.PutObjectCommand({Bucket:i,Key:p,Body:new Uint8Array(l),ContentType:e.type,ContentLength:e.size,ContentDisposition:"attachment",CacheControl:"max-age=********"});await o.send(c);let m={key:p,name:e.name,size:e.size,type:d(e.name,e.type),url:u(p),lastModified:new Date,folder:r};return{success:!0,file:m}}catch(e){return console.error("Error uploading to R2:",e),{success:!1,error:e instanceof Error?e.message:"Upload failed"}}}async function l(e){try{let r=new s.DeleteObjectCommand({Bucket:i,Key:e});return await o.send(r),!0}catch(e){return console.error("Error deleting from R2:",e),!1}}async function c(e,r=100){try{let t=new s.ListObjectsV2Command({Bucket:i,Prefix:e?`${e}/`:void 0,MaxKeys:r}),n=await o.send(t);if(!n.Contents)return[];return n.Contents.map(e=>{let r=e.Key,t=r.split("/").pop()||r;return{key:r,name:t,size:e.Size||0,type:d(t),url:u(r),lastModified:e.LastModified||new Date,folder:r.includes("/")?r.split("/")[0]:void 0}})}catch(e){return console.error("Error listing R2 files:",e),[]}}function d(e,r){let t=e.split(".").pop()?.toLowerCase();if(r){if(r.startsWith("image/"))return"image";if(r.startsWith("video/"))return"video";if("application/pdf"===r||r.startsWith("application/msword")||r.startsWith("application/vnd.openxmlformats-officedocument"))return"document"}return["jpg","jpeg","png","gif","webp","svg","bmp","ico"].includes(t||"")?"image":["mp4","avi","mov","wmv","flv","webm","mkv","m4v"].includes(t||"")?"video":["pdf","doc","docx","txt","rtf","xls","xlsx","ppt","pptx"].includes(t||"")?"document":"other"}function f(e){if(e.size>10485760)return{valid:!1,error:"File size must be less than 10MB"};if(!["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","video/mp4","video/webm","application/pdf"].includes(e.type))return{valid:!1,error:"File type not supported"};let r=e.name.toLowerCase();if(![".jpg",".jpeg",".png",".gif",".webp",".svg",".mp4",".webm",".pdf"].some(e=>r.endsWith(e)))return{valid:!1,error:"File extension not allowed"};let t=r.split(".");if(t.length>2){let e=["php","exe","sh","bat","cmd","com","scr","vbs","js","jar"];for(let r=0;r<t.length-1;r++)if(e.includes(t[r]))return{valid:!1,error:"Suspicious file name detected"}}return r.includes("\0")?{valid:!1,error:"Invalid file name"}:"image/svg+xml"===e.type||r.endsWith(".svg")?{valid:!0,error:void 0}:{valid:!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var o=t(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=o?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972,8691,2830,8376,5306],()=>t(89062));module.exports=s})();